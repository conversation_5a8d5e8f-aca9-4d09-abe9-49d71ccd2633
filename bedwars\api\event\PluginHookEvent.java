package de.marcely.bedwars.api.event;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.plugin.Plugin;

/**
 * Gets called before this plugin is attempting to hook itself into another one.
 */
public class PluginHookEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Plugin plugin;

  @Getter @Setter
  private boolean cancelled = false;

  public PluginHookEvent(Plugin plugin) {
    this.plugin = plugin;
  }

  /**
   * Returns the plugin in which MBedwars wants to hook into.
   *
   * @return The plugin in which we want to hook into
   */
  public Plugin getPlugin() {
    return this.plugin;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
