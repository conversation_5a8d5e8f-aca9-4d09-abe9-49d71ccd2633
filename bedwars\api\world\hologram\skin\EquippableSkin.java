package de.marcely.bedwars.api.world.hologram.skin;

import de.marcely.bedwars.api.world.hologram.HologramEquipment;
import de.marcely.bedwars.api.world.hologram.HologramSkin;

/**
 * Represents a skin that is able to hold equipment.
 */
public interface EquippableSkin extends HologramSkin {

  /**
   * Get the equipment instance of this entity.
   *
   * @return The equipment that this skin currently holds
   */
  HologramEquipment getEquipment();
}
