package cn.acebrand.acevotemode.events.good;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.Location;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.PotionMeta;
import org.bukkit.inventory.meta.Damageable;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 物品奖励事件 - 好事件
 * 奖励：1.随机剑类 2.击退棒 3.试作型神器森罗万象虫洞吞噬者MK-2
 */
public class ItemRewardEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;

    public ItemRewardEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File goodDir = new File(eventsDir, "good");
            if (!goodDir.exists()) {
                goodDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(goodDir, "item_reward.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/good/item_reward.yml", false);
                plugin.getLogger().info("已生成物品奖励事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载物品奖励事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载物品奖励事件配置失败: " + e.getMessage());
            createDefaultConfig();
        }
    }

    /**
     * 创建默认配置
     */
    private void createDefaultConfig() {
        config = new YamlConfiguration();
        // 设置默认值
        config.set("event.weight", 10);
        config.set("rewards.random_sword.weight", 40);
        config.set("rewards.knockback_stick.weight", 30);
        config.set("rewards.special_fishing_rod.weight", 30);
    }

    @Override
    public void execute(Player player, Location location) {
        // 检查是否是直接生效的BUFF
        if (shouldGiveInstantBuff()) {
            giveInstantBuff(player);
            return;
        }

        ItemStack reward = getRandomReward();

        if (reward != null) {
            // 在破坏位置掉落物品
            if (location.getWorld() != null) {
                location.getWorld().dropItemNaturally(location, reward);
            } else {
                player.getInventory().addItem(reward);
            }

            // 发送奖励消息（如果启用）
            String itemName = reward.getItemMeta() != null && reward.getItemMeta().hasDisplayName()
                    ? reward.getItemMeta().getDisplayName()
                    : reward.getType().name();

            if (shouldSendRewardMessage()) {
                String messagePrefix = getMessagePrefix();
                player.sendMessage(messagePrefix + "你获得了: " + itemName);
            }

            // 记录到日志（始终记录）
            plugin.getLogger().info("玩家 " + player.getName() + " 获得物品奖励: " + itemName);
        }
    }

    /**
     * 检查是否应该给予直接生效的BUFF
     */
    private boolean shouldGiveInstantBuff() {
        if (config == null)
            return false;

        int instantBuffWeight = config.getInt("rewards.instant_buff.weight", 8);
        int totalWeight = getTotalWeight();

        int roll = random.nextInt(totalWeight);
        return roll < instantBuffWeight;
    }

    /**
     * 获取总权重（用于BUFF概率计算）
     */
    private int getTotalWeight() {
        // 这里简化计算，实际应该包含所有奖励的权重
        return 200; // 假设总权重为200
    }

    /**
     * 获取随机奖励物品
     */
    private ItemStack getRandomReward() {
        if (config == null) {
            return getRandomSword(); // 默认奖励
        }

        // 计算总权重
        int swordWeight = config.getInt("rewards.random_sword.weight", 20);
        int stickWeight = config.getInt("rewards.knockback_stick.weight", 15);
        int rodWeight = config.getInt("rewards.special_fishing_rod.weight", 15);
        int divineWeight = config.getInt("rewards.divine_sword.weight", 15);
        int axeWeight = config.getInt("rewards.instant_kill_axe.weight", 10);
        int eggWeight = config.getInt("rewards.pigeon_eggs.weight", 15);
        int bowWeight = config.getInt("rewards.normal_bow.weight", 10);
        int powerBowWeight = config.getInt("rewards.power_bow.weight", 10);
        int divineBowWeight = config.getInt("rewards.divine_bow.weight", 10);
        int arrowWeight = config.getInt("rewards.arrows.weight", 15);
        int toolWeight = config.getInt("rewards.random_tool.weight", 12);
        int worldEaterWeight = config.getInt("rewards.world_eater.weight", 8);
        int beneficialPotionWeight = config.getInt("rewards.beneficial_potion.weight", 12);
        int splashPotionWeight = config.getInt("rewards.splash_potion.weight", 10);
        int tntWeight = config.getInt("rewards.tnt.weight", 8);
        int fireChargeWeight = config.getInt("rewards.fire_charge.weight", 10);
        int enderPearlWeight = config.getInt("rewards.ender_pearl.weight", 6);
        int cobwebWeight = config.getInt("rewards.cobweb.weight", 8);
        int waterBucketWeight = config.getInt("rewards.water_bucket.weight", 7);
        int steakWeight = config.getInt("rewards.steak.weight", 12);
        int goldenAppleWeight = config.getInt("rewards.golden_apple.weight", 5);
        int rescuePlatformWeight = config.getInt("rewards.rescue_platform.weight", 6);
        int bridgeEggWeight = config.getInt("rewards.bridge_egg.weight", 7);
        int ironGolemWeight = config.getInt("rewards.iron_golem_guard.weight", 4);
        int silverfishWeight = config.getInt("rewards.silverfish.weight", 5);
        int tntSheepWeight = config.getInt("rewards.tnt_sheep.weight", 4);
        int instantBuffWeight = config.getInt("rewards.instant_buff.weight", 8);

        int totalWeight = swordWeight + stickWeight + rodWeight + divineWeight +
                axeWeight + eggWeight + bowWeight + powerBowWeight +
                divineBowWeight + arrowWeight + toolWeight + worldEaterWeight +
                beneficialPotionWeight + splashPotionWeight + tntWeight +
                fireChargeWeight + enderPearlWeight + cobwebWeight +
                waterBucketWeight + steakWeight + goldenAppleWeight +
                rescuePlatformWeight + bridgeEggWeight + ironGolemWeight +
                silverfishWeight + tntSheepWeight + instantBuffWeight;

        // 随机选择
        int roll = random.nextInt(totalWeight);
        int current = 0;

        if (roll < (current += swordWeight)) {
            return getRandomSword();
        } else if (roll < (current += stickWeight)) {
            return getKnockbackStick();
        } else if (roll < (current += rodWeight)) {
            return getSpecialFishingRod();
        } else if (roll < (current += divineWeight)) {
            return getDivineSword();
        } else if (roll < (current += axeWeight)) {
            return getInstantKillAxe();
        } else if (roll < (current += eggWeight)) {
            return getPigeonEggs();
        } else if (roll < (current += bowWeight)) {
            return getNormalBow();
        } else if (roll < (current += powerBowWeight)) {
            return getPowerBow();
        } else if (roll < (current += divineBowWeight)) {
            return getDivineBow();
        } else if (roll < (current += arrowWeight)) {
            return getArrows();
        } else if (roll < (current += toolWeight)) {
            return getRandomTool();
        } else if (roll < (current += worldEaterWeight)) {
            return getWorldEater();
        } else if (roll < (current += beneficialPotionWeight)) {
            return getBeneficialPotion();
        } else if (roll < (current += splashPotionWeight)) {
            return getSplashPotion();
        } else if (roll < (current += tntWeight)) {
            return getTNT();
        } else if (roll < (current += fireChargeWeight)) {
            return getFireCharge();
        } else if (roll < (current += enderPearlWeight)) {
            return getEnderPearl();
        } else if (roll < (current += cobwebWeight)) {
            return getCobweb();
        } else if (roll < (current += waterBucketWeight)) {
            return getWaterBucket();
        } else if (roll < (current += steakWeight)) {
            return getSteak();
        } else if (roll < (current += goldenAppleWeight)) {
            return getGoldenApple();
        } else if (roll < (current += rescuePlatformWeight)) {
            return getRescuePlatform();
        } else if (roll < (current += bridgeEggWeight)) {
            return getBridgeEgg();
        } else if (roll < (current += ironGolemWeight)) {
            return getIronGolemGuard();
        } else if (roll < (current += silverfishWeight)) {
            return getSilverfish();
        } else if (roll < (current += tntSheepWeight)) {
            return getTNTSheep();
        } else {
            return getInstantBuff();
        }
    }

    /**
     * 获取随机剑类
     */
    protected ItemStack getRandomSword() {
        // 从配置获取剑类材料列表
        List<String> materials = config != null ? config.getStringList("rewards.random_sword.materials")
                : Arrays.asList("WOODEN_SWORD", "STONE_SWORD", "IRON_SWORD", "DIAMOND_SWORD");

        if (materials.isEmpty()) {
            materials = Arrays.asList("WOODEN_SWORD", "STONE_SWORD", "IRON_SWORD", "DIAMOND_SWORD");
        }

        String selectedMaterial = materials.get(random.nextInt(materials.size()));
        Material material;
        try {
            material = Material.valueOf(selectedMaterial);
        } catch (IllegalArgumentException e) {
            material = Material.IRON_SWORD; // 默认
        }

        ItemStack sword = new ItemStack(material);
        ItemMeta meta = sword.getItemMeta();
        if (meta != null) {
            String displayName = config != null ? config.getString("rewards.random_sword.display_name", "§6幸运之剑")
                    : "§6幸运之剑";
            List<String> lore = config != null ? config.getStringList("rewards.random_sword.lore")
                    : Arrays.asList("§7来自幸运方块的神秘武器");

            meta.setDisplayName(displayName);
            meta.setLore(lore);
            sword.setItemMeta(meta);
        }

        return sword;
    }

    /**
     * 获取击退棒
     */
    protected ItemStack getKnockbackStick() {
        // 从配置获取材料
        String materialName = config != null ? config.getString("rewards.knockback_stick.material", "STICK") : "STICK";
        Material material;
        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            material = Material.STICK; // 默认
        }

        ItemStack stick = new ItemStack(material);
        ItemMeta meta = stick.getItemMeta();
        if (meta != null) {
            String displayName = config != null ? config.getString("rewards.knockback_stick.display_name", "§c击退棒")
                    : "§c击退棒";
            List<String> lore = config != null ? config.getStringList("rewards.knockback_stick.lore")
                    : Arrays.asList("§7一根神奇的木棒", "§7能够击退敌人");

            meta.setDisplayName(displayName);
            meta.setLore(lore);

            // 添加附魔
            if (config != null) {
                List<String> enchantments = config.getStringList("rewards.knockback_stick.enchantments");
                for (String enchantStr : enchantments) {
                    String[] parts = enchantStr.split(":");
                    if (parts.length == 2) {
                        try {
                            Enchantment enchant = getEnchantmentByName(parts[0]);
                            int level = Integer.parseInt(parts[1]);
                            if (enchant != null) {
                                meta.addEnchant(enchant, level, true);
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("无效的附魔配置: " + enchantStr);
                        }
                    }
                }

                // 隐藏附魔信息，只在lore中显示
                if (!enchantments.isEmpty()) {
                    meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                }
            } else {
                meta.addEnchant(Enchantment.KNOCKBACK, 2, true);
                meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
            }

            stick.setItemMeta(meta);
        }

        return stick;
    }

    /**
     * 获取试作型神器森罗万象虫洞吞噬者MK-2
     */
    protected ItemStack getSpecialFishingRod() {
        // 从配置获取材料
        String materialName = config != null ? config.getString("rewards.special_fishing_rod.material", "FISHING_ROD")
                : "FISHING_ROD";
        Material material;
        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            material = Material.FISHING_ROD; // 默认
        }

        ItemStack rod = new ItemStack(material);
        ItemMeta meta = rod.getItemMeta();
        if (meta != null) {
            String displayName = config != null
                    ? config.getString("rewards.special_fishing_rod.display_name", "§d试作型神器森罗万象虫洞吞噬者MK-2")
                    : "§d试作型神器森罗万象虫洞吞噬者MK-2";
            List<String> lore = config != null ? config.getStringList("rewards.special_fishing_rod.lore")
                    : Arrays.asList("§7传说中的神器", "§7拥有神秘的力量", "§c击退 I", "§c锋利 III");

            meta.setDisplayName(displayName);
            meta.setLore(lore);

            // 添加附魔
            if (config != null) {
                List<String> enchantments = config.getStringList("rewards.special_fishing_rod.enchantments");
                for (String enchantStr : enchantments) {
                    String[] parts = enchantStr.split(":");
                    if (parts.length == 2) {
                        try {
                            Enchantment enchant = getEnchantmentByName(parts[0]);
                            int level = Integer.parseInt(parts[1]);
                            if (enchant != null) {
                                meta.addEnchant(enchant, level, true);
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("无效的附魔配置: " + enchantStr);
                        }
                    }
                }

                // 隐藏附魔信息，只在lore中显示
                if (!enchantments.isEmpty()) {
                    meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                }
            } else {
                meta.addEnchant(Enchantment.KNOCKBACK, 1, true);
                meta.addEnchant(Enchantment.DAMAGE_ALL, 3, true);
                meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
            }

            rod.setItemMeta(meta);
        }

        return rod;
    }

    @Override
    public String getName() {
        return "ITEM_REWARD";
    }

    @Override
    public EventType getType() {
        return EventType.GOOD;
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 10) : 10;
    }

    /**
     * 获取腐竹赐予的神剑
     */
    protected ItemStack getDivineSword() {
        return createConfigurableItem("divine_sword", Material.DIAMOND_SWORD,
                "§b腐竹赐予的神剑",
                Arrays.asList("§7腐竹亲自锻造的神器", "§7蕴含着强大的力量", "§c锋利 III", "§c击退 I"));
    }

    /**
     * 获取秒人斧
     */
    protected ItemStack getInstantKillAxe() {
        ItemStack axe = createConfigurableItem("instant_kill_axe", Material.GOLDEN_AXE,
                "§6秒人斧",
                Arrays.asList("§c危险！极其锋利的斧头", "§c一击必杀！", "§7但是很容易损坏...", "§c锋利 255"));

        // 设置耐久值为1（使用现代API）
        if (config != null && config.contains("rewards.instant_kill_axe.durability")) {
            int durability = config.getInt("rewards.instant_kill_axe.durability", 1);
            ItemMeta axeMeta = axe.getItemMeta();
            if (axeMeta instanceof Damageable) {
                Damageable damageableMeta = (Damageable) axeMeta;
                int maxDurability = axe.getType().getMaxDurability();
                int damage = maxDurability - durability;
                damageableMeta.setDamage(damage);
                axe.setItemMeta(axeMeta);
            }
        }

        return axe;
    }

    /**
     * 获取鸽鸽的蛋
     */
    protected ItemStack getPigeonEggs() {
        int amount = config != null ? config.getInt("rewards.pigeon_eggs.amount", 12) : 12;
        return createConfigurableItem("pigeon_eggs", Material.EGG, amount,
                "§f鸽鸽的蛋",
                Arrays.asList("§7来自神秘鸽子的蛋", "§7据说有特殊的力量", "§e数量: " + amount + "个"));
    }

    /**
     * 获取普通弓
     */
    protected ItemStack getNormalBow() {
        return createConfigurableItem("normal_bow", Material.BOW,
                "§a幸运之弓",
                Arrays.asList("§7来自幸运方块的弓", "§7射击精准度很高"));
    }

    /**
     * 获取力量弓
     */
    protected ItemStack getPowerBow() {
        return createConfigurableItem("power_bow", Material.BOW,
                "§c力量之弓",
                Arrays.asList("§7蕴含强大力量的弓", "§7箭矢威力大幅提升", "§c力量 I"));
    }

    /**
     * 获取腐竹送你的神弓
     */
    protected ItemStack getDivineBow() {
        return createConfigurableItem("divine_bow", Material.BOW,
                "§b腐竹送你的神弓",
                Arrays.asList("§7腐竹亲自制作的神弓", "§7拥有强大的威力和冲击力", "§c力量 I", "§c冲击 I"));
    }

    /**
     * 获取箭矢
     */
    protected ItemStack getArrows() {
        int amount = config != null ? config.getInt("rewards.arrows.amount", 16) : 16;
        return createConfigurableItem("arrows", Material.ARROW, amount,
                "§f箭矢",
                Arrays.asList("§7精制的箭矢", "§7射击精准度很高", "§e数量: " + amount + "支"));
    }

    /**
     * 获取随机工具
     */
    protected ItemStack getRandomTool() {
        if (config == null) {
            return new ItemStack(Material.IRON_AXE); // 默认
        }

        // 计算工具权重
        int axeWeight = config.getInt("rewards.random_tool.tools.axe.weight", 40);
        int pickaxeWeight = config.getInt("rewards.random_tool.tools.pickaxe.weight", 40);
        int shearsWeight = config.getInt("rewards.random_tool.tools.shears.weight", 20);
        int totalWeight = axeWeight + pickaxeWeight + shearsWeight;

        // 随机选择工具类型
        int roll = random.nextInt(totalWeight);
        int current = 0;

        if (roll < (current += axeWeight)) {
            return getRandomAxe();
        } else if (roll < (current += pickaxeWeight)) {
            return getRandomPickaxe();
        } else {
            return getShears();
        }
    }

    /**
     * 获取随机斧头
     */
    private ItemStack getRandomAxe() {
        List<String> materials = config != null ? config.getStringList("rewards.random_tool.tools.axe.materials")
                : Arrays.asList("STONE_AXE", "IRON_AXE", "GOLDEN_AXE", "DIAMOND_AXE");

        if (materials.isEmpty()) {
            materials = Arrays.asList("STONE_AXE", "IRON_AXE", "GOLDEN_AXE", "DIAMOND_AXE");
        }

        String selectedMaterial = materials.get(random.nextInt(materials.size()));
        Material material;
        try {
            material = Material.valueOf(selectedMaterial);
        } catch (IllegalArgumentException e) {
            material = Material.IRON_AXE; // 默认
        }

        ItemStack axe = new ItemStack(material);
        ItemMeta meta = axe.getItemMeta();
        if (meta != null) {
            String displayName = config != null
                    ? config.getString("rewards.random_tool.tools.axe.display_name", "§6幸运斧头")
                    : "§6幸运斧头";
            List<String> lore = config != null ? config.getStringList("rewards.random_tool.tools.axe.lore")
                    : Arrays.asList("§7来自幸运方块的斧头", "§7砍伐效率很高");

            meta.setDisplayName(displayName);
            meta.setLore(lore);
            axe.setItemMeta(meta);
        }

        return axe;
    }

    /**
     * 获取随机镐子
     */
    private ItemStack getRandomPickaxe() {
        List<String> materials = config != null ? config.getStringList("rewards.random_tool.tools.pickaxe.materials")
                : Arrays.asList("STONE_PICKAXE", "IRON_PICKAXE", "GOLDEN_PICKAXE", "DIAMOND_PICKAXE");

        if (materials.isEmpty()) {
            materials = Arrays.asList("STONE_PICKAXE", "IRON_PICKAXE", "GOLDEN_PICKAXE", "DIAMOND_PICKAXE");
        }

        String selectedMaterial = materials.get(random.nextInt(materials.size()));
        Material material;
        try {
            material = Material.valueOf(selectedMaterial);
        } catch (IllegalArgumentException e) {
            material = Material.IRON_PICKAXE; // 默认
        }

        ItemStack pickaxe = new ItemStack(material);
        ItemMeta meta = pickaxe.getItemMeta();
        if (meta != null) {
            String displayName = config != null
                    ? config.getString("rewards.random_tool.tools.pickaxe.display_name", "§6幸运镐子")
                    : "§6幸运镐子";
            List<String> lore = config != null ? config.getStringList("rewards.random_tool.tools.pickaxe.lore")
                    : Arrays.asList("§7来自幸运方块的镐子", "§7挖掘效率很高");

            meta.setDisplayName(displayName);
            meta.setLore(lore);
            pickaxe.setItemMeta(meta);
        }

        return pickaxe;
    }

    /**
     * 获取剪刀
     */
    private ItemStack getShears() {
        String materialName = config != null ? config.getString("rewards.random_tool.tools.shears.material", "SHEARS")
                : "SHEARS";
        Material material;
        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            material = Material.SHEARS; // 默认
        }

        ItemStack shears = new ItemStack(material);
        ItemMeta meta = shears.getItemMeta();
        if (meta != null) {
            String displayName = config != null
                    ? config.getString("rewards.random_tool.tools.shears.display_name", "§6幸运剪刀")
                    : "§6幸运剪刀";
            List<String> lore = config != null ? config.getStringList("rewards.random_tool.tools.shears.lore")
                    : Arrays.asList("§7来自幸运方块的剪刀", "§7剪切效率很高");

            meta.setDisplayName(displayName);
            meta.setLore(lore);
            shears.setItemMeta(meta);
        }

        return shears;
    }

    /**
     * 获取世界吞噬者
     */
    protected ItemStack getWorldEater() {
        return createConfigurableItem("world_eater", Material.DIAMOND_PICKAXE,
                "§5世界吞噬者",
                Arrays.asList("§7传说中的神器镐子", "§7能够吞噬一切方块", "§c效率 V"));
    }

    /**
     * 获取有益药水
     */
    protected ItemStack getBeneficialPotion() {
        if (config == null) {
            return new ItemStack(Material.POTION); // 默认
        }

        // 计算药水权重
        int healWeight = config.getInt("rewards.beneficial_potion.potions.instant_health.weight", 20);
        int regenWeight = config.getInt("rewards.beneficial_potion.potions.regeneration.weight", 18);
        int invisWeight = config.getInt("rewards.beneficial_potion.potions.invisibility.weight", 15);
        int speedWeight = config.getInt("rewards.beneficial_potion.potions.speed.weight", 17);
        int jumpWeight = config.getInt("rewards.beneficial_potion.potions.jump_boost.weight", 15);
        int strengthWeight = config.getInt("rewards.beneficial_potion.potions.strength.weight", 15);

        int totalWeight = healWeight + regenWeight + invisWeight + speedWeight + jumpWeight + strengthWeight;

        // 随机选择药水类型
        int roll = random.nextInt(totalWeight);
        int current = 0;

        String potionType;
        if (roll < (current += healWeight)) {
            potionType = "instant_health";
        } else if (roll < (current += regenWeight)) {
            potionType = "regeneration";
        } else if (roll < (current += invisWeight)) {
            potionType = "invisibility";
        } else if (roll < (current += speedWeight)) {
            potionType = "speed";
        } else if (roll < (current += jumpWeight)) {
            potionType = "jump_boost";
        } else {
            potionType = "strength";
        }

        return createPotionItem("beneficial_potion", potionType);
    }

    /**
     * 获取喷溅药水
     */
    protected ItemStack getSplashPotion() {
        if (config == null) {
            return new ItemStack(Material.SPLASH_POTION); // 默认
        }

        // 计算药水权重
        int slowWeight = config.getInt("rewards.splash_potion.potions.slowness.weight", 35);
        int poisonWeight = config.getInt("rewards.splash_potion.potions.poison.weight", 35);
        int weaknessWeight = config.getInt("rewards.splash_potion.potions.weakness.weight", 30);

        int totalWeight = slowWeight + poisonWeight + weaknessWeight;

        // 随机选择药水类型
        int roll = random.nextInt(totalWeight);
        int current = 0;

        String potionType;
        if (roll < (current += slowWeight)) {
            potionType = "slowness";
        } else if (roll < (current += poisonWeight)) {
            potionType = "poison";
        } else {
            potionType = "weakness";
        }

        return createPotionItem("splash_potion", potionType);
    }

    /**
     * 获取TNT
     */
    protected ItemStack getTNT() {
        int amount = config != null ? config.getInt("rewards.tnt.amount", 2) : 2;
        return createConfigurableItem("tnt", Material.TNT, amount,
                "§cTNT",
                Arrays.asList("§7危险的爆炸物", "§7小心使用！", "§e数量: " + amount + "个"));
    }

    /**
     * 获取火焰弹
     */
    protected ItemStack getFireCharge() {
        int amount = config != null ? config.getInt("rewards.fire_charge.amount", 4) : 4;
        return createSpecialItem("fire_charge", "Fireball", amount,
                "§6火焰弹",
                Arrays.asList("§7燃烧的弹药", "§7投掷后产生爆炸", "§e数量: " + amount + "个"));
    }

    /**
     * 获取末影珍珠
     */
    protected ItemStack getEnderPearl() {
        return createConfigurableItem("ender_pearl", Material.ENDER_PEARL,
                "§5末影珍珠",
                Arrays.asList("§7神秘的传送道具", "§7投掷后瞬间传送"));
    }

    /**
     * 获取蜘蛛网
     */
    protected ItemStack getCobweb() {
        return createConfigurableItem("cobweb", Material.COBWEB,
                "§f蜘蛛网",
                Arrays.asList("§7粘性的陷阱", "§7可以减慢敌人速度"));
    }

    /**
     * 获取水桶
     */
    protected ItemStack getWaterBucket() {
        return createConfigurableItem("water_bucket", Material.WATER_BUCKET,
                "§9水桶",
                Arrays.asList("§7装满水的桶", "§7可以用来灭火或阻挡敌人"));
    }

    /**
     * 获取牛排
     */
    protected ItemStack getSteak() {
        int amount = config != null ? config.getInt("rewards.steak.amount", 12) : 12;
        return createConfigurableItem("steak", Material.COOKED_BEEF, amount,
                "§6牛排",
                Arrays.asList("§7美味的熟牛肉", "§7恢复大量饥饿值", "§e数量: " + amount + "个"));
    }

    /**
     * 获取金苹果
     */
    protected ItemStack getGoldenApple() {
        int amount = config != null ? config.getInt("rewards.golden_apple.amount", 4) : 4;
        return createConfigurableItem("golden_apple", Material.GOLDEN_APPLE, amount,
                "§6金苹果",
                Arrays.asList("§7珍贵的治疗食物", "§7提供强大的恢复效果", "§e数量: " + amount + "个"));
    }

    /**
     * 获取救援平台
     */
    protected ItemStack getRescuePlatform() {
        return createSpecialItem("rescue_platform", "RescuePlatform", 2,
                "§a救援平台",
                Arrays.asList("§7紧急救援道具", "§7可以快速搭建平台", "§e数量: 2个"));
    }

    /**
     * 获取搭桥蛋
     */
    protected ItemStack getBridgeEgg() {
        return createSpecialItem("bridge_egg", "egg-bridger", 1,
                "§e搭桥蛋",
                Arrays.asList("§7神奇的搭桥道具", "§7投掷后自动搭建桥梁"));
    }

    /**
     * 获取铁傀儡守卫
     */
    protected ItemStack getIronGolemGuard() {
        return createSpecialItem("iron_golem_guard", "GuardDog", 1,
                "§7铁傀儡守卫",
                Arrays.asList("§7忠诚的守护者", "§7会保护你免受敌人攻击"));
    }

    /**
     * 获取蠹虫
     */
    protected ItemStack getSilverfish() {
        return createSpecialItem("silverfish", "silverfish", 1,
                "§8蠹虫",
                Arrays.asList("§7投掷后召唤友善的蠹虫", "§7它们会帮助你攻击敌人"));
    }

    /**
     * 获取自爆羊
     */
    protected ItemStack getTNTSheep() {
        return createSpecialItem("tnt_sheep", "TNTSheep", 1,
                "§c自爆羊",
                Arrays.asList("§7危险的爆炸羊", "§7会追击敌人并爆炸"));
    }

    /**
     * 获取直接生效BUFF（这个方法不会被调用，因为BUFF是直接生效的）
     */
    private ItemStack getInstantBuff() {
        // 这个方法实际上不会被调用，因为BUFF是直接生效的
        // 返回一个占位符物品
        ItemStack placeholder = new ItemStack(Material.PAPER);
        ItemMeta meta = placeholder.getItemMeta();
        if (meta != null) {
            meta.setDisplayName("§d神秘祝福");
            meta.setLore(Arrays.asList("§7来自幸运方块的神秘力量"));
            placeholder.setItemMeta(meta);
        }
        return placeholder;
    }

    /**
     * 给予玩家直接生效的BUFF
     */
    protected void giveInstantBuff(Player player) {
        if (config == null) {
            return;
        }

        // 计算BUFF权重
        int healWeight = config.getInt("rewards.instant_buff.buffs.instant_heal.weight", 20);
        int regenWeight = config.getInt("rewards.instant_buff.buffs.regeneration.weight", 18);
        int invisWeight = config.getInt("rewards.instant_buff.buffs.invisibility.weight", 15);
        int speedWeight = config.getInt("rewards.instant_buff.buffs.speed.weight", 17);
        int jumpWeight = config.getInt("rewards.instant_buff.buffs.jump_boost.weight", 15);
        int resistanceWeight = config.getInt("rewards.instant_buff.buffs.resistance.weight", 15);

        int totalWeight = healWeight + regenWeight + invisWeight + speedWeight + jumpWeight + resistanceWeight;

        // 随机选择BUFF类型
        int roll = random.nextInt(totalWeight);
        int current = 0;

        String buffType;
        if (roll < (current += healWeight)) {
            buffType = "instant_heal";
        } else if (roll < (current += regenWeight)) {
            buffType = "regeneration";
        } else if (roll < (current += invisWeight)) {
            buffType = "invisibility";
        } else if (roll < (current += speedWeight)) {
            buffType = "speed";
        } else if (roll < (current += jumpWeight)) {
            buffType = "jump_boost";
        } else {
            buffType = "resistance";
        }

        // 应用BUFF效果
        applyBuff(player, buffType);
    }

    /**
     * 应用BUFF效果到玩家
     */
    private void applyBuff(Player player, String buffType) {
        String configPath = "rewards.instant_buff.buffs." + buffType;

        String effectName = config.getString(configPath + ".effect", "SPEED");
        int level = config.getInt(configPath + ".level", 1);
        int duration = config.getInt(configPath + ".duration", 600); // 游戏刻
        String message = config.getString(configPath + ".message", "§a[幸运方块] §f你获得了神秘祝福！");

        try {
            // 获取药水效果类型
            org.bukkit.potion.PotionEffectType effectType = org.bukkit.potion.PotionEffectType.getByName(effectName);
            if (effectType != null) {
                // 应用药水效果
                org.bukkit.potion.PotionEffect effect = new org.bukkit.potion.PotionEffect(effectType, duration,
                        level - 1);
                player.addPotionEffect(effect);

                // 发送消息（如果启用）
                if (shouldSendBuffMessage()) {
                    player.sendMessage(message);
                }

                plugin.getLogger().info(
                        "玩家 " + player.getName() + " 获得BUFF: " + effectName + " " + level + " (" + duration + "刻)");
            } else {
                plugin.getLogger().warning("无效的药水效果类型: " + effectName);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("应用BUFF失败: " + e.getMessage());
        }
    }

    /**
     * 创建可配置的物品
     */
    private ItemStack createConfigurableItem(String configKey, Material defaultMaterial,
            String defaultName, List<String> defaultLore) {
        return createConfigurableItem(configKey, defaultMaterial, 1, defaultName, defaultLore);
    }

    /**
     * 创建可配置的物品（带数量）
     */
    private ItemStack createConfigurableItem(String configKey, Material defaultMaterial, int amount,
            String defaultName, List<String> defaultLore) {
        // 从配置获取材料
        String materialName = config != null
                ? config.getString("rewards." + configKey + ".material", defaultMaterial.name())
                : defaultMaterial.name();
        Material material;
        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            material = defaultMaterial;
        }

        ItemStack item = new ItemStack(material, amount);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            String displayName = config != null
                    ? config.getString("rewards." + configKey + ".display_name", defaultName)
                    : defaultName;
            List<String> lore = config != null ? config.getStringList("rewards." + configKey + ".lore") : defaultLore;

            meta.setDisplayName(displayName);
            meta.setLore(lore);

            // 添加附魔
            if (config != null) {
                List<String> enchantments = config.getStringList("rewards." + configKey + ".enchantments");
                for (String enchantStr : enchantments) {
                    String[] parts = enchantStr.split(":");
                    if (parts.length == 2) {
                        try {
                            Enchantment enchant = getEnchantmentByName(parts[0]);
                            int level = Integer.parseInt(parts[1]);
                            if (enchant != null) {
                                meta.addEnchant(enchant, level, true);
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("无效的附魔配置: " + enchantStr);
                        }
                    }
                }

                // 隐藏附魔信息，只在lore中显示
                if (!enchantments.isEmpty()) {
                    meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                }
            }

            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * 创建药水物品
     */
    private ItemStack createPotionItem(String categoryKey, String potionType) {
        String configPath = "rewards." + categoryKey + ".potions." + potionType;

        // 从配置获取材料
        String materialName = config != null ? config.getString(configPath + ".material", "POTION") : "POTION";
        Material material;
        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            material = Material.POTION;
        }

        ItemStack potion = new ItemStack(material);

        // 设置药水效果
        if (potion.getItemMeta() instanceof PotionMeta) {
            PotionMeta potionMeta = (PotionMeta) potion.getItemMeta();

            // 根据药水类型设置效果
            PotionEffectType effectType = getPotionEffectType(potionType);
            if (effectType != null) {
                int duration = config != null ? config.getInt(configPath + ".duration", 300) : 300; // 默认15秒
                int level = config != null ? config.getInt(configPath + ".level", 1) : 1; // 默认等级1
                int amplifier = level - 1; // Bukkit中amplifier = level - 1

                PotionEffect effect = new PotionEffect(effectType, duration, amplifier);
                potionMeta.addCustomEffect(effect, true);
            }

            // 设置显示名称和描述
            String displayName = config != null ? config.getString(configPath + ".display_name", "§d神秘药水") : "§d神秘药水";
            List<String> lore = config != null ? config.getStringList(configPath + ".lore") : Arrays.asList("§7神秘的药水");

            potionMeta.setDisplayName(displayName);
            potionMeta.setLore(lore);

            potion.setItemMeta(potionMeta);
        } else {
            // 如果不是药水类型，使用普通ItemMeta
            ItemMeta meta = potion.getItemMeta();
            if (meta != null) {
                String displayName = config != null ? config.getString(configPath + ".display_name", "§d神秘药水")
                        : "§d神秘药水";
                List<String> lore = config != null ? config.getStringList(configPath + ".lore")
                        : Arrays.asList("§7神秘的药水");

                meta.setDisplayName(displayName);
                meta.setLore(lore);
                potion.setItemMeta(meta);
            }
        }

        return potion;
    }

    /**
     * 根据药水类型名称获取PotionEffectType
     */
    private PotionEffectType getPotionEffectType(String potionType) {
        switch (potionType.toLowerCase()) {
            case "slowness":
                return PotionEffectType.SLOW;
            case "poison":
                return PotionEffectType.POISON;
            case "weakness":
                return PotionEffectType.WEAKNESS;
            case "instant_heal":
                return PotionEffectType.HEAL;
            case "regeneration":
                return PotionEffectType.REGENERATION;
            case "invisibility":
                return PotionEffectType.INVISIBILITY;
            case "speed":
                return PotionEffectType.SPEED;
            case "jump_boost":
                return PotionEffectType.JUMP;
            case "strength":
                return PotionEffectType.INCREASE_DAMAGE;
            case "instant_health":
                return PotionEffectType.HEAL;
            case "night_vision":
                return PotionEffectType.NIGHT_VISION;
            case "fire_resistance":
                return PotionEffectType.FIRE_RESISTANCE;
            case "water_breathing":
                return PotionEffectType.WATER_BREATHING;
            case "resistance":
                return PotionEffectType.DAMAGE_RESISTANCE;
            default:
                plugin.getLogger().warning("未知的药水效果类型: " + potionType);
                return null;
        }
    }

    /**
     * 创建特殊物品
     */
    private ItemStack createSpecialItem(String configKey, String specialItemId, int amount,
            String defaultName, List<String> defaultLore) {
        // 尝试从BedWars获取特殊物品
        try {
            SpecialItem specialItem = GameAPI.get().getSpecialItem(specialItemId);
            if (specialItem != null) {
                ItemStack item = specialItem.getItemStack().clone();
                item.setAmount(amount);

                // 从配置更新显示名称和描述
                ItemMeta meta = item.getItemMeta();
                if (meta != null && config != null) {
                    String displayName = config.getString("rewards." + configKey + ".display_name", defaultName);
                    List<String> lore = config.getStringList("rewards." + configKey + ".lore");
                    if (lore.isEmpty()) {
                        lore = defaultLore;
                    }

                    meta.setDisplayName(displayName);
                    meta.setLore(lore);
                    item.setItemMeta(meta);
                }

                return item;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法获取特殊物品: " + specialItemId + " - " + e.getMessage());
        }

        // 如果获取特殊物品失败，返回默认物品
        ItemStack fallback = new ItemStack(Material.PAPER, amount);
        ItemMeta meta = fallback.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(defaultName + " §c(未找到特殊物品)");
            meta.setLore(defaultLore);
            fallback.setItemMeta(meta);
        }

        return fallback;
    }

    /**
     * 获取附魔（兼容新旧版本）
     */
    @SuppressWarnings("deprecation")
    private Enchantment getEnchantmentByName(String name) {
        // 首先处理名称映射
        String mappedName = mapEnchantmentName(name);

        try {
            // 尝试使用新的方法（1.13+）
            NamespacedKey key = NamespacedKey.minecraft(mappedName.toLowerCase());
            return Enchantment.getByKey(key);
        } catch (Exception e) {
            // 回退到旧方法（用于兼容性）
            try {
                return Enchantment.getByName(name);
            } catch (Exception ex) {
                plugin.getLogger().warning("无法找到附魔: " + name + " (映射为: " + mappedName + ")");
                return null;
            }
        }
    }

    /**
     * 映射旧的附魔名称到新的名称
     */
    private String mapEnchantmentName(String oldName) {
        switch (oldName.toUpperCase()) {
            case "ARROW_DAMAGE":
                return "power";
            case "ARROW_KNOCKBACK":
                return "punch";
            case "ARROW_FIRE":
                return "flame";
            case "ARROW_INFINITE":
                return "infinity";
            case "DAMAGE_ALL":
                return "sharpness";
            case "DAMAGE_ARTHROPODS":
                return "bane_of_arthropods";
            case "DAMAGE_UNDEAD":
                return "smite";
            case "DIG_SPEED":
                return "efficiency";
            case "DURABILITY":
                return "unbreaking";
            case "LOOT_BONUS_BLOCKS":
                return "fortune";
            case "LOOT_BONUS_MOBS":
                return "looting";
            case "OXYGEN":
                return "respiration";
            case "PROTECTION_ENVIRONMENTAL":
                return "protection";
            case "PROTECTION_EXPLOSIONS":
                return "blast_protection";
            case "PROTECTION_FALL":
                return "feather_falling";
            case "PROTECTION_FIRE":
                return "fire_protection";
            case "PROTECTION_PROJECTILE":
                return "projectile_protection";
            case "SILK_TOUCH":
                return "silk_touch";
            case "WATER_WORKER":
                return "aqua_affinity";
            case "KNOCKBACK":
                return "knockback";
            case "FIRE_ASPECT":
                return "fire_aspect";
            case "THORNS":
                return "thorns";
            default:
                return oldName.toLowerCase();
        }
    }

    /**
     * 检查是否应该发送奖励消息
     */
    private boolean shouldSendRewardMessage() {
        if (config == null)
            return true; // 默认发送
        return config.getBoolean("messages.send_reward_message", true);
    }

    /**
     * 检查是否应该发送BUFF消息
     */
    private boolean shouldSendBuffMessage() {
        if (config == null)
            return true; // 默认发送
        return config.getBoolean("messages.send_buff_message", true);
    }

    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null)
            return "§a[幸运方块] §f"; // 默认前缀
        return config.getString("messages.message_prefix", "§a[幸运方块] §f");
    }
}
