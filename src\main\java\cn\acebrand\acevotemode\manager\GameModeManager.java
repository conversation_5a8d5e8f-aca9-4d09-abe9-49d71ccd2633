package cn.acebrand.acevotemode.manager;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.ArtilleryMasterMode;
import cn.acebrand.acevotemode.gamemode.GameModeBase;
import cn.acebrand.acevotemode.gamemode.LowFireMode;
import cn.acebrand.acevotemode.gamemode.LuckyDropMode;
import cn.acebrand.acevotemode.gamemode.NormalMode;
import cn.acebrand.acevotemode.gamemode.SpeedMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import cn.acebrand.acevotemode.gamemode.UnlimitedFireMode;
import cn.acebrand.acevotemode.model.GameMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.event.arena.ArenaStatusChangeEvent;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 游戏模式管理器
 * 负责管理所有游戏模式的注册、激活和生命周期
 */
public class GameModeManager implements Listener {

    private final AceVoteMode plugin;

    // 注册的游戏模式
    private final Map<String, GameModeBase> registeredModes;

    // 当前激活的游戏模式 (竞技场 -> 游戏模式)
    private final Map<Arena, GameModeBase> activeModes;

    // 已处理的竞技场 (防止重复处理)
    private final Set<Arena> processedArenas;

    public GameModeManager(AceVoteMode plugin) {
        this.plugin = plugin;
        this.registeredModes = new HashMap<>();
        this.activeModes = new ConcurrentHashMap<>();
        this.processedArenas = ConcurrentHashMap.newKeySet();

        // 注册所有游戏模式
        registerGameModes();

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 注册所有游戏模式
     */
    private void registerGameModes() {
        // 注册无限火力模式
        registerGameMode(new UnlimitedFireMode(plugin));

        // 注册火力不足模式
        registerGameMode(new LowFireMode(plugin));

        // 注册急速模式
        registerGameMode(new SpeedMode(plugin));

        // 注册炮爷出击模式
        registerGameMode(new ArtilleryMasterMode(plugin));

        // 注册普通模式
        registerGameMode(new NormalMode(plugin));

        // 注册幸运降临模式
        registerGameMode(new LuckyDropMode(plugin));

        // 注册恐怖降临模式
        registerGameMode(new TerrorDescentMode(plugin));

        // 这里可以添加更多游戏模式

    }

    /**
     * 注册游戏模式
     */
    public void registerGameMode(GameModeBase gameMode) {
        registeredModes.put(gameMode.getModeId(), gameMode);
    }

    /**
     * 获取游戏模式
     */
    public GameModeBase getGameMode(String modeId) {
        return registeredModes.get(modeId);
    }

    /**
     * 获取所有注册的游戏模式
     */
    public Map<String, GameModeBase> getAllGameModes() {
        return new HashMap<>(registeredModes);
    }

    /**
     * 获取所有启用的游戏模式
     */
    public Map<String, GameModeBase> getEnabledGameModes() {
        Map<String, GameModeBase> enabledModes = new HashMap<>();
        for (Map.Entry<String, GameModeBase> entry : registeredModes.entrySet()) {
            if (entry.getValue().isEnabled()) {
                enabledModes.put(entry.getKey(), entry.getValue());
            }
        }
        return enabledModes;
    }

    /**
     * 检查竞技场是否已被处理
     */
    public boolean isArenaProcessed(Arena arena) {
        return processedArenas.contains(arena);
    }

    /**
     * 激活游戏模式
     */
    public boolean activateGameMode(Arena arena, String modeId) {
        // 标记为已处理
        processedArenas.add(arena);
        GameModeBase gameMode = registeredModes.get(modeId);
        if (gameMode == null) {
            return false;
        }

        if (!gameMode.isEnabled()) {
            return false;
        }

        // 如果竞技场已经有激活的模式，先停止它
        deactivateGameMode(arena);

        try {
            // 激活新模式
            activeModes.put(arena, gameMode);
            gameMode.onGameStart(arena);

            return true;

        } catch (Exception e) {
            e.printStackTrace();

            // 清理失败的激活
            activeModes.remove(arena);
            return false;
        }
    }

    /**
     * 停用游戏模式
     */
    public void deactivateGameMode(Arena arena) {
        GameModeBase activeMode = activeModes.remove(arena);
        if (activeMode != null) {
            try {
                activeMode.onGameEnd(arena);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取竞技场当前激活的游戏模式
     */
    public GameModeBase getActiveGameMode(Arena arena) {
        return activeModes.get(arena);
    }

    /**
     * 检查竞技场是否有激活的游戏模式
     */
    public boolean hasActiveGameMode(Arena arena) {
        return activeModes.containsKey(arena);
    }

    /**
     * 玩家加入竞技场时调用
     */
    public void onPlayerJoin(Player player, Arena arena) {
        GameModeBase activeMode = activeModes.get(arena);
        if (activeMode != null) {
            try {
                activeMode.onPlayerJoin(player, arena);
            } catch (Exception e) {
            }
        }
    }

    /**
     * 玩家离开竞技场时调用
     */
    public void onPlayerQuit(Player player, Arena arena) {
        GameModeBase activeMode = activeModes.get(arena);
        if (activeMode != null) {
            try {
                activeMode.onPlayerQuit(player, arena);
            } catch (Exception e) {
            }
        }
    }

    /**
     * 获取当前竞技场激活的游戏模式ID
     */
    public String getCurrentMode(Arena arena) {
        GameModeBase activeMode = activeModes.get(arena);
        return activeMode != null ? activeMode.getModeId() : null;
    }

    /**
     * 重载所有游戏模式配置
     */
    public void reloadAllConfigs() {
        for (GameModeBase gameMode : registeredModes.values()) {
            try {
                gameMode.reloadConfig();
            } catch (Exception e) {
            }
        }
    }

    /**
     * 清理所有激活的游戏模式
     */
    public void cleanup() {
        for (Arena arena : new HashMap<>(activeModes).keySet()) {
            deactivateGameMode(arena);
        }
        activeModes.clear();
    }

    /**
     * 获取游戏模式统计信息
     */
    public String getStatistics() {
        int totalModes = registeredModes.size();
        int enabledModes = getEnabledGameModes().size();
        int activeModes = this.activeModes.size();

        return String.format("Game Modes - Total: %d, Enabled: %d, Active: %d",
                totalModes, enabledModes, activeModes);
    }

    /**
     * 监听竞技场状态变化事件
     */
    @EventHandler
    public void onArenaStatusChange(ArenaStatusChangeEvent event) {
        Arena arena = event.getArena();
        ArenaStatus newStatus = event.getNewStatus();
        ArenaStatus oldStatus = event.getOldStatus();

        // 只处理游戏结束，游戏开始由 ArenaEventListener 处理，避免重复
        if (oldStatus == ArenaStatus.RUNNING && newStatus != ArenaStatus.RUNNING) {
            handleGameEnd(arena);
        }
    }

    /**
     * 处理游戏结束
     */
    private void handleGameEnd(Arena arena) {

        // 清理激活的模式
        GameModeBase activeMode = activeModes.remove(arena);
        if (activeMode != null) {
            try {
                activeMode.onGameEnd(arena);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 清理处理状态
        processedArenas.remove(arena);
    }

    /**
     * 获取无限火力模式实例
     */
    public UnlimitedFireMode getUnlimitedFireMode() {
        for (GameModeBase gameMode : registeredModes.values()) {
            if (gameMode instanceof UnlimitedFireMode) {
                return (UnlimitedFireMode) gameMode;
            }
        }
        return null;
    }

    /**
     * 获取火力不足模式实例
     */
    public LowFireMode getLowFireMode() {
        for (GameModeBase gameMode : registeredModes.values()) {
            if (gameMode instanceof LowFireMode) {
                return (LowFireMode) gameMode;
            }
        }
        return null;
    }
}
