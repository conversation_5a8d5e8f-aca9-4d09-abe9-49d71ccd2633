package de.marcely.bedwars.tools.location;

import de.marcely.bedwars.tools.Validate;
import java.util.Locale;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.BlockFace;
import org.bukkit.configuration.serialization.ConfigurationSerializable;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents a 3-dimensional position and a compass direction
 */
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class XYZD extends XYZ implements Cloneable, ConfigurationSerializable {

  protected Direction dir;

  public XYZD(Location loc) {
    this(loc.getX(), loc.getY(), loc.getZ());
  }

  public XYZD(Vector vec) {
    this(vec.getX(), vec.getY(), vec.getZ());
  }

  public XYZD(XYZ xyz) {
    this(xyz.x, xyz.y, xyz.z);
  }

  public XYZD(XYZD xyz) {
    this(xyz.x, xyz.y, xyz.z, xyz.dir);
  }

  public XYZD() {
    this(0, 0, 0);
  }

  public XYZD(double x, double y, double z) {
    this(x, y, z, Direction.NORTH);
  }

  public XYZD(double x, double y, double z, Direction dir) {
    Validate.notNull(dir, "direction");

    this.x = x;
    this.y = y;
    this.z = z;
    this.dir = dir;
  }

  @Override
  public XYZD setX(double x) {
    this.x = x;

    return this;
  }

  @Override
  public XYZD setY(double y) {
    this.y = y;

    return this;
  }

  @Override
  public XYZD setZ(double z) {
    this.z = z;

    return this;
  }

  @Override
  public XYZD set(double x, double y, double z) {
    this.x = x;
    this.y = y;
    this.z = z;

    return this;
  }

  @Override
  public XYZD set(XYZ xyz) {
    this.x = xyz.x;
    this.y = xyz.y;
    this.z = xyz.z;

    return this;
  }

  @Override
  public XYZD set(Location loc) {
    this.x = loc.getX();
    this.y = loc.getY();
    this.z = loc.getZ();

    return this;
  }

  @Override
  public XYZD add(double x, double y, double z) {
    this.x += x;
    this.y += y;
    this.z += z;

    return this;
  }

  @Override
  public XYZD add(XYZ xyz) {
    this.x += xyz.x;
    this.y += xyz.y;
    this.z += xyz.z;

    return this;
  }

  @Override
  public XYZD add(Location loc) {
    this.x += loc.getX();
    this.y += loc.getY();
    this.z += loc.getZ();

    return this;
  }

  @Override
  public XYZD add(Vector vec) {
    this.x += vec.getX();
    this.y += vec.getY();
    this.z += vec.getZ();

    return this;
  }

  @Override
  public XYZD subtract(double x, double y, double z) {
    this.x -= x;
    this.y -= y;
    this.z -= z;

    return this;
  }

  @Override
  public XYZD subtract(XYZ xyz) {
    this.x -= xyz.x;
    this.y -= xyz.y;
    this.z -= xyz.z;

    return this;
  }

  @Override
  public XYZD subtract(Location loc) {
    this.x -= loc.getX();
    this.y -= loc.getY();
    this.z -= loc.getZ();

    return this;
  }

  @Override
  public XYZD subtract(Vector vec) {
    this.x -= vec.getX();
    this.y -= vec.getY();
    this.z -= vec.getZ();

    return this;
  }

  @Override
  public XYZD multiply(double amount) {
    this.x *= amount;
    this.y *= amount;
    this.z *= amount;

    return this;
  }

  @Override
  public XYZD multiply(double x, double y, double z) {
    this.x *= x;
    this.y *= y;
    this.z *= z;

    return this;
  }

  @Override
  public XYZD zero() {
    this.x = 0;
    this.y = 0;
    this.z = 0;

    return this;
  }

  @Override
  public Location toLocation(World world) {
    return new Location(world, this.x, this.y, this.z);
  }

  /**
   * Copies and sets the xyz coordinates and direction coordinates from the given object
   *
   * @param xyz The object from which it shall be taken from
   * @return This XYZ instance
   */
  public XYZD set(XYZD xyz) {
    this.x = xyz.x;
    this.y = xyz.y;
    this.z = xyz.z;
    this.dir = xyz.dir;

    return this;
  }

  public Direction getDirection() {
    return this.dir;
  }

  public XYZD setDirection(Direction dir) {
    Validate.notNull(dir, "direction");

    this.dir = dir;

    return this;
  }

  @Override
  public XYZD clone() {
    return new XYZD(this.x, this.y, this.z, this.dir);
  }

  @Override
  public Map<String, Object> serialize() {
    final Map<String, Object> data = new HashMap<String, Object>();

    data.put("x", this.x);
    data.put("y", this.y);
    data.put("z", this.z);
    data.put("direction", this.dir.name());

    return data;
  }

  /**
   * Required method for deserialization
   *
   * @param data map to deserialize
   * @return deserialized xyzd instance
   * @see ConfigurationSerializable
   */
  public static XYZD deserialize(Map<String, Object> data) {
    return new XYZD(
        NumberConversions.toDouble(data.get("x")),
        NumberConversions.toDouble(data.get("y")),
        NumberConversions.toDouble(data.get("z")),
        Direction.valueOf((String) data.get("direction")));
  }


  /**
   * Represents a compass direction.
   * <p>
   *   Mainly used for storing beds and their direction.
   * </p>
   */
  public enum Direction {

    NORTH(BlockFace.NORTH),
    SOUTH(BlockFace.SOUTH),
    EAST(BlockFace.EAST),
    WEST(BlockFace.WEST);

    private final BlockFace blockFace;

    Direction(BlockFace blockFace) {
      this.blockFace = blockFace;
    }

    public BlockFace asBlockFace() {
      return this.blockFace;
    }

    /**
     * Returns the x coordinate modifier for this direction
     * <p>
     *   {@link #EAST} returns 1, {@link #WEST} returns -1, and all other directions return 0
     * </p>
     *
     * @return The x coordinate modifier
     */
    public int getModX() {
      switch (this) {
        case EAST:
          return 1;
        case WEST:
          return -1;
        default:
          return 0;
      }
    }

    /**
     * Returns the z coordinate modifier for this direction
     * <p>
     *   {@link #SOUTH} returns 1, {@link #NORTH} returns -1, and all other directions return 0
     * </p>
     *
     * @return The z coordinate modifier
     */
    public int getModZ() {
      switch (this) {
        case SOUTH:
          return 1;
        case NORTH:
          return -1;
        default:
          return 0;
      }
    }

    /**
     * Returns the id that has been used in the legacy versions (1.12 and older) of the game
     * <p>
     *   This refers to the block data that was used for beds.
     * </p>
     *
     * @return The legacy id
     */
    public int toLegacy() {
      switch (this) {
        case SOUTH:
          return 0;
        case WEST:
          return 1;
        case NORTH:
          return 2;
        case EAST:
          return 3;
      }

      return -1;
    }

    /**
     * Returns the direction from the given name
     * <p>
     *   Name refers to {@link #name()}. The name is case-insensitive.
     * </p>
     *
     * @param name The name of the direction
     * @return The direction or <code>null</code> if the name is invalid
     */
    @Nullable
    public static Direction fromName(String name) {
      try {
        return valueOf(name.toUpperCase(Locale.ENGLISH));
      } catch (IllegalArgumentException e) {
        return null;
      }
    }

    /**
     * Returns the direction from the given block face
     *
     * @param face The block face
     * @return The direction or <code>null</code> if the block face is invalid
     */
    @Nullable
    public static Direction fromBlockFace(BlockFace face) {
      switch (face) {
        case NORTH:
          return NORTH;
        case SOUTH:
          return SOUTH;
        case EAST:
          return EAST;
        case WEST:
          return WEST;
        default:
          return null;
      }
    }

    /**
     * Returns the direction from the given legacy id
     *
     * @param id The legacy id
     * @return The direction or <code>null</code> if the id is invalid
     * @see #toLegacy()
     */
    @Nullable
    public static Direction fromLegacy(int id) {
      switch (id) {
        case 0:
          return SOUTH;
        case 1:
          return WEST;
        case 2:
          return NORTH;
        case 3:
          return EAST;
      }

      return null;
    }
  }
}
