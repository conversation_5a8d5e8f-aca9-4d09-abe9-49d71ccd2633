package cn.acebrand.acevotemode.manager;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.spawner.SpawnerDurationModifier;
import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.GameAPI;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.scheduler.BukkitTask;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 全局资源生成器管理器
 * 为全局模式（默认模式）提供铁锭和金锭的自定义生成
 */
public class GlobalResourceManager {
    private final AceVoteMode plugin;
    private FileConfiguration config;
    private File configFile;

    // 资源配置
    private Map<String, ResourceConfig> resourceConfigs = new HashMap<>();

    // 生成器任务管理
    private final Map<Spawner, BukkitTask> spawnerTasks = new HashMap<>();
    private final Map<Spawner, Double> originalDropDurations = new HashMap<>();
    private final String MODIFIER_ID = "acevotemode:global-resource";

    // 全局唯一计数器，确保每个NBT标签都不同
    private static final AtomicLong globalCounter = new AtomicLong(0);

    public GlobalResourceManager(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        configFile = new File(plugin.getDataFolder(), "global-resources.yml");

        if (!configFile.exists()) {
            createDefaultConfig();
        }

        config = YamlConfiguration.loadConfiguration(configFile);
        loadResourceConfigs();
    }

    /**
     * 创建默认配置
     */
    private void createDefaultConfig() {
        try {
            configFile.getParentFile().mkdirs();
            configFile.createNewFile();

            FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(configFile);

            // 基础配置
            defaultConfig.set("enabled", true);
            defaultConfig.set("description", "全局资源生成器配置，为默认模式提供铁锭和金锭的自定义生成");

            // 铁锭配置（默认速度）
            defaultConfig.set("resources.iron.interval", 5); // 生成间隔（秒）
            defaultConfig.set("resources.iron.amount", 4); // 每次生成数量
            defaultConfig.set("resources.iron.enabled", true); // 是否启用自定义生成
            defaultConfig.set("resources.iron.max-nearby-items", 32); // 生成器附近最大物品数量，防止卡服
            defaultConfig.set("resources.iron.display-name", "&a经验"); // 自定义显示名称

            // 金锭配置（默认速度）
            defaultConfig.set("resources.gold.interval", 10); // 生成间隔（秒）
            defaultConfig.set("resources.gold.amount", 3); // 每次生成数量
            defaultConfig.set("resources.gold.enabled", true); // 是否启用自定义生成
            defaultConfig.set("resources.gold.max-nearby-items", 16); // 生成器附近最大物品数量，防止卡服
            defaultConfig.set("resources.gold.display-name", "&a经验"); // 自定义显示名称

            // 排除的模式（这些模式有自己的资源生成系统）
            defaultConfig.set("excluded-modes", Arrays.asList("unlimited-fire", "low-fire"));

            // 添加注释
            defaultConfig.setComments("enabled", Arrays.asList(
                    "是否启用全局资源生成器",
                    "true = 启用, false = 禁用"));

            defaultConfig.setComments("resources", Arrays.asList(
                    "资源生成配置",
                    "interval: 生成间隔（秒）",
                    "amount: 每次生成数量",
                    "enabled: 是否启用自定义生成",
                    "max-nearby-items: 生成器附近最大物品数量，防止卡服",
                    "display-name: 自定义显示名称"));

            defaultConfig.setComments("excluded-modes", Arrays.asList(
                    "排除的游戏模式列表",
                    "这些模式有自己的资源生成系统，不会使用全局资源生成"));

            defaultConfig.save(configFile);
            plugin.getLogger().info("[全局资源] 已创建默认配置文件: global-resources.yml");

        } catch (IOException e) {
            plugin.getLogger().severe("[全局资源] 创建配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 加载资源配置
     */
    private void loadResourceConfigs() {
        resourceConfigs.clear();

        if (config.getConfigurationSection("resources") != null) {
            for (String resourceType : config.getConfigurationSection("resources").getKeys(false)) {
                String path = "resources." + resourceType;

                boolean enabled = config.getBoolean(path + ".enabled", false);
                double interval = config.getDouble(path + ".interval", 10.0);
                int amount = config.getInt(path + ".amount", 1);
                int maxNearbyItems = config.getInt(path + ".max-nearby-items", 32); // 默认32个物品
                String displayName = config.getString(path + ".display-name", resourceType);

                ResourceConfig resourceConfig = new ResourceConfig(resourceType, interval, amount, enabled,
                        displayName, maxNearbyItems);
                resourceConfigs.put(resourceType, resourceConfig);



            }
        }

    }

    /**
     * 检查是否应该应用全局资源生成
     */
    public boolean shouldApplyGlobalResources(Arena arena) {
        if (!config.getBoolean("enabled", true)) {
            return false;
        }

        // 检查当前模式是否在排除列表中
        String currentMode = plugin.getGameModeManager().getCurrentMode(arena);
        List<String> excludedModes = config.getStringList("excluded-modes");

        return currentMode == null || !excludedModes.contains(currentMode);
    }

    /**
     * 为竞技场应用全局资源生成
     */
    public void applyGlobalResources(Arena arena) {
        if (!shouldApplyGlobalResources(arena)) {
            return;
        }

        try {
            for (Spawner spawner : arena.getSpawners()) {
                String typeId = spawner.getDropType().getId();

                // 只处理铁锭和金锭
                if ("iron".equals(typeId) || "gold".equals(typeId)) {
                    ResourceConfig resourceConfig = resourceConfigs.get(typeId);
                    if (resourceConfig != null && resourceConfig.isEnabled()) {

                        // 记录原始生成间隔
                        double originalInterval = spawner.getCurrentDropDuration();
                        originalDropDurations.put(spawner, originalInterval);

                        // 设置自定义资源生成
                        setupCustomResourceGeneration(spawner, resourceConfig);
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    /**
     * 设置自定义资源生成（完全替换原始生成逻辑）
     */
    private void setupCustomResourceGeneration(Spawner spawner, ResourceConfig resourceConfig) {
        String resourceType = resourceConfig.getType();

        // 禁用原始生成器（设置极长的间隔）
        try {
            spawner.addDropDurationModifier(
                    MODIFIER_ID + ":disable",
                    plugin,
                    SpawnerDurationModifier.Operation.SET,
                    999999.0); // 设置极长间隔来禁用原始生成

        } catch (Exception e) {

        }

        // 创建自定义生成任务
        long intervalTicks = (long) (resourceConfig.getInterval() * 20); // 转换为tick

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查游戏是否还在进行中
                    Arena arena = spawner.getArena();
                    if (arena == null || arena.getStatus() != de.marcely.bedwars.api.arena.ArenaStatus.RUNNING) {

                        // 取消当前任务
                        BukkitTask currentTask = spawnerTasks.get(spawner);
                        if (currentTask != null && !currentTask.isCancelled()) {
                            currentTask.cancel();
                            spawnerTasks.remove(spawner);
                        }
                        return;
                    }

                    // 检查生成器是否仍然存在
                    if (!spawner.exists()) {

                        return;
                    }

                    // 检查生成器附近是否有太多物品（使用配置中的max-nearby-items）
                    int maxNearbyItems = resourceConfig.getMaxNearbyItems();
                    int currentNearbyItems = GlobalResourceManager.this.countNearbyResourceItems(spawner, resourceType);

                    // 添加详细的调试日志


                    if (currentNearbyItems >= maxNearbyItems) {

                        return;
                    }

                    // 生成指定数量的资源（添加NBT防止堆叠）
                    for (int i = 0; i < resourceConfig.getAmount(); i++) {
                        // 先正常掉落
                        spawner.drop(true);

                        // 延迟添加NBT标签防止堆叠
                        final int dropIndex = i;
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            addNBTToNearbyItems(spawner, resourceType, dropIndex);
                        }, 1L);
                    }

                } catch (Exception e) {

                }
            }
        }, intervalTicks, intervalTicks); // 按配置的间隔执行

        // 保存任务引用以便后续清理
        spawnerTasks.put(spawner, task);

    }

    /**
     * 为附近的物品添加NBT标签防止堆叠
     */
    private void addNBTToNearbyItems(Spawner spawner, String resourceType, int dropIndex) {
        try {
            // 获取生成器附近的物品实体
            de.marcely.bedwars.tools.location.XYZ spawnerXYZ = spawner.getLocation();
            org.bukkit.Location spawnerLoc = spawnerXYZ.toLocation(spawner.getArena().getGameWorld());
            java.util.Collection<org.bukkit.entity.Entity> nearbyEntities = spawnerLoc.getWorld()
                    .getNearbyEntities(spawnerLoc, 2.0, 2.0, 2.0);

            for (org.bukkit.entity.Entity entity : nearbyEntities) {
                if (entity instanceof org.bukkit.entity.Item) {
                    org.bukkit.entity.Item item = (org.bukkit.entity.Item) entity;
                    org.bukkit.inventory.ItemStack itemStack = item.getItemStack();

                    // 检查是否是我们要处理的资源类型
                    if (isTargetResourceType(itemStack, resourceType)) {
                        // 添加唯一NBT标签
                        org.bukkit.inventory.meta.ItemMeta meta = itemStack.getItemMeta();
                        if (meta != null) {
                            // 使用PersistentDataContainer添加唯一标识
                            // 组合多个因素确保绝对唯一：时间戳 + 全局计数器 + 随机数 + dropIndex
                            long uniqueId = globalCounter.incrementAndGet();
                            long timestamp = System.currentTimeMillis();
                            int randomNum = (int) (Math.random() * 10000);
                            String uniqueValue = resourceType + "_" + timestamp + "_" + uniqueId + "_" + randomNum + "_"
                                    + dropIndex;

                            org.bukkit.NamespacedKey key = new org.bukkit.NamespacedKey(plugin, "global_resource_id");
                            meta.getPersistentDataContainer().set(key,
                                    org.bukkit.persistence.PersistentDataType.STRING,
                                    uniqueValue);

                            itemStack.setItemMeta(meta);
                            item.setItemStack(itemStack);

                        }
                    }
                }
            }
        } catch (Exception e) {

        }
    }

    /**
     * 检查物品是否是目标资源类型
     */
    private boolean isTargetResourceType(org.bukkit.inventory.ItemStack itemStack, String resourceType) {
        if (itemStack == null || itemStack.getType() == org.bukkit.Material.AIR) {
            return false;
        }

        org.bukkit.Material material = itemStack.getType();
        switch (resourceType.toLowerCase()) {
            case "iron":
                return material == org.bukkit.Material.IRON_INGOT;
            case "gold":
                return material == org.bukkit.Material.GOLD_INGOT;
            default:
                return false;
        }
    }

    /**
     * 清理竞技场的资源生成
     */
    public void cleanupArena(Arena arena) {

        // 取消所有生成器任务
        Iterator<Map.Entry<Spawner, BukkitTask>> iterator = spawnerTasks.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Spawner, BukkitTask> entry = iterator.next();
            Spawner spawner = entry.getKey();
            BukkitTask task = entry.getValue();

            if (spawner.getArena().equals(arena)) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
                iterator.remove();

                // 恢复原始生成间隔
                Double originalInterval = originalDropDurations.remove(spawner);
                if (originalInterval != null) {
                    try {
                        // 移除我们的修改器
                        SpawnerDurationModifier modifier = spawner.getDropDurationModifier(MODIFIER_ID + ":disable");
                        if (modifier != null) {
                            spawner.removeDropDurationModifier(modifier);

                        }
                    } catch (Exception e) {

                    }
                }
            }
        }
    }

    /**
     * 计算生成器附近的特定资源物品数量
     */
    private int countNearbyResourceItems(Spawner spawner, String resourceType) {
        try {
            // 获取生成器位置
            de.marcely.bedwars.tools.location.XYZ spawnerXYZ = spawner.getLocation();
            org.bukkit.Location spawnerLoc = spawnerXYZ.toLocation(spawner.getArena().getGameWorld());

            // 获取附近的实体
            java.util.Collection<org.bukkit.entity.Entity> nearbyEntities = spawnerLoc.getWorld()
                    .getNearbyEntities(spawnerLoc, 3.0, 3.0, 3.0); // 3x3x3 范围

            int count = 0;
            for (org.bukkit.entity.Entity entity : nearbyEntities) {
                if (entity instanceof org.bukkit.entity.Item) {
                    org.bukkit.entity.Item item = (org.bukkit.entity.Item) entity;
                    org.bukkit.inventory.ItemStack itemStack = item.getItemStack();

                    // 检查是否是目标资源类型
                    if (isTargetResourceType(itemStack, resourceType)) {
                        count += itemStack.getAmount();
                    }
                }
            }

            return count;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 资源配置类
     */
    public static class ResourceConfig {
        private final String type;
        private double interval; // 改为可变，支持升级修改
        private final int amount;
        private final boolean enabled;
        private final String displayName;
        private int maxNearbyItems; // 生成器附近最大物品数量

        public ResourceConfig(String type, double interval, int amount, boolean enabled, String displayName,
                int maxNearbyItems) {
            this.type = type;
            this.interval = interval;
            this.amount = amount;
            this.enabled = enabled;
            this.displayName = displayName;
            this.maxNearbyItems = maxNearbyItems;
        }

        // 兼容旧的构造函数
        public ResourceConfig(String type, double interval, int amount, boolean enabled, String displayName) {
            this(type, interval, amount, enabled, displayName, 64); // 默认64个物品
        }

        public String getType() {
            return type;
        }

        public double getInterval() {
            return interval;
        }

        public int getAmount() {
            return amount;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public String getDisplayName() {
            return displayName;
        }

        public int getMaxNearbyItems() {
            return maxNearbyItems;
        }

        // 添加setter方法
        public void setInterval(double interval) {
            this.interval = interval;
        }

        public void setMaxNearbyItems(int maxNearbyItems) {
            this.maxNearbyItems = maxNearbyItems;
        }
    }

    /**
     * 更新生成器速度（响应spawner_multiplier升级）
     */
    public void updateSpawnerSpeed(Spawner spawner, String resourceType, double amplifier) {
        try {
            // 获取当前资源配置
            ResourceConfig resourceConfig = resourceConfigs.get(resourceType);
            if (resourceConfig == null || !resourceConfig.isEnabled()) {
                return;
            }

            // 计算新的生成间隔（原间隔除以倍数）
            double originalInterval = resourceConfig.getInterval();
            double newInterval = originalInterval / amplifier;


            // 取消现有的生成任务
            BukkitTask existingTask = spawnerTasks.get(spawner);
            if (existingTask != null && !existingTask.isCancelled()) {
                existingTask.cancel();
                spawnerTasks.remove(spawner);
            }

            // 更新现有配置的间隔
            resourceConfig.setInterval(newInterval);

            // 重新启动生成器
            setupCustomResourceGeneration(spawner, resourceConfig);

        } catch (Exception e) {
        }
    }
}
