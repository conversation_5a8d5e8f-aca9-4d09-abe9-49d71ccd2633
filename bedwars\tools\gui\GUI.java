package de.marcely.bedwars.tools.gui;

import org.bukkit.entity.Player;

import java.util.Collection;
import java.util.function.Consumer;

/**
 * Interface that includes anything that any kind of GUI have in common
 */
public interface GUI {

  /**
   * Opens the GUI for the player
   *
   * @param player The player who shall see the inventory
   */
  void open(Player player);

  /**
   * Closes this GUI for any player
   */
  void closeAll();

  /**
   * Set the title of the GUI. Keep in mind that not every type supports this
   *
   * @param title The new title
   */
  void setTitle(String title);

  /**
   * Returns the set title
   *
   * @return The title
   */
  String getTitle();

  /**
   * Returns if this type allows its items to get moved around and to be dropped
   *
   * @return If the items are moveable/dragable
   */
  boolean areItemsMoveable();

  /**
   * Ignore it. Only for internal use
   *
   * @return Something you probably don't need
   */
  default boolean ignoresCancelEvent() {
    return false;
  }

  /**
   * Returns the player for whom the GUI is currently open
   *
   * @return The players for who the GUI has been opened
   */
  Collection<Player> getPlayers();

  /**
   * Get whether the given player has the inventory currently open
   *
   * @param player The player who might have the GUI open
   * @return <code>true</code> if he's seeing it right now
   */
  boolean hasOpen(Player player);

  /**
   * Adds a close listener to the gui. This listener will be
   * called when a player closes the gui
   *
   * @param callback the listener that is being added
   * @return if the listener was added successfully
   */
  boolean addCloseListener(Consumer<Player> callback);

  /**
   * Removes a close listener from the gui.
   *
   * @param callback the listener that is being removed
   * @return if the listener was removed successfully
   */
  boolean removeCloseListener(Consumer<Player> callback);

  /**
   * Gets all the close listeners attached to the GUI
   *
   * @return a collection of all the attached listeners
   */
  Collection<Consumer<Player>> getCloseListeners();

  /**
   * Resets its content
   */
  void clear();

  /**
   * Event method that's getting called whenever a player closes the inventory
   *
   * @param player The player who closed the GUI
   * @deprecated Poor API implementation. Not using #super(player) with AnvilGUI and VillagerGUI can cause memory leaks. Use {@link #addCloseListener(Consumer)} instead
   */
  @Deprecated
  default void onClose(Player player) {
  }
}
