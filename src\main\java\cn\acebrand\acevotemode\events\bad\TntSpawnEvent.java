package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.entity.TNTPrimed;
import org.bukkit.util.Vector;

import java.io.File;
import java.util.Random;

/**
 * TNT生成事件
 * 生成5个点燃的TNT
 */
public class TntSpawnEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    
    public TntSpawnEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(badDir, "tnt_spawn.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/tnt_spawn.yml", false);
                plugin.getLogger().info("已生成TNT生成事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载TNT生成事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载TNT生成事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public String getName() {
        return "TNT_SPAWN";
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 30) : 30;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }
        
        // 获取配置参数
        int tntCount = config != null ? config.getInt("tnt_spawn.tnt_count", 5) : 5;
        double spawnRadius = config != null ? config.getDouble("tnt_spawn.spawn_radius", 5.0) : 5.0;
        int fuseTime = config != null ? config.getInt("tnt_spawn.fuse_time", 80) : 80;
        float explosionPower = (float) (config != null ? config.getDouble("tnt_spawn.explosion_power", 4.0) : 4.0);
        boolean breakBlocks = config != null ? config.getBoolean("tnt_spawn.break_blocks", true) : true;
        boolean setFire = config != null ? config.getBoolean("tnt_spawn.set_fire", true) : true;
        int heightOffset = config != null ? config.getInt("tnt_spawn.height_offset", 2) : 2;
        
        // 随机速度配置
        boolean randomVelocityEnabled = config != null ? config.getBoolean("tnt_spawn.random_velocity.enabled", true) : true;
        double horizontalRange = config != null ? config.getDouble("tnt_spawn.random_velocity.horizontal_range", 0.3) : 0.3;
        double verticalRange = config != null ? config.getDouble("tnt_spawn.random_velocity.vertical_range", 0.2) : 0.2;
        
        // 生成TNT
        for (int i = 0; i < tntCount; i++) {
            Location tntLocation = getRandomTntLocation(player.getLocation(), spawnRadius, heightOffset);
            if (tntLocation != null) {
                TNTPrimed tnt = tntLocation.getWorld().spawn(tntLocation, TNTPrimed.class);
                
                // 设置TNT属性
                tnt.setFuseTicks(fuseTime);
                tnt.setYield(explosionPower);
                tnt.setIsIncendiary(setFire);
                
                // 设置随机速度
                if (randomVelocityEnabled) {
                    double vx = (random.nextDouble() - 0.5) * 2 * horizontalRange;
                    double vy = random.nextDouble() * verticalRange;
                    double vz = (random.nextDouble() - 0.5) * 2 * horizontalRange;
                    
                    Vector velocity = new Vector(vx, vy, vz);
                    tnt.setVelocity(velocity);
                }
            }
        }
        
        // 播放音效
        playTntSound(player);
        
        // 显示粒子效果
        showTntParticles(player.getLocation());
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了TNT生成事件，生成了 " + tntCount + " 个TNT");
    }
    
    /**
     * 获取随机TNT生成位置
     */
    private Location getRandomTntLocation(Location center, double radius, int heightOffset) {
        for (int attempts = 0; attempts < 10; attempts++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = random.nextDouble() * radius;
            double x = center.getX() + Math.cos(angle) * distance;
            double z = center.getZ() + Math.sin(angle) * distance;
            double y = center.getY() + heightOffset;
            
            Location tntLoc = new Location(center.getWorld(), x, y, z);
            
            // 检查位置是否安全（不在固体方块内）
            if (tntLoc.getBlock().getType().isAir()) {
                return tntLoc;
            }
        }
        
        // 如果找不到合适位置，就在玩家上方生成
        return center.clone().add(0, heightOffset, 0);
    }
    
    /**
     * 播放TNT音效
     */
    private void playTntSound(Player player) {
        if (config == null) return;
        
        if (config.getBoolean("tnt_spawn.sound.enabled", true)) {
            String soundType = config.getString("tnt_spawn.sound.sound_type", "ENTITY_TNT_PRIMED");
            float volume = (float) config.getDouble("tnt_spawn.sound.volume", 1.0);
            float pitch = (float) config.getDouble("tnt_spawn.sound.pitch", 1.0);
            
            try {
                Sound sound = Sound.valueOf(soundType);
                player.playSound(player.getLocation(), sound, volume, pitch);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的音效类型: " + soundType);
            }
        }
    }
    
    /**
     * 显示TNT粒子效果
     */
    private void showTntParticles(Location location) {
        if (config == null) return;
        
        if (config.getBoolean("tnt_spawn.particles.enabled", true)) {
            String particleType = config.getString("tnt_spawn.particles.particle_type", "SMOKE_NORMAL");
            int count = config.getInt("tnt_spawn.particles.count", 10);
            
            try {
                Particle particle = Particle.valueOf(particleType);
                location.getWorld().spawnParticle(particle, location, count);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的粒子类型: " + particleType);
            }
        }
    }
    
    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null) return true;
        return config.getBoolean("messages.send_message", true);
    }
    
    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null) return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }
    
    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null) return "§c嘶嘶嘶...TNT来了！快跑！";
        return config.getString("messages.event_message", "§c嘶嘶嘶...TNT来了！快跑！");
    }
}
