package de.marcely.bedwars.api.game.shop.price;

import de.marcely.bedwars.api.game.spawner.DropType;
import org.jetbrains.annotations.Nullable;

public enum ShopPriceType {

  /**
   * Represents an ItemStack ({@link ItemShopPrice})
   */
  ITEM,

  /**
   * Represents a {@link DropType} ({@link SpawnerItemShopPrice})
   */
  SPAWNER_ITEM;

  private static final ShopPriceType[] VALUES = values();

  private final String id;

  ShopPriceType() {
    this.id = name().toLowerCase().replace("_", "-");
  }

  /**
   * Returns the id of this type that is being used to identify it in e.g. the shop config file.
   *
   * @return The of this price type
   */
  public String getId() {
    return this.id;
  }

  /**
   * Tries to look up for a price type with a specific id.
   *
   * @param id The id that we want to match
   * @return The given price. <code>null</code> if we couldn't match any
   */
  @Nullable
  public static ShopPriceType getById(String id) {
    for (ShopPriceType type : VALUES) {
      if (type.getId().equalsIgnoreCase(id))
        return type;
    }

    return null;
  }
}