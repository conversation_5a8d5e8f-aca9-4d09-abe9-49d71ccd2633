package de.marcely.bedwars.api.hook;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a hook that is used to interact with the cloud system.
 */
public interface CloudSystemHook extends Hook {

  default HookCategory getCategory() {
    return HookCategory.CLOUD_SYSTEM;
  }

  /**
   * Get the name of the server.
   * <p>
   *   This is usually also the name that the cloud system uses
   *   within the Bungee settings as the server's channel name.
   * </p>
   *
   * @return The name of the server
   */
  String getServerName();

  /**
   * Apply the given information to the cloud system.
   * <p>
   *   This method is used to update the cloud system's status,
   *   player amounts and extra information that is displayed to the user.
   * </p>
   *
   * @param info The information that shall be displayed
   */
  void applyInfo(ServerInfoDto info);



  /**
   * Represents the information that the server shall display.
   */
  public static class ServerInfoDto {

    private final Arena arena;
    private final ArenaStatus status;
    private final int activePlayers;
    private final int maxPlayers;
    @Nullable
    private final String extra;

    public ServerInfoDto(Arena arena, ArenaStatus status, int activePlayers, int maxPlayers, @Nullable String extra) {
      this.arena = arena;
      this.status = status;
      this.activePlayers = activePlayers;
      this.maxPlayers = maxPlayers;
      this.extra = extra;
    }

    /**
     * Get the arena that shall be displayed.
     *
     * @return The arena that shall be displayed
     */
    public Arena getArena() {
      return this.arena;
    }

    /**
     * Get the status that shall be displayed.
     *
     * @return The status that shall be displayed
     */
    public ArenaStatus getStatus() {
      return this.status;
    }

    /**
     * Get the amount of active players that are currently playing.
     *
     * @return The amount of active players that are currently playing
     */
    public int getActivePlayers() {
      return this.activePlayers;
    }

    /**
     * Get the amount of maximum players that can play.
     *
     * @return The amount of maximum players that can play
     */
    public int getMaxPlayers() {
      return this.maxPlayers;
    }

    /**
     * Get the extra information that shall be displayed.
     * <p>
     *   May be <code>null</code> if the user does not want to display any extra information.
     * </p>
     *
     * @return The extra information that shall be displayed. May be <code>null</code>
     */
    @Nullable
    public String getExtra() {
      return this.extra;
    }
  }
}
