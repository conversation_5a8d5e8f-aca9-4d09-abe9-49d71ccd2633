package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when an arena starts to regenerate
 */
public class ArenaRegenerationStartEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final CommandSenderWrapper sender;
  private ArenaRegenerationStartEvent.Result result;

  public ArenaRegenerationStartEvent(Arena arena, @Nullable CommandSenderWrapper sender, ArenaRegenerationStartEvent.Result result) {
    this.arena = arena;
    this.sender = sender;
    this.result = result;
  }

  /**
   * Returns the person who started the regeneration.
   * <p>
   * 	Can be <code>null</code> when something else (or nobody on this server) started it
   * </p>
   *
   * @return The person who started it
   */
  @Nullable
  public CommandSender getSender() {
    return this.sender != null ? this.sender.getCommandSender() : null;
  }

  /**
   * Returns the person who started this regeneration.
   * <p>
   *     Can be <code>null</code> when something else started it.
   * </p>
   *
   * @return The person who started it
   */
  @Nullable
  public CommandSenderWrapper getSenderWrapped() {
    return this.sender;
  }

  /**
   * Returns what actually should happen after this event.
   *
   * @return The result of this event
   */
  public ArenaRegenerationStartEvent.Result getResult() {
    return this.result;
  }

  /**
   * Set what actually should happen after this event.
   *
   * @param result The new result
   */
  public void setResult(ArenaRegenerationStartEvent.Result result) {
    Validate.notNull(result, "result");

    this.result = result;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  public enum Result {

    /**
     * Arena will regenerate like normal
     */
    REGENERATE,

    /**
     * State will stay as {@link ArenaStatus#RESETTING}, but won't start the regeneration process.
     * This allows you to use your own regenerator.
     */
    DO_NOT_REGENERATE,

    /**
     * Will keep the status as it is and won't start the regeneration process
     */
    CANCEL
  }
}