package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.PlayerDamageInfo;
import de.marcely.bedwars.api.message.Message;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.HandlerList;
import org.bukkit.event.entity.EntityDamageEvent.DamageCause;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.jetbrains.annotations.Nullable;

import java.util.Set;

/**
 * Gets called when someone is killing someone else during a game
 */
public class PlayerKillPlayerEvent extends PlayerIngameDeathEvent {

  @Getter
  private final Arena arena;
  private final Player killer;
  private final PlayerDamageInfo damageInfo;

  public PlayerKillPlayerEvent(
      PlayerDeathEvent bukkitEvent,
      Arena arena,
      boolean fatalDeath,
      Message deathMessage,
      Set<Player> deathMessageTargets,
      int deathSpectateDuration,
      Player killer,
      PlayerDamageInfo damageInfo) {
    super(bukkitEvent, arena, fatalDeath, deathMessage, deathMessageTargets, deathSpectateDuration);

    this.arena = arena;
    this.killer = killer;
    this.damageInfo = damageInfo;
  }

  /**
   * Returns the player that has been killed by {@link PlayerKillPlayerEvent#getPlayer()}.
   * <p>
   *     This method returns the same exact player as {@link #getPlayer()}.
   * </p>
   *
   * @return The killed/damaged player
   */
  public Player getDamaged() {
    return this.getPlayer();
  }

  /**
   * Returns the player who killed the other player.
   *
   * @return The killer
   */
  public Player getKiller() {
    return this.killer;
  }

  /**
   * Returns the cause of his death.
   *
   * @return The last DamageCause he received
   */
  public DamageCause getCause() {
    return this.damageInfo.getEvent().getCause();
  }

  /**
   * Returns the projectile that the {@link PlayerKillPlayerEvent#getPlayer()} might have used to kill the player.
   * <p>
   * Might return <code>null</code>, meaning that he didn't use any projectile
   *
   * @return The projectile that was used to kill him
   */
  public @Nullable Projectile getProjectile() {
    return this.damageInfo.getProjectileDamager();
  }

  /**
   * Get the past damage info of when the damager directly or indirectly damaged the killed player of this event.
   *
   * @return The related damage info of this event
   */
  public PlayerDamageInfo getDamageInfo() {
    return this.damageInfo;
  }

  @Override
  public HandlerList getHandlers() {
    return PlayerIngameDeathEvent.getHandlerList();
  }

  public static HandlerList getHandlerList() {
    return PlayerIngameDeathEvent.getHandlerList();
  }
}
