package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.upgrade.UpgradeLevel;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when an upgrade is bought (and thus automatically triggered) or a trap is triggered.
 */
public class PlayerTriggerUpgradeEvent extends Event implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Player player;
  private final Arena arena;
  private final Team team;
  private final UpgradeLevel upgradeLevel;
  private final boolean isAPICall;
  private boolean broadcastingMessage;
  private boolean broadcastingTitle;
  private boolean broadcastingSound;
  private boolean takingFromQueue;
  @Getter @Setter
  private boolean cancelled = false;

  public PlayerTriggerUpgradeEvent(
      Player player,
      Arena arena,
      Team team,
      UpgradeLevel upgradeLevel,
      boolean broadcastingMessage,
      boolean broadcastingTitle,
      boolean broadcastingSound,
      boolean takingFromQueue) {

    this.player = player;
    this.arena = arena;
    this.team = team;
    this.upgradeLevel = upgradeLevel;
    this.isAPICall = false;
    this.broadcastingMessage = broadcastingMessage;
    this.broadcastingTitle = broadcastingTitle;
    this.broadcastingSound = broadcastingSound;
    this.takingFromQueue = takingFromQueue;
  }
  
  public PlayerTriggerUpgradeEvent(
      @Nullable Player player,
      Arena arena,
      Team team,
      UpgradeLevel upgradeLevel) {

    this.player = player;
    this.arena = arena;
    this.team = team;
    this.upgradeLevel = upgradeLevel;
    this.isAPICall = true;
  }

  /**
   * Returns the player that triggered the upgrade or trap.
   * <p>
   *   It can be <code>null</code> if the upgrade was triggered using the API.
   * </p>
   *
   * @return The player that triggered the upgrade or trap. May be <code>null</code>
   * @see #hasPlayer()
   * @see #isAPICall()
   */
  @Nullable
  public Player getPlayer() {
    return this.player;
  }

  /**
   * Get whether any player triggered the upgrade or trap.
   * <p>
   *   This may be <code>false</code> if the upgrade was triggered using the API.
   * </p>
   *
   * @return <code>true</code> if there is a known player that triggered the upgrade or trap
   * @see #getPlayer()
   * @see #isAPICall()
   */
  public boolean hasPlayer() {
    return this.player != null;
  }

  /**
   * Returns the arena in which the trap was triggered.
   *
   * @return The arena involved
   */
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the team in which the upgrade or trap belongs to.
   *
   * @return The team involved
   */
  public Team getTeam() {
    return this.team;
  }

  /**
   * Get whether this upgrade was virtually done using the API.
   * 
   * @return <code>true</code> if an addon triggered. <code>false</code> if a player himself triggered it
   * @see de.marcely.bedwars.api.game.upgrade.UpgradeState#doUpgrade(de.marcely.bedwars.api.game.upgrade.UpgradeLevel, org.bukkit.entity.Player)
   */
  public boolean isAPICall() {
    return this.isAPICall;
  }

  /**
   * If true the trap will be pulled from the upgrade queue.
   * This has no effect if this UpgradeLevel handler is not a trap
   *
   * @return weather or not a trap will be pulled from the team's trap queue
   */
  public boolean isTakingFromQueue() {
    return this.takingFromQueue;
  }

  /**
   * If true the trap will be pulled from the upgrade queue.
   * This has no effect if this UpgradeLevel handler is not a trap
   *
   * @param takingFromQueue weather or not a trap will be pulled from the team's trap queue
   */
  public void setTakingFromQueue(boolean takingFromQueue) {
    this.takingFromQueue = takingFromQueue;
  }

  /**
   * Whether or not a message will be sent out to all team players when an
   * upgrade is purchased, or when a trap is triggered
   *
   * @return if a message will be broadcast
   */
  public boolean isBroadcastingMessage() {
    return this.broadcastingMessage;
  }

  /**
   * Sets whether or not a message will be sent out to all team players when an
   * upgrade is purchased, or when a trap is triggered
   *
   * @param broadcastingMessage if a message should be broadcast
   */
  public void setBroadcastingMessage(boolean broadcastingMessage) {
    this.broadcastingMessage = broadcastingMessage;
  }

  /**
   * Whether or not a title will be sent out to all team players when an
   * upgrade is purchased, or when a trap is triggered
   *
   * @return if a title will be sent
   */
  public boolean isBroadcastingTitle() {
    return this.broadcastingTitle;
  }

  /**
   * Sets whether or not a title will be sent out to all team players when an
   * upgrade is purchased, or when a trap is triggered
   *
   * @param broadcastingTitle if a message should be broadcast
   */
  public void setBroadcastingTitle(boolean broadcastingTitle) {
    this.broadcastingTitle = broadcastingTitle;
  }

  /**
   * Whether or not a sound will be played when an upgrade is purchased,
   * or when a trap is triggered
   *
   * @return if the sound is being played
   */
  public boolean isBroadcastingSound() {
    return this.broadcastingSound;
  }

  /**
   * Set whether or not a sound should be played when an upgrade is purchased,
   * or when a trap is triggered
   *
   * @param broadcastingSound if the sound should be played
   */
  public void setBroadcastingSound(boolean broadcastingSound) {
    this.broadcastingSound = broadcastingSound;
  }

  /**
   * Returns the UpgradeLevel that belongs to the upgrade that's being triggered,
   * or the trap that's being purchased
   *
   * @return UpgradeLevel that's being triggered
   */
  public UpgradeLevel getUpgradeLevel() {
    return this.upgradeLevel;
  }

  /**
   * Returns the amplifier that's being applied to this upgrade
   *
   * @return the amplifier for the upgrade level being triggered
   */
  public double getAmplifier() {
    return this.upgradeLevel.getAmplifier();
  }

  /**
   * Returns the duration that's being applied to this upgrade
   *
   * @return the duration for the upgrade level being triggered
   */
  public int getDuration() {
    return this.upgradeLevel.getDuration();
  }

  /**
   * Returns if the handler attached to this UpgradeLevel is a trap handler
   *
   * @return check if the upgrade level that was purchased is a trap
   */
  public boolean isTrap() {
    return upgradeLevel.isTrap();
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
