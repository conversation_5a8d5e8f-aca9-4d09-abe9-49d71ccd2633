package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.tools.Validate;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

/**
 * Data container for {@link Arena#destroyBedNaturally(BedDestructionInfo)}.
 */
@EqualsAndHashCode
@ToString
public class BedDestructionInfo {

  private Team targetTeam;
  private Player destroyer;
  private String destroyerName;
  private boolean isPlayingSound = true, isSendingTitle = true, isSendingMessage = true;
  private Cause cause = Cause.PLUGIN;

  private BedDestructionInfo() { }

  /**
   * Get the team of whom the bed shall be destroyed.
   *
   * @return The target team
   */
  public Team getTargetTeam() {
    return this.targetTeam;
  }

  /**
   * Set the team of whom the bed shall be destroyed.
   *
   * @param team The target team
   */
  public void setTargetTeam(Team team) {
    Validate.notNull(team, "team");

    this.targetTeam = team;
  }

  /**
   * Get the player that is responsible for the bed destruction.
   *
   * @return The responsible player. May be <code>null</code> if there is none
   */
  @Nullable
  public Player getDestroyer() {
    return this.destroyer;
  }

  /**
   * Set the player that is responsible for the bed destruction.
   *
   * @param player The responsible player. May be <code>null</code> if there is none
   */
  public void setDestroyer(@Nullable Player player) {
    this.destroyer = player;
  }

  /**
   * Get the explicit name of the thing that broke it (e.g. TNT).
   * <p>
   *   Tries to fallback to e.g. the player name if none is given.
   * </p>
   *
   * @return The name of the thing that broke it. May be <code>null</code>
   */
  @Nullable
  public String getDestroyerName() {
    return this.destroyerName;
  }

  /**
   * Set the explicit name of the thing that broke it (e.g. TNT).
   * <p>
   *   Tries to fallback to e.g. the player name if none is given.
   * </p>
   *
   * @param name The name of the thing that broke it. May be <code>null</code>
   */
  public void setDestroyerName(@Nullable String name) {
    this.destroyerName = name;
  }

  /**
   * Get whether a sound shall be played.
   * <p>
   *   This is enabled by default.
   * </p>
   *
   * @return Whether a sound shall be played
   */
  public boolean isPlayingSound() {
    return this.isPlayingSound;
  }

  /**
   * Set whether a sound shall be played.
   * <p>
   *   This is enabled by default.
   * </p>
   *
   * @param playSound Whether a sound shall be played
   */
  public void setPlayingSound(boolean playSound) {
    this.isPlayingSound = playSound;
  }

  /**
   * Get whether a title shall be sent.
   * <p>
   *   This is enabled by default.
   * </p>
   *
   * @return Whether a title shall be sent
   * @see <a href="https://minecraft.wiki/w/Commands/title">Minecraft Wiki</a>
   */
  public boolean isSendingTitle() {
    return this.isPlayingSound;
  }

  /**
   * Set whether a title shall be sent.
   * <p>
   *   This is enabled by default.
   * </p>
   *
   * @param sendTitle Whether a title shall be sent
   * @see <a href="https://minecraft.wiki/w/Commands/title">Minecraft Wiki</a>
   */
  public void setSendingTitle(boolean sendTitle) {
    this.isSendingTitle = sendTitle;
  }

  /**
   * Get whether a chat message shall be sent.
   * <p>
   *   This is enabled by default
   * </p>
   *
   * @return Whether a chat message shall be sent
   */
  public boolean isSendingChatMessage() {
    return this.isSendingMessage;
  }

  /**
   * Set whether a chat message shall be sent.
   * <p>
   *   This is enabled by default
   * </p>
   *
   * @param sendMessage Whether a chat message shall be sent
   */
  public void isSendingChatMessage(boolean sendMessage) {
    this.isSendingMessage = sendMessage;
  }

  /**
   * Set the specific cause or action of the bed destruction.
   *
   * @param cause The cause
   */
  public void setCause(Cause cause) {
    Validate.notNull(cause, "cause");

    this.cause = cause;
  }


  /**
   * Get the specific cause or action of the bed destruction.
   *
   * @return The cause
   */
  public Cause getCause() {
    return this.cause;
  }

  /**
   * Construct a new instance of this data container.
   *
   * @param targetTeam The team of whom the bed shall be destroyed
   * @return A newly constructed instance
   */
  public static BedDestructionInfo construct(Team targetTeam) {
    final BedDestructionInfo info = new BedDestructionInfo();

    info.setTargetTeam(targetTeam);

    return info;
  }


  /**
   * The specifc cause or action of the bed destruction.
   */
  public static enum Cause {

    /**
     * The player manually broke the block with his hand.
     */
    PUNCH_BLOCK,

    /**
     * An explosive (potentially placed by somebody) caused by its impact a destruction.
     */
    EXPLOSIVE,

    /**
     * All the team's members left the game.
     * <p>
     * In contrary to {@link #REJOIN_TIMEOUT}, no opportunity was given to rejoin before the elimination. They left the match and the bed was destroyed immediately with that.
     * </p>
     *
     * @see #REJOIN_TIMEOUT
     */
    TEAM_LEFT,

    /**
     * All the team's members left the game and did not rejoin in time.
     * <p>
     * In contrary to {@link #TEAM_LEFT}, an opportunity was given to rejoin before the elimination.
     * </p>
     *
     * @see #TEAM_LEFT
     */
    REJOIN_TIMEOUT,

    /**
     * The bed was destroyed by a plugin using the API.
     * <p>
     * Note that plugins may also define another cause. This is the default value for the API if none other was specified.
     * </p>
     *
     * @see Arena#destroyBedNaturally(BedDestructionInfo)
     */
    PLUGIN,

    /**
     * The bed was destroyed using a debug command.
     *
     * @deprecated May be removed at any time
     */
    @Deprecated
    DEBUG;


    /**
     * Get the chat message that might be used for this cause.
     *
     * @return The chat message
     */
    public Message getChatMessage() {
      switch (this) {
        case TEAM_LEFT:
          return Message.buildByKey("Destroyed_Bed_Chat_Left");
        case REJOIN_TIMEOUT:
          return Message.buildByKey("Destroyed_Bed_Chat_LeftIgnoredRejoin");
        default:
          return Message.buildByKey("Destroyed_Bed_Chat");
      }
    }
  }
}
