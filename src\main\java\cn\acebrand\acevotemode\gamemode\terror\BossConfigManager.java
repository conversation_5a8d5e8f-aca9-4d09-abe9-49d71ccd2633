package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import org.bukkit.configuration.ConfigurationSection;

/**
 * Boss配置管理器
 * 处理简化版和增强版Boss配置的切换
 */
public class BossConfigManager {
    
    private final AceVoteMode plugin;
    private final ConfigurationSection config;
    
    // Boss版本类型
    public enum BossVersion {
        SIMPLE("simple"),
        ENHANCED("enhanced");
        
        private final String configKey;
        
        BossVersion(String configKey) {
            this.configKey = configKey;
        }
        
        public String getConfigKey() {
            return configKey;
        }
    }
    
    // Boss配置信息
    public static class BossConfig {
        private final String mythicMobId;
        private final int health;
        private final int damage;
        private final int armor;
        private final int skillCount;
        private final String version;
        
        public BossConfig(String mythicMobId, int health, int damage, int armor, int skillCount, String version) {
            this.mythicMobId = mythicMobId;
            this.health = health;
            this.damage = damage;
            this.armor = armor;
            this.skillCount = skillCount;
            this.version = version;
        }
        
        // Getters
        public String getMythicMobId() { return mythicMobId; }
        public int getHealth() { return health; }
        public int getDamage() { return damage; }
        public int getArmor() { return armor; }
        public int getSkillCount() { return skillCount; }
        public String getVersion() { return version; }
    }
    
    public BossConfigManager(AceVoteMode plugin, ConfigurationSection config) {
        this.plugin = plugin;
        this.config = config;
    }
    
    /**
     * 获取当前Boss版本
     */
    public BossVersion getCurrentVersion() {
        String version = config.getString("boss.version", "enhanced");
        try {
            return BossVersion.valueOf(version.toUpperCase());
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid boss version: " + version + ", using ENHANCED as default");
            return BossVersion.ENHANCED;
        }
    }
    
    /**
     * 设置Boss版本
     */
    public void setVersion(BossVersion version) {
        config.set("boss.version", version.getConfigKey());
        plugin.getLogger().info("Boss version set to: " + version.getConfigKey());
    }
    
    /**
     * 获取Boss配置
     */
    public BossConfig getBossConfig() {
        BossVersion version = getCurrentVersion();
        return getBossConfig(version);
    }
    
    /**
     * 获取指定版本的Boss配置
     */
    public BossConfig getBossConfig(BossVersion version) {
        String mythicMobId = config.getString("boss.mythic-mob-id", "TerrorLord");
        
        ConfigurationSection versionConfig = config.getConfigurationSection("boss." + version.getConfigKey());
        if (versionConfig == null) {
            // 使用默认配置
            return getDefaultBossConfig(version, mythicMobId);
        }
        
        int health = versionConfig.getInt("health", getDefaultHealth(version));
        int damage = versionConfig.getInt("damage", getDefaultDamage(version));
        int armor = versionConfig.getInt("armor", getDefaultArmor(version));
        int skillCount = versionConfig.getInt("skill-count", getDefaultSkillCount(version));
        
        return new BossConfig(mythicMobId, health, damage, armor, skillCount, version.getConfigKey());
    }
    
    /**
     * 获取默认Boss配置
     */
    private BossConfig getDefaultBossConfig(BossVersion version, String mythicMobId) {
        int health = getDefaultHealth(version);
        int damage = getDefaultDamage(version);
        int armor = getDefaultArmor(version);
        int skillCount = getDefaultSkillCount(version);
        
        return new BossConfig(mythicMobId, health, damage, armor, skillCount, version.getConfigKey());
    }
    
    /**
     * 获取默认血量
     */
    private int getDefaultHealth(BossVersion version) {
        switch (version) {
            case SIMPLE:
                return 300;
            case ENHANCED:
                return 350;
            default:
                return 300;
        }
    }
    
    /**
     * 获取默认伤害
     */
    private int getDefaultDamage(BossVersion version) {
        switch (version) {
            case SIMPLE:
                return 6;
            case ENHANCED:
                return 7;
            default:
                return 6;
        }
    }
    
    /**
     * 获取默认护甲
     */
    private int getDefaultArmor(BossVersion version) {
        switch (version) {
            case SIMPLE:
                return 15;
            case ENHANCED:
                return 18;
            default:
                return 15;
        }
    }
    
    /**
     * 获取默认技能数量
     */
    private int getDefaultSkillCount(BossVersion version) {
        switch (version) {
            case SIMPLE:
                return 14;
            case ENHANCED:
                return 30;
            default:
                return 14;
        }
    }
    
    /**
     * 检查版本是否启用
     */
    public boolean isVersionEnabled(BossVersion version) {
        return config.getBoolean("boss." + version.getConfigKey() + ".enabled", true);
    }
    
    /**
     * 启用/禁用版本
     */
    public void setVersionEnabled(BossVersion version, boolean enabled) {
        config.set("boss." + version.getConfigKey() + ".enabled", enabled);
    }
    
    /**
     * 获取推荐的服务器配置信息
     */
    public String getServerRequirements(BossVersion version) {
        switch (version) {
            case SIMPLE:
                return "推荐配置：4GB+ 内存，适合3-8人团队";
            case ENHANCED:
                return "推荐配置：8GB+ 内存，适合5-12人团队";
            default:
                return "未知版本";
        }
    }
    
    /**
     * 获取版本特性描述
     */
    public String getVersionFeatures(BossVersion version) {
        switch (version) {
            case SIMPLE:
                return "简化版：14种技能，基础粒子效果，稳定性优先";
            case ENHANCED:
                return "增强版：30种技能，华丽粒子效果，视觉体验优先";
            default:
                return "未知版本";
        }
    }
    
    /**
     * 验证配置完整性
     */
    public boolean validateConfig() {
        // 检查必需的配置项
        if (config.getString("boss.mythic-mob-id") == null) {
            plugin.getLogger().severe("Missing boss.mythic-mob-id in terror descent config!");
            return false;
        }
        
        BossVersion currentVersion = getCurrentVersion();
        if (!isVersionEnabled(currentVersion)) {
            plugin.getLogger().warning("Current boss version " + currentVersion + " is disabled!");
            return false;
        }
        
        // 检查阶段配置
        ConfigurationSection phasesConfig = config.getConfigurationSection("boss.phases");
        if (phasesConfig == null) {
            plugin.getLogger().warning("Missing boss phases configuration!");
            return false;
        }
        
        // 检查召唤物配置
        ConfigurationSection minionsConfig = config.getConfigurationSection("boss.minions");
        if (minionsConfig == null) {
            plugin.getLogger().warning("Missing boss minions configuration!");
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        BossConfig bossConfig = getBossConfig();
        StringBuilder summary = new StringBuilder();
        
        summary.append("=== 恐怖降临Boss配置摘要 ===\n");
        summary.append("版本: ").append(bossConfig.getVersion().toUpperCase()).append("\n");
        summary.append("MythicMob ID: ").append(bossConfig.getMythicMobId()).append("\n");
        summary.append("血量: ").append(bossConfig.getHealth()).append("\n");
        summary.append("伤害: ").append(bossConfig.getDamage()).append("\n");
        summary.append("护甲: ").append(bossConfig.getArmor()).append("\n");
        summary.append("技能数量: ").append(bossConfig.getSkillCount()).append("\n");
        summary.append("服务器要求: ").append(getServerRequirements(getCurrentVersion())).append("\n");
        summary.append("版本特性: ").append(getVersionFeatures(getCurrentVersion()));
        
        return summary.toString();
    }
    
    /**
     * 自动选择最佳版本
     */
    public BossVersion getRecommendedVersion(int playerCount, long serverMemory) {
        // 根据玩家数量和服务器内存推荐版本
        if (playerCount <= 6 || serverMemory < 6 * 1024 * 1024 * 1024L) { // 6GB
            return BossVersion.SIMPLE;
        } else {
            return BossVersion.ENHANCED;
        }
    }
    
    /**
     * 应用推荐版本
     */
    public void applyRecommendedVersion(int playerCount) {
        long maxMemory = Runtime.getRuntime().maxMemory();
        BossVersion recommended = getRecommendedVersion(playerCount, maxMemory);
        
        if (recommended != getCurrentVersion()) {
            plugin.getLogger().info(String.format(
                "Recommending boss version change from %s to %s (players: %d, memory: %dMB)",
                getCurrentVersion(), recommended, playerCount, maxMemory / 1024 / 1024
            ));
            
            setVersion(recommended);
        }
    }
}
