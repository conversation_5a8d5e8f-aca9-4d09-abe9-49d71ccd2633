package de.marcely.bedwars.api.remote;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.tools.NMSHelper;
import org.jetbrains.annotations.Nullable;
import de.marcely.bedwars.api.event.remote.RemoteCustomMessageReceiveEvent;

import java.util.Collection;

/**
 * Represents a server in the network, including ours.
 */
public interface RemoteServer {

  /**
   * Gets all the arena that the server holds.
   *
   * @return The server that are being managed by the server
   */
  Collection<? extends RemoteArena> getArenas();

  /**
   * Gets whether this instance represents the server on which you are accessing the API with-
   *
   * @return <code>true</code> when it is local
   */
  boolean isLocal();

  /**
   * Gets whether the arena is still online.
   *
   * @return <code>true</code> in case it is online
   */
  boolean isOnline();

  /**
   * Gets the name of this server that has been configured in Bungeecord's config.
   * <p>
   *     The default implementation (the one without the ProxySync) addon, might return <code>null</code>.
   *     Reason being, that it has to fetch the info manually. And it's possible that the server is not running in bungee mode.
   * </p>
   *
   * @return The channel name of the arena. May be <code>null</code>
   */
  @Nullable
  String getBungeeChannelName();

  /**
   * Whether this server represents a location to which the player gets teleported when he leaves the arena (or when the match ends).
   *
   * @return <code>true</code> when it's a hub server
   */
  boolean isHub();

  /**
   * Returns the minor version of the running server software.
   * <p>
   *   Example: If it's running 1.8.6, then this method returns 8
   * </p>
   *
   * @return The minor version of the running server software
   * @see NMSHelper#getVersion()
   */
  int getMinecraftVersion();

  /** Returns the patch version of the running server software.
   * <p>
   *  Example: If it's running 1.8.6, then this method returns 6
   * </p>
   *
   * @return The patch version of the running server software
   * @see NMSHelper#getPatchVersion()
   */
  int getMinecraftPatchVersion();

  /**
   * Gets the total amount of players that are currently on this server.
   * <p>
   *     This includes ALL players, not just spectating and playing players.
   * </p>
   * <p>
   *   Note: This method might be off to {@link #getPlayers()} for
   *   a short period of time whenever a player joins or leaves the server.
   * </p>
   *
   * @return The amount of players on the server
   */
  int getPlayersCount();

  /**
   * Returns a collection of <b>all</b> known online players on the given server.
   * <p>
   *     This includes ALL players, not just spectating and playing players.
   * </p>
   * <p>
   *   Note: This method might be off to {@link #getPlayersCount()} for
   *   a short period of time whenever a player joins or leaves the server.
   * </p>
   *
   * @return All players on this server
   */
  Collection<? extends RemotePlayer> getPlayers();

  /**
   * Gets the maximum amount of players that the server is able to hold.
   *
   * @return The max amount of players
   */
  int getMaxPlayersCount();

  /**
   * Gets the version of the API of the Bedwars plugin that is running on the server.
   * <p>
   *     Obtains the info from {@link BedwarsAPI#getAPIVersion()}.
   * </p>
   *
   * @return The version of the API on the server
   */
  int getAPIVersion();

  /**
   * Gets the version of the Bedwars plugin that is running on the server.
   *
   * @return The version of the Bedwars plugin on the server
   */
  String getPluginVersion();

  /**
   * Executes a command on the server, as if a player would run it.
   * <p>
   *     Keep in mind that not all commands support that. The handler must be implemented using {@link de.marcely.bedwars.api.command.CommandHandlerWrappedSender}.
   * </p>
   *
   * @param sender The player who run it
   * @param label The lable of the command. May be e.g. "mbedwars"
   * @param args The arguments that he has passed
   */
  void executeBedwarsCommand(RemotePlayer sender, String label, String[] args);

  /**
   * Executes a command on the server, as if a console would run it.
   * <p>
   *     Keep in mind that not all commands support that. The handler must be implemented using {@link de.marcely.bedwars.api.command.CommandHandlerWrappedSender}.
   * </p>
   *
   * @param label The lable of the command. May be e.g. "mbedwars"
   * @param args The arguments that he has passed
   */
  void executeBedwarsCommandAsConsole(String label, String[] args);

  /**
   * Sends a message to the console of the given server using {@link org.bukkit.command.ConsoleCommandSender#sendMessage(String)})
   *
   * @param messages The messages that shall be printed into the console
   */
  void sendConsoleMessage(String... messages);

  /**
   * Sends a custom message to this server.
   *
   * <p>
   *   This method is useful when you want to communicate between servers.
   *   Use {@link RemoteCustomMessageReceiveEvent} to listen to the message.
   * </p>
   * <p>
   *   The <code>channel</code> parameter is useful as it allows you to be distant from other plugins that make use of the messaging system
   *   as well. You may e.g. insert the name of your plugin. Note that encoded as UTF-8.
   *   You may later use {@link RemoteCustomMessageReceiveEvent#getChannel()} to identify the channel again.
   * </p>
   *
   * @param channel The channel in which you want to communicate
   * @param payload The actual message that you want to send. Use e.g. {@link String#getBytes()} to pass a String.
   * @see RemoteAPI#broadcastCustomMessage(String, byte[])
   * @see RemoteCustomMessageReceiveEvent
   * @throws IllegalStateException When trying to send it to a local server ({@link #isLocal()} returns true)
   * @throws IllegalArgumentException When channel name is larger than 255 bytes or when payload is larger than 10MB (not a typo)
   */
  void sendCustomMessage(String channel, byte[] payload);

  /**
   * Looks for an arena from this server whose real name is identical to the given parameter.
   *
   * @param name The name of the arena we are looking
   * @return The arena whose {@link RemoteArena#getRealName()} on this server matches with the parameter. <code>null</code> if there is none
   */
  @Nullable
  RemoteArena getArenaByExactRealName(String name);
}
