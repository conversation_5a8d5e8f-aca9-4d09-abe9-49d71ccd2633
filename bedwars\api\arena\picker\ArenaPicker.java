package de.marcely.bedwars.api.arena.picker;

import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.picker.condition.ArenaConditionGroup;
import de.marcely.bedwars.api.remote.RemoteAPI;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.tools.Validate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the final ArenaPicker product that persists of a selector and a bunch of conditions (aka as a condition group).
 * <p>
 *     Use {@link ArenaPickerAPI#parsePicker(String)} to parse it given by a String.
 * </p>
 * */
public class ArenaPicker {

  private final ArenaSelector selector;
  private final ArenaConditionGroup condition;

  public ArenaPicker(ArenaSelector selector, ArenaConditionGroup condition) {
    Validate.notNull(selector, "selector");
    Validate.notNull(condition, "condition");

    this.selector = selector;
    this.condition = condition;
  }

  /**
   * Returns the selector.
   * <p>
   *     The selector decides which one arena shall be chosen from all the arenas that got filtered
   *     using {@link #getCondition()}.
   * </p>
   *
   * @return The selector of this picker
   */
  public ArenaSelector getSelector() {
    return this.selector;
  }

  /**
   * The condition filters arena.
   *
   * @return The condition of this picker
   */
  public ArenaConditionGroup getCondition() {
    return this.condition;
  }

  /**
   * Filters out all local arenas that do not match the conditions and gets the arena that matches the arena.
   * <p>
   *     The given collection is not being modified in any way.
   * </p>
   *
   * @param arenas The local arenas that shall be processed
   * @return The matching arena. <code>null</code> in case all were filtered out or none matches the selector
   */
  @Nullable
  public Arena processLocal(Collection<Arena> arenas) {
    if (arenas.isEmpty())
      return null;

    final List<Arena> pArenas = new ArrayList<>(arenas);

    // filter them out
    this.condition.filterLocalArenas(pArenas);

    // select
    if (pArenas.isEmpty())
      return null;

    final List<RemoteArena> rArenas = pArenas.stream()
        .map(Arena::asRemote)
        .collect(Collectors.toList());
    final RemoteArena result = this.selector.run(rArenas, null);

    return result != null ? result.getLocal() : null;
  }

  /**
   * Filters out all remote arenas that do not match the conditions and gets the arena that matches the arena.
   * <p>
   *     The given collection is not being modified in any way.
   * </p>
   *
   * @param arenas The remote arenas that shall be processed
   * @return The matching arena. <code>null</code> in case all were filtered out or none matches the selector
   */
  @Nullable
  public RemoteArena processRemote(Collection<? extends RemoteArena> arenas) {
    if (arenas.isEmpty())
      return null;

    final List<RemoteArena> pArenas = new ArrayList<>(arenas);

    // filter them out
    this.condition.filterRemoteArenas(pArenas);

    // select
    if (pArenas.isEmpty())
      return null;

    return this.selector.run(pArenas, null);
  }

  /**
   * Filters out all local arenas that do not match the conditions and gets the arena that matches the arena.
   * <p>
   *     Uses {@link GameAPI#getArenas()} as the reference.
   * </p>
   *
   * @return The matching arena. <code>null</code> in case all were filtered out or none matches the selector
   */
  @Nullable
  public Arena processLocal() {
    return processLocal(GameAPI.get().getArenas());
  }

  /**
   * Filters out all local arenas that do not match the conditions and gets the arena that matches the arena.
   * <p>
   *     Uses {@link RemoteAPI#getArenas()} as the reference.
   * </p>
   *
   * @return The matching arena. <code>null</code> in case all were filtered out or none matches the selector
   */
  @Nullable
  public RemoteArena processRemote() {
    return processRemote(RemoteAPI.get().getArenas());
  }

  /**
   * Serializes a string representation that may as well be used to deserialize/parse it.
   *
   * @return A string representation of this picker
   * @see de.marcely.bedwars.api.arena.picker.ArenaPickerAPI#parsePicker(String)
   */
  public String serialize() {
    final StringBuilder builder = new StringBuilder();

    builder.append('%');
    builder.append(this.selector.getName());
    builder.append(this.condition.serialize(true));
    builder.append('%');

    return builder.toString();
  }

  @Override
  public String toString() {
    final StringBuilder builder = new StringBuilder();

    builder.append("(selector=");
    builder.append(this.selector.getName());
    builder.append(", condition=");
    builder.append(this.condition.toString());
    builder.append(")");

    return builder.toString();
  }
}