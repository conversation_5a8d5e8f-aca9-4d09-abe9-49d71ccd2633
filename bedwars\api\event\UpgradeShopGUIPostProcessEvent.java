package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.game.upgrade.layout.UpgradeShopLayout;
import de.marcely.bedwars.tools.gui.GUI;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

public class UpgradeShopGUIPostProcessEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Player player;
  private final UpgradeShopLayout layout;

  private GUI gui;

  public UpgradeShopGUIPostProcessEvent(Player player, UpgradeShopLayout layout, @Nullable GUI gui) {
    this.player = player;
    this.layout = layout;
    this.gui = gui;
  }

  /**
   * Returns the player that opened the upgrade shop
   *
   * @return The player involved in this
   */
  public Player getPlayer() {
    return this.player;
  }

  /**
   * Returns the layout that was used for the upgrade shop GUI
   *
   * @return The used layout
   */
  public UpgradeShopLayout getLayout() {
    return this.layout;
  }

  /**
   * Returns the GUI that'll be shown to the player.
   * <p>
   * Can be <code>null</code> whereby the GUI won't be shown
   *
   * @return The gui that will be displayed
   */
  public @Nullable GUI getGUI() {
    return this.gui;
  }

  /**
   * Set the GUI that shall be shown to the player.
   * <p>
   * Won't open any gui when passing <code>null</code>
   *
   * @param gui The new gui
   */
  public void setGUI(@Nullable GUI gui) {
    this.gui = gui;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
