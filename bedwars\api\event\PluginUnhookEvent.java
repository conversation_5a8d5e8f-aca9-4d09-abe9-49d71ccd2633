package de.marcely.bedwars.api.event;

import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.plugin.Plugin;

/**
 * Gets called after a plugin has been unhooked.
 */
public class PluginUnhookEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Plugin plugin;

  public PluginUnhookEvent(Plugin plugin) {
    this.plugin = plugin;
  }

  /**
   * Returns the plugin with which this plugin has removed a hook from.
   *
   * @return The unhooked plugin
   */
  public Plugin getPlugin() {
    return this.plugin;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
