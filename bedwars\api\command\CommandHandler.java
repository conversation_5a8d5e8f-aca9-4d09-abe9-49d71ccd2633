package de.marcely.bedwars.api.command;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import org.bukkit.command.CommandSender;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.List;

/**
 * A CommandHandler handles the execution of a command
 */
public interface CommandHandler {

  /**
   * Returns the plugin that created this handler
   *
   * @return The plugin of this handler
   */
  Plugin getPlugin();

  /**
   * Gets called whenever the command is getting added as handler
   *
   * @param cmd The command to which the handler has been added to
   */
  void onRegister(SubCommand cmd);

  /**
   * Override the shown usage at certain conditions.
   * <p>
   * By overriding this method, you may replace {@link SubCommand#getUsage()} with something different.
   * E.g. some parameters may require some permissions.
   * </p>
   *
   * @param senderWrapper The sender of the command
   * @return The overriding usage. <code>null</code> if there is no overriding usage
   */
  default @Nullable String getOverridingUsage(CommandSenderWrapper senderWrapper) {
    return null;
  }

  /**
   * Gets called whenever someone executes the command
   *
   * @param sender The person who executed the command
   * @param fullUsage The full usage, including the label and everything
   * @param args Passed arguments to this command
   */
  void onFire(CommandSender sender, String fullUsage, String[] args);

  /**
   * Gets called whenever someone autocompletes (presses tab) on the command
   *
   * @param sender The person who did the autocomplete
   * @param args The given arguments
   * @return What shall be shown to the player as options. Returning null will display all players
   */
  @Nullable List<String> onAutocomplete(CommandSender sender, String[] args);

  /**
   * A command may display an amount as an additional info in /bw [...] help
   * <p>
   * It's optional. It's not required to override it.
   * Keep in mind that {@link SubCommand#setHasContentAmount(boolean)} must be set to true for it to be actually shown
   *
   * @param sender The sender to which this amount shall be shown
   * @return The amount of entries this (list) command has. <code>null</code> if it shouldn't be shown in this event
   */
  default @Nullable Integer getContentAmount(CommandSender sender) {
    return 0;
  }

  /**
   * Returns the name of the command using the given full usage.
   * <p>
   * Example: With "/bw arena" this method returns "bw"
   *
   * @param fullUsage The full usage that's being passed in {@link #onFire(CommandSender, String, String[])}
   * @return The name of the command that was used to execute the command
   */
  default String getBukkitLabel(String fullUsage) {
    return fullUsage.substring(1, fullUsage.indexOf(' ') != -1 ? fullUsage.indexOf(' ') : fullUsage.length());
  }


  /**
   * The default command handler that effectively does nothing
   */
  class Silent implements CommandHandler {

    public static final CommandHandler INSTANCE = new CommandHandler.Silent();

    private Silent() {
    }

    @Override
    public Plugin getPlugin() {
      return BedwarsAPI.getPlugin();
    }

    @Override
    public void onRegister(SubCommand cmd) {
    }

    @Override
    public void onFire(CommandSender sender, String fullUsage, String[] args) {
      throw new RuntimeException("No handler has been set");
    }

    @Override
    public List<String> onAutocomplete(CommandSender sender, String[] args) {
      return Collections.emptyList();
    }
  }
}
