package de.marcely.bedwars.api.command;

import de.marcely.bedwars.tools.CommandSenderWrapper;
import org.bukkit.command.CommandSender;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * A CommandHandler handles the execution of a command that supports {@link CommandSenderWrapper}.
 *
 * <p>
 *     An alternative to {@link CommandHandler} that allows the usage of {@link CommandSenderWrapper}.
 *     With this, you may permit e.g. players from remote server using this command.
 * </p>
 */
public interface CommandHandlerWrappedSender extends CommandHandler {


  /**
   * Gets called whenever someone executes the command
   *
   * @param sender The person who executed the command
   * @param fullUsage The full usage, including the label and everything
   * @param args Passed arguments to this command
   */
  void onFire(CommandSenderWrapper sender, String fullUsage, String[] args);

  default void onFire(CommandSender sender, String fullUsage, String[] args) {
    onFire(CommandSenderWrapper.wrap(sender), fullUsage, args);
  }

  /**
   * Gets called whenever someone autocompletes (presses tab) on the command
   *
   * @param sender The person who did the autocomplete
   * @param args The given arguments
   * @return What shall be shown to the player as options. Returning null will display all players
   */
  @Nullable List<String> onAutocomplete(CommandSenderWrapper sender, String[] args);

  default List<String> onAutocomplete(CommandSender sender, String[] args) {
    return onAutocomplete(CommandSenderWrapper.wrap(sender), args);
  }

  /**
   * A command may display an amount as an additional info in /bw [...] help
   * <p>
   * It's optional. It's not required to override it.
   * Keep in mind that {@link SubCommand#setHasContentAmount(boolean)} must be set to true for it to be actually shown
   *
   * @param sender The sender to which this amount shall be shown
   * @return The amount of entries this (list) command has. <code>null</code> if it shouldn't be shown in this event
   */
  default @Nullable Integer getContentAmount(CommandSenderWrapper sender) {
    return 0;
  }

  default @Nullable Integer getContentAmount(CommandSender sender) {
    return getContentAmount(CommandSenderWrapper.wrap(sender));
  }
}
