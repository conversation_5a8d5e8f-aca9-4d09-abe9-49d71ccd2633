package cn.acebrand.acevotemode.events;

import org.bukkit.entity.Player;
import org.bukkit.Location;

/**
 * 幸运方块事件接口
 */
public interface LuckyEvent {
    
    /**
     * 执行事件
     * @param player 触发事件的玩家
     * @param location 幸运方块的位置
     */
    void execute(Player player, Location location);
    
    /**
     * 获取事件名称
     * @return 事件名称
     */
    String getName();
    
    /**
     * 获取事件类型
     * @return 事件类型 (GOOD, BAD, NEUTRAL)
     */
    EventType getType();
    
    /**
     * 获取事件权重（在同类型事件中的选择概率）
     * @return 权重值
     */
    int getWeight();
}
