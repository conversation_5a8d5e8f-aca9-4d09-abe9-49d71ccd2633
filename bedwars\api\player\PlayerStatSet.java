package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.message.Message;
import org.bukkit.command.CommandSender;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a handler for a data set<br>
 * Data sets might be the kills, rank or the play time of the player<br>
 * It has been built to be fully modular<br>
 * There's only one global instance that's handling all players
 */
public interface PlayerStatSet {

  /**
   * Returns the unique id of this data set<br>
   * This is being used for other plugins that might want to modify the data set and to make it easier for them<br>
   * It'll also be used for the %mbedwars_stats{@literal -<id>%} PAPI placeholder<br>
   * And it'll also get stored inside the SQL database including {@link #getValue(PlayerStats)} but with lower priority.
   * Meaning they can get replaced by entries with the same key of {@link PlayerStats}.
   * The reason why it's getting stored in the database as well is that it's being used for e.g. the ranking an webinterface
   *
   * @return The unique id of this data set
   */
  String getId();

  /**
   * Returns the plugin that's providing this data set
   *
   * @return The plugin behind the data set
   */
  Plugin getPlugin();

  /**
   * Returns the name that shall be displayed everywhere<br>
   * Tip: Use {@link Message#buildByKey(String, String)} when you're saving the message in the messages file
   *
   * @param sender Shall return the name in the language of him. Can be null
   * @return The name of this data set that'll be displayed to the users
   */
  String getName(@Nullable CommandSender sender);

  /**
   * Returns what essentially will be displayed as the value
   *
   * @param stats The stats from which it should pick its info
   * @return The displayed value
   */
  String getDisplayedValue(PlayerStats stats);

  /**
   * Returns what would be displayed as the value for a certian number.
   * <p>
   *   Useful if you want to format a value similar to how stats are being displayed.
   *   Note: This method is not required to override, but it's recommended to do so.
   *   If its not overriden it'll return {@link #formatInt(Number)}
   * </p>
   *
   * @param number the number to format
   * @return the formated version of the number
   */
  default String formatValue(Number number) {
    return formatInt(number);
  }

  /**
   * Similar to {@link #getDisplayedValue(PlayerStats)}, but as a direct number<br>
   * Will amongst other things be used for the leaderboard
   *
   * @param stats The stats from which it should pick its info
   * @return The value of this prop set
   * @throws IllegalStateException When this hasn't been implemented/not being supported or other reasons
   */
  Number getValue(PlayerStats stats);

  /**
   * Stat can implement this for easier use for others wanting to modify it
   *
   * @param stats The stats from which it should pick its info
   * @param value The new value
   * @throws IllegalStateException When this hasn't been implemented/not being supported or other reasons
   */
  default void setValue(PlayerStats stats, Number value) {
    throw new IllegalStateException("Not implemented exception");
  }

  /**
   * Will add value to the current value
   *
   * @param stats The stats from which it should pick its info
   * @param amount How much it should add to the value
   * @throws IllegalStateException When this hasn't been implemented/not being supported or other reasons
   */
  default void addValue(PlayerStats stats, Number amount) {
    setValue(stats, getValue(stats).doubleValue() + amount.doubleValue());
  }

  /**
   * Helper method for {@link #getDisplayedValue(PlayerStats)}
   *
   * @param num A number
   * @return Returns the number with only two decimal places
   */
  default String formatDouble(Number num) {
    return String.format("%.2f", Double.isFinite(num.doubleValue()) ? num.doubleValue() : 0);
  }

  /**
   * Helper method for {@link #getDisplayedValue(PlayerStats)}
   *
   * @param num A number
   * @return Returns the number without any decimal places
   */
  default String formatInt(Number num) {
    return "" + num.intValue();
  }
}