package de.marcely.bedwars.api.game.shop.layout;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.api.game.shop.ShopPage;
import de.marcely.bedwars.api.player.PlayerProperties;
import de.marcely.bedwars.tools.gui.GUI;
import de.marcely.bedwars.tools.gui.GUIItem;
import de.marcely.bedwars.tools.gui.VillagerOffer;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * The logic behind the look-and-feel of a {@link ShopLayout}.<br>
 * It receives an {@link OpenEvent} that contains various informations that are needed in that event to display it.<br>
 * The event {@link ShopLayoutHandler#build(OpenEvent)}, that gets called whenever it's time to process it, gets called
 * very frequently, including whenever the player changes his page. You should try to make your code as efficient as possible.
 * At the end you're gonna return a {@link GUI}, probably of the type {@link de.marcely.bedwars.tools.gui.type.ChestGUI}, that will be displayed to the player.
 * Use methods that are included in your {@link OpenEvent}, such as {@link OpenEvent#build(ShopPage)} to
 * construct clickable buttons for the GUI that handle the item purchasing, page changing and etc.
 */
public interface ShopLayoutHandler {

  /**
   * Gets called when layout is getting opened or when a player changed the page.
   * <p>
   *   This method is being called on a separate thread!
   * </p>
   *
   * @param event The event of it containing information
   * @return The GUI that shall be shown to the player
   */
  GUI build(OpenEvent event);

  /**
   * Read the user-defined configurations from the layouts config file.
   *
   * @param section The section that you previously added your configs into
   * @see #writeConfigProperties(ConfigurationSection)
   */
  default void readConfigProperties(ConfigurationSection section) { }

  /**
   * Write the user-defined configurations into the layouts config file.
   * <p>
   *   Make sure to set the values as the values that were previously read
   *   using #readConfigs(ConfigurationSection) to make sure they stay with an update.
   *   If it hasn't been called before, set your default values.
   * </p>
   *
   * @param section Write your stuff into this section
   */
  default void writeConfigProperties(ConfigurationSection section) { }


  /**
   * Used to pass information to {@link ShopLayoutHandler#build(OpenEvent)}
   */
  interface OpenEvent {

    /**
     * Returns the layout that has been opened
     *
     * @return The layout
     */
    ShopLayout getLayout();

    /**
     * Returns the player that opened it
     *
     * @return The player
     */
    Player getPlayer();

    /**
     * Returns the arena the player is a part of.
     *
     * @return The arena of the player. May be <code>null</code> of he's in none
     */
    @Nullable
    Arena getArena();

    /**
     * Returns the already loaded and ready to use player properties instance of the player
     *
     * @return The PlayerProperties instance of the given player
     */
    PlayerProperties getPlayerProperties();

    /**
     * Returns what shall be shown in this given sitation.
     * <p>
     *   The exact value depends on the plugin configuration.<br>
     *   Factors include e.g.: Whether {@link #isGUITitleAsShopPage()}
     *   should be conisdered and what messages have been configured.
     * </p>
     * <p>
     *   The plugin does not automatically apply it. Make sure
     *   to apply it after setting the shop page.
     * </p>
     *
     * @return The title that shall be used in the GUI
     */
    String getSituationalGUITitle();

    /**
     * Returns whether the title of the GUI should be the same as the shop page.
     * <p>
     *   Whether this feature is active depends on the plugin configuration.<br>
     *   The plugin will handle this automatically.
     *   One edge case is when you have custom pages (e.g. quick buy):
     *   In this case you should check if this feature is enabled and set it yourself.
     * </p>
     * @return Whether the title of the GUI should be the same as the shop page
     */
    boolean isGUITitleAsShopPage();

    /**
     * The available pages with which the handler has to work with<br>
     * The collections and EVERYTHING in the collection has been cloned,<br>
     * meaning the handler can do anything with it that he wants
     *
     * @return The available pages
     */
    List<ShopPage> getPages();

    /**
     * The currently open page<br>
     * Can be null when there's no page open
     *
     * @return The currently open page
     */
    @Nullable ShopPage getOpenPage();

    /**
     * Sets the new open page.<br>
     * Won't reopen the GUI when set
     *
     * @param page The new open page, can be null
     */
    void setOpenPage(@Nullable ShopPage page);

    /**
     * All items that should be displayed to the player
     *
     * @return all items that will be displayed
     */
    List<? extends ShopItem> getPageItems();

    /**
     * The default value is null
     *
     * @return Data that can be passed around
     */
    Object getData();

    /**
     * Returns data that can be used during the session
     *
     * @return Data that can be passed around
     * @param def The default value which will be taken if none is set
     * @param <T> (Unsafe operation:) Automatically casts the intern data object to the given generic
     */
    <T> T getData(T def);

    /**
     * Will reopen the current GUI
     */
    void refresh();

    /**
     * Builds a GUIItem that can be used to set the item inside the GUI
     *
     * @param item The item that shall be displayed
     * @return The item that shall be placed inside the GUI
     */
    GUIItem build(ShopItem item);

    /**
     * Builds a GUIItem that can be used to set the page inside the GUI.<br>
     * Also resets the current data back to null once pressed
     *
     * @param page The page that shall be displayed
     * @return The item that shall be placed inside the GUI
     */
    default GUIItem build(ShopPage page) {
      return build(page, null);
    }

    /**
     * Builds a GUIItem that can be used to set the page inside the GUI
     *
     * @param page The page that shall be displayed
     * @param data The new data that will be taken after pressing on the page
     * @return The item that shall be placed inside the GUI
     */
    GUIItem build(ShopPage page, Object data);

    /**
     * Builds a GUIItem that can be used to set the page inside the villager GUI
     *
     * @param item The item that shall be displayed
     * @return The item that shall be placed inside the GUI
     */
    VillagerOffer buildOffer(ShopItem item);

    /**
     * Builds a GUIItem that will flush any info about the info on click.<br>
     * This behavior is similar to as if the player is closing and reopening the shop
     *
     * @param is The item that shall be presented as the button
     * @return The item that shall be placed inside the GUI
     */
    GUIItem buildHome(ItemStack is);
  }
}
