package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

import java.util.HashMap;
import java.util.Map;

/**
 * Boss技能监听器
 * 负责监听Boss技能使用并向玩家发送提示消息
 */
public class BossSkillListener implements Listener {

    private final AceVoteMode plugin;
    private final TerrorDescentMode terrorMode;
    
    // 技能名称映射 (技能ID -> 中文名称)
    private final Map<String, String> skillNames = new HashMap<>();
    
    public BossSkillListener(AceVoteMode plugin, TerrorDescentMode terrorMode) {
        this.plugin = plugin;
        this.terrorMode = terrorMode;
        
        // 初始化技能名称映射
        initializeSkillNames();
    }
    
    /**
     * 初始化技能名称映射
     */
    private void initializeSkillNames() {
        // 第一阶段技能 (LightHerald1)
        skillNames.put("LH1", "&e⚡ 光明冲击");
        skillNames.put("LH2", "&e⚡ 光明横扫");
        skillNames.put("LH3", "&e⚡ 光明蓄力");
        skillNames.put("LH4", "&e⚡ 光明连击");
        skillNames.put("LH5", "&e⚡ 光明震荡");
        skillNames.put("LH6", "&e⚡ 光明眩晕");
        skillNames.put("LH7", "&e⚡ 光明传送");
        skillNames.put("LH8", "&e⚡ 光明反射");
        
        // 第二阶段技能 (LightHerald2)
        skillNames.put("LK1", "&5🌙 强化冲击");
        skillNames.put("LK2", "&5🌙 强化横扫");
        skillNames.put("LK3", "&5🌙 强化三连击");
        skillNames.put("LK4", "&5🌙 强化眩晕");
        skillNames.put("LK5", "&5🌙 强化震荡");
        skillNames.put("LK6", "&5🌙 强化蓄力");
        skillNames.put("LK8", "&5🌙 光明冲刺");
        skillNames.put("LK9", "&5🌙 光明爆发");
        skillNames.put("LK10", "&5🌙 光明旋风");
        skillNames.put("LK11", "&5🌙 光明长矛");
        skillNames.put("LK12", "&5🌙 光明光束");
        skillNames.put("LK13", "&5🌙 双重光束");
        
        // 第三阶段技能 (LightCore)
        skillNames.put("LC1", "&c💀 核心爆发");
        skillNames.put("LC2", "&c💀 核心传送");
        skillNames.put("LC2_2", "&c💀 核心后退");
        skillNames.put("LC3", "&c💀 核心投射");
        skillNames.put("LC4", "&c💀 核心震荡");
        skillNames.put("LC5", "&c💀 核心蓄力");
        skillNames.put("LC6", "&c💀 召唤光明战士");
        skillNames.put("LC6_2", "&c💀 光束轰炸");
        skillNames.put("LC7", "&c💀 终极光束");
        skillNames.put("LC8", "&c💀 核心光束");
        skillNames.put("LC9", "&c💀 核心风暴");
        skillNames.put("LC10", "&c💀 核心闪现");
        skillNames.put("LC11", "&c💀 核心狂暴");
        
        // 召唤物技能 (LightWarrior)
        skillNames.put("LW1", "&e⚔ 战士攻击");
        skillNames.put("LW2", "&e⚔ 战士横扫");
        skillNames.put("LW3", "&e⚔ 战士突刺");
    }
    
    /**
     * 手动广播技能使用消息
     * 由于MythicMobs技能事件可能不可用，改为手动调用
     */
    public void broadcastSkillUsage(Arena arena, String bossType, String skillName) {
        // 检查是否启用技能提示
        if (!terrorMode.getConfig().getBoolean("boss.skill-notifications.enabled", true)) {
            return;
        }

        // 获取技能的中文名称
        String displayName = skillNames.get(skillName);
        if (displayName == null) {
            // 如果没有配置的技能名称，使用默认格式
            displayName = "&f" + skillName;
        }

        // 获取Boss显示名称
        String bossDisplayName = getBossDisplayName(bossType);

        // 构建技能使用消息
        String message = "&8[&f技能&8] " + bossDisplayName + " &f使用了 " + displayName;

        // 向竞技场内所有玩家发送消息
        broadcastSkillMessage(arena, message);
    }
    

    
    /**
     * 获取Boss显示名称
     */
    private String getBossDisplayName(String bossType) {
        switch (bossType) {
            case "LightHerald1":
                return "&e⚡ 光明先驱";
            case "LightHerald2":
                return "&5🌙 光明先驱";
            case "LightCore":
                return "&c💀 光明核心";
            case "LightWarrior":
                return "&e⚔ 光明战士";
            default:
                return "&f" + bossType;
        }
    }
    
    /**
     * 向竞技场广播技能消息
     */
    private void broadcastSkillMessage(Arena arena, String message) {
        // 检查是否启用技能提示
        if (!terrorMode.getConfig().getBoolean("boss.skill-notifications.enabled", true)) {
            return;
        }
        
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);
        
        for (Player player : arena.getPlayers()) {
            player.sendMessage(coloredMessage);
        }
    }
}
