package de.marcely.bedwars.api.arena;

import java.time.Duration;
import lombok.AllArgsConstructor;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;

/**
 * Contains info of the last even in which a player directly or indirectly received damaged.
 *
 * @see de.marcely.bedwars.api.GameAPI#getLastPlayerCausedDamage(Player)
 */
@AllArgsConstructor
public class PlayerDamageInfo {

  /**
   * Get the max age, until an instance is counted as invalid.
   */
  public static final Duration MAX_AGE = Duration.ofSeconds(15);

  private final Player damager;
  private final EntityDamageByEntityEvent event;
  private final Instant time;

  /**
   * Get the player who directly/indirectly caused the game.
   *
   * @return The damaging player
   */
  public Player getDamager() {
    return this.damager;
  }

  /**
   * Get the event that was used to obtain this info from.
   *
   * @return The related event
   */
  public EntityDamageByEntityEvent getEvent() {
    return this.event;
  }

  /**
   * Get the Instant when this info was obtained.
   *
   * @return The instant this info was constructed
   */
  public Instant getInstant() {
    return this.time;
  }

  /**
   * Tries to get the projectile that was used to damage the player.
   *
   * @return The projectile used in this event. May be <code>null</code> if none has been used
   */
  @Nullable
  public Projectile getProjectileDamager() {
    final Entity entity = this.event.getDamager();

    if (entity instanceof Projectile)
      return (Projectile) entity;

    return null;
  }

  /**
   * Get whether the age of this object is larger than {@link #MAX_AGE}.
   *
   * @return Get whether this object is outdated, and thus won't be used internally
   */
  public boolean isOutdated() {
    return System.currentTimeMillis() - this.time.toEpochMilli() >= MAX_AGE.toMillis();
  }
}
