package de.marcely.bedwars.api.game.spawner;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

/**
 * Custom implementation for overrding drop and shop capabilities of a drop type.
 * <p>
 *   An example use case: If you want to buy with experience points.
 *   They don't exist as items, but they can still be dropped as xp orbs.
 *   When picked up, players are able to use their virtual balance to buy items.
 * </p>
 */
public abstract class CustomDropTypeHandler {

  private final String id;
  private final Plugin plugin;

  /**
   * @param id The id that is later used within the spawners config file
   * @param plugin Your plugin implementing this handler
   */
  public CustomDropTypeHandler(String id, Plugin plugin) {
    this.id = id;
    this.plugin = plugin;
  }

  /**
   * Gets called whenever the spawner drops something.
   * <p>
   *  Being invoked after it has been passed to Bukkit's event pipeline.
   * </p>
   *
   * @param spawner The spawner that's being spawned
   * @param dropLocation The location at which it shall drop the item. Can differ to the one configured inside the spawner
   */
  public abstract void handleDrop(Spawner spawner, Location dropLocation);

  /**
   * Get the amount of items the player is holding within its inventory.
   * <p>
   *   This is used to i.a. determine whether a player has the balance to buy an item.
   * </p>
   *
   * @param player The player to check
   * @return The amount of items the player is holding
   */
  public abstract int getHoldingAmount(Player player);

  /**
   * Take a given amount from a player's balance.
   * <p>
   *   This is used to i.a. remove the items from the player's inventory after buying an item.
   * </p>
   *
   * @param player The player to take the items from
   * @param amount The amount to take
   */
  public abstract void take(Player player, int amount);

  /**
   * Give a given amount to a player's balance.
   * <p>
   *   This is used to i.a. give the items to the player's inventory
   *   after buying an item that is a drop type implementing this custom handler.
   * </p>
   *
   * @param player The player to give the items to
   * @param amount The amount to give
   */
  public abstract void give(Player player, int amount);



  /**
   * The id of this custom spawner
   *
   * @return The identifier
   */
  public final String getId() {
    return this.id;
  }

  /**
   * The plugin that created this handler
   *
   * @return The plugin whose ClassLoader is equal to the one of this class
   */
  public final Plugin getPlugin() {
    return this.plugin;
  }
}