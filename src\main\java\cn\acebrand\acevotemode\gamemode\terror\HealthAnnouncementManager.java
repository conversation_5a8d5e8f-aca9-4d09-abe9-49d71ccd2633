package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.message.Message;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

/**
 * Boss血量公告管理器
 * 支持自定义血量百分比公告点和消息
 */
public class HealthAnnouncementManager {

    private final AceVoteMode plugin;
    private final Arena arena;
    private final ConfigurationSection config;

    private final Set<Integer> announcedPercentages = new HashSet<>();
    private int lastHealthPercentage = 100;

    public HealthAnnouncementManager(AceVoteMode plugin, Arena arena, ConfigurationSection config) {
        this.plugin = plugin;
        this.arena = arena;
        this.config = config;
    }

    /**
     * 检查并公告Boss血量
     */
    public void checkAndAnnounce(LivingEntity boss) {
        if (!config.getBoolean("messages.boss-health-announcements.enabled", true)) {
            return;
        }

        int currentHealthPercentage = calculateHealthPercentage(boss);

        // 获取需要公告的血量百分比
        List<Integer> healthPercentages = config
                .getIntegerList("messages.boss-health-announcements.health-percentages");
        if (healthPercentages.isEmpty()) {
            // 默认血量公告点
            healthPercentages = Arrays.asList(90, 75, 50, 25, 10, 5);
        }

        // 检查是否需要公告
        for (int percentage : healthPercentages) {
            if (currentHealthPercentage <= percentage &&
                    lastHealthPercentage > percentage &&
                    !announcedPercentages.contains(percentage)) {

                announceHealth(percentage, currentHealthPercentage);
                announcedPercentages.add(percentage);
            }
        }

        lastHealthPercentage = currentHealthPercentage;
    }

    /**
     * 计算血量百分比
     */
    @SuppressWarnings("deprecation")
    private int calculateHealthPercentage(LivingEntity boss) {
        double health = boss.getHealth();
        double maxHealth = boss.getMaxHealth();
        return (int) Math.round((health / maxHealth) * 100);
    }

    /**
     * 公告血量状态
     */
    private void announceHealth(int triggerPercentage, int actualPercentage) {
        ConfigurationSection conditions = config
                .getConfigurationSection("messages.boss-health-announcements.conditions");
        ConfigurationSection messages = config.getConfigurationSection("messages.boss-health-announcements.messages");

        // 获取该血量点的配置
        ConfigurationSection condition = conditions.getConfigurationSection(String.valueOf(triggerPercentage));
        if (condition == null) {
            // 使用默认配置
            announceDefaultHealth(actualPercentage);
            return;
        }

        // 获取消息类型和模板
        String messageType = condition.getString("message-type", "default");
        String messageTemplate = messages.getString(messageType,
                "&c&l【Boss状态】&f 恐怖领主剩余血量：&c{percentage}%");

        // 格式化消息
        String message = messageTemplate.replace("{percentage}", String.valueOf(actualPercentage));

        // 发送消息
        arena.broadcast(Message.build(message));

        // 播放音效
        String soundName = condition.getString("sound");
        if (soundName != null && config.getBoolean("messages.boss-health-announcements.sounds.enabled", true)) {
            playSound(soundName);
        }

        // 发送Title
        String title = condition.getString("title");
        String subtitle = condition.getString("subtitle");
        if (title != null || subtitle != null) {
            sendTitle(title, subtitle, actualPercentage);
        }

        plugin.getLogger().info(String.format("血量公告：%d%% (触发点: %d%%)，竞技场：%s",
                actualPercentage, triggerPercentage, arena.getName()));
    }

    /**
     * 默认血量公告
     */
    private void announceDefaultHealth(int percentage) {
        String message = "&c&l【Boss状态】&f 恐怖领主剩余血量：&c" + percentage + "%";
        arena.broadcast(Message.build(message));

        // 播放默认音效
        playSound("BLOCK_NOTE_BLOCK_PLING");
    }

    /**
     * 播放音效
     */
    private void playSound(String soundName) {
        try {
            Sound sound = Sound.valueOf(soundName);
            float volume = (float) config.getDouble("messages.boss-health-announcements.sounds.volume", 1.0);
            float pitch = (float) config.getDouble("messages.boss-health-announcements.sounds.pitch", 1.0);

            for (Player player : arena.getPlayers()) {
                player.playSound(player.getLocation(), sound, volume, pitch);
            }
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的音效名称: " + soundName);
        }
    }

    /**
     * 发送Title
     */
    private void sendTitle(String title, String subtitle, int percentage) {
        // 替换占位符
        if (title != null) {
            title = title.replace("{percentage}", String.valueOf(percentage));
        }
        if (subtitle != null) {
            subtitle = subtitle.replace("{percentage}", String.valueOf(percentage));
        }

        for (Player player : arena.getPlayers()) {
            player.sendTitle(title, subtitle, 10, 40, 10);
        }
    }

    /**
     * 重置公告状态
     */
    public void reset() {
        announcedPercentages.clear();
        lastHealthPercentage = 100;
    }

    /**
     * 获取已公告的血量百分比
     */
    public Set<Integer> getAnnouncedPercentages() {
        return new HashSet<>(announcedPercentages);
    }

    /**
     * 手动添加已公告的百分比（避免重复公告）
     */
    public void addAnnouncedPercentage(int percentage) {
        announcedPercentages.add(percentage);
    }

    /**
     * 获取当前血量百分比
     */
    public int getLastHealthPercentage() {
        return lastHealthPercentage;
    }

    /**
     * 强制公告当前血量
     */
    public void forceAnnounce(LivingEntity boss) {
        int currentHealthPercentage = calculateHealthPercentage(boss);
        announceDefaultHealth(currentHealthPercentage);
    }

    /**
     * 添加自定义血量公告点
     */
    public void addHealthPercentage(int percentage) {
        List<Integer> currentPercentages = config
                .getIntegerList("messages.boss-health-announcements.health-percentages");
        if (!currentPercentages.contains(percentage)) {
            currentPercentages.add(percentage);
            currentPercentages.sort((a, b) -> b.compareTo(a)); // 降序排列
            config.set("messages.boss-health-announcements.health-percentages", currentPercentages);
        }
    }

    /**
     * 移除血量公告点
     */
    public void removeHealthPercentage(int percentage) {
        List<Integer> currentPercentages = config
                .getIntegerList("messages.boss-health-announcements.health-percentages");
        currentPercentages.remove(Integer.valueOf(percentage));
        config.set("messages.boss-health-announcements.health-percentages", currentPercentages);
    }

    /**
     * 获取下一个血量公告点
     */
    public int getNextAnnouncementPercentage() {
        List<Integer> healthPercentages = config
                .getIntegerList("messages.boss-health-announcements.health-percentages");

        for (int percentage : healthPercentages) {
            if (lastHealthPercentage > percentage && !announcedPercentages.contains(percentage)) {
                return percentage;
            }
        }

        return -1; // 没有下一个公告点
    }

    /**
     * 检查是否为关键血量点
     */
    public boolean isCriticalHealth(int percentage) {
        return percentage <= 10; // 10%以下为关键血量
    }

    /**
     * 检查是否为阶段转换点
     */
    public boolean isPhaseTransition(int percentage) {
        return percentage == 75 || percentage == 50 || percentage == 25;
    }
}
