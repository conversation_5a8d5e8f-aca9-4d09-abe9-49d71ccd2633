package de.marcely.bedwars.api.game.specialitem;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import org.bukkit.ChatColor;
import org.bukkit.plugin.Plugin;

/**
 * The default use handler of a special item<br>
 * It'll send a warning to the player whenever he's using it
 */
public class DeadSpecialItemUseHandler implements SpecialItemUseHandler {

  public static SpecialItemUseHandler INSTANCE = new DeadSpecialItemUseHandler();

  @Override
  public Plugin getPlugin() {
    return BedwarsAPI.getPlugin();
  }

  @Override
  public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
    event.getPlayer().sendMessage(ChatColor.DARK_PURPLE + "No handler has been added yet");
    event.getPlayer().sendMessage(ChatColor.LIGHT_PURPLE + " Use SpecialItem#setHandler(SpecialItemUseHandler handler) to add one");

    final SpecialItemUseSession session = new SpecialItemUseSession(event) {
      protected void handleStop() {
      }
    };

    session.stop();

    return session;
  }
}
