package de.marcely.bedwars.api.game.shop.price;

import de.marcely.bedwars.api.game.spawner.DropType;

public interface SpawnerItemShopPrice extends ItemShopPrice {

  /**
   * Returns the {@link DropType} aka spawner that will be taken from the player on purchase.
   *
   * @return The drop type of this price
   */
  DropType getDropType();

  /**
   * Set the {@link DropType} aka the spawner that will be taken from the player on purchase.
   *
   * @param dropType The new dropType
   */
  void setDropType(DropType dropType);
}