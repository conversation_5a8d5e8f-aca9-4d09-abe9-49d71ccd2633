package de.marcely.bedwars.api.game.upgrade;

import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.event.player.PlayerTriggerUpgradeEvent;
import org.bukkit.command.CommandSender;
import org.bukkit.plugin.Plugin;

import java.util.Collections;
import java.util.List;

/**
 * Represents the logical handler of a upgrade trigger.
 */
public abstract class UpgradeTriggerHandler {

  private final String id;
  private final boolean isTrap;
  private final Plugin plugin;

  /**
   * @param id The id of this handler, should be unique to others. Default ones start with bedwars:
   * @param plugin The plugin that constructs this handler
   */
  public UpgradeTriggerHandler(String id, boolean isTrap, Plugin plugin) {
    this.id = id;
    this.isTrap = isTrap;
    this.plugin = plugin;
  }

  /**
   * Returns the plugin that's in charge of this handler
   *
   * @return The plugin that created this handler
   */
  public Plugin getPlugin() {
    return this.plugin;
  }


  /**
   * Returns the id of this handler
   *
   * @return The id of this handler
   */
  public String getId() {
    return this.id;
  }

  /**
   * Returns weather or not this is a trap handler. If this is a trap handler
   * the handler will be put into a queue, and will be executed when a base is invaded.
   *
   * @return Is this handler is a trap handler
   */
  public boolean isTrap() {
    return this.isTrap;
  }

  /**
   * Returns the name of this handler in the default language.
   * <p>
   *  This name may be used in the upgrade shop lore
   * </p>
   *
   * @return The generic name of this handler
   */
  public String getName() {
    return getName(null);
  }

  /**
   * Returns the name of this handler.
   * <p>
   *  This name may be used in the upgrade shop lore
   * </p>
   *
   * @return The name of the handler
   */
  public String getName(CommandSender sender) {
    return "Name Unknown";
  }

  /**
   * Returns the description of this handler in the default language.
   * <p>
   *  This description may be used in the upgrade shop lore
   * </p>
   *
   * @return The generic description lines
   */
  public List<String> getDescriptionLines() {
    return getDescriptionLines(null);
  }

  /**
   * Returns the description of this handler.
   * <p>
   *  This description may be used in the upgrade shop lore
   * </p>
   *
   * @return The description lines for this handler
   */
  public List<String> getDescriptionLines(CommandSender sender) {
    return Collections.singletonList("Description Unknown");
  }

  /**
   * Returns the type of this handler.
   * Custom ones should return {@link UpgradeTriggerHandlerType#PLUGIN}.
   *
   * @return The type of this handler
   */
  public UpgradeTriggerHandlerType getType() {
    return UpgradeTriggerHandlerType.PLUGIN;
  }

  /**
   * Handles the execution of the upgrade.
   *
   * @param event The information needed to handle the event
   */
  public abstract void onTrigger(PlayerTriggerUpgradeEvent event);

  /**
   * Returns whether this handler is registered.
   * <p>
   * Use {@link GameAPI#registerUpgradeTriggerHandler(UpgradeTriggerHandler)} to register it,
   * or {@link GameAPI#unregisterUpgradeTriggerHandler(UpgradeTriggerHandler)} to unregister it.
   * </p>
   *
   * @return <code>true</code> if it's registered
   */
  public final boolean isRegistered() {
    final UpgradeTriggerHandler handler = GameAPI.get().getUpgradeTriggerHandler(getId());

    return handler == this;
  }
}