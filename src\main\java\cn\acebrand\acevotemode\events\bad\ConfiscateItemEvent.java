package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.util.List;
import java.util.Random;

/**
 * 没收道具事件
 * 没收当前手上的道具
 */
public class ConfiscateItemEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    
    public ConfiscateItemEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(badDir, "confiscate_item.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/confiscate_item.yml", false);
                plugin.getLogger().info("已生成没收道具事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载没收道具事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载没收道具事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public String getName() {
        return "CONFISCATE_ITEM";
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 30) : 30;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }
        
        // 获取配置参数
        String mode = config != null ? config.getString("confiscate.mode", "MAIN_HAND") : "MAIN_HAND";
        boolean confiscateFullStack = config != null ? config.getBoolean("confiscate.confiscate_full_stack", true) : true;
        
        // 执行没收
        boolean success = confiscateItem(player, mode, confiscateFullStack);
        
        if (success) {
            // 播放效果
            playConfiscateEffects(player);
        } else {
            // 没有道具可没收
            String noItemMessage = getNoItemMessage();
            player.sendMessage(noItemMessage);
        }
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了没收道具事件，成功: " + success);
    }
    
    /**
     * 没收道具
     */
    private boolean confiscateItem(Player player, String mode, boolean confiscateFullStack) {
        ItemStack itemToConfiscate = null;
        boolean isMainHand = false;
        
        // 根据模式选择要没收的道具
        switch (mode.toUpperCase()) {
            case "MAIN_HAND":
                itemToConfiscate = player.getInventory().getItemInMainHand();
                isMainHand = true;
                break;
            case "OFF_HAND":
                itemToConfiscate = player.getInventory().getItemInOffHand();
                isMainHand = false;
                break;
            case "BOTH_HANDS":
                // 优先没收主手，如果主手没有则没收副手
                itemToConfiscate = player.getInventory().getItemInMainHand();
                isMainHand = true;
                if (itemToConfiscate == null || itemToConfiscate.getType() == Material.AIR) {
                    itemToConfiscate = player.getInventory().getItemInOffHand();
                    isMainHand = false;
                }
                break;
            case "RANDOM_HAND":
                if (random.nextBoolean()) {
                    itemToConfiscate = player.getInventory().getItemInMainHand();
                    isMainHand = true;
                } else {
                    itemToConfiscate = player.getInventory().getItemInOffHand();
                    isMainHand = false;
                }
                break;
        }
        
        // 检查是否有有效道具
        if (itemToConfiscate == null || itemToConfiscate.getType() == Material.AIR) {
            return false;
        }
        
        // 检查是否在黑名单中
        if (isItemBlacklisted(itemToConfiscate)) {
            return false;
        }
        
        // 检查特殊处理规则
        if (!shouldConfiscateItem(itemToConfiscate)) {
            return false;
        }
        
        // 获取道具名称用于消息
        String itemName = getItemDisplayName(itemToConfiscate);
        
        // 执行没收
        if (confiscateFullStack) {
            // 没收整个堆叠
            if (isMainHand) {
                player.getInventory().setItemInMainHand(new ItemStack(Material.AIR));
            } else {
                player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
            }
        } else {
            // 只没收一个
            itemToConfiscate.setAmount(itemToConfiscate.getAmount() - 1);
            if (itemToConfiscate.getAmount() <= 0) {
                if (isMainHand) {
                    player.getInventory().setItemInMainHand(new ItemStack(Material.AIR));
                } else {
                    player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
                }
            }
        }
        
        // 发送没收成功消息
        String successMessage = getConfiscateSuccessMessage(itemName);
        player.sendMessage(successMessage);
        
        return true;
    }
    
    /**
     * 检查道具是否在黑名单中
     */
    private boolean isItemBlacklisted(ItemStack item) {
        if (config == null) return false;
        
        List<String> blacklist = config.getStringList("confiscate.blacklist");
        return blacklist.contains(item.getType().name());
    }
    
    /**
     * 检查是否应该没收该道具
     */
    private boolean shouldConfiscateItem(ItemStack item) {
        if (config == null) return true;
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null) return true;
        
        // 检查附魔物品
        if (!config.getBoolean("confiscate.special_handling.enchanted_items", true)) {
            if (meta.hasEnchants()) {
                return false;
            }
        }
        
        // 检查命名物品
        if (!config.getBoolean("confiscate.special_handling.named_items", true)) {
            if (meta.hasDisplayName()) {
                return false;
            }
        }
        
        // 检查不可破坏物品
        if (!config.getBoolean("confiscate.special_handling.unbreakable_items", false)) {
            if (meta.isUnbreakable()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取道具显示名称
     */
    private String getItemDisplayName(ItemStack item) {
        ItemMeta meta = item.getItemMeta();
        if (meta != null && meta.hasDisplayName()) {
            return meta.getDisplayName();
        }
        return item.getType().name();
    }
    
    /**
     * 播放没收效果
     */
    private void playConfiscateEffects(Player player) {
        if (config == null) return;
        
        // 播放音效
        if (config.getBoolean("confiscate.effects.sound.enabled", true)) {
            String soundType = config.getString("confiscate.effects.sound.sound_type", "ENTITY_ITEM_PICKUP");
            float volume = (float) config.getDouble("confiscate.effects.sound.volume", 1.0);
            float pitch = (float) config.getDouble("confiscate.effects.sound.pitch", 0.5);
            
            try {
                Sound sound = Sound.valueOf(soundType);
                player.playSound(player.getLocation(), sound, volume, pitch);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的音效类型: " + soundType);
            }
        }
        
        // 显示粒子效果
        if (config.getBoolean("confiscate.effects.particles.enabled", true)) {
            String particleType = config.getString("confiscate.effects.particles.particle_type", "SMOKE_NORMAL");
            int count = config.getInt("confiscate.effects.particles.count", 20);
            
            try {
                Particle particle = Particle.valueOf(particleType);
                player.getLocation().getWorld().spawnParticle(particle, player.getLocation().add(0, 1, 0), count);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的粒子类型: " + particleType);
            }
        }
    }
    
    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null) return true;
        return config.getBoolean("messages.send_message", true);
    }
    
    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null) return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }
    
    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null) return "§c你的道具被神秘力量夺走了！";
        return config.getString("messages.event_message", "§c你的道具被神秘力量夺走了！");
    }
    
    /**
     * 获取没收成功消息
     */
    private String getConfiscateSuccessMessage(String itemName) {
        if (config == null) return "§c" + itemName + " 被没收了！";
        String template = config.getString("messages.confiscate_success", "§c{item_name} 被没收了！");
        return template.replace("{item_name}", itemName);
    }
    
    /**
     * 获取没有道具消息
     */
    private String getNoItemMessage() {
        if (config == null) return "§7你手上没有道具可以没收...";
        return config.getString("messages.no_item_message", "§7你手上没有道具可以没收...");
    }
}
