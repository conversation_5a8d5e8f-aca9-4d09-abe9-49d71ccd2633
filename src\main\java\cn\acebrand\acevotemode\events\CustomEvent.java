package cn.acebrand.acevotemode.events;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.entity.Player;

/**
 * 自定义事件接口
 * 所有自定义事件都需要实现这个接口
 */
public interface CustomEvent {
    
    /**
     * 执行事件
     * @param plugin 插件实例
     * @param arena 竞技场
     * @param eventConfig 事件配置参数
     */
    void execute(AceVoteMode plugin, Arena arena, EventConfig eventConfig);
    
    /**
     * 获取事件类型ID
     * @return 事件类型ID
     */
    String getEventType();
    
    /**
     * 获取事件名称
     * @return 事件名称
     */
    String getEventName();
    
    /**
     * 向竞技场中的所有玩家发送消息
     * @param arena 竞技场
     * @param message 消息内容
     */
    default void broadcastMessage(Arena arena, String message) {
        if (message == null || message.isEmpty()) {
            return;
        }
        
        String coloredMessage = org.bukkit.ChatColor.translateAlternateColorCodes('&', message);
        for (Player player : arena.getPlayers()) {
            if (player.isOnline()) {
                player.sendMessage(coloredMessage);
            }
        }
    }
    
    /**
     * 向竞技场中的所有玩家播放音效
     * @param arena 竞技场
     * @param sound 音效
     * @param volume 音量
     * @param pitch 音调
     */
    default void playSound(Arena arena, org.bukkit.Sound sound, float volume, float pitch) {
        for (Player player : arena.getPlayers()) {
            if (player.isOnline()) {
                try {
                    player.playSound(player.getLocation(), sound, volume, pitch);
                } catch (Exception e) {
                    // 忽略音效错误
                }
            }
        }
    }
}
