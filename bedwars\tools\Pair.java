package de.marcely.bedwars.tools;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.jetbrains.annotations.Nullable;

/**
 * A convenience class to represent name-value pairs.
 *
 *
 * @param <K> Key type
 * @param <V> Value type
 */
@EqualsAndHashCode
@ToString
public class Pair<K, V> {

  private final K key;
  private final V value;

  public Pair(@Nullable K key, @Nullable V value) {
    this.key = key;
    this.value = value;
  }

  /**
   * Gets the key for this pair.
   *
   * @return The key of this pair
   */
  public @Nullable K getKey() {
    return this.key;
  }

  /**
   * Gets the value for this pair.
   *
   * @return The value of this pair
   */
  public @Nullable V getValue() {
    return this.value;
  }
}
