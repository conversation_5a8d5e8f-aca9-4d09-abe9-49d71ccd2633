package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.inventory.ItemStack;

/**
 * Gets called whenever a spawner drops something.
 * <p>
 * This event is getting called very frequently, so make sure that anything that you're doing isn't causing a huge CPU usage.
 */
public class SpawnerDropEvent extends Event implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Spawner spawner;
  private ItemStack[] droppingMaterials;

  private boolean cancel;

  public SpawnerDropEvent(Spawner spawner, ItemStack[] droppingMaterials) {
    this.spawner = spawner;
    this.droppingMaterials = droppingMaterials;
  }

  /**
   * Returns the spawner that's about to drop something.
   *
   * @return The spawner involved in this event
   */
  public Spawner getSpawner() {
    return this.spawner;
  }

  /**
   * Returns all the DropTypes a spawner should drop.
   *
   * @return An array of all the ItemStacks the spawner will drop
   */
  public ItemStack[] getDroppingMaterials() {
    return droppingMaterials;
  }

  /**
   * Change the materials that the spawner should drop
   *
   * @param droppingMaterials The materials the spawner will drop
   */
  public void setDroppingMaterials(ItemStack[] droppingMaterials) {
    Validate.notNullDeep(droppingMaterials, "droppingMaterials");

    this.droppingMaterials = droppingMaterials;
  }

  @Override
  public Arena getArena() {
    return this.spawner.getArena();
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public void setCancelled(boolean cancel) {
    this.cancel = cancel;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}