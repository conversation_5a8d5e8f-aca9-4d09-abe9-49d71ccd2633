package de.marcely.bedwars.api.arena.picker.condition.variable;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.tools.Validate;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * Represents either a dynamic variable or a static value.
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ArenaConditionInput {

  private ArenaConditionVariable<?> variable;
  private ArenaConditionVariableValue<?> value;

  /**
   * Fetches the value of a local arena.
   *
   * @param arena The local arena
   * @return The given property of this current moment
   */
  public ArenaConditionVariableValue<?> getValue(Arena arena) {
    if (this.variable != null)
      return this.variable.getValue(arena);

    return this.value;
  }

  /**
   * Fetches the value of a remote arena.
   *
   * @param arena The remote arena
   * @return The given property of this current moment
   */
  public ArenaConditionVariableValue<?> getValue(RemoteArena arena) {
    if (this.variable != null)
      return this.variable.getValue(arena);

    return this.value;
  }

  /**
   * Creates a new instance of this class with a variable.
   *
   * @param variable The variable
   * @return The new instance
   */
  public static ArenaConditionInput of(ArenaConditionVariable<?> variable) {
    Validate.notNull(variable, "variable");

    return new ArenaConditionInput(variable, null);
  }

  /**
   * Creates a new instance of this class with a value.
   *
   * @param value The value
   * @return The new instance
   */
  public static ArenaConditionInput of(ArenaConditionVariableValue<?> value) {
    Validate.notNull(value, "value");

    return new ArenaConditionInput(null, value);
  }

  @Override
  public String toString() {
    if (this.variable != null)
      return this.variable.getName();
    else
      return this.value.toString();
  }
}
