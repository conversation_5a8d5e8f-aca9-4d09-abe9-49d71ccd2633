package de.marcely.bedwars.api.exception;

import java.io.IOException;

/**
 * Thrown when an arena condition could not be parsed.
 *
 * @see de.marcely.bedwars.api.arena.picker.ArenaPickerAPI#parseCondition(String)
 */
public class ArenaConditionParseException extends IOException {

  private final int position;

  public ArenaConditionParseException(String message) {
    this(message, -1);
  }

  public ArenaConditionParseException(String message, int position) {
    super(message);

    this.position = position;
  }

  /**
   * Get the char position where the exception occurred.
   * <p>
   *   It may return -1 if the position is unknown.
   * </p>
   *
   * @return The char position of the original string with the error. May be -1
   */
  public int getPosition() {
    return this.position;
  }
}
