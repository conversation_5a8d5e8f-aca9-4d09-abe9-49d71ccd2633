package de.marcely.bedwars.api.game.spectator;

import de.marcely.bedwars.api.GameAPI;
import org.bukkit.plugin.Plugin;

/**
 * Handles the execution of a {@link SpectatorItem}.
 * <P>
 * Make sure to register it with {@link GameAPI#registerSpectatorItemHandler(SpectatorItemHandler)}.
 */
public abstract class SpectatorItemHandler {

  private final String id;
  private final Plugin plugin;

  /**
   * @param id The id of this handler, should be unique to others. Default ones start with bedwars:
   * @param plugin The plugin that contructs this handler
   */
  public SpectatorItemHandler(String id, Plugin plugin) {
    this.id = id;
    this.plugin = plugin;
  }

  /**
   * Handles the execution of the item.
   *
   * @param spectator The spectator who used the item
   * @param item The item that has been used
   */
  public abstract void handleUse(Spectator spectator, SpectatorItem item);

  /**
   * Whether or not the item shall be shown and usable at the given circumstances.
   *
   * @param spectator The spectator to whom the item shall be added to
   * @param item The item to which this handler was added to
   * @return <code>true</code> if it shall be visible in this session
   */
  public abstract boolean isVisible(Spectator spectator, SpectatorItem item);

  /**
   * Returns the id of this handler. This one should be unique to others.
   * <p>
   * It's being used by {@link SpectatorItem} to allow users to configure which exact handler they want to use for their item.
   *
   * @return The id of this handler
   */
  public final String getId() {
    return this.id;
  }

  /**
   * Returns the plugin that constructed this handler.
   *
   * @return The plugin behind this handler
   */
  public final Plugin getPlugin() {
    return this.plugin;
  }

  /**
   * Returns the type of this handler.
   * Custom ones should return {@link SpectatorItemHandlerType#PLUGIN}.
   *
   * @return The type of this handler
   */
  public SpectatorItemHandlerType getType() {
    return SpectatorItemHandlerType.PLUGIN;
  }

  /**
   * Returns whether or not this handler is registered.
   * <p>
   * Use {@link GameAPI#registerSpectatorItemHandler(SpectatorItemHandler)} to register it,
   * or {@link GameAPI#unregisterSpectatorItemHandler(SpectatorItemHandler)} to unregister it.
   *
   * @return <code>true</code> if it's registered
   */
  public final boolean isRegistered() {
    final SpectatorItemHandler handler = GameAPI.get().getSpectatorItemHandler(getId());

    return handler == this;
  }
}