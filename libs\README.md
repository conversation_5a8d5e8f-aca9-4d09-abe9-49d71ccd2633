# 本地依赖库目录

## 📁 目录说明

此目录用于存放项目的本地依赖库文件。

## 📋 所需文件

请将以下文件放置到此目录中：

### MythicMobs-5.7.2.jar
- **文件名**: `MythicMobs-5.7.2.jar`
- **版本**: 5.7.2
- **用途**: 恐怖降临模式的Boss系统
- **下载地址**: [MythicMobs官网](https://www.mythiccraft.io/)

## 🔧 配置说明

项目的 `pom.xml` 已配置为使用本地依赖：

```xml
<dependency>
    <groupId>io.lumine</groupId>
    <artifactId>MythicMobs</artifactId>
    <version>5.7.2</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/libs/MythicMobs-5.7.2.jar</systemPath>
</dependency>
```

## 📂 目录结构

```
libs/
├── README.md                 # 此说明文件
└── MythicMobs-5.7.2.jar     # MythicMobs依赖文件（需要手动添加）
```

## ⚠️ 重要提示

1. **文件名必须准确**: 确保文件名为 `MythicMobs-5.7.2.jar`
2. **版本匹配**: 确保使用的是5.7.2版本
3. **路径正确**: 文件必须直接放在 `libs` 目录下
4. **编译前检查**: 编译前确保文件已正确放置

## 🚀 使用步骤

1. 下载 MythicMobs-5.7.2.jar 文件
2. 将文件复制到此 `libs` 目录
3. 确认文件名为 `MythicMobs-5.7.2.jar`
4. 运行 `mvn clean compile` 编译项目

## 🔍 故障排除

### 编译错误：找不到依赖
- 检查文件是否存在于 `libs` 目录
- 检查文件名是否正确
- 检查文件是否损坏

### 运行时错误：类找不到
- 确保服务器已安装 MythicMobs 插件
- 确保 MythicMobs 版本兼容

## 📝 版本兼容性

| MythicMobs版本 | 支持状态 | 说明 |
|---------------|---------|------|
| 5.7.2 | ✅ 推荐 | 完全支持所有功能 |
| 5.6.x | ⚠️ 部分支持 | 可能缺少某些新特性 |
| 5.5.x | ❌ 不支持 | API变化较大 |

## 🔗 相关链接

- [MythicMobs官网](https://www.mythiccraft.io/)
- [MythicMobs文档](https://git.lumine.io/mythiccraft/MythicMobs/-/wikis/home)
- [MythicMobs API文档](https://www.mythiccraft.io/javadocs/mythic/)

## 📞 技术支持

如果遇到依赖相关问题，请检查：
1. 文件是否正确放置
2. 文件名是否匹配
3. 版本是否兼容
4. 服务器是否安装了对应插件
