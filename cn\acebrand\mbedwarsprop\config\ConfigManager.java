package cn.acebrand.mbedwarsprop.config;

import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class ConfigManager {

    private final Plugin plugin;
    private FileConfiguration config;
    private final Map<String, ItemConfig> itemConfigs = new HashMap<>();
    private boolean debug;
    private boolean autoAddToShop;

    public ConfigManager(Plugin plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    public void loadConfig() {
        // 确保配置文件存在
        plugin.saveDefaultConfig();
        
        // 重新加载配置
        plugin.reloadConfig();
        config = plugin.getConfig();
        
        // 加载全局设置
        debug = config.getBoolean("settings.debug", false);
        autoAddToShop = config.getBoolean("settings.auto-add-to-shop", true);
        
        // 加载道具配置
        loadItems();
        
        if (debug) {
            plugin.getLogger().info("配置文件已加载");
            plugin.getLogger().info("已加载 " + itemConfigs.size() + " 个道具配置");
        }
    }

    private void loadItems() {
        itemConfigs.clear();
        
        ConfigurationSection itemsSection = config.getConfigurationSection("items");
        if (itemsSection == null) {
            plugin.getLogger().warning("配置文件中没有找到道具配置部分");
            return;
        }
        
        for (String itemKey : itemsSection.getKeys(false)) {
            ConfigurationSection itemSection = itemsSection.getConfigurationSection(itemKey);
            if (itemSection == null) continue;
            
            // 检查道具是否启用
            if (!itemSection.getBoolean("enabled", true)) {
                if (debug) {
                    plugin.getLogger().info("道具 " + itemKey + " 已禁用，跳过加载");
                }
                continue;
            }
            
            try {
                // 基本信息
                String name = itemSection.getString("name", "&c" + itemKey);
                List<String> lore = itemSection.getStringList("lore");
                Material material = Material.valueOf(itemSection.getString("material", "STONE").toUpperCase());
                
                // 商店设置
                ConfigurationSection shopSection = itemSection.getConfigurationSection("shop");
                String priceType = "IRON";
                int priceAmount = 16;
                boolean keepOnDeath = false;
                boolean oneTimePurchase = false;
                
                if (shopSection != null) {
                    priceType = shopSection.getString("price-type", "IRON").toUpperCase();
                    priceAmount = shopSection.getInt("price-amount", 16);
                    keepOnDeath = shopSection.getBoolean("keep-on-death", false);
                    oneTimePurchase = shopSection.getBoolean("one-time-purchase", false);
                }
                
                // 效果设置
                ConfigurationSection effectsSection = itemSection.getConfigurationSection("effects");
                Map<String, EffectConfig> effects = new HashMap<>();
                
                if (effectsSection != null) {
                    for (String effectKey : effectsSection.getKeys(false)) {
                        if (effectKey.endsWith("-level") || effectKey.endsWith("-duration")) {
                            continue;
                        }
                        
                        String baseKey = effectKey.split("-")[0];
                        int level = effectsSection.getInt(baseKey + "-level", 0);
                        int duration = effectsSection.getInt(baseKey + "-duration", 5);
                        
                        effects.put(baseKey, new EffectConfig(level, duration));
                    }
                }
                
                // 创建道具配置
                ItemConfig itemConfig = new ItemConfig(
                        itemKey,
                        name,
                        lore,
                        material,
                        priceType,
                        priceAmount,
                        keepOnDeath,
                        oneTimePurchase,
                        effects
                );
                
                itemConfigs.put(itemKey, itemConfig);
                
                if (debug) {
                    plugin.getLogger().info("已加载道具配置: " + itemKey);
                }
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "加载道具 " + itemKey + " 配置时出错", e);
            }
        }
    }

    public ItemConfig getItemConfig(String itemKey) {
        return itemConfigs.get(itemKey);
    }

    public boolean isDebug() {
        return debug;
    }

    public boolean isAutoAddToShop() {
        return autoAddToShop;
    }

    public static class ItemConfig {
        private final String id;
        private final String name;
        private final List<String> lore;
        private final Material material;
        private final String priceType;
        private final int priceAmount;
        private final boolean keepOnDeath;
        private final boolean oneTimePurchase;
        private final Map<String, EffectConfig> effects;

        public ItemConfig(String id, String name, List<String> lore, Material material,
                          String priceType, int priceAmount, boolean keepOnDeath,
                          boolean oneTimePurchase, Map<String, EffectConfig> effects) {
            this.id = id;
            this.name = name;
            this.lore = lore;
            this.material = material;
            this.priceType = priceType;
            this.priceAmount = priceAmount;
            this.keepOnDeath = keepOnDeath;
            this.oneTimePurchase = oneTimePurchase;
            this.effects = effects;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public List<String> getLore() {
            return lore;
        }

        public Material getMaterial() {
            return material;
        }

        public String getPriceType() {
            return priceType;
        }

        public int getPriceAmount() {
            return priceAmount;
        }

        public boolean isKeepOnDeath() {
            return keepOnDeath;
        }

        public boolean isOneTimePurchase() {
            return oneTimePurchase;
        }

        public Map<String, EffectConfig> getEffects() {
            return effects;
        }

        public EffectConfig getEffect(String effectName) {
            return effects.get(effectName);
        }
    }

    public static class EffectConfig {
        private final int level;
        private final int duration;

        public EffectConfig(int level, int duration) {
            this.level = level;
            this.duration = duration;
        }

        public int getLevel() {
            return level;
        }

        public int getDuration() {
            return duration;
        }
    }
}
