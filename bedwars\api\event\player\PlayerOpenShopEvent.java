package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.api.game.shop.ShopOpenCause;
import de.marcely.bedwars.api.game.shop.ShopPage;
import de.marcely.bedwars.api.game.shop.layout.ShopLayout;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * Gets called when the player is opening the shop or when he's clicking on a page in the GUI
 */
public class PlayerOpenShopEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final ShopOpenCause cause;

  private final Arena arena;
  private ShopLayout layout;
  private ShopPage page;
  private Object layoutData;
  private boolean cancel = false;

  public PlayerOpenShopEvent(
      Player player,
      @Nullable Arena arena,
      ShopLayout layout,
      ShopOpenCause cause,
      @Nullable ShopPage page,
      @Nullable Object layoutData) {

    super(player);

    this.arena = arena;
    this.layout = layout;
    this.cause = cause;
    this.page = page;
    this.layoutData = layoutData;

    Validate.isTrue(page == null || page.isClone(), "Page is not cloned");
  }

  /**
   * Returns the arena (of the player) in which the shop was opened.
   * <p>
   *     May be <code>null</code> if the player isn't actually a part of a match as he e.g. used
   *     <code>/bw tools</code> to open and debug it.
   * </p>
   *
   * @return The arena in which the shop was opened
   */
  @Nullable
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the layout that will be used for the GUI.
   *
   * @return The layout that shall be shown
   */
  public ShopLayout getLayout() {
    return this.layout;
  }

  /**
   * Set the layout of the shop GUI that shall be shown.
   *
   * @param layout The layout that shall be shown to the player
   */
  public void setLayout(ShopLayout layout) {
    Validate.notNull(layout, "layout");

    this.layout = layout;
  }

  /**
   * Returns the cloned page that was opened.
   * <p>
   *  Is may returns <code>null</code> when the shop was opened the first time.
   *  In this case, it will likely open the main page (what exactly happens depends on the layout).
   * </p>
   * <p>
   *     Note that this the page will always be cloned, that is because to give the layout more freedom in
   *     designing the GUIs without affecting the original states.
   *     You may use {@link ShopPage#getOriginal()} to get the non-cloned page.
   * </p>
   *
   * @return The page that shall be shown
   */
  @Nullable
  public ShopPage getClonedPage() {
    return this.page;
  }

  /**
   * Sets the page that shall be opened.
   * <p>
   *  Is may be <code>null</code> when the shop was opened the first time.
   *  In this case, it will likely open the main page (what exactly happens depends on the layout).
   * </p>
   * <p>
   *     This method also automatically clones the page if it hasn't been already.
   * </p>
   *
   * @param page The new page
   */
  public void setPage(@Nullable ShopPage page) {
    if (page != null)
      page = page.clone();

    this.page = page;
  }

  /**
   * Gets all the items that the player would theoretically see in the GUI.
   * <p>
   *     May be <code>null</code> if {@link #getClonedPage()} returns null as well.
   * </p>
   *
   * @return The items that the player would see in the GUI
   */
  @Nullable
  public List<? extends ShopItem> getItems() {
    if (this.page == null)
      return null;

    if (this.arena == null)
      return this.page.getItems();

    return this.page.getVisiblePageItems(this.arena, this.player);
  }

  /**
   * Tries to remove a shop item and causes it to not be seen.
   *
   * @param shopItem The shopItem that shall be removed
   * @return <code>true</code> if it has been removed
   * @throws IllegalStateException If the shopItem is not cloned
   */
  public boolean removeShopItem(ShopItem shopItem) {
    Validate.notNull(shopItem, "shopItem");
    Validate.isTrue(shopItem.isClone(), "shopItem is not cloned. This is rather a warning as no shopItems will be removed. You might want to use ShopPage#removeConnectedShopItems");

    return this.page.removeItem(shopItem);
  }

  /**
   * Tries to add a shopItem.
   *
   * @param shopItem The cloned shopItem that shall be added
   * @return <code>true</code> if it has been successfully added
   * @throws IllegalStateException If the shopItem is not cloned
   */
  public boolean addShopItem(ShopItem shopItem) {
    if (this.page == null)
      return false;

    return this.page.addItem(shopItem);
  }

  /**
   * A layout may hold and pass around data during its session.
   * <p>
   * This might be useful when you're trying to manipulate the layout.
   *
   * @return The layout specific data
   */
  @Nullable
  public Object getLayoutData() {
    return this.layoutData;
  }

  /**
   * A layout may hold and pass around data during its session.
   * <p>
   * This might be useful when you're trying to manipulate the layout.
   * Keep in mind that errors might occur with the layout when inserting unexpected data.
   *
   * @param layoutData The new layout data
   */
  public void setLayoutData(@Nullable Object layoutData) {
    this.layoutData = layoutData;
  }

  /**
   * Returns the way how the player opened the shop
   *
   * @return The cause of the opening
   */
  public ShopOpenCause getCause() {
    return this.cause;
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public void setCancelled(boolean bool) {
    this.cancel = bool;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
