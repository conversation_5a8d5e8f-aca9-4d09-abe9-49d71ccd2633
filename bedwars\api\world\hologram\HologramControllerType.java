package de.marcely.bedwars.api.world.hologram;

import java.util.Locale;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the type of the {@link HologramController}
 */
public enum HologramControllerType {

  /**
   * Opens a shop when interacted to it
   */
  DEALER,

  /**
   * Open an upgrade-shop when interacted to it
   */
  UPGRADE_DEALER,

  /**
   * Used during lobby phase. Change your current team to another one by clicking on it
   */
  TEAM_SELECTOR,

  /**
   * Displays a players stats on a hologram
   */
  STATS_HOLO,

  /**
   * Opens an ArenasGUI when clicked on it
   */
  ARENASGUI_OPENER,

  /**
   * A ranking statue that displays a player info of a certain rank
   */
  RANKING_STATUE,

  /**
   * The default type when spawning a new hologram. Does literally nothing by itself
   */
  DEAD;

  /**
   * Returns the id that's internally being used.
   *
   * @return The id of this type
   */
  public String getInternalId() {
    return name().toLowerCase(Locale.ENGLISH);
  }

  /**
   * Tries to locate a type given by its {@link #getInternalId()}.
   * <p>
   *   This method is case-insensitive.
   * </p>
   *
   * @param internalId The id of the type
   * @return The type whose id is equal to the given one. <code>null</code> when there's none
   */
  @Nullable
  public static HologramControllerType fromInternalId(String internalId) {
    try {
      return HologramControllerType.valueOf(internalId.toUpperCase(Locale.ENGLISH));
    } catch (IllegalArgumentException e) {
      return null;
    }
  }
}
