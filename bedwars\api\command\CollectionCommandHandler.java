package de.marcely.bedwars.api.command;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.event.CommandExecuteEvent;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * The default handler of a {@link CommandsCollection}
 */
public class CollectionCommandHandler implements CommandHandlerWrappedSender {

  private CommandsCollection collection;

  @Override
  public Plugin getPlugin() {
    return BedwarsAPI.getPlugin();
  }

  @Override
  public void onRegister(SubCommand collection) {
    this.collection = (CommandsCollection) collection;
  }

  @Override
  public void onFire(CommandSenderWrapper sender, String fullUsage, String[] args) {
    Integer page = 1;

    // execute command
    if (args.length >= 1) {
      page = BedwarsAPI.getHelper().parseInt(args[0]);

      // executes a command
      if (page == null) {
        final SubCommand cmd = this.collection.getCommand(args[0]);

        // no command with that name
        if (cmd == null) {
          Message.buildByKey("Unknown_Argument").placeholder("arg", args[0]).send(sender);
          return;
        }

        // no permission
        if (cmd.getPermission() != null && !sender.hasPermission(cmd.getPermission())) {
          Message.buildByKey("Unknown_Argument").placeholder("arg", args[0]).send(sender);
          return;
        }

        // only as player
        if (cmd.isOnlyForPlayers() && !sender.isBukkitPlayer() && (!(cmd.getHandler() instanceof CommandHandlerWrappedSender) || !sender.isRemotePlayer())) {
          Message.buildByKey("OnlyAs_Player").send(sender);
          return;
        }

        final String[] fArgs = Arrays.copyOfRange(args, 1, args.length);

        // event
        if (sender.getCommandSender() != null) {
          final CommandExecuteEvent event = new CommandExecuteEvent(sender.getCommandSender(), cmd, args[0], fArgs);

          if (event.isCancelled())
            return;
        }

        // fire
        final String usage = cmd.getUsage(sender);
        final String newFullUsage = cmd.getFullName(getBukkitLabel(fullUsage)) + (usage.isEmpty() ? "" : " " + usage);

        if (cmd.getHandler() instanceof CommandHandlerWrappedSender) {
          ((CommandHandlerWrappedSender) cmd.getHandler()).onFire(
              sender,
              newFullUsage,
              fArgs);

        } else {
          if (sender.getCommandSender() == null) {
            Message.buildByKey("RemoteArena_UnsupportedOperation").send(sender);
            return;
          }

          cmd.getHandler().onFire(
              sender.getCommandSender(),
              newFullUsage,
              fArgs);
        }

        return;
      }
    }

    // show help
    {
      final CommandHandler helpHandler = this.collection.getHelpCommand();

      if (helpHandler instanceof CommandHandlerWrappedSender)
        ((CommandHandlerWrappedSender) this.collection.getHelpCommand()).onFire(sender, fullUsage, new String[]{"" + page});
      else {
        if (sender.getCommandSender() == null) {
          Message.buildByKey("RemoteArena_UnsupportedOperation").send(sender);
          return;
        }

        this.collection.getHelpCommand().onFire(sender.getCommandSender(), fullUsage, new String[]{"" + page});
      }
    }
  }

  @Override
  public @Nullable List<String> onAutocomplete(CommandSenderWrapper sender, String[] args) {
    final Predicate<SubCommand> initialFilter = cmd -> {
      if (!cmd.isVisible())
        return false;

      if (cmd.isOnlyForPlayers() && !sender.isBukkitPlayer() && (!(cmd.getHandler() instanceof CommandHandlerWrappedSender) || !sender.isRemotePlayer()))
        return false;

      return cmd.getPermission() == null || sender.hasPermission(cmd.getPermission());
    };

    if (args.length == 0) {
      return this.collection.getCommands().stream()
          .filter(initialFilter)
          .map(cmd -> cmd.getName())
          .collect(Collectors.toList());

    } else if (args.length == 1) {
      final String cmdName = args[0].toLowerCase();

      return this.collection.getCommands().stream()
          .filter(cmd -> {
            return cmd.getName().startsWith(cmdName) && initialFilter.test(cmd);
          })
          .map(cmd -> cmd.getName())
          .collect(Collectors.toList());

    } else {
      final SubCommand cmd = this.collection.getCommand(args[0], true);

      if (cmd == null)
        return Collections.emptyList();

      if (cmd.getHandler() instanceof CommandHandlerWrappedSender)
        return ((CommandHandlerWrappedSender) cmd.getHandler()).onAutocomplete(sender, Arrays.copyOfRange(args, 1, args.length));
      else {
        if (sender.getCommandSender() == null)
          return Collections.emptyList();

        return cmd.getHandler().onAutocomplete(sender.getCommandSender(), Arrays.copyOfRange(args, 1, args.length));
      }
    }
  }
}
