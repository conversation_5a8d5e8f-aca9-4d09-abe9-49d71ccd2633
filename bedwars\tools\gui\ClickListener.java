package de.marcely.bedwars.tools.gui;

import de.marcely.bedwars.tools.Validate;
import org.bukkit.entity.Player;

/**
 * Listens for clicks on items
 */
public interface ClickListener {

  /**
   * Gets called whenever a player clicked on an item in the GUI
   *
   * @param player The player who clicked
   * @param leftClick If it was a left or right click
   * @param shiftClick If he pressed shift while clicking on it
   */
  void onClick(Player player, boolean leftClick, boolean shiftClick);

  /**
   * Gets called when a player clicks on a number on his keyboard while hovering over item.
   *
   * @param player The player who clicked
   * @param number The number clicked on the keyboard. Ranges from 1 to 9
   */
  default void onNumClick(Player player, int number) { }


  /**
   * An implementation of ClickListener that does effectively nothing
   */
  class Silent implements ClickListener {

    public static ClickListener INSTANCE = new Silent();

    private Silent() {
    }

    @Override
    public final void onClick(Player player, boolean leftClick, boolean shiftClick) {
    }
  }


  /**
   * Wraps an existing ClickListener instance, where you may optionally override its listener methods.
   */
  class Proxy implements ClickListener {

    private final ClickListener listener;

    public Proxy(ClickListener listener) {
      Validate.notNull(listener, "listener");

      this.listener = listener;
    }

    @Override
    public void onClick(Player player, boolean leftClick, boolean shiftClick) {
      this.listener.onClick(player, leftClick, shiftClick);
    }

    @Override
    public void onNumClick(Player player, int number) {
      this.listener.onNumClick(player, number);
    }
  }
}
