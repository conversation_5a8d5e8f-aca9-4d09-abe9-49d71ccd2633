package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.io.File;
import java.util.List;
import java.util.Random;
import java.util.Set;

/**
 * 负面BUFF事件
 * 给玩家随机负面状态效果
 */
public class NegativeBuffEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    
    public NegativeBuffEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(badDir, "negative_buff.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/negative_buff.yml", false);
                plugin.getLogger().info("已生成负面BUFF事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载负面BUFF事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载负面BUFF事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public String getName() {
        return "NEGATIVE_BUFF";
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 40) : 40;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }
        
        // 选择随机负面BUFF
        String selectedBuff = selectRandomBuff();
        if (selectedBuff == null) {
            plugin.getLogger().warning("无法选择负面BUFF");
            return;
        }
        
        // 应用负面BUFF
        applyNegativeBuff(player, selectedBuff);
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了负面BUFF事件: " + selectedBuff);
    }
    
    /**
     * 选择随机负面BUFF
     */
    private String selectRandomBuff() {
        if (config == null) return null;
        
        ConfigurationSection buffsSection = config.getConfigurationSection("negative_buffs");
        if (buffsSection == null) return null;
        
        // 计算总权重
        int totalWeight = 0;
        Set<String> buffNames = buffsSection.getKeys(false);
        for (String buffName : buffNames) {
            totalWeight += buffsSection.getInt(buffName + ".weight", 1);
        }
        
        // 随机选择
        int roll = random.nextInt(totalWeight);
        int current = 0;
        
        for (String buffName : buffNames) {
            current += buffsSection.getInt(buffName + ".weight", 1);
            if (roll < current) {
                return buffName;
            }
        }
        
        return buffNames.iterator().next(); // 回退选择
    }
    
    /**
     * 应用负面BUFF
     */
    private void applyNegativeBuff(Player player, String buffName) {
        if (config == null) return;
        
        ConfigurationSection buffSection = config.getConfigurationSection("negative_buffs." + buffName);
        if (buffSection == null) return;
        
        // 发送BUFF特定消息
        String buffMessage = buffSection.getString("message", "");
        if (!buffMessage.isEmpty()) {
            player.sendMessage(buffMessage);
        }
        
        // 检查是否是多效果BUFF
        if (buffSection.contains("effects")) {
            // 多效果BUFF（如失明+缓慢）
            List<?> effectsList = buffSection.getList("effects");
            if (effectsList != null) {
                for (Object effectObj : effectsList) {
                    if (effectObj instanceof ConfigurationSection) {
                        ConfigurationSection effectConfig = (ConfigurationSection) effectObj;
                        applyPotionEffect(player, effectConfig);
                    }
                }
            }
        } else {
            // 单效果BUFF
            applyPotionEffect(player, buffSection);
        }
    }
    
    /**
     * 应用药水效果
     */
    private void applyPotionEffect(Player player, ConfigurationSection effectConfig) {
        String effectName = effectConfig.getString("effect", "SPEED");
        int level = effectConfig.getInt("level", 1);
        int duration = effectConfig.getInt("duration", 200);
        
        try {
            // 获取药水效果类型
            PotionEffectType effectType = getPotionEffectType(effectName);
            if (effectType != null) {
                // 应用药水效果
                PotionEffect effect = new PotionEffect(effectType, duration, level - 1);
                player.addPotionEffect(effect);
                
                plugin.getLogger().info("对玩家 " + player.getName() + " 应用负面效果: " + 
                                       effectName + " " + level + " (" + duration + "刻)");
            } else {
                plugin.getLogger().warning("无效的药水效果类型: " + effectName);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("应用负面BUFF失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据名称获取药水效果类型
     */
    private PotionEffectType getPotionEffectType(String effectName) {
        switch (effectName.toUpperCase()) {
            case "BLINDNESS":
                return PotionEffectType.BLINDNESS;
            case "SLOW":
            case "SLOWNESS":
                return PotionEffectType.SLOW;
            case "POISON":
                return PotionEffectType.POISON;
            case "WEAKNESS":
                return PotionEffectType.WEAKNESS;
            case "SLOW_DIGGING":
            case "MINING_FATIGUE":
                return PotionEffectType.SLOW_DIGGING;
            case "NAUSEA":
            case "CONFUSION":
                return PotionEffectType.CONFUSION;
            case "WITHER":
                return PotionEffectType.WITHER;
            case "HUNGER":
                return PotionEffectType.HUNGER;
            default:
                // 尝试直接获取
                try {
                    return PotionEffectType.getByName(effectName);
                } catch (Exception e) {
                    plugin.getLogger().warning("未知的药水效果类型: " + effectName);
                    return null;
                }
        }
    }
    
    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null) return true;
        return config.getBoolean("messages.send_message", true);
    }
    
    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null) return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }
    
    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null) return "§c你感到身体不适...";
        return config.getString("messages.event_message", "§c你感到身体不适...");
    }
}
