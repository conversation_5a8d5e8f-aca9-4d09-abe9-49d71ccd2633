package de.marcely.bedwars.api.remote;

import de.marcely.bedwars.api.arena.AddPlayerIssue;
import de.marcely.bedwars.tools.Pair;
import de.marcely.bedwars.tools.Validate;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * Represents the result of what happened to the player that tried to enter an arena.
 */
public final class RemotePlayerAddResult {

  private final GeneralResult result;
  private Map<UUID, Pair<PlayerResult, AddPlayerIssue>> players;

  public RemotePlayerAddResult(GeneralResult result, Map<UUID, Pair<PlayerResult, AddPlayerIssue>> players) {
    Validate.notNull(result, "result");
    Validate.notNull(players, "players");

    this.result = result;
    this.players = players;
  }

  public RemotePlayerAddResult(Map<UUID, Pair<PlayerResult, AddPlayerIssue>> players) {
    Validate.notNull(players, "players");

    this.players = players;

    // count issues
    {
      int issuesCount = 0;

      for (Pair<PlayerResult, AddPlayerIssue> p : players.values()) {
        if (p.getKey() != PlayerResult.SUCCESS && p.getKey() != PlayerResult.SUCCESS_SPECTATE)
          issuesCount++;
      }

      if (issuesCount == 0)
        this.result = GeneralResult.SUCCESS;
      else if (issuesCount == players.size())
        this.result = GeneralResult.ALL_HAVE_FAILED;
      else
        this.result = GeneralResult.SOME_HAVE_FAILED;
    }

    this.players = Collections.unmodifiableMap(this.players);
  }

  public RemotePlayerAddResult(AddRemotePlayerInfo info, @Nullable AddPlayerIssue issue) {
    Validate.notNull(info, "info");

    this.players = new HashMap<>();

    for (RemotePlayer player : info.getPlayers()) {
      if (issue != null)
        this.players.put(player.getUniqueId(), new Pair<>(PlayerResult.HAS_ISSUE, issue));
      else

        this.players.put(player.getUniqueId(), new Pair<>(PlayerResult.SUCCESS, null));
    }

    if (issue == null)
      this.result = GeneralResult.SUCCESS;
    else
      this.result = GeneralResult.ALL_HAVE_FAILED;

    this.players = Collections.unmodifiableMap(this.players);
  }

  public RemotePlayerAddResult(AddRemotePlayerInfo info, GeneralResult result) {
    Validate.notNull(info, "info");
    Validate.notNull(result, "result");

    this.result = result;
    this.players = new HashMap<>();

    for (RemotePlayer player : info.getPlayers()) {
      this.players.put(
          player.getUniqueId(),
          new Pair<>(result == GeneralResult.SUCCESS ? PlayerResult.SUCCESS : PlayerResult.GENERAL_ISSUE, null));
    }

    this.players = Collections.unmodifiableMap(this.players);
  }

  /**
   * Gets the unique id of the players.
   *
   * @return The uuids of the players
   */
  public Set<UUID> getPlayerUniqueIds() {
    return this.players.keySet();
  }

  /**
   * Gets the result of a specific player.
   *
   * @param uuid The uuid of the player
   * @return The result. <code>null</code> in case the player hasn't been tried to be added in the first place
   */
  @Nullable
  public PlayerResult getPlayerResult(UUID uuid) {
    return this.players.get(uuid).getKey();
  }

  /**
   * Gets the issue that possibly caused the player to not join the arena.
   *
   * @param uuid The uuid of the player
   * @return The result. <code>null</code> in case the player hasn't been tried to be added in the first place or when the cause wasn't an issue
   */
  @Nullable
  public AddPlayerIssue getPlayerIssue(UUID uuid) {
    return this.players.get(uuid).getValue();
  }

  /**
   * Checks whether the given uuid was a part of the joining action.
   *
   * @param uuid The uuid of the player
   * @return <code>true</code> in case he was
   */
  public boolean includesPlayer(UUID uuid) {
    return this.players.containsKey(uuid);
  }

  /**
   * Returns the result of the general action.
   *
   * @return The general result
   */
  public GeneralResult getGeneralResult() {
    return this.result;
  }


  /**
   * Represents the result of a specific player.
   */
  public enum PlayerResult {

    /**
     * Player successfully made it.
     */
    SUCCESS,

    /**
     * The arena was already running, and he instead joined as a spectator.
     * <p>
     *     {@link AddRemotePlayerInfo#setSpectatorFallback(boolean)} must be set to <code>true</code> for that.
     * </p>
     */
    SUCCESS_SPECTATE,

    /**
     * There was an issue that made him not join the arena.
     * <p>
     *     You may find more info with {@link RemotePlayerAddResult#getPlayerUniqueIds()}
     * </p>
     */
    HAS_ISSUE,

    /**
     * There was a generic reason why it failed.
     * <p>
     *     You may find more info with {@link RemotePlayerAddResult#getGeneralResult()}
     * </p>
     */
    GENERAL_ISSUE,

    /**
     * Player went offline.
     */
    PLAYER_OFFLINE,

    /**
     * He's already doing something else, such as getting added as a spectator.
     */
    ALREADY_MOVING,

    /**
     * We tried to make him into a spectator, but that failed.
     */
    SPECTATE_FAILED,

    /**
     * It took him too long to join. The arena is either not in the lobby state anymore.
     */
    JOINED_TOO_LATE,

    /**
     * We tried to contact the server, but we haven't received any info. Or the player didn't get moved into the server.
     */
    TIMEOUT,

    /**
     * We don't know why exactly it failed.
     */
    UNKNOWN
  }

  /**
   * Represents a general result of what happened to all the players.
   */
  public enum GeneralResult {

    /**
     * Everyone successfully entered.
     */
    SUCCESS,

    /**
     * Some have failed.
     */
    SOME_HAVE_FAILED,

    /**
     * Everyone has failed.
     */
    ALL_HAVE_FAILED,

    /**
     * The arena has been removed during the process.
     */
    ARENA_NOT_EXISTING_ANYMORE,

    /**
     * The arena is not in the lobby state anymore.
     */
    ARENA_NOT_LOBBY,

    /**
     * A plugin cancelled the process.
     */
    PLUGIN_EVENT_CANCELLED,

    /**
     * We tried to contact the server, but we haven't received any info.
     */
    TIMEOUT,

    /**
     * We don't know why it exactly failed.
     */
    UNKNOWN
  }
}
