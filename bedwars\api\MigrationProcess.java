package de.marcely.bedwars.api;

import java.time.Duration;
import java.util.Collection;

/**
 * It's possible to migrate from previous sources. This class represents a process that has been manually run.
 * The process is being done on a seperate thread.
 * <p>
 *     Possibly the user for instance has been using an other Bedwars plugin and is now using this one.
 * </p>
 */
public interface MigrationProcess {

  /**
   * Returns the previously used system.
   *
   * @return The origin from which we're migrating from
   */
  Origin getOrigin();

  /**
   * You must manually run this method in order to run this.
   * You might want to add callbacks before that using {@link #addCallback(Callback)} in order to know when it's done.
   * It's not possible to run a process twice.
   *
   * @return <code>true</code> when it has been started for the first time, and it's now actually running
   */
  boolean run();

  /**
   * Forcefully terminate the running process.
   * It's possible that this doesn't occur immediately and that it is still running for a while beyond that.
   *
   * @return <code>true</code> if the process has been running and now has been cancelled.
   */
  boolean cancel();

  /**
   * Returns whether it is still running
   *
   * @return <code>true</code> if the migration is still being processed
   */
  boolean isRunning();

  /**
   * Add a callback to know then the process is done.
   * It's highly recommended doing that before running the process, otherwise you possibly face a race condition.
   *
   * @param callback The new callback you want to add
   */
  void addCallback(Callback callback);

  /**
   * Remove a previously added callback.
   *
   * @param callback The callback you don't want to be called
   * @return <code>true</code> if it has been found and removed
   */
  boolean removeCallback(Callback callback);

  /**
   * Returns all callbacks that have been added.
   * This collection is modifiable, meaning that you don't really have to use {@link #addCallback(Callback)}.
   *
   * @return All callbacks that have been added
   */
  Collection<Callback> getCallbacks();

  /**
   * The previous system that has been used from which we're migrating from.
   */
  enum Origin {

    /**
     * The "Bedwars1058" plugin
     */
    BEDWARS1058,

    /**
     * The "Bedwars Reloaded" plugin
     */
    BEDWARS_RELOADED,

    /**
     * From local storage to the current active one
     */
    LOCAL,

    /**
     * From sql storage to the current active one
     */
    SQL,

    /**
     * From mongodb storage to the current active one
     */
    MONGO_DB;

    /**
     * Whether the given type is a storage type.
     * <p>
     *     This includes {@link #LOCAL}, {@link #SQL} and {@link #MONGO_DB}.
     * </p>
     *
     * @return <code>Whether the given origin is a storage type</code>
     */
    public boolean isStorage() {
      switch (this) {
        case LOCAL:
        case SQL:
        case MONGO_DB:
          return true;
        default:
          return false;
      }
    }
  }

  /**
   * A callback gets called when the process is done.
   * You can add your own one with {@link #addCallback(Callback)}.
   * All methods are getting synchronized to Bukkit's main thread before getting called.
   */
  interface Callback {

    /**
     * Gets called when the process went sucessfully as inititally planned.
     *
     * @param duration The time it took to execute this process
     */
    void onSuccess(Duration duration);

    /**
     * Gets called when the process didn't finish due to an error or a manual cancellation
     *
     * @param duration The time it took to execute this process
     * @param errorMessage The reason it got cancelled
     */
    void onFail(Duration duration, String errorMessage);
  }
}
