package de.marcely.bedwars.api.world.hologram.skin;

import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.api.world.hologram.HologramSkin;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.function.Consumer;

/**
 * Represents a "holograph" (don't get confused by "holograms").
 * <p>
 *     This one is a holographic hologram that's able to display text.
 * </p>
 */
public interface HolographicHologramSkin extends HologramSkin {

  /**
   * Returns the lines that are currently being displayed.
   *
   * @return The lines currently shown
   */
  Collection<String> getLines();

  /**
   * Set the lines that shall be shown
   *
   * @param lines The new lines
   */
  void setLines(String... lines);

  /**
   * Set the lines that shall be shown
   *
   * @param lines The new lines
   */
  void setLines(Collection<String> lines);

  /**
   * Set the (optional) supplier if you want to display lines specific to a player.
   *
   * @param supplier The new supplier. <code>null</code> if you want to have static lines
   */
  void setPlayerSpecificLinesSupplier(@Nullable PlayerSpecificLinesSupplier supplier);

  /**
   * Returns whether a lines supplier has been set.
   *
   * @return <code>true</code> if one is set
   */
  boolean hasPlayerSpecificLinesSupplier();

  /**
   * Forcefully updates all lines provided by the lines-supplier ({@link #setPlayerSpecificLinesSupplier(PlayerSpecificLinesSupplier)}) for all players
   */
  void updatePlayerSpecificLines();

  /**
   * Forcefully updates all lines provided by the lines-supplier ({@link #setPlayerSpecificLinesSupplier(PlayerSpecificLinesSupplier)}) only for a specific player
   *
   * @param player The player who whom the params shall be updated
   * @return <code>false</code> if it failed (because he's either not seeing the hologram or there's no lines-supplier), otherwise <code>true</code>
   */
  boolean updatePlayerSpecificLines(Player player);

  /**
   * Returns the current lines supplier.
   *
   * @return The current lines supplier, <code>null</code> when there's none
   */
  @Nullable
  PlayerSpecificLinesSupplier getPlayerSpecificLinesSupplier();


  /**
   * Displays specific lines that vary to each player.
   * <p>
   *     This is for instance used by the stats hologram to display different stats to each player.
   * </p>
   */
  interface PlayerSpecificLinesSupplier {

    /**
     * Gets called when we want to know which lines shall be displayed to the player.
     *
     * @param player The according player
     * @param callback The callback to which you shall return the lines. May be async
     */
    void getLines(Player player, Consumer<List<String>> callback);

    /**
     * Creates a new supplier that auto-translates the given message for each player.
     *
     * @param message The message to display
     * @return The new supplier
     */
    static PlayerSpecificLinesSupplier of(Message message) {
      return (player, callback) -> {
        callback.accept(Arrays.asList(message.done(player, false).split("\\\\n")));
      };
    }

    /**
     * Creates a new supplier that auto-translates the given message for each player.
     *
     * @param lines The lines to display
     * @return The new supplier
     */
    static PlayerSpecificLinesSupplier of(List<Message> lines) {
      return (player, callback) -> {
        final List<String> finalLines = new ArrayList<>(lines.size());

        for (Message line : lines)
          finalLines.addAll(Arrays.asList(line.done(player, false).split("\\\\n")));

        callback.accept(finalLines);
      };
    }
  }
}
