package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.tools.StringMapSerializationHelper;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

/**
 * Basically a key/value (key and value being a String) Map that persists of various general-purpose information of a player.
 */
public interface PlayerProperties extends StringMapSerializationHelper {

  /**
   * Returns the uuid of the player behind these properties.
   *
   * @return The UUID of the player who owns the properties
   */
  UUID getPlayerUUID();

  /**
   * Will look for the property with that key.
   * <p>
   * 	Ignores whether the key is uppercase or undercase.
   * </p>
   *
   * @param key The key/id of the property
   * @return The optional value of the property
   */
  Optional<String> get(String key);

  /**
   * Returns all keys/ids of the properties that are currently stored in this object.
   *
   * @return All keys/ids set
   */
  Collection<String> getStoredKeys();

  /**
   * Will set the value of the property with that key.
   * <p>
   * 	Ignores whether the key is uppercase or undercase.
   * </p>
   * <p>
   * 	You may only use the following chars for the key: a{@literal -}z, 0{@literal -}9, :, _
   * </p>
   * <p>
   * 	It's safe to use UTF8 for the value.
   * 	Values are limited to 8192 bytes, otherwise an exception will be thrown.
   * </p>
   * <p>
   *     Keep in mind that the {@link de.marcely.bedwars.api.event.player.PlayerPropertyChangeEvent} is likely being dispatched with this.
   * </p>
   *
   * @param key The key/id of the property
   * @param value The new value
   * @throws IllegalArgumentException When key or value don't fit the format
   */
  default void set(String key, String value) {
    set(key, value, AttributeChangeCause.GENERIC);
  }

  /**
   * Will set the value of the property with that key<br>
   * Ignores whether the key is uppercase or undercase<br>
   * You may only use the following chars for the key: a{@literal -}z, 0{@literal -}9, :, _<br>
   * It's safe to use UTF8 for the value.<br>
   * Values are limited to 8192 bytes, otherwise an exception will be thrown
   *
   * @param key The key/id of the property
   * @param value The new value
   * @param cause The technical cause for setting this
   * @throws IllegalArgumentException When key or value don't fit the format
   */
  void set(String key, String value, AttributeChangeCause cause);

  /**
   * Combines {@link PlayerProperties#get(String)} with {@link PlayerProperties#set(String, String)}.
   *
   * @param key The key/id of the property
   * @param value The new value
   * @return The previous value
   * @throws IllegalArgumentException When key or value don't fit the format
   */
  default Optional<String> replace(String key, String value) {
    return replace(key, value, AttributeChangeCause.GENERIC);
  }

  /**
   * Combines {@link PlayerProperties#get(String)} with {@link PlayerProperties#set(String, String)}.
   *
   * @param key The key/id of the property
   * @param value The new valuevalue
   * @param cause The technical cause for setting this
   * @return The previous value
   * @throws IllegalArgumentException When key or value don't fit the format
   */
  Optional<String> replace(String key, String value, AttributeChangeCause cause);

  /**
   * Removes the property from the internal storage.
   * <p>
   *     Keep in mind that the {@link de.marcely.bedwars.api.event.player.PlayerPropertyChangeEvent} is likely being dispatched with this.
   * </p>
   *
   * @param key The key/id of the property
   * @return The value of the removed property
   */
  default Optional<String> remove(String key) {
    return remove(key, AttributeChangeCause.GENERIC);
  }

  /**
   * Removes the property from the internal storage.
   *
   * @param key The key/id of the propertyvalue
   * @param cause The technical cause for setting this
   * @return The value of the removed property
   */
  Optional<String> remove(String key, AttributeChangeCause cause);

  /**
   * Any changes made to this instance will not be saved, even if tried.
   * <p>
   *   While it is possible to change the values, MBedwars' auto-saving
   *   and {@link #save()} will not actually do anything and any changes
   *   will be lost with the next loading.
   * </p>
   * <p>
   *   Reasons for read-only are:
   *   <ul>
   *     <li>We failed to load it fully (due to an error).
   *     Reasons for that may include e.g. a disconnection to the storage server.</li>
   *     <li>The player might be online on another server and we
   *     don't want to intercept with whatever the other server is doing.
   *     Although cross-server support can be obtained using the ProxySync addon and
   *     making sure that player-data-syncing is enabled within it, in which case this
   *     instance won't be set as read-only.</li>
   *   </ul>
   *
   * @return Whether this instance is read-only and changes won't be saved
   */
  boolean isReadOnly();

  /**
   * Sets whether this instance is read-only.
   * <p>
   *   This mechanism exists to prevent desyncs between multiple servers.
   *   Use it with caution! Read {@link #isReadOnly()} for more info.
   * </p>
   *
   * @param readOnly Whether this instance should be read-only
   */
  void setReadOnly(boolean readOnly);

  /**
   * Asynchronously saves these achievements.
   * <p>
   * 	It's usually not needed to call this method when the player is currently on the server as the plugin will already handle it.
   * </p>
   */
  default void save() {
    save(null);
  }

  /**
   * Asynchronously saves these achievements.
   * <p>
   * 	It's usually not needed to call this method when the player is currently on the server as the plugin will already handle it.
   * </p>
   * <p>
   * 	<b>Important:</b> The callback doesn't get synced to the main thread.
   * </p>
   *
   * @param callback Gets called when the operation was finished
   */
  void save(@Nullable Runnable callback);

  /**
   * Parses the entry {@link DefaultPlayerProperty#SHOPDESIGN_HYPIXELV2_QUICKBUY} and returns all the items stored in the quick buy.
   * <p>
   *     In case the entry does not exist (he has never opened the HyPixelV2 shop layout before), or the data got corrupted,
   *     a new random set of items is being generated given by the currently configured pages.
   * </p>
   * <p>
   *     Entries in the array may be null. Those represent air.
   * </p>
   *
   * @return All the items stored in the HyPixelV2 quick buy
   * @see #setShopHypixelV2QuickBuyItems(ShopItem[])
   */
  ShopItem[] getShopHypixelV2QuickBuyItems();

  /**
   * Serializes and sets the entry {@link DefaultPlayerProperty#SHOPDESIGN_HYPIXELV2_QUICKBUY} for all the items that are stored in the quick buy.
   * <p>
   *     You may not pass <code>null</code>. To reset the items, simply remove the property key using {@link #remove(String)}.
   * </p>
   * <p>
   *     It is legt for entries to be null. Those represent air
   * </p>
   * <p>
   *     Note that those changes (likely) do not apply in case the shop is currently open.
   * </p>
   *
   * @param items All the items stored in the HyPixelV2 quick buy
   * @see #getShopHypixelV2QuickBuyItems()
   */
  void setShopHypixelV2QuickBuyItems(ShopItem[] items);
}
