package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import java.util.Set;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a player/sender tries to enable the arena
 */
public class ArenaEnableEvent extends ArenaIssuesCheckEvent {

  public ArenaEnableEvent(Arena arena, Set<Issue> issues, @Nullable CommandSenderWrapper sender) {
    super(arena, issues, sender);
  }

  @Override
  public HandlerList getHandlers() {
    return ArenaIssuesCheckEvent.getHandlerList();
  }

  public static HandlerList getHandlerList() {
    return ArenaIssuesCheckEvent.getHandlerList();
  }
}
