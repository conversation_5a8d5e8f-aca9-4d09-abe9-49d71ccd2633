package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.MBedwarsProp;
import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.game.spawner.SpawnerDurationModifier;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import de.marcely.bedwars.tools.location.XYZ;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.data.BlockData;
import org.bukkit.block.data.Directional;
import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.world.WorldStorage;
import de.marcely.bedwars.api.world.hologram.HologramEntity;
import de.marcely.bedwars.api.world.hologram.HologramSkinType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;

import java.io.File;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public class ResourceBoosterHandler implements SpecialItemUseHandler, Listener {

    private final MBedwarsProp plugin;
    private final Map<Location, ResourceBooster> boosters = new HashMap<>();
    private final Map<UUID, List<Location>> playerBoosters = new HashMap<>();
    private final String MODIFIER_ID = "mbedwarsprop:resource_booster";

    // 配置信息
    private int defaultRadius = 5;
    private int defaultDuration = 60;
    private boolean showParticles = true;
    private boolean playSound = true;
    private boolean enableDebugLog = false; // 新增日志开关
    private final Map<String, Double> resourceIntervals = new HashMap<>();
    private final Map<String, Integer> resourceAmounts = new HashMap<>(); // 添加生成数量映射
    private final Map<Spawner, List<BukkitTask>> additionalTasks = new HashMap<>(); // 存储额外任务
    
    // 资源名称映射表，用于将本地化名称映射到配置中的标识符
    private final Map<String, String> resourceNameMapping = new HashMap<>();

    // 新增类成员变量 - 储存原始生成间隔
    private final Map<Spawner, Double> originalDropDurations = new HashMap<>();
    
    // 主类日志辅助方法
    private void logDebug(String message) {
        if (enableDebugLog) {
            plugin.getLogger().info("[资源加速器] " + message);
        }
    }
    
    private void logWarning(String message) {
        plugin.getLogger().warning("[资源加速器] " + message);
    }
    
    private void logError(String message) {
        plugin.getLogger().severe("[资源加速器] " + message);
    }

    public ResourceBoosterHandler(MBedwarsProp plugin) {
        this.plugin = plugin;

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
        
        // 注册竞技场状态变化监听器
        registerArenaStateListener();
        
        // 初始化资源名称映射
        initResourceNameMapping();

        // 加载配置
        loadConfig();
        
        // 添加启动时全局清理逻辑
        Bukkit.getScheduler().runTaskLater(plugin, this::performGlobalCleanup, 60L);
    }

    // 全局清理逻辑，用于启动时或重载插件时清理所有可能的残留效果
    private void performGlobalCleanup() {
        logDebug("执行全局资源加速器清理...");
        
        int cleanedSpawners = 0;
        
        // 遍历所有竞技场
        for (Arena arena : GameAPI.get().getArenas()) {
            if (arena.getStatus() != ArenaStatus.RUNNING) {
                continue;
            }
            
            // 遍历竞技场中的所有生成器
            for (Spawner spawner : arena.getSpawners()) {
                try {
                    // 查找我们的修改器
                    List<SpawnerDurationModifier> modifiersToRemove = new ArrayList<>();
                    
                    for (SpawnerDurationModifier modifier : spawner.getDropDurationModifiers()) {
                        if (modifier.getId().startsWith(MODIFIER_ID)) {
                            modifiersToRemove.add(modifier);
                        }
                    }
                    
                    // 移除找到的修改器
                    for (SpawnerDurationModifier modifier : modifiersToRemove) {
                        boolean removed = spawner.removeDropDurationModifier(modifier);
                        if (removed) {
                            cleanedSpawners++;
                            logDebug("清理了残留的资源加速器修改器: " + modifier.getId() + " 从 " + 
                                                 spawner.getDropType().getName() + " 生成器");
                        }
                    }
                    
                    // 如果找到了修改器，尝试重置生成器
                    if (!modifiersToRemove.isEmpty()) {
                        try {
                            java.lang.reflect.Method resetMethod = spawner.getClass().getMethod("resetDropTimer");
                            resetMethod.invoke(spawner);
                        } catch (Exception e) {
                            logDebug("全局清理时重置生成器计时器失败: " + e.getMessage());
                        }
                        
                        // 尝试恢复全息显示
                        HologramEntity hologram = spawner.getHologram();
                        if (hologram != null && hologram.exists()) {
                            String displayName = hologram.getDisplayName();
                            if (displayName != null && displayName.contains("[加速中")) {
                                String newText = displayName.replaceAll("\\s*\\[加速中 \\d+x\\]\\s*", "");
                                hologram.setDisplayName(newText);
                                logDebug("清理了残留的全息显示文本: " + displayName + " -> " + newText);
                            }
                        }
                    }
                } catch (Exception e) {
                    logWarning("全局清理时处理生成器失败: " + e.getMessage());
                }
            }
        }
        
        logDebug("全局清理完成，清理了 " + cleanedSpawners + " 个生成器上的残留效果");
    }

    // 初始化资源名称映射
    private void initResourceNameMapping() {
        // 添加已知的资源名称映射
        resourceNameMapping.put("§7铁锭", "iron");
        resourceNameMapping.put("iron", "iron");
        resourceNameMapping.put("铁锭", "iron");
        resourceNameMapping.put("§6金锭", "gold");
        resourceNameMapping.put("gold", "gold");
        resourceNameMapping.put("金锭", "gold");
        resourceNameMapping.put("§a绿宝石", "emerald");
        resourceNameMapping.put("emerald", "emerald");
        resourceNameMapping.put("绿宝石", "emerald");
        resourceNameMapping.put("§b钻石", "diamond");
        resourceNameMapping.put("diamond", "diamond");
        resourceNameMapping.put("钻石", "diamond");
        
        logDebug("已初始化资源名称映射: " + resourceNameMapping.size() + " 个条目");
    }
    
    // 获取标准化的资源类型名称
    private String getNormalizedResourceName(String typeName) {
        // 移除所有颜色代码
        String normalizedName = typeName.replaceAll("§[0-9a-fk-or]", "").toLowerCase();
        
        // 尝试从映射中获取标准名称
        if (resourceNameMapping.containsKey(typeName)) {
            String mappedName = resourceNameMapping.get(typeName);
            logDebug("资源名称映射: " + typeName + " -> " + mappedName);
            return mappedName;
        }
        
        // 尝试从映射中获取标准化后的名称
        if (resourceNameMapping.containsKey(normalizedName)) {
            String mappedName = resourceNameMapping.get(normalizedName);
            logDebug("资源名称规范化: " + typeName + " -> " + normalizedName + " -> " + mappedName);
            // 添加到映射中，以便下次直接查找
            resourceNameMapping.put(typeName, mappedName);
            return mappedName;
        }
        
        // 如果上述方法都失败，尝试猜测
        for (String key : resourceIntervals.keySet()) {
            if (normalizedName.contains(key) || key.contains(normalizedName)) {
                logDebug("资源名称猜测: " + typeName + " -> " + key);
                // 添加到映射中，以便下次直接查找
                resourceNameMapping.put(typeName, key);
                return key;
            }
        }
        
        // 如果仍然无法匹配，记录警告并返回原始名称
        logWarning("无法映射资源名称: " + typeName + ", 已规范化为: " + normalizedName);
        // 将找不到的资源名称也添加到映射中，避免重复日志
        resourceNameMapping.put(typeName, normalizedName);
        return normalizedName;
    }

    // 从配置文件加载设置
    private void loadConfig() {
        try {
            // 清空之前的配置，避免旧配置干扰
            resourceIntervals.clear();
            resourceAmounts.clear();
            
            // 获取资源加速器配置
            ItemConfigManager.ItemConfig config = plugin.itemConfigManager.getItemConfig("resource_booster");
            if (config == null) {
                logWarning("无法加载资源加速器配置，使用默认设置");
                return;
            }

            // 读取基本设置
            ItemConfigManager.EffectConfig radiusConfig = config.getEffect("radius");
            if (radiusConfig != null) {
                defaultRadius = radiusConfig.getLevel();
            }

            ItemConfigManager.EffectConfig durationConfig = config.getEffect("duration");
            if (durationConfig != null) {
                defaultDuration = durationConfig.getDuration();
            }

            ItemConfigManager.EffectConfig particlesConfig = config.getEffect("show-particles");
            if (particlesConfig != null) {
                showParticles = particlesConfig.getLevel() > 0;
            }

            ItemConfigManager.EffectConfig soundConfig = config.getEffect("play-sound");
            if (soundConfig != null) {
                playSound = soundConfig.getLevel() > 0;
            }

            // 读取日志开关设置
            ItemConfigManager.EffectConfig debugLogConfig = config.getEffect("debug-log");
            if (debugLogConfig != null) {
                enableDebugLog = debugLogConfig.getLevel() > 0;
                logDebug("资源加速器调试日志: " + (enableDebugLog ? "已启用" : "已禁用"));
            }

            // 读取资源加速设置 - 使用直接读取配置文件的方式
            if (enableDebugLog) {
                logDebug("开始读取资源配置...");
            }
            
            // 手动读取资源配置
            File configFile = new File(plugin.getDataFolder(), "items/resource_booster.yml");
            if (configFile.exists()) {
                if (enableDebugLog) {
                    logDebug("找到配置文件：" + configFile.getAbsolutePath());
                }
                
                FileConfiguration yamlConfig = YamlConfiguration.loadConfiguration(configFile);
                ConfigurationSection resources = yamlConfig.getConfigurationSection("effects.resources");
                
                // 读取日志开关 - 从主配置部分也读取一次，以确保兼容性
                if (!yamlConfig.contains("effects.debug-log") && yamlConfig.getBoolean("effects.debug-log", false)) {
                    enableDebugLog = true;
                    logDebug("从主配置读取到调试日志设置: 已启用");
                }
                
                if (resources != null) {
                    if (enableDebugLog) {
                        logDebug("找到resources部分，包含以下资源类型：" + resources.getKeys(false));
                    }
                    
                    // 读取铁锭配置
                    ConfigurationSection ironSection = resources.getConfigurationSection("iron");
                    if (ironSection != null) {
                        double interval = ironSection.getDouble("interval", 1.0);
                        int amount = ironSection.getInt("amount", 2);
                        resourceIntervals.put("iron", interval);
                        resourceAmounts.put("iron", amount);
                        if (enableDebugLog) {
                            logDebug("读取铁锭配置：" + interval + "秒生成" + amount + "个");
                        }
                    }
                    
                    // 读取金锭配置
                    ConfigurationSection goldSection = resources.getConfigurationSection("gold");
                    if (goldSection != null) {
                        double interval = goldSection.getDouble("interval", 2.0);
                        int amount = goldSection.getInt("amount", 2);
                        resourceIntervals.put("gold", interval);
                        resourceAmounts.put("gold", amount);
                        if (enableDebugLog) {
                            logDebug("读取金锭配置：" + interval + "秒生成" + amount + "个");
                        }
                    }
                    
                    // 读取绿宝石配置
                    ConfigurationSection emeraldSection = resources.getConfigurationSection("emerald");
                    if (emeraldSection != null) {
                        double interval = emeraldSection.getDouble("interval", 8.0);
                        int amount = emeraldSection.getInt("amount", 2);
                        resourceIntervals.put("emerald", interval);
                        resourceAmounts.put("emerald", amount);
                        if (enableDebugLog) {
                            logDebug("读取绿宝石配置：" + interval + "秒生成" + amount + "个");
                        }
                    }
                    
                    // 读取钻石配置
                    ConfigurationSection diamondSection = resources.getConfigurationSection("diamond");
                    if (diamondSection != null) {
                        double interval = diamondSection.getDouble("interval", 15.0);
                        int amount = diamondSection.getInt("amount", 2);
                        resourceIntervals.put("diamond", interval);
                        resourceAmounts.put("diamond", amount);
                        if (enableDebugLog) {
                            logDebug("读取钻石配置：" + interval + "秒生成" + amount + "个");
                        }
                    }
                } else {
                    logWarning("在配置文件中未找到resources部分");
                }
            } else {
                logWarning("未找到配置文件：" + configFile.getAbsolutePath());
            }

            // 设置默认值（如果配置中没有）
            // 默认生成间隔
            if (!resourceIntervals.containsKey("iron"))
                resourceIntervals.put("iron", 1.0);
            if (!resourceIntervals.containsKey("gold"))
                resourceIntervals.put("gold", 2.0);
            if (!resourceIntervals.containsKey("emerald"))
                resourceIntervals.put("emerald", 8.0);
            if (!resourceIntervals.containsKey("diamond"))
                resourceIntervals.put("diamond", 15.0);

            // 默认生成数量
            if (!resourceAmounts.containsKey("iron"))
                resourceAmounts.put("iron", 2); // 默认每次生成2个
            if (!resourceAmounts.containsKey("gold"))
                resourceAmounts.put("gold", 2); // 默认每次生成2个
            if (!resourceAmounts.containsKey("emerald"))
                resourceAmounts.put("emerald", 2); // 默认每次生成2个
            if (!resourceAmounts.containsKey("diamond"))
                resourceAmounts.put("diamond", 2); // 默认每次生成2个

            logDebug("资源加速器配置加载完成");
            // 只有在启用调试日志时才输出详细信息
            if (enableDebugLog) {
                logDebug("- 铁锭: " + resourceIntervals.get("iron") + "秒生成" + resourceAmounts.get("iron") + "个");
                logDebug("- 金锭: " + resourceIntervals.get("gold") + "秒生成" + resourceAmounts.get("gold") + "个");
                logDebug("- 绿宝石: " + resourceIntervals.get("emerald") + "秒生成" + resourceAmounts.get("emerald") + "个");
                logDebug("- 钻石: " + resourceIntervals.get("diamond") + "秒生成" + resourceAmounts.get("diamond") + "个");
            }

        } catch (Exception e) {
            logError("加载资源加速器配置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 重新加载配置，确保使用最新的设置
        loadConfig();
        logDebug("已重新加载资源加速器配置");
        // 只有在启用调试日志时才输出详细信息
        if (enableDebugLog) {
            logDebug("加载的配置信息：");
            logDebug("- 铁锭: " + resourceIntervals.getOrDefault("iron", 0.0) + "秒生成" + resourceAmounts.getOrDefault("iron", 0) + "个");
            logDebug("- 金锭: " + resourceIntervals.getOrDefault("gold", 0.0) + "秒生成" + resourceAmounts.getOrDefault("gold", 0) + "个");
            logDebug("- 绿宝石: " + resourceIntervals.getOrDefault("emerald", 0.0) + "秒生成" + resourceAmounts.getOrDefault("emerald", 0) + "个");
            logDebug("- 钻石: " + resourceIntervals.getOrDefault("diamond", 0.0) + "秒生成" + resourceAmounts.getOrDefault("diamond", 0) + "个");
        }

        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        // 检查是否破坏了资源加速器
        Block block = event.getBlock();
        if (block.getType() == Material.REDSTONE_TORCH) {
            Location location = block.getLocation();
            boolean found = false;

            // 检查是否是资源加速器
            if (boosters.containsKey(location)) {
                found = true;
                ResourceBooster booster = boosters.get(location);

                // 记录移除信息
                logDebug("检测到玩家破坏资源加速器: " + location);

                // 取消加速效果
                booster.cancel();

                // 移除加速器
                boosters.remove(location);

                // 通知玩家
                Player player = event.getPlayer();
                player.sendMessage("§c你破坏了一个资源加速器！");
                return; // 已找到并处理，直接返回
            } 
            
            // 如果原始位置没有找到，检查周围位置
            if (!found) {
                // 检查周围的方块
                BlockFace[] faces = { 
                    BlockFace.UP, BlockFace.DOWN, 
                    BlockFace.NORTH, BlockFace.EAST, BlockFace.SOUTH, BlockFace.WEST 
                };
                
                for (BlockFace face : faces) {
                    Block nearbyBlock = block.getRelative(face);
                    Location nearbyLoc = nearbyBlock.getLocation();
                    
                    if (boosters.containsKey(nearbyLoc)) {
                        ResourceBooster booster = boosters.get(nearbyLoc);
                        
                        // 记录移除信息
                        logDebug("检测到玩家破坏资源加速器邻近方块: " + nearbyLoc);
                        
                        // 取消加速效果
                        booster.cancel();
                        
                        // 移除加速器
                        boosters.remove(nearbyLoc);
                        
                        // 通知玩家
                        Player player = event.getPlayer();
                        player.sendMessage("§c你破坏了一个资源加速器！");
                        found = true;
                        break;
                    }
                }
                
                if (found) {
                    // 已在周围找到并处理，直接返回
                    return;
                }
            }
        }
    }

    // 资源加速器类
    private class ResourceBooster {
        private final Location location;
        private final int radius;
        private final int duration;
        private final Set<Spawner> affectedSpawners = new HashSet<>();
        private final Map<Spawner, SpawnerDurationModifier> modifiers = new HashMap<>();
        // 存储资源生成点的原始全息显示信息
        private final Map<Spawner, String> originalHologramTexts = new HashMap<>();
        private final BukkitTask particleTask;
        private final BukkitTask expirationTask;
        private final BukkitTask hologramUpdateTask;
        private HologramEntity hologram; // 全息显示实体
        private int remainingSeconds; // 剩余时间（秒）
        private BukkitTask monitoringTask = null;
        private boolean isExpired = false;
        private Player owner; // 添加放置者玩家引用
        private final Map<Spawner, BukkitTask> spawnerTasks = new HashMap<>();
        // 添加原始资源生成间隔记录
        private final Map<Spawner, Double> originalDropDurations = new HashMap<>();

        public ResourceBooster(Location location, int radius, int duration, Player owner) {
            this.location = location;
            this.radius = radius;
            this.duration = duration;
            this.remainingSeconds = duration;
            this.isExpired = false;
            this.owner = owner;

            // 创建全息显示
            createHologram();

            // 查找范围内的资源生成点
            findSpawnersInRange();
            logDebug("找到 " + affectedSpawners.size() + " 个资源生成点在范围内");

            // 如果找到了资源生成点，应用加速效果
            if (!affectedSpawners.isEmpty()) {
                // 延迟一个 tick 再应用加速效果，确保全息显示已创建
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        applyBoostEffect();
                    }
                }.runTaskLater(plugin, 1L);
            } else {
                // 即使没有找到资源生成点，也不取消资源加速器
                // 可能玩家只是想放置一个红石火把作为标记或装饰
                logDebug("在范围内没有找到资源生成点，但资源加速器仍会保持放置状态");
            }

            // 创建粒子效果任务
            this.particleTask = new BukkitRunnable() {
                @Override
                public void run() {
                    showParticles();
                }
            }.runTaskTimer(plugin, 0L, 20L); // 每秒显示一次粒子效果

            // 创建全息显示更新任务
            this.hologramUpdateTask = new BukkitRunnable() {
                @Override
                public void run() {
                    if (remainingSeconds > 0) {
                        remainingSeconds--;
                        // 立即更新全息显示，确保显示正确的倒计时
                        updateHologram();
                        
                        // 当剩余时间为5秒时提醒玩家
                        if (remainingSeconds == 5 && owner != null && owner.isOnline()) {
                            owner.sendMessage("§e你的资源加速器还剩 §c5 §e秒就要失效了！");
                        }
                        
                        // 当时间到期时（倒计时为0）取消任务
                        if (remainingSeconds <= 0) {
                            logDebug("资源加速器倒计时结束");
                            cancel();
                        }
                    }
                }
            }.runTaskTimer(plugin, 20L, 20L); // 每秒更新一次

            // 创建过期任务
            this.expirationTask = new BukkitRunnable() {
                @Override
                public void run() {
                    logDebug("资源加速器过期，开始清理资源");
                    
                    // 标记为已过期，防止监控任务重新放置红石火把
                    isExpired = true;
                    
                    // 停止所有资源生成监控任务
                    for (BukkitTask task : spawnerTasks.values()) {
                        if (task != null && !task.isCancelled()) {
                            task.cancel();
                            logDebug("取消资源生成监控任务");
                        }
                    }
                    
                    // 清空任务映射
                    spawnerTasks.clear();
                    
                    // 停止监控任务
                    if (monitoringTask != null && !monitoringTask.isCancelled()) {
                        monitoringTask.cancel();
                        logDebug("已停止红石火把监控任务");
                    }

                    // 恢复原始生成速率
                    restoreOriginalRates();

                    // 取消任务
                    if (particleTask != null && !particleTask.isCancelled()) {
                        particleTask.cancel();
                    }

                    if (hologramUpdateTask != null && !hologramUpdateTask.isCancelled()) {
                        hologramUpdateTask.cancel();
                    }

                    // 移除全息显示
                    if (hologram != null && hologram.exists()) {
                        hologram.remove();
                    }

                    // 从管理器中移除
                    boosters.remove(location);
                    logDebug("从管理器中移除资源加速器");

                    // 直接移除红石火把 - 不使用API，直接设置方块类型
                    removeRedstoneTorch();

                    // 播放效果结束的声音
                    World world = location.getWorld();
                    if (world != null) {
                        world.playSound(location, Sound.BLOCK_BEACON_DEACTIVATE, 1.0f, 1.0f);
                        world.playSound(location, Sound.BLOCK_REDSTONE_TORCH_BURNOUT, 1.0f, 1.0f);

                        // 添加粒子效果表示结束
                        world.spawnParticle(Particle.SMOKE_NORMAL, location.clone().add(0.5, 0.5, 0.5), 20, 0.3, 0.3,
                                0.3, 0.05);
                    }
                    
                    // 通知玩家资源加速器已过期
                    if (owner != null && owner.isOnline()) {
                        owner.sendMessage("§c你的资源加速器已过期，资源生成速度已恢复正常！");
                    }

                    // 通知附近的玩家资源生成速度已恢复
                    notifyNearbyPlayers("§a附近的资源生成速度已恢复正常！");

                    // 确保完全清理所有相关数据
                    finalCleanup();

                    // 取消当前任务
                    cancel();
                }
            }.runTaskLater(plugin, duration * 20L);
        }

        // 最终清理逻辑，确保所有资源都被释放
        private void finalCleanup() {
            // 再次检查所有任务是否已取消
            if (particleTask != null && !particleTask.isCancelled()) {
                particleTask.cancel();
            }
            
            if (hologramUpdateTask != null && !hologramUpdateTask.isCancelled()) {
                hologramUpdateTask.cancel();
            }
            
            if (expirationTask != null && !expirationTask.isCancelled()) {
                expirationTask.cancel();
            }
            
            if (monitoringTask != null && !monitoringTask.isCancelled()) {
                monitoringTask.cancel();
            }
            
            // 取消所有生成监控任务
            for (BukkitTask task : spawnerTasks.values()) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            spawnerTasks.clear();
            
            // 取消所有额外任务
            for (List<BukkitTask> tasks : additionalTasks.values()) {
                for (BukkitTask task : tasks) {
                    if (task != null && !task.isCancelled()) {
                        task.cancel();
                    }
                }
            }
            additionalTasks.clear();
            
            // 清理修改器
            for (Spawner spawner : affectedSpawners) {
                try {
                    // 尝试强力重置生成器
                    resetSpawnerCompletely(spawner);
                } catch (Exception e) {
                    plugin.getLogger().severe("最终清理时重置生成器失败: " + e.getMessage());
                }
            }
            
            // 清空所有映射
            modifiers.clear();
            originalHologramTexts.clear();
            
            plugin.getLogger().info("完成资源加速器最终清理");
        }

        // 查找范围内的资源生成点
        private void findSpawnersInRange() {
            World world = location.getWorld();
            if (world == null)
                return;

            // 获取所有竞技场
            for (Arena arena : GameAPI.get().getArenas()) {
                // 只处理正在进行的竞技场
                if (arena.getStatus() != ArenaStatus.RUNNING)
                    continue;

                // 检查竞技场世界是否匹配
                if (!arena.getGameWorld().equals(world))
                    continue;

                // 获取竞技场中的所有资源生成点
                for (Spawner spawner : arena.getSpawners()) {
                    // 获取生成点位置
                    XYZ spawnerXYZ = spawner.getLocation();
                    Location spawnerLoc = new Location(world, spawnerXYZ.getX(), spawnerXYZ.getY(), spawnerXYZ.getZ());

                    // 检查资源生成点是否在范围内
                    if (spawnerLoc.distance(location) <= radius) {
                        affectedSpawners.add(spawner);
                    }
                }
            }
        }

        // 应用加速效果
        private void applyBoostEffect() {
            int successCount = 0;
            for (Spawner spawner : affectedSpawners) {
                DropType dropType = spawner.getDropType();
                String rawTypeName = dropType.getName();
                
                // 获取标准化的资源类型名称
                String typeName = getNormalizedResourceName(rawTypeName);
                
                int amount = 1; // 默认生成数量

                // 获取资源生成数量 - 使用标准化后的名称
                if (ResourceBoosterHandler.this.resourceAmounts.containsKey(typeName)) {
                    amount = ResourceBoosterHandler.this.resourceAmounts.get(typeName);
                    logDebug("资源 " + rawTypeName + "(" + typeName + ") 的生成数量配置为: " + amount);
                } else {
                    logWarning("未找到资源 " + rawTypeName + "(" + typeName + ") 的生成数量配置，使用默认值1");
                }

                // 获取资源生成间隔 - 使用标准化后的名称
                double interval = 0.0;
                if (ResourceBoosterHandler.this.resourceIntervals.containsKey(typeName)) {
                    interval = ResourceBoosterHandler.this.resourceIntervals.get(typeName);
                } else {
                    // 如果没有配置间隔，使用当前间隔的一半作为默认值（加速2倍）
                    interval = spawner.getCurrentDropDuration() / 2;
                }

                // 记录当前生成间隔 - 在修改前保存原始值
                double currentDuration = spawner.getCurrentDropDuration();
                originalDropDurations.put(spawner, currentDuration);

                // 输出调试信息
                logDebug("资源类型: " + rawTypeName + ", 当前间隔: " + currentDuration + 
                                       ", 设置新间隔: " + interval + ", 生成数量: " + amount + "，已保存原始间隔");

                try {
                    // 强制应用设置的间隔，确保生效
                    // 先移除所有现有的修改器，避免干扰
                    List<SpawnerDurationModifier> existingModifiers = new ArrayList<>(spawner.getDropDurationModifiers());
                    for (SpawnerDurationModifier mod : existingModifiers) {
                        if (mod.getId().startsWith(MODIFIER_ID)) {
                            spawner.removeDropDurationModifier(mod);
                            logDebug("移除旧的修改器: " + mod.getId());
                        }
                    }
                    
                    // 添加新的修改器来设置资源生成间隔
                    // 使用SET操作直接设置为指定的间隔时间
                    SpawnerDurationModifier modifier = spawner.addDropDurationModifier(
                            MODIFIER_ID,
                            plugin,
                            SpawnerDurationModifier.Operation.SET,
                            interval);

                    // 检查修改器是否成功添加
                    if (modifier != null) {
                        // 保存修改器以便稍后移除
                        modifiers.put(spawner, modifier);
                        logDebug("成功添加修改器，新生成间隔: " + spawner.getCurrentDropDuration());

                        // 强制更新生成器状态
                        // 尝试使用反射调用resetDropTimer方法（如果存在）
                        try {
                            java.lang.reflect.Method resetMethod = spawner.getClass().getMethod("resetDropTimer");
                            resetMethod.invoke(spawner);
                            logDebug("已重置生成器计时器");
                        } catch (Exception e) {
                            logDebug("无法重置生成器计时器: " + e.getMessage());
                        }
                        
                        // 验证修改是否生效
                        double newDuration = spawner.getCurrentDropDuration();
                        if (Math.abs(newDuration - interval) > 0.001) {
                            logWarning("修改器可能未正确生效! 目标:" + interval + ", 实际:" + newDuration);
                    } else {
                            logDebug("修改器成功应用! 目标:" + interval + ", 实际:" + newDuration);
                            successCount++;
                        }
                    } else {
                        logWarning("添加修改器失败！");
                    }

                    // 设置资源生成监听器，用于生成多个资源
                    if (amount > 1) {
                        setupResourceMultiplier(spawner, amount);
                        logDebug("已设置资源生成数量为: " + amount);
                    }
                } catch (Exception e) {
                    logError("应用加速效果时出错: " + e.getMessage());
                    e.printStackTrace();
                }

                // 保存原始全息显示文本
                HologramEntity spawnerHologram = spawner.getHologram();
                if (spawnerHologram != null) {
                    String originalText = spawnerHologram.getDisplayName();
                    // 如果原始文本为 null，使用资源类型名称代替
                    if (originalText == null) {
                        originalText = "§f" + spawner.getDropType().getName() + " 生成点";
                    }

                    originalHologramTexts.put(spawner, originalText);

                    // 设置新的全息显示文本，显示加速状态和生成数量
                    String boostText = "§a[加速中 " + amount + "x] " + originalText;
                    spawnerHologram.setDisplayName(boostText);
                    logDebug("更新资源生成点全息显示: " + boostText);
                }
            }
            
            // 输出总结信息
            logDebug("资源加速器应用完成，成功处理 " + successCount + "/" + affectedSpawners.size() + " 个资源生成点");
        }
        
        // 修改资源生成监听器的实现方式，使其更可靠
        private void setupResourceMultiplier(Spawner spawner, int amount) {
            // 确保amount值正确
            String rawTypeName = spawner.getDropType().getName();
            String typeName = getNormalizedResourceName(rawTypeName);
            
            logDebug("设置资源 " + rawTypeName + "(" + typeName + ") 的生成数量为: " + amount);
            
            // 先清除可能已存在的任务
            if (spawnerTasks.containsKey(spawner) && !spawnerTasks.get(spawner).isCancelled()) {
                spawnerTasks.get(spawner).cancel();
                logDebug("取消已存在的资源监控任务");
            }
            
            // 清除之前的额外任务
            if (additionalTasks.containsKey(spawner)) {
                List<BukkitTask> tasks = additionalTasks.get(spawner);
                for (BukkitTask task : tasks) {
                    if (task != null && !task.isCancelled()) {
                        task.cancel();
                        logDebug("取消已存在的额外任务");
                    }
                }
                additionalTasks.remove(spawner);
            }
            
            if (amount <= 1) {
                logDebug("资源生成数量为1或更少，不需要额外处理");
                return;
            }
            
            // 更新全息显示
            HologramEntity spawnerHologram = spawner.getHologram();
            if (spawnerHologram != null) {
                String displayName = spawnerHologram.getDisplayName();
                if (displayName != null && displayName.contains("[加速中")) {
                    // 更新全息显示，确保显示正确的数量
                    String newText = displayName.replaceAll("\\[加速中 \\d+x\\]", "[加速中 " + amount + "x]");
                    spawnerHologram.setDisplayName(newText);
                    logDebug("更新全息显示为: " + newText);
                }
            }
            
            // 创建本地变量跟踪任务状态，防止资源泄漏
            final boolean[] taskActive = {true};
            
            // 创建一个精确的资源检测器，通过监控原生资源是否生成来触发额外资源生成
            // 记录上一次检查的内部时钟和上一次触发生成的时间
            final int[] lastInternalClock = {getInternalClock(spawner)};
            final long[] lastTriggerTime = {System.currentTimeMillis()};
            final boolean[] isGenerating = {false}; // 避免重复触发
            
            BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                // 检查是否应该继续执行任务
                if (isExpired || !taskActive[0]) {
                    // 记录任务取消
                    logDebug("资源生成监控任务停止执行，过期状态：" + isExpired);
                    return;
                }
                
                try {
                    // 再次确认加速器未过期，防止上一条检查后状态变化
                    if (isExpired) {
                        return;
                    }
                    
                    // 获取当前内部时钟和剩余时间
                    int currentClock = getInternalClock(spawner);
                    int remainingTicks = getRemainingNextDropTime(spawner);
                    
                    // 检测资源生成：内部时钟变化了，说明经过了一个tick
                    if (currentClock != lastInternalClock[0]) {
                        lastInternalClock[0] = currentClock;
                        
                        // 资源生成时刻的检测：当剩余时间为0或者非常小时
                        if (remainingTicks <= 1) {
                            // 如果是真正的资源生成时刻（而不仅仅是时钟变化）
                            long currentTime = System.currentTimeMillis();
                            
                            // 防止在短时间内重复触发：至少需要间隔500毫秒
                            if (!isGenerating[0] && currentTime - lastTriggerTime[0] > 500) {
                                isGenerating[0] = true;
                                lastTriggerTime[0] = currentTime;
                                
                                // 延迟1tick后生成额外资源，确保原始资源已经生成
                                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                                    // 再次检查过期状态
                                    if (isExpired || !taskActive[0]) {
                                        isGenerating[0] = false;
                                        return;
                                    }
                                    
                                    try {
                                        // 获取生成点位置和资源类型
                                        XYZ spawnerXYZ = spawner.getLocation();
                                        Location spawnLocation = new Location(
                                            location.getWorld(), 
                                            spawnerXYZ.getX(), 
                                            spawnerXYZ.getY() + 0.7, 
                                            spawnerXYZ.getZ()
                                        );
                                        
                                        logDebug("检测到 " + rawTypeName + " 资源生成，准备添加额外资源");
                                        
                                        // 直接使用生成器自带的生成方法，确保额外资源能正确生成
                                        for (int i = 0; i < amount - 1; i++) {
                                            spawner.drop(true);
                                            
                                            // 短暂延迟避免物理引擎问题
                                            try {
                                                Thread.sleep(10);
                                            } catch (InterruptedException e) {
                                                logWarning("生成额外资源时线程被中断: " + e.getMessage());
                                            }
                                        }
                                        
                                        logDebug("生成了 " + (amount - 1) + " 个额外 " + rawTypeName + " 资源");
                                    } catch (Exception e) {
                                        logError("生成额外资源时发生错误: " + e.getMessage());
                                        e.printStackTrace();
                                    } finally {
                                        // 释放生成标志
                                        isGenerating[0] = false;
                                    }
                                }, 1L);
                            }
                        }
                    }
                } catch (Exception e) {
                    logError("监控生成器时发生错误: " + e.getMessage());
                    e.printStackTrace();
                    
                    // 重置生成标志，避免卡住
                    isGenerating[0] = false;
                }
            }, 1L, 1L); // 每1tick检查一次
            
            // 注册一个任务在30分钟后自动取消，防止任务永久运行导致内存泄漏
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                if (!task.isCancelled()) {
                    logWarning("资源监控任务运行超过30分钟，强制取消以防止内存泄漏");
                    task.cancel();
                    taskActive[0] = false;
                    spawnerTasks.remove(spawner);
                }
            }, 36000L); // 30分钟 = 36000 ticks
            
            // 保存任务引用
            spawnerTasks.put(spawner, task);
        }

        // 恢复原始生成速率
        private void restoreOriginalRates() {
            logDebug("开始恢复原始生成速率，修改器数量: " + modifiers.size());

            boolean anyFailed = false;
            StringBuilder debugInfo = new StringBuilder("资源恢复状态：");
            
            // 先取消所有的生成任务
            for (BukkitTask task : spawnerTasks.values()) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                    logDebug("取消资源生成任务");
                }
            }
            spawnerTasks.clear();
            
            // 取消所有额外任务
            for (List<BukkitTask> tasks : additionalTasks.values()) {
                for (BukkitTask task : tasks) {
                    if (task != null && !task.isCancelled()) {
                        task.cancel();
                    }
                }
            }
            additionalTasks.clear();

            for (Map.Entry<Spawner, SpawnerDurationModifier> entry : modifiers.entrySet()) {
                Spawner spawner = entry.getKey();
                SpawnerDurationModifier modifier = entry.getValue();

                try {
                    // 先获取当前生成间隔和原始间隔，便于调试
                    double currentDuration = spawner.getCurrentDropDuration();
                    String resourceType = spawner.getDropType().getName();
                    String normalizedType = getNormalizedResourceName(resourceType);
                    
                    // 获取原始生成间隔
                    double originalDuration;
                    if (originalDropDurations.containsKey(spawner)) {
                        originalDuration = originalDropDurations.get(spawner);
                        logDebug("尝试恢复 " + resourceType + " 资源，恢复前生成间隔: " + currentDuration + 
                                              ", 原始间隔: " + originalDuration);
                    } else {
                        // 如果没有记录原始值，尝试从资源类型推断
                        if (normalizedType.equals("iron")) {
                            originalDuration = 1.0;
                        } else if (normalizedType.equals("gold")) {
                            originalDuration = 4.0; // 金锭默认生成间隔通常是4秒
                        } else if (normalizedType.equals("diamond")) {
                            originalDuration = 30.0; // 钻石默认生成间隔
                        } else if (normalizedType.equals("emerald")) {
                            originalDuration = 60.0; // 绿宝石默认生成间隔
                        } else {
                            // 使用游戏API获取
                            originalDuration = spawner.getDropType().getDropDuration(spawner.getArena());
                        }
                        logDebug("尝试恢复 " + resourceType + " 资源，恢复前生成间隔: " + currentDuration + 
                                              ", 未找到原始记录，使用默认间隔: " + originalDuration);
                    }

                    // 移除所有我们自己添加的修改器（保留系统默认修改器）
                    List<SpawnerDurationModifier> ourModifiers = new ArrayList<>();
                    
                    // 先收集所有我们自己的修改器
                    for (SpawnerDurationModifier mod : spawner.getDropDurationModifiers()) {
                        if (mod.getId().startsWith(MODIFIER_ID)) {
                            ourModifiers.add(mod);
                        } else {
                            logDebug("保留系统修改器: " + mod.getId());
                        }
                    }
                    
                    // 然后移除这些修改器
                    for (SpawnerDurationModifier mod : ourModifiers) {
                        boolean removed = spawner.removeDropDurationModifier(mod);
                        logDebug("移除修改器: " + mod.getId() + ", 结果: " + removed);
                    }

                    // 检查移除后的生成间隔
                    double afterRemoveDuration = spawner.getCurrentDropDuration();
                    logDebug("移除修改器后生成间隔: " + afterRemoveDuration);
                    
                    // 如果移除修改器后的速率与原始速率不一致，直接重置生成器
                    // 修改这里：对每种资源类型进行单独处理
                    if (Math.abs(afterRemoveDuration - originalDuration) > 0.1) {
                        logDebug("需要重置生成器: " + normalizedType);
                        
                        // 设置为恢复状态
                        boolean resetSuccess = false;
                        
                        // 针对每种资源类型进行特殊处理
                        if (normalizedType.equals("gold") && Math.abs(afterRemoveDuration - 1.0) < 0.1) {
                            logDebug("检测到金锭生成速率被重置为1秒，尝试特殊修复");
                            // 添加金锭专用修改器
                            SpawnerDurationModifier goldMod = spawner.addDropDurationModifier(
                                "bedwars:base", 
                                plugin, 
                                SpawnerDurationModifier.Operation.SET, 
                                4.0); // 金锭默认是4秒
                            
                            if (goldMod != null) {
                                logDebug("已为金锭添加基础修改器(4秒)");
                                resetSuccess = true;
                            }
                        }
                        
                        // 如果没有特殊处理成功，尝试通用方法
                        if (!resetSuccess) {
                            // 添加临时恢复修改器
                            SpawnerDurationModifier resetMod = spawner.addDropDurationModifier(
                                MODIFIER_ID + "_reset",
                                plugin,
                                SpawnerDurationModifier.Operation.SET,
                                originalDuration);
                                
                            if (resetMod != null) {
                                logDebug("添加了恢复修改器: " + resetMod.getId() + ", 间隔: " + originalDuration);
                                
                                // 等待3tick再移除
                                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                                    try {
                                        boolean removed = spawner.removeDropDurationModifier(resetMod);
                                        double finalDuration = spawner.getCurrentDropDuration();
                                        logDebug(resourceType + " 恢复修改器已移除(结果:" + removed + ")，最终生成间隔: " + finalDuration);
                                        
                                        // 再次检查金锭特殊情况
                                        if (normalizedType.equals("gold") && Math.abs(finalDuration - 1.0) < 0.1) {
                                            logDebug("二次检测到金锭生成速率为1秒，添加默认4秒修改器");
                                            SpawnerDurationModifier goldMod = spawner.addDropDurationModifier(
                                                "bedwars:base", 
                                                plugin, 
                                                SpawnerDurationModifier.Operation.SET, 
                                                4.0);
                                            
                                            if (goldMod != null) {
                                                logDebug("已为金锭添加默认4秒修改器");
                                            }
                                        }
                                    } catch (Exception e) {
                                        logWarning("移除临时恢复修改器时出错: " + e.getMessage());
                                    }
                                }, 3L);
                                
                                resetSuccess = true;
                            }
                        }
                        
                        // 如果无法恢复，记录警告
                        if (!resetSuccess) {
                            logWarning("无法为 " + resourceType + " 创建恢复修改器");
                            anyFailed = true;
                        }
                    } else {
                        logDebug(resourceType + " 的速率已成功恢复到 " + afterRemoveDuration);
                    }

                    // 尝试重置计时器
                    try {
                        java.lang.reflect.Method resetMethod = spawner.getClass().getMethod("resetDropTimer");
                        resetMethod.invoke(spawner);
                        logDebug("已重置 " + resourceType + " 生成器计时器");
                    } catch (Exception e) {
                        logDebug("无法重置 " + resourceType + " 生成器计时器: " + e.getMessage());
                    }

                    // 恢复原始全息显示文本
                    if (originalHologramTexts.containsKey(spawner)) {
                        HologramEntity spawnerHologram = spawner.getHologram();
                        if (spawnerHologram != null && spawnerHologram.exists()) {
                            String originalText = originalHologramTexts.get(spawner);
                            // 再次检查原始文本是否为 null
                            if (originalText == null) {
                                originalText = "§f" + spawner.getDropType().getName() + " 生成点";
                            }
                            spawnerHologram.setDisplayName(originalText);
                            logDebug("已恢复资源生成点全息显示: " + originalText);
                        }
                    }
                    
                    debugInfo.append(resourceType).append("(已恢复) ");
                } catch (Exception e) {
                    logError("恢复原始生成速率时出错: " + e.getMessage());
                    e.printStackTrace();
                    anyFailed = true;
                    debugInfo.append(spawner.getDropType().getName()).append("(失败) ");
                }
            }

            // 清空所有相关映射
            modifiers.clear();
            originalHologramTexts.clear();
            // 保留 originalDropDurations 以便在调试或重试时使用
            
            logDebug("已清空修改器映射和全息图映射");
            
            // 最后进行一次全局重置尝试，确保所有生成器都被正确重置
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                for (Spawner spawner : affectedSpawners) {
                    try {
                        String typeName = getNormalizedResourceName(spawner.getDropType().getName());
                        double currentDuration = spawner.getCurrentDropDuration();
                        double expectedDuration;
                        
                        // 获取预期的生成速率
                        if (originalDropDurations.containsKey(spawner)) {
                            expectedDuration = originalDropDurations.get(spawner);
                        } else if (typeName.equals("iron")) {
                            expectedDuration = 1.0;
                        } else if (typeName.equals("gold")) {
                            expectedDuration = 4.0;
                        } else if (typeName.equals("diamond")) {
                            expectedDuration = 30.0;
                        } else if (typeName.equals("emerald")) {
                            expectedDuration = 60.0;
                        } else {
                            expectedDuration = spawner.getDropType().getDropDuration(spawner.getArena());
                        }
                        
                        logDebug("最终检查: " + typeName + " 当前间隔: " + currentDuration + 
                                              ", 预期间隔: " + expectedDuration);
                        
                        // 如果速率仍然不正确，尝试最后的修复
                        if (Math.abs(currentDuration - expectedDuration) > 0.1) {
                            logWarning(typeName + " 最终速率检查失败，当前: " + currentDuration + 
                                                    ", 期望: " + expectedDuration + "，尝试强力重置");
                            
                            resetSpawnerCompletely(spawner);
                        }
                    } catch (Exception e) {
                        logWarning("最终检查阶段出错: " + e.getMessage());
                    }
                }
                
                // 最终清空保存的原始值
                originalDropDurations.clear();
                
            }, 10L); // 延迟10tick执行，确保之前的修改已应用
            
            // 输出恢复状态信息
            if (anyFailed) {
                logWarning(debugInfo.toString());
            } else if (!modifiers.isEmpty()) {
                logDebug("所有资源生成点已成功恢复原始速率: " + debugInfo);
            }
        }
        
        // 新增一个强力重置方法，确保完全恢复原始设置
        private void resetSpawnerCompletely(Spawner spawner) {
            String resourceName = spawner.getDropType().getName();
            String typeName = getNormalizedResourceName(resourceName);
            
            logDebug("强力重置生成器: " + resourceName + " (" + typeName + ")");
            
            try {
                // 1. 获取原始速率
                double originalDuration;
                
                // 首先从我们的记录中获取
                if (originalDropDurations.containsKey(spawner)) {
                    originalDuration = originalDropDurations.get(spawner);
                    logDebug("使用已保存的原始生成间隔: " + originalDuration);
                } else {
                    // 如果没有记录，从资源类型获取默认值
                    // 针对不同资源类型使用不同的默认值
                    if (typeName.equals("iron")) {
                        originalDuration = 1.0; // 默认铁是1秒
                    } else if (typeName.equals("gold")) {
                        originalDuration = 4.0; // 默认金是4秒
                    } else if (typeName.equals("diamond")) {
                        originalDuration = 30.0; // 默认钻石是30秒
                    } else if (typeName.equals("emerald")) {
                        originalDuration = 60.0; // 默认绿宝石是60秒
                    } else {
                        // 使用游戏API获取
                        originalDuration = spawner.getDropType().getDropDuration(spawner.getArena());
                    }
                    logDebug("无保存记录，使用默认的生成间隔: " + originalDuration);
                }
                
                // 2. 获取当前生成间隔
                double currentDuration = spawner.getCurrentDropDuration();
                logDebug("当前生成间隔: " + currentDuration);
                
                // 如果当前间隔已经正确，不需要进一步操作
                if (Math.abs(currentDuration - originalDuration) < 0.1) {
                    logDebug("当前间隔已经正确，无需重置");
                    return;
                }
                
                // 3. 移除所有我们自己添加的修改器（保留系统默认修改器）
                List<SpawnerDurationModifier> allModifiers = new ArrayList<>(spawner.getDropDurationModifiers());
                for (SpawnerDurationModifier mod : allModifiers) {
                    if (mod.getId().startsWith(MODIFIER_ID)) {
                        spawner.removeDropDurationModifier(mod);
                        logDebug("移除修改器: " + mod.getId());
                    } else {
                        logDebug("保留系统修改器: " + mod.getId());
                    }
                }
                
                // 4. 金锭特殊处理
                if (typeName.equals("gold") && Math.abs(currentDuration - 1.0) < 0.1) {
                    logDebug("检测到金锭间隔为1秒，应用特殊处理");
                    
                    // 查找是否存在bedwars:base修改器
                    boolean hasBaseModifier = false;
                    for (SpawnerDurationModifier mod : spawner.getDropDurationModifiers()) {
                        if (mod.getId().equals("bedwars:base")) {
                            hasBaseModifier = true;
                            break;
                        }
                    }
                    
                    // 如果没有基础修改器，添加一个
                    if (!hasBaseModifier) {
                        SpawnerDurationModifier goldMod = spawner.addDropDurationModifier(
                            "bedwars:base", 
                            plugin, 
                            SpawnerDurationModifier.Operation.SET, 
                            4.0);
                        if (goldMod != null) {
                            logDebug("成功添加金锭基础修改器");
                        } else {
                            logWarning("无法添加金锭基础修改器");
                        }
                    } else {
                        logDebug("已存在bedwars:base修改器，不再添加");
                    }
                } 
                // 5. 非金锭标准处理 - 使用临时修改器重置
                else {
                    // 添加一个临时的恢复修改器
                    SpawnerDurationModifier resetMod = spawner.addDropDurationModifier(
                        MODIFIER_ID + "_final_reset",
                        plugin,
                        SpawnerDurationModifier.Operation.SET,
                        originalDuration);
                    
                    if (resetMod != null) {
                        logDebug("添加最终恢复修改器，值: " + originalDuration);
                        
                        // 等待短暂时间后移除临时修改器
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            try {
                                spawner.removeDropDurationModifier(resetMod);
                                double finalDuration = spawner.getCurrentDropDuration();
                                logDebug("移除最终恢复修改器，最终间隔: " + finalDuration);
                                
                                // 再次检查是否已经恢复到预期值
                                if (Math.abs(finalDuration - originalDuration) > 0.1) {
                                    logWarning("最终检查仍然失败! 预期: " + originalDuration + ", 实际: " + finalDuration);
                                } else {
                                    logDebug("恢复成功，最终间隔: " + finalDuration);
                                }
                            } catch (Exception e) {
                                logWarning("移除最终恢复修改器时出错: " + e.getMessage());
                            }
                        }, 3L);
                    } else {
                        logWarning("无法创建最终恢复修改器");
                    }
                }
                
                // 6. 重置内部计时器
                try {
                    java.lang.reflect.Method resetMethod = spawner.getClass().getMethod("resetDropTimer");
                    resetMethod.invoke(spawner);
                    logDebug("重置内部计时器成功");
                } catch (Exception e) {
                    logWarning("重置内部计时器失败: " + e.getMessage());
                }
                
                // 7. 输出所有当前的修改器，便于调试
                logDebug("重置后的所有修改器:");
                for (SpawnerDurationModifier mod : spawner.getDropDurationModifiers()) {
                    logDebug("- " + mod.getId() + ": " + mod.getOperation() + " " + mod.getValue());
                }
                
            } catch (Exception e) {
                logError("强力重置生成器失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 显示粒子效果
        private void showParticles() {
            World world = location.getWorld();
            if (world == null)
                return;

            // 在资源加速器周围显示粒子效果 - 直接使用字段radius而不是硬编码值
            logDebug("显示粒子效果，半径: " + radius);
            for (double angle = 0; angle < Math.PI * 2; angle += Math.PI / 16) {
                double x = location.getX() + Math.cos(angle) * radius;
                double z = location.getZ() + Math.sin(angle) * radius;

                Location particleLoc = new Location(world, x, location.getY() + 0.5, z);
                world.spawnParticle(Particle.REDSTONE, particleLoc, 1, new Particle.DustOptions(Color.RED, 1.0f));
            }

            // 在受影响的资源生成点上显示粒子效果
            for (Spawner spawner : affectedSpawners) {
                // 获取生成点位置
                XYZ spawnerXYZ = spawner.getLocation();
                Location spawnerLoc = new Location(world, spawnerXYZ.getX(), spawnerXYZ.getY(), spawnerXYZ.getZ());
                spawnerLoc.add(0.5, 1.0, 0.5);

                // 根据资源类型显示不同颜色的粒子
                Color color;
                String typeName = spawner.getDropType().getName();

                if (typeName.equalsIgnoreCase("iron")) {
                    color = Color.WHITE;
                } else if (typeName.equalsIgnoreCase("gold")) {
                    color = Color.YELLOW;
                } else if (typeName.equalsIgnoreCase("emerald")) {
                    color = Color.GREEN;
                } else if (typeName.equalsIgnoreCase("diamond")) {
                    color = Color.AQUA;
                } else {
                    color = Color.RED;
                }

                world.spawnParticle(Particle.REDSTONE, spawnerLoc, 3, 0.2, 0.2, 0.2, 0,
                        new Particle.DustOptions(color, 1.0f));
            }
        }

        // 创建全息显示
        private void createHologram() {
            World world = location.getWorld();
            if (world == null)
                return;

            // 获取世界存储
            WorldStorage worldStorage = BedwarsAPI.getWorldStorage(world);
            if (worldStorage == null)
                return;

            // 创建全息显示的位置（在资源加速器上方）
            Location holoLoc = location.clone().add(0.5, 1.5, 0.5);

            // 使用 MBedwars API 创建全息显示
            try {
                this.hologram = worldStorage.spawnHologram(HologramSkinType.HOLOGRAM, holoLoc);
                logDebug("创建全息显示成功，使用HOLOGRAM类型");
            } catch (Exception e) {
                logWarning("使用HOLOGRAM类型创建全息显示失败，错误：" + e.getMessage());
                
                // 尝试使用其他类型
                try {
                    // 如果HOLOGRAM类型失败，尝试使用ARMOR_STAND类型
            this.hologram = worldStorage.spawnHologram(HologramSkinType.ARMOR_STAND, holoLoc);
                    logDebug("创建全息显示成功，使用ARMOR_STAND类型");
                } catch (Exception e2) {
                    logError("创建全息显示失败，无法创建全息显示: " + e2.getMessage());
                    // 无法创建全息显示，将继续运行但没有全息显示
                }
            }

            // 更新全息显示文本
            updateHologram();
        }

        // 更新全息显示
        private void updateHologram() {
            if (this.hologram != null && this.hologram.exists()) {
                // 设置显示文本，包含剩余时间
                String displayText = "§a资源加速器 §e剩余: §c" + remainingSeconds + " §e秒!";
                this.hologram.setDisplayName(displayText);

                // 每10秒输出一次调试信息，或者当剩余时间少于5秒时每秒输出
                if (remainingSeconds % 10 == 0 || remainingSeconds <= 5) {
                    logDebug("更新资源加速器全息显示: " + displayText);
                }
            } else {
                // 如果全息图不存在或已被移除，尝试重新创建
                logWarning("全息图已失效，尝试重新创建");
                createHologram();
            }
        }

        // 取消加速效果
        public void cancel() {
            logDebug("取消资源加速器效果");
            
            // 取消所有生成监听任务
            for (BukkitTask task : spawnerTasks.values()) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            spawnerTasks.clear();
            
            // 取消所有额外任务
            for (List<BukkitTask> tasks : additionalTasks.values()) {
                for (BukkitTask task : tasks) {
                    if (task != null && !task.isCancelled()) {
                        task.cancel();
                    }
                }
            }
            additionalTasks.clear();
            
            // 标记为已过期，防止监控任务重新放置红石火把
            isExpired = true;
            
            // 停止监控任务
            if (monitoringTask != null && !monitoringTask.isCancelled()) {
                monitoringTask.cancel();
                logDebug("已停止红石火把监控任务");
            }

            // 恢复原始生成速率
            restoreOriginalRates();

            // 取消任务
            if (particleTask != null && !particleTask.isCancelled()) {
                particleTask.cancel();
            }

            if (expirationTask != null && !expirationTask.isCancelled()) {
                expirationTask.cancel();
            }

            if (hologramUpdateTask != null && !hologramUpdateTask.isCancelled()) {
                hologramUpdateTask.cancel();
            }

            // 移除全息显示
            if (hologram != null && hologram.exists()) {
                hologram.remove();
            }

            // 直接移除红石火把 - 不使用API，直接设置方块类型
            removeRedstoneTorch();

            // 播放取消音效
            World world = location.getWorld();
            if (world != null) {
                world.playSound(location, Sound.BLOCK_REDSTONE_TORCH_BURNOUT, 1.0f, 1.0f);
                world.playSound(location, Sound.BLOCK_BEACON_DEACTIVATE, 1.0f, 1.0f);

                // 添加粒子效果表示结束
                world.spawnParticle(Particle.SMOKE_NORMAL, location.clone().add(0.5, 0.5, 0.5), 20, 0.3, 0.3,
                        0.3, 0.05);
            }
            
            // 通知玩家资源加速器已过期
            if (owner != null && owner.isOnline()) {
                owner.sendMessage("§c你的资源加速器已手动移除，资源生成速度已恢复正常！");
            }
            
            // 通知附近的玩家资源生成速度已恢复
            notifyNearbyPlayers("§a附近的资源生成速度已恢复正常！");
        }

        // 添加可靠的红石火把移除方法
        private void removeRedstoneTorch() {
            // 检查原始位置
            Block block = location.getBlock();
            boolean removed = false;
            
            // 确保方块被标记为非玩家放置，这样MBedWars可以正确处理它
            try {
                // 找到所在的竞技场
                    for (Arena arena : GameAPI.get().getArenas()) {
                        if (arena.getGameWorld() != null && arena.getGameWorld().equals(location.getWorld())) {
                        // 标记为非玩家放置
                        arena.setBlockPlayerPlaced(block, false);
                        break;
                    }
                }
                            } catch (Exception e) {
                logWarning("处理方块标记时出错: " + e.getMessage());
            }
            
            // 检查并移除原始位置的红石火把
            if (block.getType() == Material.REDSTONE_TORCH) {
                block.setType(Material.AIR);
                logDebug("已移除原始位置的红石火把");
                removed = true;
            }
            
            // 如果原始位置没有红石火把，检查周围位置
            if (!removed) {
                logDebug("在原始位置未找到红石火把，检查周围位置");
                // 检查周围方向的方块 - 只检查基本方向避免特殊位置
                BlockFace[] faces = { 
                    BlockFace.UP, BlockFace.DOWN, 
                    BlockFace.NORTH, BlockFace.EAST, BlockFace.SOUTH, BlockFace.WEST
                };
                
                for (BlockFace face : faces) {
                    Block nearbyBlock = location.getBlock().getRelative(face);
                    
                    // 先标记为非玩家放置
                    try {
                        for (Arena arena : GameAPI.get().getArenas()) {
                            if (arena.getGameWorld() != null && arena.getGameWorld().equals(location.getWorld())) {
                                arena.setBlockPlayerPlaced(nearbyBlock, false);
                            break;
                        }
                    }
                } catch (Exception e) {
                        logWarning("处理周围方块标记时出错: " + e.getMessage());
                    }
                    
                    if (nearbyBlock.getType() == Material.REDSTONE_TORCH) {
                        nearbyBlock.setType(Material.AIR);
                        logDebug("已移除" + face + "方向的红石火把");
                        removed = true;
                    }
                }
            }
            
            if (!removed) {
                logWarning("未找到任何红石火把可移除");
            }
        }

        // 通知附近的玩家信息
        private void notifyNearbyPlayers(String message) {
            if (location.getWorld() == null) return;
            
            // 获取方块位置附近的玩家(10格范围内)
            for (Player player : location.getWorld().getPlayers()) {
                if (player.getLocation().distance(location) <= 10) {
                    player.sendMessage(message);
                }
            }
        }

        // 获取生成器内部时钟的辅助方法
        private int getInternalClock(Spawner spawner) {
            try {
                // 反射获取内部时钟字段
                java.lang.reflect.Method method = spawner.getClass().getMethod("getInternalClock");
                return (int) method.invoke(spawner);
                } catch (Exception e) {
                // 如果找不到方法，返回随机值，保证每次调用都不同，使检测逻辑仍然能工作
                logWarning("无法获取生成器内部时钟: " + e.getMessage());
                return (int) (System.currentTimeMillis() % 1000);
            }
        }
        
        // 获取生成器剩余时间的辅助方法
        private int getRemainingNextDropTime(Spawner spawner) {
            try {
                // 反射获取剩余时间字段
                java.lang.reflect.Method method = spawner.getClass().getMethod("getRemainingNextDropTime");
                return (int) method.invoke(spawner);
            } catch (Exception e) {
                // 如果找不到方法，使用估计值
                logWarning("无法获取生成器剩余时间: " + e.getMessage());
                double duration = spawner.getCurrentDropDuration();
                return (int) (duration * 20) / 2; // 返回约一半的时间作为估计
            }
        }

        // 辅助方法：记录调试日志
        private void logDebug(String message) {
            if (ResourceBoosterHandler.this.enableDebugLog) {
                plugin.getLogger().info("[资源加速器] " + message);
            }
        }
        
        // 辅助方法：记录警告日志（无论开关如何都记录）
        private void logWarning(String message) {
            plugin.getLogger().warning("[资源加速器] " + message);
        }
        
        // 辅助方法：记录错误日志（无论开关如何都记录）
        private void logError(String message) {
            plugin.getLogger().severe("[资源加速器] " + message);
        }
    }

    private class Session extends SpecialItemUseSession {

        private ResourceBooster booster;

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }
        
        // 添加日志辅助方法
        private void logDebug(String message) {
            if (ResourceBoosterHandler.this.enableDebugLog) {
                plugin.getLogger().info("[资源加速器] " + message);
            }
        }
        
        private void logWarning(String message) {
            plugin.getLogger().warning("[资源加速器] " + message);
        }
        
        private void logError(String message) {
            plugin.getLogger().severe("[资源加速器] " + message);
        }

        @Override
        protected void handleStop() {
            // 检查是否是道具刚刚放置的情况，如果是则不取消
            if (this.booster != null && this.booster.remainingSeconds >= (defaultDuration - 1)) {
                // 道具刚刚放置（不到1秒），不执行取消操作
                logDebug("检测到道具刚刚放置，不执行取消操作");
                return;
            }
            
            // 如果会话被强制停止，清理资源加速器
            if (this.booster != null) {
                logDebug("会话强制停止，取消资源加速器");
                this.booster.cancel();
                boosters.remove(this.booster.location);
            }
        }

        public void run() {
            Player player = getEvent().getPlayer();
            Block clickedBlock = getEvent().getClickedBlock();

            // 检查是否点击了方块
            if (clickedBlock == null) {
                player.sendMessage("§c请点击一个方块来放置资源加速器！");
                stop();
                return;
            }

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(player);
            if (arena == null) {
                player.sendMessage("§c你必须在游戏中才能使用此道具！");
                stop();
                return;
            }

            // 检查竞技场状态
            if (arena.getStatus() != ArenaStatus.RUNNING) {
                player.sendMessage("§c只能在游戏进行中使用此道具！");
                stop();
                return;
            }

            // 检查玩家是否已经放置了资源加速器
            UUID playerUUID = player.getUniqueId();
            if (playerBoosters.containsKey(playerUUID) && !playerBoosters.get(playerUUID).isEmpty()) {
                // 检查玩家的所有资源加速器是否仍然有效
                boolean hasActiveBooster = false;
                List<Location> toRemove = new ArrayList<>();

                for (Location loc : playerBoosters.get(playerUUID)) {
                    if (boosters.containsKey(loc)) {
                        hasActiveBooster = true;
                        ResourceBooster booster = boosters.get(loc);
                        int remainingTime = booster.remainingSeconds;
                        player.sendMessage("§c你已经放置了一个资源加速器，还剩余 §e" + remainingTime + " §c秒才能放置新的！");
                        break;
                    } else {
                        // 这个加速器已经不存在，标记为待移除
                        toRemove.add(loc);
                    }
                }

                // 清理已过期的加速器记录
                for (Location loc : toRemove) {
                    playerBoosters.get(playerUUID).remove(loc);
                }

                if (hasActiveBooster) {
                    stop();
                    return;
                }
            }

            // 获取配置
            int radius = defaultRadius; // 使用配置文件中的影响范围
            int duration = defaultDuration; // 使用配置文件中的持续时间
            
            // 输出当前使用的设置
            logDebug("使用影响范围: " + radius + ", 持续时间: " + duration);

            // 检查点击的方块是否为实心方块（可以插入火把）
            if (!clickedBlock.getType().isSolid()) {
                player.sendMessage("§c资源加速器必须放置在实心方块上！");
                stop();
                return;
            }

            // 获取玩家点击的方块面
            BlockFace clickedFace = getEvent().getClickedBlockFace() != null ? getEvent().getClickedBlockFace() : BlockFace.UP;
            
            // 验证这个面是否适合放置红石火把
            // 红石火把不能放在底面，必须是侧面或顶面
            if (clickedFace == BlockFace.DOWN) {
                player.sendMessage("§c不能在方块底部放置资源加速器！");
                stop();
                return;
            }

            // 根据点击的面确定放置位置
            Block placeBlock = clickedBlock.getRelative(clickedFace);
            Location placeLoc = placeBlock.getLocation();

            // 检查放置位置是否可用
            if (placeBlock.getType() != Material.AIR) {
                // 尝试其他位置
                BlockFace[] faces = { BlockFace.UP, BlockFace.NORTH, BlockFace.EAST, BlockFace.SOUTH, BlockFace.WEST };
                boolean foundPlace = false;
                
                for (BlockFace face : faces) {
                    if (face == clickedFace || face == BlockFace.DOWN) continue; // 跳过已检查的面和底面
                    
                    Block alternativeBlock = clickedBlock.getRelative(face);
                    if (alternativeBlock.getType() == Material.AIR) {
                        placeBlock = alternativeBlock;
                        placeLoc = placeBlock.getLocation();
                        clickedFace = face;
                        foundPlace = true;
                        break;
                    }
                }
                
                if (!foundPlace) {
                    player.sendMessage("§c无法在此处放置资源加速器！需要更多空间。");
                    stop();
                    return;
                }
            }
            
            // 确保这个位置可以放置红石火把
            final Block finalPlaceBlock = placeBlock;
            final Location finalPlaceLoc = placeLoc;
            final BlockFace finalFace = clickedFace;
            
            // 放置红石火把并设置其方向
            finalPlaceBlock.setType(Material.REDSTONE_TORCH);
            
            // 明确标记为玩家放置的方块，确保可以被玩家破坏
            arena.setBlockPlayerPlaced(finalPlaceBlock, true);
            
            // 记录红石火把被标记为玩家放置
            logDebug("红石火把已被标记为玩家放置的方块，可以被玩家破坏");
            
            // 先检查红石火把是否成功放置
            if (finalPlaceBlock.getType() != Material.REDSTONE_TORCH) {
                logError("红石火把放置失败，取消操作");
                player.sendMessage("§c资源加速器放置失败，请尝试在其他位置放置！");
                stop();
                                    return;
                                }

            logDebug("已将红石火把放置在位置: " + finalPlaceLoc);
            
            // 创建资源加速器 - 必须在设置监控任务之前创建
            this.booster = new ResourceBooster(finalPlaceLoc, radius, duration, player);
            boosters.put(finalPlaceLoc, this.booster);
            
            // 输出调试信息 - 再次确认配置是否正确传递
            logDebug("放置资源加速器完成：");
            logDebug("- 使用半径: " + radius);
            logDebug("- 持续时间: " + duration);
            logDebug("- 配置: 铁" + resourceIntervals.get("iron") + "秒" + resourceAmounts.get("iron") + "个");
            
            // 设置持续检查红石火把是否存在的任务
            final BukkitTask[] monitoringTask = new BukkitTask[1];
            monitoringTask[0] = new BukkitRunnable() {
                int attempts = 0;
                int maxAttempts = 5;
                
                                    @Override
                                    public void run() {
                    // 检查红石火把是否存在
                    if (finalPlaceBlock.getType() != Material.REDSTONE_TORCH) {
                        // 检查资源加速器是否已过期，如果过期就不再重新放置
                        if (booster != null && booster.isExpired) {
                            logDebug("资源加速器已过期，不再重新放置红石火把");
                            cancel();
                            return;
                        }
                        
                        logDebug("检测到红石火把消失，尝试重新放置 (尝试 " + (attempts+1) + "/" + maxAttempts + ")");
                        
                        // 再次标记和放置
                        arena.setBlockPlayerPlaced(finalPlaceBlock, true);
                        finalPlaceBlock.setType(Material.REDSTONE_TORCH);
                        arena.setBlockPlayerPlaced(finalPlaceBlock, true);
                        
                        attempts++;
                        
                        // 如果多次尝试失败，取消并通知玩家
                        if (attempts >= maxAttempts) {
                            logError("无法保持红石火把放置，放弃尝试");
                            player.sendMessage("§c警告：资源加速器可能无法正常工作，请尝试放置在其他位置。");
                            cancel();
                        }
                    } else {
                        // 火把存在，检查成功
                        logDebug("红石火把检查正常，放置成功");
                        cancel();
                    }
                }
            }.runTaskTimer(plugin, 5L, 10L);
            
            // 保存监控任务引用到ResourceBooster对象中，便于之后取消
            if (this.booster != null) {
                this.booster.monitoringTask = monitoringTask[0];
            }
            
            // 10秒后停止监控任务
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                    if (monitoringTask[0] != null && !monitoringTask[0].isCancelled()) {
                        monitoringTask[0].cancel();
                        logDebug("停止红石火把监控任务");
                    }
                }
            }.runTaskLater(plugin, 200L);

            // 记录玩家的资源加速器
            if (!playerBoosters.containsKey(player.getUniqueId())) {
                playerBoosters.put(player.getUniqueId(), new ArrayList<>());
            }
            playerBoosters.get(player.getUniqueId()).add(finalPlaceLoc);

            // 播放放置音效
            player.getWorld().playSound(finalPlaceLoc, Sound.BLOCK_REDSTONE_TORCH_BURNOUT, 1.0f, 0.5f);
            player.getWorld().playSound(finalPlaceLoc, Sound.BLOCK_BEACON_ACTIVATE, 0.5f, 1.2f);

            // 显示成功消息
            int affectedCount = this.booster.affectedSpawners.size();
            player.sendMessage("§a成功放置资源加速器！");
            player.sendMessage("§a影响了 " + affectedCount + " 个资源生成点，持续时间: " + duration + "秒");

            // 消耗道具
            takeItem();

            // 结束会话
            stop();
        }
    }

    // 添加竞技场状态变化监听逻辑
    private void registerArenaStateListener() {
        // 监听竞技场状态变化，在游戏结束前清理所有资源加速器
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onArenaStatusChange(de.marcely.bedwars.api.event.arena.ArenaStatusChangeEvent event) {
                Arena arena = event.getArena();
                ArenaStatus newStatus = event.getNewStatus();
                
                // 如果竞技场将要结束或重置
                if (newStatus == ArenaStatus.RESETTING) {
                    logDebug("竞技场 " + arena.getName() + " 即将重置，清理所有资源加速器");
                    
                    // 清理该竞技场中的所有资源加速器
                    cleanupAllBoostersInArena(arena);
                }
            }
        }, plugin);
    }

    // 清理指定竞技场中的所有资源加速器
    private void cleanupAllBoostersInArena(Arena arena) {
        World gameWorld = arena.getGameWorld();
        if (gameWorld == null)
            return;
        
        // 创建要移除的加速器位置列表
        List<Location> boostersToRemove = new ArrayList<>();
        
        // 查找当前竞技场中的所有资源加速器
        for (Map.Entry<Location, ResourceBooster> entry : boosters.entrySet()) {
            Location boosterLoc = entry.getKey();
            
            // 检查位置是否在当前竞技场世界
            if (boosterLoc.getWorld().equals(gameWorld)) {
                boostersToRemove.add(boosterLoc);
            }
        }
        
        // 移除找到的所有资源加速器
        for (Location loc : boostersToRemove) {
            ResourceBooster booster = boosters.get(loc);
            if (booster != null) {
                logDebug("游戏结束，清理位于 " + loc + " 的资源加速器");
                booster.cancel();  // 这会调用removeRedstoneTorch()方法
            }
        }
    }
}
