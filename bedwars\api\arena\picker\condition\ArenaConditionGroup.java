package de.marcely.bedwars.api.arena.picker.condition;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.tools.Validate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * A collection that persists of ArenaCondition to which we are applying logical conjunctions.
 * <p>
 *     This ArenaCondition type only does either AND or OR operations.
 * </p>
 */
public class ArenaConditionGroup extends ArenaCondition {

  private static final byte OPERATION_AND = 0;
  private static final byte OPERATION_OR = 1;

  private final List<ArenaCondition> childrens;
  private byte operation = OPERATION_AND;

  public ArenaConditionGroup() {
    this(new ArrayList<>());
  }

  public ArenaConditionGroup(List<ArenaCondition> childs) {
    Validate.notNull(childs, "childs");

    this.childrens = childs;
  }

  /**
   * Gets all childrens that have been aded.
   * <p>
   *     The returned collection is by default fully mutable.<br>
   *     It may not only be in case an immutable type has been passed with {@link #ArenaConditionGroup(List)}:
   * </p>
   *
   * @return A collection with all the childs added to this group
   */
  public List<ArenaCondition> getChildrens() {
    return this.childrens;
  }

  /**
   * Whether all childrens are being checked using the AND operator.
   *
   * @return Whether AND is currently defined as the operator. <code>true</code> if it is
   */
  public boolean isAND() {
    return this.operation == OPERATION_AND;
  }

  /**
   * Whether all childrens are being checked using the OR operator.
   *
   * @return Whether OR is currently defined as the operator. <code>true</code> if it is
   */
  public boolean isOR() {
    return this.operation == OPERATION_OR;
  }

  /**
   * Use the AND operator.
   */
  public void setAND() {
    this.operation = OPERATION_AND;
  }

  /**
   * Use the OR operator.
   */
  public void setOR() {
    this.operation = OPERATION_OR;
  }

  /**
   * Serializes a string representation that may as well be used to deserialize/parse it.
   * <p>
   *   Root groups use [ ] as their outer brackets, while inner groups use ( )
   * </p>
   *
   * @param isRoot Whether this group is the root group
   * @return A string representation of this group
   * @see de.marcely.bedwars.api.arena.picker.ArenaPickerAPI#parseCondition(String)
   */
  public String serialize(boolean isRoot) {
    final StringBuilder builder = new StringBuilder();

    builder.append(isRoot ? '[' : '(');

    for (int i=0; i<this.childrens.size(); i++) {
      final ArenaCondition child = this.childrens.get(i);

      // each child
      if (child instanceof ArenaConditionGroup)
        builder.append(((ArenaConditionGroup) child).serialize(false));
      else {
        final ArenaConditionComparative comp = (ArenaConditionComparative) child;

        builder.append(comp.getLeftInput().toString());
        builder.append(comp.getComparisonOperator().getUsage());
        builder.append(comp.getRightInput().toString());
      }

      // seperator
      if (i != this.childrens.size()-1) {
        builder.append(" ");
        builder.append(isOR() ? '|' : '&');
        builder.append(" ");
      }
    }

    builder.append(isRoot ? ']' : ')');

    return builder.toString();
  }

  @Override
  public boolean check(Arena arena) {
    Validate.notNull(arena, "arena");

    if (this.childrens.isEmpty())
      return true;

    for (ArenaCondition child : this.childrens) {
      if (child.check(arena)) {
        if (this.operation == OPERATION_OR)
          return true;

      } else {
        if (this.operation == OPERATION_AND)
          return false;
      }
    }

    return this.operation == OPERATION_AND;
  }

  @Override
  public boolean check(RemoteArena arena) {
    Validate.notNull(arena, "arena");

    if (this.childrens.isEmpty())
      return true;

    for (ArenaCondition child : this.childrens) {
      if (child.check(arena)) {
        if (this.operation == OPERATION_OR)
          return true;

      } else {
        if (this.operation == OPERATION_AND)
          return false;
      }
    }

    return this.operation == OPERATION_AND;
  }

  @Override
  public String toString() {
    final StringBuilder builder = new StringBuilder();

    builder.append("(operation=");
    builder.append(isOR() ? "OR" : "AND");
    builder.append(", childrens=[");
    builder.append(String.join(",", this.childrens.stream()
        .map(Object::toString)
        .collect(Collectors.toList())));
    builder.append("])");

    return builder.toString();
  }
}
