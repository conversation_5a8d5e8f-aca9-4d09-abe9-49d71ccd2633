package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.QuitPlayerMemory;
import de.marcely.bedwars.api.arena.RejoinPlayerCause;
import de.marcely.bedwars.api.arena.RejoinPlayerIssue;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

import java.util.Set;

/**
 * Player attempts to get added back to an arena after having quit it in the past.
 * <p>
 *   Commonly, this is used when a player attempts to rejoin a running game.
 *   There is a special case however, and that is specators getting added into an already ended game, see {@link RejoinPlayerCause#END_LOBBY}.
 * </p>
 */
public class PlayerRejoinArenaEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final RejoinPlayerCause cause;
  private final QuitPlayerMemory memory;
  private final Set<RejoinPlayerIssue> issues;

  public PlayerRejoinArenaEvent(Player player, Arena arena, RejoinPlayerCause cause, @Nullable QuitPlayerMemory memory, Set<RejoinPlayerIssue> issues) {
    super(player);

    this.arena = arena;
    this.cause = cause;
    this.memory = memory;
    this.issues = issues;
  }

  /**
   * Returns the cause why the player is rejoining the arena.
   *
   * @return The cause
   */
  public RejoinPlayerCause getCause() {
    return this.cause;
  }

  /**
   * Returns the memory of the player that contains all info needed to successfully make him rejoin the arena.
   * <p>
   *     It's possible that it's <code>null</code> as no memory was found for the player.
   * </p>
   *
   * @return The memory of the player. Possibly <code>null</code>
   */
  public @Nullable QuitPlayerMemory getMemory() {
    return this.memory;
  }

  /**
   * Returns if the player has any issues rejoining the arena.
   *
   * @return If there are issues which cause the player not being able to rejoin the arena
   */
  public boolean hasIssues() {
    return !this.issues.isEmpty();
  }

  /**
   * Returns all issues there are for the player to rejoin the arena.
   *
   * @return The issues why the player can't rejoin the arena
   */
  public Set<RejoinPlayerIssue> getIssues() {
    return this.issues;
  }

  /**
   * Adds an issue which causes the player to not be able to rejoin the arena.
   *
   * @param issue The issue that shall be added
   * @return <code>false</code> if that issue already existed before
   */
  public boolean addIssue(RejoinPlayerIssue issue) {
    Validate.notNull(issue, "issue");

    return this.issues.add(issue);
  }

  /**
   * Removes an issue. If all were removed the player will be able to rejoin the arena.
   *
   * @param issue The issue that shall be removed
   * @return <code>true</code> if it has been removed
   */
  public boolean removeIssue(RejoinPlayerIssue issue) {
    Validate.notNull(issue, "issue");

    return this.issues.remove(issue);
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
