package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.BedDestructionInfo;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.tools.Validate;
import de.marcely.bedwars.tools.location.ImmutableLocation;
import java.util.function.Function;
import lombok.Getter;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a player breaks a bed during a game
 */
public class ArenaBedBreakEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Player player;
  private final Team team;
  private final ImmutableLocation bedLocation;
  private final BedDestructionInfo.Cause cause;

  private String altDisplayName;
  private ArenaBedBreakEvent.Result result;
  private boolean playSound, sendTitle, sendMessage;
  private Function<Player, Message> messageFunction;

  public ArenaBedBreakEvent(
      Arena arena,
      @Nullable Player player,
      String altDisplayName,
      Team team,
      Location bedLocation,
      BedDestructionInfo.Cause cause,
      ArenaBedBreakEvent.Result result,
      boolean playSound,
      boolean sendTitle,
      boolean sendMessage,
      Function<Player, Message> messageFunction) {

    this.arena = arena;
    this.player = player;
    this.altDisplayName = altDisplayName;
    this.team = team;
    this.bedLocation = new ImmutableLocation(bedLocation);
    this.cause = cause;
    this.result = result;
    this.playSound = playSound;
    this.sendTitle = sendTitle;
    this.sendMessage = sendMessage;
    this.messageFunction = messageFunction;
  }

  /**
   * Returns the team who own the bed.
   *
   * @return The team who own the bed
   */
  public Team getTeam() {
    return this.team;
  }

  /**
   * Returns the location of the broken bed.
   *
   * @return The location of the broken bed
   */
  public ImmutableLocation getBedLocation() {
    return this.bedLocation;
  }

  /**
   * Returns the specific cause of the bed destruction.
   *
   * @return The cause or action of the bed destruction
   */
  public BedDestructionInfo.Cause getCause() {
    return this.cause;
  }

  /**
   * Returns what shall happen after the event.
   *
   * @return The result of the event
   */
  public ArenaBedBreakEvent.Result getResult() {
    return this.result;
  }

  /**
   * Set what should happen after the event.
   *
   * @param result The new result
   */
  public void setResult(ArenaBedBreakEvent.Result result) {
    Validate.notNull(result, "result");

    this.result = result;
  }

  /**
   * Get whether a chat message shall be sent.
   *
   * @return <code>true</code> if a message shall be sent to all players
   * @see #setSendingChatMessage(boolean)
   * @see #getChatMessageFunction()
   */
  public boolean isSendingChatMessage() {
    return this.sendMessage;
  }

  /**
   * Define whether a chat message shall be sent.
   *
   * @param sendMessage <code>true</code> if a message shall be sent to all players
   * @see #isSendingChatMessage()
   * @see #setChatMessageFunction(Function)
   */
  public void setSendingChatMessage(boolean sendMessage) {
    this.sendMessage = sendMessage;
  }

  /**
   * Get the function that is used to obtain the message that shall be sent to each player.
   * <p>
   *   Note that it's not being used in case {@link #isSendingChatMessage()} returns false.
   * </p>
   *
   * @return A function that is used for the chat message process
   */
  public Function<Player, Message> getChatMessageFunction() {
    return this.messageFunction;
  }

  /**
   * Set the function that is used to obtain the message that shall be sent to each player.
   * <p>
   *   Note that it's not being used in case {@link #isSendingChatMessage()} returns false.
   * </p>
   *
   * @param function A function that is used for the chat message process
   */
  public void setChatMessageFunction(Function<Player, Message> function) {
    Validate.notNull(function, "functcion");

    this.messageFunction = function;
  }

  /**
   * Set a constant message that is equal for all players.
   * <p>
   *   Basically calls {@link #setChatMessageFunction(Function)} with a non-upcyable message instance.
   *   Note that it's not being used in case {@link #isSendingChatMessage()} returns false.
   * </p>
   *
   * @param message The new message
   */
  public void setChatMessage(Message message) {
    Validate.notNull(message, "message");

    final Message cMsg = message.cloneNonUpcyable();

    this.messageFunction = player -> cMsg;
  }

  /**
   * Get whether a title message shall be sent.
   *
   * @return <code>true</code> if a title shall be sent to all players
   * @see <a href="https://minecraft.wiki/w/Commands/title">Minecraft Wiki</a>
   */
  public boolean isSendingTitle() {
    return this.sendTitle;
  }

  /**
   * Define whether a title message shall be sent.
   *
   * @param sendTitle <code>true</code> if a title shall be sent to all players
   * @see <a href="https://minecraft.wiki/w/Commands/title">Minecraft Wiki</a>
   */
  public void setSendingTitle(boolean sendTitle) {
    this.sendTitle = sendTitle;
  }


  /**
   * Whether the bed break sound is being played.
   *
   * @return If the event is plying the bed break sound.
   */
  public boolean isPlayingSound() {
    return this.playSound;
  }

  /**
   * Define whether the bed break sound shall be played.
   *
   * @param playSound Whether the bed break sound shall be played.
   */
  public void setPlayingSound(boolean playSound) {
    this.playSound = playSound;
  }

  /**
   * It's possible that beds get broken without the influence of a player.
   * E.g. a plugin or a creeper could've broken the bed.
   * Use this method to check whether if effectively the player caused for the bed to get broken.
   *
   * @return Whether a player has caused the bed to get broken
   */
  public boolean isPlayerCaused() {
    return this.player != null;
  }

  /**
   * Returns the player who broke the bed.
   * <p>
   * Keep in mind that it doesn't always have to be a player who broke the bed.
   * E.g. a plugin or a creeper could've broken the bed. In this case it'll return null.
   * Use {@link #isPlayerCaused()} to check whether there's a player who caused this to happen.
   *
   * @return The player who broke the bed or <code>null</code> when there isn't one
   */
  @Nullable
  public Player getPlayer() {
    return this.player;
  }

  /**
   * Will show this to the player as the cause that broke the bed.
   * Priorities this over the player's name (if there's even one).
   *
   * @return The name of the cause
   */
  public String getAltDisplayName() {
    return this.altDisplayName;
  }

  /**
   * Will show this to the player as the cause that broke the bed.
   * Priorities this over the player's name (if there's even one).
   *
   * @param altDisplayName The new name for the cause
   */
  public void setAltDisplayName(String altDisplayName) {
    Validate.notNull(altDisplayName, "altDisplayName");

    this.altDisplayName = altDisplayName;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }



  /**
   * Use it to declare what's supposed to happen next.
   */
  public enum Result {

    /**
     * Continue as normal.
     * This includes block breaking, broadcasts (if i.a. {@link ArenaBedBreakEvent#isPlayingSound()} etc. is set), internal state setting etc...
     */
    DONT_CANCEL,

    /**
     * Does everything as {@link #DONT_CANCEL}, only doesn't update the internal state.
     * Meaning that bed theoretically still isn't broken, but visually it looks like it is.
     */
    IGNORE_STATE_UPDATE,

    /**
     * Act like the player didn't break the block
     */
    CANCEL
  }
}
