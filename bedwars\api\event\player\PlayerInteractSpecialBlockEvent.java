package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.world.block.SpecialBlock;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a player tries to interact with a {@link SpecialBlock}
 */
public class PlayerInteractSpecialBlockEvent extends PlayerEvent implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final SpecialBlock block;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerInteractSpecialBlockEvent(Player player, SpecialBlock block) {
    super(player);

    this.block = block;
  }

  /**
   * Get the special block the player tried to interact with.
   *
   * @return The special block that is involved in this event
   */
  public SpecialBlock getBlock() {
    return this.block;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
