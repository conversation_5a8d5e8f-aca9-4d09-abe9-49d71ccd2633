package de.marcely.bedwars.tools.gui;

import de.marcely.bedwars.tools.Validate;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * Represents an interactable item that can be added to a {@link GUI}
 */
public class GUIItem implements Cloneable {

  private final ItemStack item;
  private final ClickListener listener;

  private Object attachement;

  public GUIItem(ItemStack is, ClickListener listener) {
    this(is, listener, null);
  }

  public GUIItem(ItemStack is, ClickListener listener, @Nullable Object attachement) {
    Validate.notNull(is, "is");
    Validate.notNull(listener, "listener");

    this.item = is;
    this.listener = listener;
    this.attachement = attachement;
  }

  /**
   * Get the item that will be displayed in the inventory.
   *
   * @return The item that this GUIItem wrap around
   */
  public ItemStack getItem() {
    return this.item;
  }

  /**
   * Get the click listener of this item.
   *
   * @return The click listener
   */
  public ClickListener getListener() {
    return this.listener;
  }

  /**
   * Get an optional attachment to this item.
   * <p>
   *   May hold additional useful information depending on the context.
   * </p>
   *
   * @return The optional attachment (maybe <code>null</code>)
   */
  @Nullable
  public Object getAttachement() {
    return this.attachement;
  }

  /**
   * Set the optional attachment of this item.
   * <p>
   *   May hold additional useful information depending on the context.
   * </p>
   *
   * @param attachement The optional attachment (maybe <code>null</code>)
   */
  public void setAttachement(@Nullable Object attachement) {
    this.attachement = attachement;
  }

  @Override
  public GUIItem clone() {
    final GUIItem item = new GUIItem(
        this.item.clone(),
        listener);

    item.attachement = this.attachement;

    return item;
  }
}