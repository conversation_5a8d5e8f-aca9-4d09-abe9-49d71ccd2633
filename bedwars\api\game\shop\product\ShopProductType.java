package de.marcely.bedwars.api.game.shop.product;

import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import org.jetbrains.annotations.Nullable;

public enum ShopProductType {

  /**
   * Represents an ItemStack
   */
  ITEM,

  /**
   * Represents a {@link SpecialItem}
   */
  SPECIAL_ITEM,

  /**
   * Represents a {@link DropType}
   */
  SPAWNER_ITEM,

  /**
   * Represents a command that will be executed on purchase
   */
  COMMAND;

  private static final ShopProductType[] VALUES = values();

  private final String id;

  ShopProductType() {
    this.id = name().toLowerCase().replace("_", "-");
  }

  /**
   * Returns the id of this type that is being used to identify it in e.g. the shop config file.
   *
   * @return The of this product type
   */
  public String getId() {
    return this.id;
  }

  /**
   * Tries to look up for a product type with a specific id.
   *
   * @param id The id that we want to match
   * @return The given product. <code>null</code> if we couldn't match any
   */
  @Nullable
  public static ShopProductType getById(String id) {
    for (ShopProductType type : VALUES) {
      if (type.getId().equalsIgnoreCase(id))
        return type;
    }

    return null;
  }
}
