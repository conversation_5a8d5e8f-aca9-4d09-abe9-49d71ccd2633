package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 蜘蛛网陷阱事件
 * 被3*3的蜘蛛网黏住
 */
public class CobwebTrapEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private FileConfiguration config;
    private final List<Location> placedCobwebs = new ArrayList<>();
    
    public CobwebTrapEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(badDir, "cobweb_trap.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/cobweb_trap.yml", false);
                plugin.getLogger().info("已生成蜘蛛网陷阱事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载蜘蛛网陷阱事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载蜘蛛网陷阱事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public String getName() {
        return "COBWEB_TRAP";
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 30) : 30;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }
        
        // 获取配置参数
        int gridSize = config != null ? config.getInt("cobweb_trap.grid_size", 3) : 3;
        int heightLayers = config != null ? config.getInt("cobweb_trap.height_layers", 2) : 2;
        boolean placeAtFeet = config != null ? config.getBoolean("cobweb_trap.place_at_feet", true) : true;
        boolean placeAboveHead = config != null ? config.getBoolean("cobweb_trap.place_above_head", true) : true;
        int lifetime = config != null ? config.getInt("cobweb_trap.lifetime", 30) : 30;
        
        // 获取竞技场
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        if (arena == null) {
            plugin.getLogger().warning("玩家不在竞技场中，无法执行蜘蛛网陷阱事件");
            return;
        }
        
        // 放置蜘蛛网陷阱
        placeCobwebTrap(player, arena, gridSize, heightLayers, placeAtFeet, placeAboveHead);
        
        // 应用额外效果
        applyAdditionalEffects(player);
        
        // 设置清理时间
        if (lifetime > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    cleanupCobwebs();
                }
            }.runTaskLater(plugin, lifetime * 20L);
        }
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了蜘蛛网陷阱事件");
    }
    
    /**
     * 放置蜘蛛网陷阱
     */
    private void placeCobwebTrap(Player player, Arena arena, int gridSize, int heightLayers, 
                               boolean placeAtFeet, boolean placeAboveHead) {
        Location playerLoc = player.getLocation();
        int halfGrid = gridSize / 2;
        
        for (int x = -halfGrid; x <= halfGrid; x++) {
            for (int z = -halfGrid; z <= halfGrid; z++) {
                // 在脚下放置
                if (placeAtFeet) {
                    for (int y = 0; y < heightLayers; y++) {
                        Location cobwebLoc = playerLoc.clone().add(x, y, z);
                        placeCobweb(cobwebLoc, arena);
                    }
                }
                
                // 在头上放置
                if (placeAboveHead) {
                    for (int y = 1; y <= heightLayers; y++) {
                        Location cobwebLoc = playerLoc.clone().add(x, y + 1, z);
                        placeCobweb(cobwebLoc, arena);
                    }
                }
            }
        }
    }
    
    /**
     * 放置单个蜘蛛网
     */
    private void placeCobweb(Location location, Arena arena) {
        Block block = location.getBlock();
        
        // 只在空气方块中放置蜘蛛网
        if (block.getType().isAir()) {
            block.setType(Material.COBWEB);
            
            // 标记为玩家放置的方块（如果支持）
            if (GameAPI.get().isPlayerBlockMarkingSupported()) {
                arena.setBlockPlayerPlaced(block, true);
            }
            
            // 记录蜘蛛网位置
            placedCobwebs.add(location);
        }
    }
    
    /**
     * 应用额外效果
     */
    private void applyAdditionalEffects(Player player) {
        if (config == null) return;
        
        // 缓慢效果
        if (config.getBoolean("cobweb_trap.additional_effects.slowness.enabled", true)) {
            int level = config.getInt("cobweb_trap.additional_effects.slowness.level", 2);
            int duration = config.getInt("cobweb_trap.additional_effects.slowness.duration", 200);
            
            PotionEffect slowness = new PotionEffect(PotionEffectType.SLOW, duration, level - 1);
            player.addPotionEffect(slowness);
        }
        
        // 中毒效果
        if (config.getBoolean("cobweb_trap.additional_effects.poison.enabled", true)) {
            int level = config.getInt("cobweb_trap.additional_effects.poison.level", 1);
            int duration = config.getInt("cobweb_trap.additional_effects.poison.duration", 100);
            
            PotionEffect poison = new PotionEffect(PotionEffectType.POISON, duration, level - 1);
            player.addPotionEffect(poison);
        }
    }
    
    /**
     * 清理蜘蛛网
     */
    private void cleanupCobwebs() {
        for (Location cobwebLoc : placedCobwebs) {
            Block block = cobwebLoc.getBlock();
            if (block.getType() == Material.COBWEB) {
                block.setType(Material.AIR);
            }
        }
        placedCobwebs.clear();
    }
    
    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null) return true;
        return config.getBoolean("messages.send_message", true);
    }
    
    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null) return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }
    
    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null) return "§c你被蜘蛛网困住了！";
        return config.getString("messages.event_message", "§c你被蜘蛛网困住了！");
    }
}
