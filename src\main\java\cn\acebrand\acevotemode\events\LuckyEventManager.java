package cn.acebrand.acevotemode.events;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.good.*;
import cn.acebrand.acevotemode.events.bad.*;
import cn.acebrand.acevotemode.events.neutral.*;
import org.bukkit.entity.Player;
import org.bukkit.Location;

import java.util.*;

/**
 * 幸运方块事件管理器
 */
public class LuckyEventManager {

    private final AceVoteMode plugin;
    private final Map<EventType, List<LuckyEvent>> events;
    private final Random random;

    public LuckyEventManager(AceVoteMode plugin) {
        this.plugin = plugin;
        this.events = new HashMap<>();
        this.random = new Random();

        // 初始化事件列表
        for (EventType type : EventType.values()) {
            events.put(type, new ArrayList<>());
        }

        // 注册所有事件
        registerEvents();
    }

    /**
     * 注册所有事件
     */
    private void registerEvents() {
        // 注册好事件
        registerEvent(new ItemRewardEvent(plugin));
        registerEvent(new BuffEffectEvent(plugin));
        registerEvent(new ResourceBonusEvent(plugin));

        // 注册坏事件
        registerEvent(new LightningStrikeEvent(plugin));
        registerEvent(new CreeperSwarmEvent(plugin));
        registerEvent(new InstantExplosionEvent(plugin));
        registerEvent(new MonsterHordeEvent(plugin));
        registerEvent(new NegativeBuffEvent(plugin));
        registerEvent(new AnvilRainEvent(plugin));
        registerEvent(new CobwebTrapEvent(plugin));
        registerEvent(new LaunchPlayerEvent(plugin));
        registerEvent(new ConfiscateItemEvent(plugin));
        registerEvent(new IronCageEvent(plugin));
        registerEvent(new ObsidianCageEvent(plugin));
        registerEvent(new TntSpawnEvent(plugin));
        // 移除了重复的DebuffEffectEvent，保留NegativeBuffEvent

        // 注册中立事件
        registerEvent(new NothingHappensEvent(plugin));
        registerEvent(new ResourceClearEvent(plugin));
        registerEvent(new ResourceSpawnerMonsterEvent(plugin));

        plugin.getLogger().info("已注册 " + getTotalEventCount() + " 个幸运方块事件");
    }

    /**
     * 注册单个事件
     */
    private void registerEvent(LuckyEvent event) {
        events.get(event.getType()).add(event);
        plugin.getLogger().info("注册事件: " + event.getName() + " (" + event.getType() + ")");
    }

    /**
     * 触发随机事件
     */
    public void triggerRandomEvent(Player player, Location location) {
        // 首先确定事件类型
        EventType eventType = selectEventType();

        // 然后从该类型中选择具体事件
        LuckyEvent event = selectEvent(eventType);

        if (event != null) {
            plugin.getLogger().info("玩家 " + player.getName() + " 触发了事件: " + event.getName());
            event.execute(player, location);
        } else {
            plugin.getLogger().warning("未找到可用的 " + eventType + " 事件");
        }
    }

    /**
     * 根据概率选择事件类型
     */
    private EventType selectEventType() {
        int roll = random.nextInt(100);
        int cumulative = 0;

        for (EventType type : EventType.values()) {
            cumulative += type.getChance();
            if (roll < cumulative) {
                return type;
            }
        }

        // 默认返回坏事件（最高概率）
        return EventType.BAD;
    }

    /**
     * 从指定类型中选择具体事件
     */
    private LuckyEvent selectEvent(EventType type) {
        List<LuckyEvent> typeEvents = events.get(type);
        if (typeEvents.isEmpty()) {
            return null;
        }

        // 计算总权重
        int totalWeight = typeEvents.stream().mapToInt(LuckyEvent::getWeight).sum();
        if (totalWeight <= 0) {
            // 如果没有权重，随机选择
            return typeEvents.get(random.nextInt(typeEvents.size()));
        }

        // 根据权重选择
        int roll = random.nextInt(totalWeight);
        int cumulative = 0;

        for (LuckyEvent event : typeEvents) {
            cumulative += event.getWeight();
            if (roll < cumulative) {
                return event;
            }
        }

        // 默认返回第一个事件
        return typeEvents.get(0);
    }

    /**
     * 获取总事件数量
     */
    private int getTotalEventCount() {
        return events.values().stream().mapToInt(List::size).sum();
    }

    /**
     * 获取指定类型的事件数量
     */
    public int getEventCount(EventType type) {
        return events.get(type).size();
    }
}
