package de.marcely.bedwars.tools;

import java.util.Objects;

/**
 * Based on Java's BiConsumer with the difference that this class may permit the throwing of an Exception.
 *
 * @param <T> the type of the first argument to the operation
 * @param <U> the type of the second argument to the operation
 * @see java.util.function.BiConsumer
 */
@FunctionalInterface
public interface ThrowingBiConsumer<T, U> {

  /**
   * Performs this operation on the given argument.
   *
   * @param t the input argument
   * @param u the second input argument
   * @throws Exception in case an exception occurs while it is being run
   */
  void accept(T t, U u) throws Exception;

  /**
   * Returns a composed {@code BiConsumer} that performs, in sequence, this
   * operation followed by the {@code after} operation. If performing either
   * operation throws an exception, it is relayed to the caller of the
   * composed operation.  If performing this operation throws an exception,
   * the {@code after} operation will not be performed.
   *
   * @param after the operation to perform after this operation
   * @return a composed {@code BiConsumer} that performs in sequence this
   * operation followed by the {@code after} operation
   * @throws NullPointerException if {@code after} is null
   */
  default ThrowingBiConsumer<T, U> andThen(ThrowingBiConsumer<? super T, ? super U> after) {
    Objects.requireNonNull(after);

    return (l, r) -> {
      accept(l, r);
      after.accept(l, r);
    };
  }
}
