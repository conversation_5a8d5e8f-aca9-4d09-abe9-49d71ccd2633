package de.marcely.bedwars.api.arena.picker.condition;

import org.jetbrains.annotations.Nullable;

/**
 * All possible operators that may be used within a condition.
 */
public enum ArenaConditionComparisonOperator {

  /**
   * Variable and value must be equal.
   */
  EQUAL("="),

  /**
   * In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueNumber}:<br>
   * - Integer of both numbers must be equal. Decimal place is ignored.<br>
   In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueString}:<br>
   * - Strings must match while letter casing is being ignored<br>
   */
  SIMILAR("~"),

  /**
   * Variable and value must not be equal.
   */
  NOT_EQUAL("!="),

  /**
   * In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueNumber}:<br>
   * - Value must be greater (not equal!) than variable<br>
   In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueString}:<br>
   * - Value string must being with the same letters as variables (letter casing is being ignored)<br>
   */
  GREATER_THAN(">"),

  /**
   * In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueNumber}:<br>
   * - Value must be less (not equal!) than variable<br>
   In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueString}:<br>
   * - Value string must end with the same letters as variables (letter casing is being ignored)<br>
   */
  LESS_THAN("<"),

  /**
   * In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueNumber}:<br>
   * - Value must be greater than or equal to variable<br>
   In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueString}:<br>
   * - Value string must being with the same letters as variables (letter casing is important)<br>
   */
  GREATER_THAN_OR_EQUAL(">="),

  /**
   * In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueNumber}:<br>
   * - Value must be less than or equal to variable<br>
   In case the value is of type {@link de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValueString}:<br>
   * - Value string must end with the same letters as variables (letter casing is important)<br>
   */
  LESS_THAN_OR_EQUAL("<=");

  public static final ArenaConditionComparisonOperator[] VALUES = values();

  private final String usage;

  ArenaConditionComparisonOperator(String usage) {
    this.usage = usage;
  }

  /**
   * Gets the explicit String that must be given to use this operator.
   *
   * @return The usage
   */
  public String getUsage() {
    return this.usage;
  }

  /**
   * Tries to obtain a operator given by its {@link #getUsage()}.
   *
   * @param usage The usage that we are matching with
   * @return The given enum that matches. May be <code>null</code> in case there are none
   */
  @Nullable
  public static ArenaConditionComparisonOperator getByUsage(String usage) {
    for (ArenaConditionComparisonOperator op : VALUES) {
      if (op.usage.equals(usage))
        return op;
    }

    return null;
  }
}