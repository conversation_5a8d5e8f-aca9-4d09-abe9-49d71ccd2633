package de.marcely.bedwars.tools;

import java.io.Closeable;
import java.io.IOException;
import java.util.Iterator;
import java.util.NoSuchElementException;

/**
 * An iterator that also must be closed at some point.
 *
 * @param <T> The type of the elements
 * @see Iterator
 * @see Closeable
 */
public interface CloseableIterator<T> extends Iterator<T>, Closeable {

  class AlwaysClosed<T> implements CloseableIterator<T> {

    @Override
    public void close() throws IOException {
    }

    @Override
    public boolean hasNext() {
      return false;
    }

    @Override
    public T next() {
      throw new NoSuchElementException();
    }
  }
}
