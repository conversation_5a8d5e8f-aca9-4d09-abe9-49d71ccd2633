package cn.acebrand.acevotemode.gamemode;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.event.player.PlayerIngameRespawnEvent;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;

/**
 * 急速模式
 * 为玩家提供速度二效果和挖掘加速
 */
public class SpeedMode extends GameModeBase implements Listener {
    
    private static SpeedMode instance;
    private final Map<Arena, BukkitTask> speedTasks = new HashMap<>();
    
    public SpeedMode(AceVoteMode plugin) {
        super(plugin, "speed-mode", "急速模式");
        instance = this;
    }
    
    /**
     * 获取实例（用于PAPI）
     */
    public static SpeedMode getInstance() {
        return instance;
    }
    
    @Override
    protected void createDefaultConfig() {
        // 添加配置文件头部注释
        addConfigComments();

        // 基础配置
        config.set("enabled", true);
        config.set("description", "移动速度和挖掘速度大幅提升，快节奏的战斗体验");

        // 速度效果配置
        config.set("speed.level", 2);                        // 速度等级 (1-255)
        config.set("speed.enabled", true);                   // 是否启用速度效果
        config.set("speed.duration", 999999);                // 效果持续时间（tick，999999表示永久）
        config.set("speed.refresh-interval", 60);            // 效果刷新间隔（秒）

        // 消息配置
        config.set("messages.mode-start", "&b&l急速模式已启动！");
        config.set("messages.mode-description", "&7移动速度和挖掘速度大幅提升，享受快节奏的战斗体验！");
        config.set("messages.speed-applied", "&a速度效果已应用！");

        saveConfig();

        // 保存后添加详细注释到文件
        addDetailedCommentsToFile();
    }
    
    @Override
    protected void onConfigReload() {
        // 重载时重新应用效果到所有活跃的竞技场
        for (Arena arena : speedTasks.keySet()) {
            applySpeedEffects(arena);
        }
    }
    
    @Override
    public void onGameStart(Arena arena) {
        // 检查是否已经启动过，防止重复执行
        if (speedTasks.containsKey(arena)) {
            return;
        }

        // 向所有玩家发送模式开始消息
        String startMessage = config.getString("messages.mode-start", "&b&l急速模式已启动！");
        String description = config.getString("messages.mode-description", "&7移动速度和挖掘速度大幅提升，享受快节奏的战斗体验！");

        for (Player player : arena.getPlayers()) {
            player.sendMessage(translateColors(startMessage));
            player.sendMessage(translateColors(description));
        }

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // 延迟应用速度效果，确保游戏完全开始
        new BukkitRunnable() {
            @Override
            public void run() {
                applySpeedEffects(arena);
            }
        }.runTaskLater(plugin, 20L); // 延迟1秒应用效果

        // 启动定期刷新任务
        startSpeedRefreshTask(arena);
    }

    @Override
    public void onGameEnd(Arena arena) {
        // 停止刷新任务
        BukkitTask task = speedTasks.remove(arena);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }

        // 移除所有玩家的速度效果
        removeSpeedEffects(arena);
    }

    @Override
    public void onPlayerJoin(Player player, Arena arena) {
        // 新加入的玩家也应该获得速度效果
        if (speedTasks.containsKey(arena)) {
            applySpeedEffectsToPlayer(player);
        }
    }

    @Override
    public void onPlayerQuit(Player player, Arena arena) {
        // 玩家离开时移除效果
        removeSpeedEffectsFromPlayer(player);
    }

    /**
     * 应用速度效果到竞技场所有玩家
     */
    private void applySpeedEffects(Arena arena) {
        for (Player player : arena.getPlayers()) {
            applySpeedEffectsToPlayer(player);
        }
    }

    /**
     * 应用速度效果到单个玩家
     */
    private void applySpeedEffectsToPlayer(Player player) {
        // 只应用速度效果
        if (config.getBoolean("speed.enabled", true)) {
            int speedLevel = config.getInt("speed.level", 2);
            int duration = config.getInt("speed.duration", 999999);
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, duration, speedLevel - 1, false, false));
        }
    }

    /**
     * 移除竞技场所有玩家的速度效果
     */
    private void removeSpeedEffects(Arena arena) {
        for (Player player : arena.getPlayers()) {
            removeSpeedEffectsFromPlayer(player);
        }
    }

    /**
     * 移除单个玩家的速度效果
     */
    private void removeSpeedEffectsFromPlayer(Player player) {
        player.removePotionEffect(PotionEffectType.SPEED);
    }

    /**
     * 启动速度效果刷新任务
     */
    private void startSpeedRefreshTask(Arena arena) {
        int refreshInterval = config.getInt("speed.refresh-interval", 60) * 20; // 转换为tick

        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                // 检查竞技场是否还在游戏中
                if (arena.getStatus() != ArenaStatus.RUNNING) {
                    cancel();
                    speedTasks.remove(arena);
                    return;
                }

                // 刷新所有玩家的速度效果
                applySpeedEffects(arena);
            }
        }.runTaskTimer(plugin, refreshInterval, refreshInterval);

        speedTasks.put(arena, task);
    }

    /**
     * 处理玩家重生事件
     */
    @EventHandler
    public void onPlayerRespawn(PlayerIngameRespawnEvent event) {
        Arena arena = event.getArena();
        Player player = event.getPlayer();

        // 检查是否是急速模式的竞技场
        if (speedTasks.containsKey(arena)) {
            // 延迟应用速度效果，确保重生完成
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        applySpeedEffectsToPlayer(player);
                    }
                }
            }.runTaskLater(plugin, 5L); // 延迟5tick应用效果
        }
    }

    /**
     * 颜色代码转换
     */
    private String translateColors(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 添加配置文件注释
     */
    private void addConfigComments() {
        // 这里可以添加配置文件的头部注释
    }

    /**
     * 添加详细注释到配置文件
     */
    private void addDetailedCommentsToFile() {
        // 这里可以添加更详细的配置说明
    }
}
