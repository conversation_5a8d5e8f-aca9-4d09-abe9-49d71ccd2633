package de.marcely.bedwars.api.world.hologram.skin;

import de.marcely.bedwars.api.world.hologram.HologramSkin;
import org.jetbrains.annotations.Nullable;

/**
 * Represents an "NPC"
 */
public interface NPCHologramSkin extends HologramSkin, DamageableSkin, EquippableSkin, BukkitEntitySkin {

  /**
   * Applies a skin to the NPC
   *
   * @param texture The base64 texture data
   * @param signature The base64 signature data
   */
  void setSkin(String texture, String signature);

  /**
   * Removes the current skin of the NPC and causes it by that to look like Alex/Steve
   */
  void resetSkin();

  /**
   * Returns whether the skin has a skin (whether he looks like <PERSON> or <PERSON>)
   *
   * @return <code>true</code> when a skin has been applied using {@link #setSkin(String, String)}
   */
  boolean hasSkin();

  /**
   * Returns the skin texture that has been passed using {@link #setSkin(String, String)}.
   * <p>
   *     Might be <code>null</code> when no skin has been set.
   * </p>
   *
   * @return The skin texture in base64. Possibly <code>null</code>
   */
  @Nullable
  String getSkinTexture();

  /**
   * Returns the skin signature that has been passed using {@link #setSkin(String, String)}.
   * <p>
   *     Might be <code>null</code> when no skin has been set.
   * </p>
   *
   * @return The skin signature in base64. Possibly <code>null</code>
   */
  @Nullable
  String getSkinSignature();

  /**
   * Sets whether the NPC is sneaking or not.
   *
   * @param sneaking <code>true</code> when the NPC should be sneaking
   */
  void setSneaking(boolean sneaking);

  /**
   * Returns whether the NPC is sneaking or not.
   *
   * @return <code>true</code> when the NPC is sneaking
   */
  boolean isSneaking();
}
