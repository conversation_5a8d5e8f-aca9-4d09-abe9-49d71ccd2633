package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.spectator.Spectator;
import de.marcely.bedwars.api.game.spectator.SpectatorItem;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called whenever a {@link Spectator} uses a {@link SpectatorItem}
 */
public class SpectatorItemUseEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Spectator spectator;
  private final SpectatorItem item;

  @Getter @Setter
  private boolean cancelled = false;

  public SpectatorItemUseEvent(Spectator spectator, SpectatorItem item) {
    super(spectator.getPlayer());

    this.spectator = spectator;
    this.item = item;
  }

  @Override
  public Arena getArena() {
    return this.spectator.getArena();
  }

  /**
   * Returns the spectator who is about to use the item.
   *
   * @return The involved spectator
   */
  public Spectator getSpectator() {
    return this.spectator;
  }

  /**
   * Returns the item that has is about to be used.
   *
   * @return The involved item
   */
  public SpectatorItem getItem() {
    return this.item;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
