package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.unsafe.ArenaStatusWrapper;
import java.util.Locale;
import org.bukkit.command.CommandSender;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the state of an arena. This determines what the plugin is processing at a given moment 
 */
public enum ArenaStatus {

  /**
   * The arena is stopped. Nobody is able to join and nothing is happening
   */
  STOPPED,

  /**
   * Players are waiting for other players and until the game starts
   */
  LOBBY,

  /**
   * The game is running with players playing and spectators spectating
   */
  RUNNING,

  /**
   * The game has finished. Now it's time to clean everything up and prepare the arena for the next match
   */
  RESETTING,

  /**
   * Possibly before {@link #RESETTING} and after {@link #RUNNING}.
   * The game has ended, now we're celebrating the winner (if there's even one)
   */
  END_LOBBY;

  private final ArenaStatusWrapper wrapper = null;
  private final String id;

  ArenaStatus() {
    this.id = name().toLowerCase(Locale.ENGLISH);
  }

  /**
   * Returns the id of this state
   *
   * @return The id
   */
  public String getId() {
    return this.id;
  }

  /**
   * You can find them in the messages file under: "Sign_[status]".<br>
   * Uses the configured language as the default language
   *
   * @return The name of the status that can be represented on a sign
   */
  public String getSignName() {
    return this.wrapper.getSignName(null);
  }

  /**
   * You can find them in the messages file under: "Sign_[status]".<br>
   * Uses the configured language as the default language
   *
   * @param sender The person from which it should look up the language. Null if it should take the default language
   * @return The name of the status that can be represented on a sign
   */
  public String getSignName(@Nullable CommandSender sender) {
    return this.wrapper.getSignName(sender);
  }

  /**
   * Returns <code>true</code> when using:<br>
   * - {@link ArenaStatus#LOBBY}<br>
   * - {@link ArenaStatus#END_LOBBY}
   *
   * @return Whether it's a lobby status or not
   */
  public boolean isLobby() {
    switch (this) {
      case LOBBY:
      case END_LOBBY:
        return true;

      default:
        return false;
    }
  }

  /**
   * Returns <code>true</code> when using:<br>
   * - {@link ArenaStatus#LOBBY}<br>
   * - {@link ArenaStatus#RUNNING}<br>
   * - {@link ArenaStatus#END_LOBBY}
   *
   * @return Whether or not players can be inside the arena during this state
   */
  public boolean canHavePlayers() {
    switch (this) {
      case LOBBY:
      case RUNNING:
      case END_LOBBY:
        return true;

      default:
        return false;
    }
  }

  /**
   * Returns the ArenaStatus which the corresponding id
   *
   * @param id The id we've got
   * @return The ArenaStatus which the corresponding id
   */
  @Nullable
  public static ArenaStatus fromId(String id) {
    id = id.toLowerCase();

    for (ArenaStatus status : values()) {
      if (status.getId().equals(id))
        return status;
    }

    return null;
  }
}
