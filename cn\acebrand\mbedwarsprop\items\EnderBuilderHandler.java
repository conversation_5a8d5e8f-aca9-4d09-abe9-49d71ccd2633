package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Enderman;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class EnderBuilderHandler implements SpecialItemUseHandler {

    private final Plugin plugin;
    private final ItemConfigManager.ItemConfig config;
    private final Map<UUID, List<Location>> playerBridges = new HashMap<>();

    public EnderBuilderHandler(Plugin plugin, ItemConfigManager.ItemConfig config) {
        this.plugin = plugin;
        this.config = config;
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    private class Session extends SpecialItemUseSession {

        private Enderman enderman;
        private List<Location> bridgeLocations;
        private BukkitTask buildTask;
        private int currentBlockIndex = 0;
        private Location startLocation; // 桥梁的起始位置
        private Vector direction; // 桥梁的方向

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            if (this.enderman != null && this.enderman.isValid()) {
                this.enderman.remove();
            }

            if (this.buildTask != null && !this.buildTask.isCancelled()) {
                this.buildTask.cancel();
            }
        }

        protected void run() {
            Player player = getEvent().getPlayer();

            // 获取玩家点击的方块
            Block clickedBlock = getEvent().getClickedBlock();
            if (clickedBlock == null) {
                player.sendMessage("§c你必须点击一个方块才能使用末影建筑师！");
                stop();
                return;
            }

            plugin.getLogger().info("玩家点击的方块: " + clickedBlock.getType().name() + " 在 " +
                clickedBlock.getX() + ", " + clickedBlock.getY() + ", " + clickedBlock.getZ());

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(player);
            if (arena == null) {
                player.sendMessage("§c你必须在游戏中才能使用末影建筑师！");
                stop();
                return;
            }

            // 获取玩家所在的队伍
            Team team = arena.getPlayerTeam(player);
            if (team == null) {
                player.sendMessage("§c你必须在一个队伍中才能使用末影建筑师！");
                stop();
                return;
            }

            plugin.getLogger().info("玩家所在队伍: " + team.getDisplayName());

            // 获取配置
            final int[] bridgeLength = {20};
            final int bridgeWidth = 5; // 修改为5格宽
            final boolean[] hasRailings = {true};
            final int[] buildDelay = {10}; // 增加延迟，使末影人建造速度更合理
            Material blockMaterial = Material.WHITE_WOOL;
            final int[] despawnDelay = {5};
            final boolean[] removeAfterBuild = {true};
            final boolean[] usePlayerHead = {true};
            final boolean[] placePlayerHeadAtEnd = {false}; // 默认不在桥的尾部放置玩家头颅

            plugin.getLogger().info("桥梁宽度设置为: " + bridgeWidth + " 格");

            if (config != null) {
                ItemConfigManager.EffectConfig lengthConfig = config.getEffect("bridge-length");
                if (lengthConfig != null) {
                    bridgeLength[0] = lengthConfig.getLevel();
                }

                // 桥梁宽度固定为3
                // bridgeWidth已经是final的，不需要修改

                ItemConfigManager.EffectConfig railingsConfig = config.getEffect("has-railings");
                if (railingsConfig != null) {
                    hasRailings[0] = railingsConfig.getLevel() > 0;
                }

                ItemConfigManager.EffectConfig delayConfig = config.getEffect("build-delay");
                if (delayConfig != null) {
                    buildDelay[0] = delayConfig.getLevel();
                    // 确保延迟至少为1刻
                    if (buildDelay[0] < 1) buildDelay[0] = 1;
                }

                ItemConfigManager.EffectConfig materialConfig = config.getEffect("block-material");
                if (materialConfig != null) {
                    try {
                        Material material = Material.valueOf(materialConfig.toString().toUpperCase());
                        if (material.isBlock()) {
                            blockMaterial = material;
                        }
                    } catch (IllegalArgumentException e) {
                        // 使用默认值
                        plugin.getLogger().warning("无效的材质名称: " + materialConfig.toString() + ", 使用默认值: WHITE_WOOL");
                    }
                }

                ItemConfigManager.EffectConfig despawnConfig = config.getEffect("despawn-delay");
                if (despawnConfig != null) {
                    despawnDelay[0] = despawnConfig.getDuration();
                }

                ItemConfigManager.EffectConfig removeConfig = config.getEffect("remove-after-build");
                if (removeConfig != null) {
                    removeAfterBuild[0] = removeConfig.getLevel() > 0;
                }

                ItemConfigManager.EffectConfig useHeadConfig = config.getEffect("use-player-head");
                if (useHeadConfig != null) {
                    usePlayerHead[0] = useHeadConfig.getLevel() > 0;
                }

                ItemConfigManager.EffectConfig placeHeadConfig = config.getEffect("place-player-head-at-end");
                if (placeHeadConfig != null) {
                    placePlayerHeadAtEnd[0] = placeHeadConfig.getLevel() > 0;
                }
            }

            // 强制使用白色羊毛作为建造材料，后续会根据队伍颜色更改
            blockMaterial = Material.WHITE_WOOL;

            // 检查点击的方块是否有效
            if (clickedBlock == null) {
                player.sendMessage("§c请点击一个方块来确定桥梁的起点！");
                stop();
                return;
            }

            // 获取玩家视线方向
            this.direction = player.getLocation().getDirection().normalize();
            this.direction.setY(0); // 保持水平方向
            this.direction.normalize();

            // 打印原始方向信息
            plugin.getLogger().info("玩家视线方向: " + this.direction.getX() + ", " + this.direction.getY() + ", " + this.direction.getZ());

            // 重要修改: 不再反转方向，而是直接使用玩家的视线方向
            // 这样末影人就会向玩家看的方向建造桥梁

            plugin.getLogger().info("桥梁建造方向: " + this.direction.getX() + ", " + this.direction.getY() + ", " + this.direction.getZ());

            // 确定桥梁的起始位置（玩家点击的方块位置）
            this.startLocation = clickedBlock.getLocation().clone();

            // 重要修改: 不再将起始位置设置为方块上方，而是保持与点击的方块同一高度
            // 这样桥梁就会与点击的方块高度一致

            // 打印起始位置信息
            plugin.getLogger().info("桥梁起始位置: " + this.startLocation.getBlockX() + ", " +
                this.startLocation.getBlockY() + ", " + this.startLocation.getBlockZ());

            // 计算桥梁的所有位置
            bridgeLocations = calculateBridgeLocations(startLocation, direction, bridgeLength[0], bridgeWidth, hasRailings[0], player, usePlayerHead[0], placePlayerHeadAtEnd[0]);

            // 检查是否有足够的空间建造桥梁
            if (bridgeLocations.isEmpty()) {
                player.sendMessage("§c没有足够的空间建造桥梁！");
                stop();
                return;
            }

            // 生成末影人
            World world = player.getWorld();

            // 在点击的方块上生成末影人
            // 确保末影人站在方块上面
            Location spawnLocation = clickedBlock.getLocation().clone().add(0.5, 1, 0.5);

            // 打印点击的方块位置和末影人生成位置
            plugin.getLogger().info("点击的方块位置: " + clickedBlock.getX() + ", " + clickedBlock.getY() + ", " + clickedBlock.getZ());
            plugin.getLogger().info("末影人生成位置: " + spawnLocation.getX() + ", " + spawnLocation.getY() + ", " + spawnLocation.getZ());

            // 让末影人面向玩家的视线方向
            Vector spawnDirection = direction.clone();

            // 打印末影人生成位置
            plugin.getLogger().info("末影人生成位置: " + spawnLocation.getBlockX() + ", " +
                spawnLocation.getBlockY() + ", " + spawnLocation.getBlockZ() + ", 朝向: " +
                spawnDirection.getX() + ", " + spawnDirection.getY() + ", " + spawnDirection.getZ());

            // 创建一个变量来跟踪末影人当前所在的位置
            final Location[] currentEnderLocation = {spawnLocation.clone()};

            // 先创建生成效果，然后再生成末影人
            // 在生成位置创建传送粒子效果
            world.spawnParticle(Particle.PORTAL, spawnLocation.clone().add(0, 1, 0), 50, 0.3, 0.5, 0.3, 0.1);
            world.spawnParticle(Particle.DRAGON_BREATH, spawnLocation, 20, 0.2, 0.2, 0.2, 0.05);

            // 移除末影人的声音效果
            // world.playSound(spawnLocation, Sound.ENTITY_ENDERMAN_AMBIENT, 0.8f, 0.5f);

            // 延迟生成末影人，增强效果
            new BukkitRunnable() {
                @Override
                public void run() {
                    // 生成末影人 - 确保生成在方块上方而不是内部
                    // 将Y坐标增加1.0，使末影人站在方块上方但不会太高
                    Location adjustedSpawnLocation = spawnLocation.clone().add(0, 1.0, 0);
                    enderman = (Enderman) world.spawnEntity(adjustedSpawnLocation, EntityType.ENDERMAN);

                    // 使用队伍颜色设置末影人的名字
                    // 获取队伍的ChatColor
                    net.md_5.bungee.api.ChatColor teamColor = team.getBungeeChatColor();
                    String colorCode = teamColor.toString();

                    // 设置带颜色的名字
                    enderman.setCustomName(colorCode + team.getDisplayName() + "的建筑师");
                    enderman.setCustomNameVisible(true);

                    // 设置末影人面向玩家的视线方向
                    Location lookLocation = spawnLocation.clone().add(direction.clone().multiply(2));
                    Vector lookDirection = lookLocation.toVector().subtract(spawnLocation.toVector()).normalize();
                    Location lookAt = spawnLocation.clone().setDirection(lookDirection);
                    enderman.teleport(lookAt);

                    // 设置末影人不会传送和不会自主移动
                    enderman.setAI(false);
                    enderman.setPersistent(true); // 使末影人不会自然消失

                    // 保留生成音效
                    world.playSound(spawnLocation, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);

                    // 生成后的粒子效果
                    world.spawnParticle(Particle.PORTAL, spawnLocation.clone().add(0, 1, 0), 30, 0.3, 0.5, 0.3, 0.05);
                    world.spawnParticle(Particle.REVERSE_PORTAL, spawnLocation.clone().add(0, 1, 0), 20, 0.2, 0.5, 0.2, 0.02);

                    // 创建一个特殊的生成动画
                    enderman.setAI(true); // 暂时启用AI以允许动作

                    // 模拟末影人生成后的动作
                    new BukkitRunnable() {
                        private int step = 0;

                        @Override
                        public void run() {
                            if (!enderman.isValid() || step >= 3) {
                                if (enderman.isValid()) {
                                    enderman.setAI(false);
                                }
                                cancel();
                                return;
                            }

                            switch (step) {
                                case 0:
                                    // 微微上升 - 确保末影人不会卡在方块里
                                    Location upLocation = enderman.getLocation().add(0, 0.1, 0);
                                    // 确保 Y 坐标不会小于方块顶部 + 1.0
                                    if (upLocation.getY() < (spawnLocation.getBlockY() + 1.0)) {
                                        upLocation.setY(spawnLocation.getBlockY() + 1.0);
                                    }
                                    enderman.teleport(upLocation);
                                    // 移除末影人声音
                                    // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_AMBIENT, 0.5f, 1.2f);
                                    break;
                                case 1:
                                    // 微微旋转 - 保持高度
                                    Location rotateLocation = enderman.getLocation();
                                    rotateLocation.setYaw(rotateLocation.getYaw() + 45);
                                    // 确保 Y 坐标不会小于方块顶部 + 1.0
                                    if (rotateLocation.getY() < (spawnLocation.getBlockY() + 1.0)) {
                                        rotateLocation.setY(spawnLocation.getBlockY() + 1.0);
                                    }
                                    enderman.teleport(rotateLocation);
                                    world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1, 0), 10, 0.2, 0.5, 0.2, 0.02);
                                    break;
                                case 2:
                                    // 回到原始朝向 - 保持高度
                                    Location finalLookAt = lookAt.clone();
                                    // 确保 Y 坐标不会小于方块顶部 + 1.0
                                    if (finalLookAt.getY() < (spawnLocation.getBlockY() + 1.0)) {
                                        finalLookAt.setY(spawnLocation.getBlockY() + 1.0);
                                    }
                                    enderman.teleport(finalLookAt);
                                    // 移除末影人声音
                                    // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_AMBIENT, 0.3f, 1.0f);
                                    break;
                            }

                            step++;
                        }
                    }.runTaskTimer(plugin, 5L, 5L);
                }
            }.runTaskLater(plugin, 10L);

            // 记录玩家的桥梁位置
            playerBridges.put(player.getUniqueId(), bridgeLocations);

            // 开始建造桥梁
            final Material finalBlockMaterial = blockMaterial;
            final byte teamColorData = getTeamColorData(team);

            // 打印队伍信息以便于调试
            plugin.getLogger().info("玩家 " + player.getName() + " 所在队伍: " + team.getDisplayName());
            plugin.getLogger().info("队伍颜色数据: " + teamColorData + ", 对应羊毛: " + getTeamColoredWool(teamColorData).name());

            // 重新组织桥梁位置，按层次排序，确保末影人先建造中间的桥面，然后再建造栏杆
            // 删除这部分代码，因为我们在后面会重新排序

            // 固定建造延迟为10刻（0.5秒）
            buildDelay[0] = 10;

            // 打印桥梁位置信息以便于调试
            for (int i = 0; i < Math.min(20, bridgeLocations.size()); i++) {
                Location loc = bridgeLocations.get(i);
                plugin.getLogger().info("桥梁位置详细 " + i + ": " + loc.getBlockX() + ", " + loc.getBlockY() + ", " + loc.getBlockZ() + ", 类型: " + loc.getBlock().getType().name());
            }

            // 重新组织桥梁位置，按照建造顺序排序
            List<Location> sortedLocations = new ArrayList<>();

            // 按照桥梁长度方向排序
            Map<Integer, List<Location>> bridgeByPosition = new HashMap<>();

            // 计算每个方块到起始点的距离
            for (Location loc : bridgeLocations) {
                // 计算距离的关键值
                int key;
                if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                    // X方向的桥梁
                    key = loc.getBlockX() * 100 + loc.getBlockZ();
                } else {
                    // Z方向的桥梁
                    key = loc.getBlockZ() * 100 + loc.getBlockX();
                }

                if (!bridgeByPosition.containsKey(key)) {
                    bridgeByPosition.put(key, new ArrayList<>());
                }
                bridgeByPosition.get(key).add(loc);
            }

            // 按照距离排序
            List<Integer> sortedKeys = new ArrayList<>(bridgeByPosition.keySet());
            Collections.sort(sortedKeys);

            // 按照排序后的顺序重新组织桥梁位置
            for (Integer key : sortedKeys) {
                List<Location> positionBlocks = bridgeByPosition.get(key);
                // 先添加中间的方块，再添加两侧的方块
                for (Location loc : positionBlocks) {
                    sortedLocations.add(loc);
                }
            }

            // 更新桥梁位置列表
            bridgeLocations = sortedLocations;

            plugin.getLogger().info("重新排序后的桥梁位置数量: " + bridgeLocations.size());
            for (int i = 0; i < Math.min(20, bridgeLocations.size()); i++) {
                Location loc = bridgeLocations.get(i);
                plugin.getLogger().info("排序后的桥梁位置 " + i + ": " + loc.getBlockX() + ", " + loc.getBlockY() + ", " + loc.getBlockZ());
            }

            // 按照桥梁层数对方块进行分组
            Map<Integer, List<Location>> bridgeByLayer = new HashMap<>();

            // 将桥梁位置按照层数分组
            for (Location loc : bridgeLocations) {
                int layer;
                if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                    // X方向的桥梁
                    layer = Math.abs(loc.getBlockX() - startLocation.getBlockX());
                } else {
                    // Z方向的桥梁
                    layer = Math.abs(loc.getBlockZ() - startLocation.getBlockZ());
                }

                if (!bridgeByLayer.containsKey(layer)) {
                    bridgeByLayer.put(layer, new ArrayList<>());
                }
                bridgeByLayer.get(layer).add(loc);
            }

            // 按照层数排序
            List<Integer> sortedLayers = new ArrayList<>(bridgeByLayer.keySet());
            Collections.sort(sortedLayers);

            // 重新组织桥梁位置，按照层数顺序排列
            bridgeLocations.clear();
            for (Integer layer : sortedLayers) {
                bridgeLocations.addAll(bridgeByLayer.get(layer));
            }

            plugin.getLogger().info("按层数重新排序后的桥梁位置数量: " + bridgeLocations.size());

            this.buildTask = new BukkitRunnable() {
                // 记录当前建造到的桥梁层数
                private int currentLayer = 0;
                // 记录上一个放置的方块位置
                private Location lastPlacedLocation = null;
                // 记录当前层的中心位置
                private Location currentLayerCenter = null;
                // 记录当前层的所有方块
                private List<Location> currentLayerBlocks = new ArrayList<>();
                // 记录当前层的已建造方块数量
                private int currentLayerBlocksPlaced = 0;

                @Override
                public void run() {
                    // 打印当前状态信息
                    plugin.getLogger().info("当前块索引: " + currentBlockIndex + ", 总块数: " + bridgeLocations.size());

                    if (currentBlockIndex >= bridgeLocations.size() || !enderman.isValid()) {
                        // 建造完成或末影人被移除
                        plugin.getLogger().info("建造完成或末影人被移除");
                        if (removeAfterBuild[0] && enderman.isValid()) {
                            // 创建更复杂的消失动画
                            // 先让末影人有一个完成建造的动作
                            enderman.setAI(true);

                            // 模拟末影人完成工作的动作
                            new BukkitRunnable() {
                                private int step = 0;
                                private final int totalSteps = 5;

                                @Override
                                public void run() {
                                    if (!enderman.isValid() || step >= totalSteps) {
                                        if (enderman.isValid()) {
                                            // 开始消失效果
                                            startDespawnEffect();
                                        }
                                        cancel();
                                        return;
                                    }

                                    switch (step) {
                                        case 0: // 看向已完成的桥梁
                                            Location lookBridge = enderman.getLocation().clone();
                                            lookBridge.setDirection(direction);
                                            enderman.teleport(lookBridge);
                                            // 移除末影人声音
                                            // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_AMBIENT, 0.5f, 1.0f);
                                            break;
                                        case 1: // 微微点头
                                            Location nodLocation = enderman.getLocation().clone();
                                            nodLocation.setPitch(nodLocation.getPitch() + 15);
                                            enderman.teleport(nodLocation);
                                            break;
                                        case 2: // 回到正常视角
                                            Location normalLocation = enderman.getLocation().clone();
                                            normalLocation.setPitch(0);
                                            enderman.teleport(normalLocation);
                                            break;
                                        case 3: // 转身准备离开
                                            Location turnLocation = enderman.getLocation().clone();
                                            turnLocation.setYaw(turnLocation.getYaw() + 180); // 旋转180度
                                            enderman.teleport(turnLocation);
                                            // 移除末影人声音
                                            // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_AMBIENT, 0.3f, 1.2f);
                                            break;
                                        case 4: // 最后一个动作
                                            world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1, 0), 20, 0.3, 0.5, 0.3, 0.05);
                                            break;
                                    }

                                    step++;
                                }

                                // 开始消失效果
                                private void startDespawnEffect() {
                                    // 保留传送音效
                                    world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);

                                    // 显示传送粒子效果
                                    world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1, 0), 50, 0.5, 1, 0.5, 0.1);
                                    world.spawnParticle(Particle.DRAGON_BREATH, enderman.getLocation(), 30, 0.3, 0.5, 0.3, 0.05);

                                    // 创建消失动画
                                    new BukkitRunnable() {
                                        private int disappearStep = 0;
                                        private final int totalDisappearSteps = 3;

                                        @Override
                                        public void run() {
                                            if (!enderman.isValid() || disappearStep >= totalDisappearSteps) {
                                                if (enderman.isValid()) {
                                                    // 最后一次粒子效果
                                                    Location finalLoc = enderman.getLocation();
                                                    world.spawnParticle(Particle.PORTAL, finalLoc.add(0, 1, 0), 100, 0.5, 1, 0.5, 0.2);
                                                    world.spawnParticle(Particle.REVERSE_PORTAL, finalLoc, 50, 0.4, 0.8, 0.4, 0.1);
                                                    // 保留传送音效
                                                    world.playSound(finalLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);

                                                    // 移除末影人
                                                    enderman.remove();
                                                }
                                                cancel();
                                                return;
                                            }

                                            // 消失动画
                                            switch (disappearStep) {
                                                case 0: // 开始消失
                                                    world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1, 0), 30, 0.3, 0.6, 0.3, 0.1);
                                                    // 移除末影人声音
                                                    // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_SCREAM, 0.3f, 1.2f);
                                                    break;
                                                case 1: // 继续消失
                                                    world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 0.5, 0), 40, 0.4, 0.8, 0.4, 0.15);
                                                    // 移除末影人声音
                                                    // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_HURT, 0.4f, 1.0f);
                                                    break;
                                                case 2: // 准备最后消失
                                                    world.spawnParticle(Particle.PORTAL, enderman.getLocation(), 50, 0.5, 1, 0.5, 0.2);
                                                    // 移除末影人声音
                                                    // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_DEATH, 0.5f, 0.8f);
                                                    break;
                                            }

                                            disappearStep++;
                                        }
                                    }.runTaskTimer(plugin, 5L, 5L);
                                }
                            }.runTaskTimer(plugin, 5L, 10L);
                        }

                        cancel();
                        return;
                    }

                    // 获取当前要放置的方块位置
                    Location blockLocation = bridgeLocations.get(currentBlockIndex);
                    plugin.getLogger().info("尝试放置方块于: " + blockLocation.getBlockX() + ", " + blockLocation.getBlockY() + ", " + blockLocation.getBlockZ());

                    // 计算当前方块所在的层数
                    int blockLayer;
                    if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                        // X方向的桥梁
                        blockLayer = Math.abs(blockLocation.getBlockX() - startLocation.getBlockX());
                    } else {
                        // Z方向的桥梁
                        blockLayer = Math.abs(blockLocation.getBlockZ() - startLocation.getBlockZ());
                    }

                    // 如果是新的一层的第一个方块，需要先收集这一层的所有方块
                    if (blockLayer != currentLayer) {
                        // 如果是第一层，直接开始建造
                        if (currentLayer == 0) {
                            currentLayer = blockLayer;
                            currentLayerBlocksPlaced = 0;

                            // 收集当前层的所有方块
                            currentLayerBlocks.clear();
                            for (int i = currentBlockIndex; i < bridgeLocations.size(); i++) {
                                Location loc = bridgeLocations.get(i);
                                int locLayer;
                                if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                                    locLayer = Math.abs(loc.getBlockX() - startLocation.getBlockX());
                                } else {
                                    locLayer = Math.abs(loc.getBlockZ() - startLocation.getBlockZ());
                                }

                                if (locLayer == currentLayer) {
                                    currentLayerBlocks.add(loc);
                                } else {
                                    break;
                                }
                            }

                            plugin.getLogger().info("当前层 " + currentLayer + " 有 " + currentLayerBlocks.size() + " 个方块需要放置");

                            // 计算当前层的中心位置（第3格）
                            Location centerPos = startLocation.clone().add(direction.clone().multiply(currentLayer));
                            currentLayerCenter = centerPos.clone();

                            // 对于第一层，末影人不移动，保持在原始生成的位置
                            // 即玩家右键点击的方块上面
                            plugin.getLogger().info("第一层建造，末影人保持在原始位置: " +
                                enderman.getLocation().getBlockX() + ", " + enderman.getLocation().getBlockY() + ", " + enderman.getLocation().getBlockZ());

                            // 确保末影人面向桥梁建造方向
                            Location lookLocation = enderman.getLocation().clone().add(direction.clone().multiply(2));
                            Vector lookDirection = lookLocation.toVector().subtract(enderman.getLocation().toVector()).normalize();
                            Location lookAt = enderman.getLocation().clone().setDirection(lookDirection);
                            enderman.teleport(lookAt);

                            // 不需要移动末影人，直接返回开始放置方块
                            lastPlacedLocation = null;
                            return;
                        } else {
                            // 如果不是第一层，需要先检查上一层是否建造完成
                            // 如果上一层已经建造完成，才能移动到新的一层
                            if (currentLayerBlocksPlaced >= currentLayerBlocks.size()) {
                                currentLayer = blockLayer;
                                currentLayerBlocksPlaced = 0;

                                // 收集当前层的所有方块
                                currentLayerBlocks.clear();
                                for (int i = currentBlockIndex; i < bridgeLocations.size(); i++) {
                                    Location loc = bridgeLocations.get(i);
                                    int locLayer;
                                    if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                                        locLayer = Math.abs(loc.getBlockX() - startLocation.getBlockX());
                                    } else {
                                        locLayer = Math.abs(loc.getBlockZ() - startLocation.getBlockZ());
                                    }

                                    if (locLayer == currentLayer) {
                                        currentLayerBlocks.add(loc);
                                    } else {
                                        break;
                                    }
                                }

                                plugin.getLogger().info("当前层 " + currentLayer + " 有 " + currentLayerBlocks.size() + " 个方块需要放置");

                                // 计算当前层的中心位置（第3格）
                                Location centerPos = startLocation.clone().add(direction.clone().multiply(currentLayer));
                                currentLayerCenter = centerPos.clone();

                                // 移动末影人到上一层的中心位置
                                // 确保末影人站在上一层已建造的方块上，避免掉入虚空
                                Location previousLayerCenter = startLocation.clone().add(direction.clone().multiply(currentLayer - 1));

                                // 确保末影人始终在桥的中间位置（第3格）
                                // 而不是在最左边或最右边
                                // 增加Y坐标到1.0，确保末影人站在方块上方而不是内部
                                Location moveLocation = previousLayerCenter.clone().add(0.5, 1.0, 0.5);

                                // 计算垂直于方向的向量，用于左右移动
                                Vector perpendicular = new Vector(-direction.getZ(), 0, direction.getX()).normalize();

                                // 先检查当前层的中心方块是否存在
                                if (currentLayerCenter.getBlock().getType() == Material.AIR) {
                                    // 如果当前层的中心方块是空气，则移动到该方块上
                                    // 增加Y坐标到1.0，确保末影人站在方块上方而不是内部
                                    moveLocation = currentLayerCenter.clone().add(0.5, 1.0, 0.5);
                                    plugin.getLogger().info("末影人将移动到当前层的中心位置: " +
                                        moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                } else if (previousLayerCenter.getBlock().getType() != Material.AIR) {
                                    // 如果当前层的中心方块不是空气，但上一层的中心方块存在，则移动到上一层的方块上
                                    moveLocation = previousLayerCenter.clone().add(0.5, 1.0, 0.5);
                                    plugin.getLogger().info("当前层中心被阻挡，末影人将移动到上一层的中心位置: " +
                                        moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                } else {
                                    // 如果上一层的中心方块不存在，尝试寻找上一层的其他方块
                                    plugin.getLogger().info("上一层的中心方块不存在，尝试寻找其他方块");

                                    // 尝试寻找上一层的其他方块
                                    boolean foundBlock = false;
                                    for (int offset = 1; offset <= 2; offset++) {
                                        // 尝试左侧方块
                                        Location leftBlock = previousLayerCenter.clone().add(perpendicular.clone().multiply(offset));
                                        if (leftBlock.getBlock().getType() != Material.AIR) {
                                            moveLocation = leftBlock.clone().add(0.5, 0.6, 0.5);
                                            foundBlock = true;
                                            plugin.getLogger().info("找到上一层的左侧方块，移动到: " +
                                                moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                            break;
                                        }

                                        // 尝试右侧方块
                                        Location rightBlock = previousLayerCenter.clone().subtract(perpendicular.clone().multiply(offset));
                                        if (rightBlock.getBlock().getType() != Material.AIR) {
                                            moveLocation = rightBlock.clone().add(0.5, 0.6, 0.5);
                                            foundBlock = true;
                                            plugin.getLogger().info("找到上一层的右侧方块，移动到: " +
                                                moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                            break;
                                        }
                                    }

                                    // 如果上一层没有任何方块，则尝试寻找其他已建造的层
                                    if (!foundBlock) {
                                        plugin.getLogger().info("上一层没有可用的方块，尝试寻找其他已建造的层");

                                        // 先检查当前层的其他位置
                                        boolean safePositionFound = false;

                                        // 尝试当前层的左右两侧方块
                                        for (int offset = 1; offset <= 2; offset++) {
                                            // 左侧
                                            Location leftBlock = currentLayerCenter.clone().add(perpendicular.clone().multiply(offset));
                                            if (leftBlock.getBlock().getType() == Material.AIR) {
                                                moveLocation = leftBlock.clone().add(0.5, 1.0, 0.5);
                                                safePositionFound = true;
                                                plugin.getLogger().info("找到当前层的左侧空气方块, 移动到: " +
                                                    moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                                break;
                                            }

                                            // 右侧
                                            Location rightBlock = currentLayerCenter.clone().subtract(perpendicular.clone().multiply(offset));
                                            if (rightBlock.getBlock().getType() == Material.AIR) {
                                                moveLocation = rightBlock.clone().add(0.5, 1.0, 0.5);
                                                safePositionFound = true;
                                                plugin.getLogger().info("找到当前层的右侧空气方块, 移动到: " +
                                                    moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                                break;
                                            }
                                        }

                                        // 如果当前层没有找到安全的位置，则向后寻找已建造的层
                                        if (!safePositionFound) {
                                            for (int backLayer = currentLayer - 2; backLayer >= 0; backLayer--) {
                                                Location backLayerCenter = startLocation.clone().add(direction.clone().multiply(backLayer));

                                                // 检查中心方块
                                                if (backLayerCenter.getBlock().getType() != Material.AIR) {
                                                    moveLocation = backLayerCenter.clone().add(0.5, 1.0, 0.5);
                                                    safePositionFound = true;
                                                    plugin.getLogger().info("找到安全的层 " + backLayer + ", 移动到: " +
                                                        moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                                    break;
                                                }

                                                // 检查左右两侧方块
                                                for (int offset = 1; offset <= 2; offset++) {
                                                    // 左侧
                                                    Location leftBlock = backLayerCenter.clone().add(perpendicular.clone().multiply(offset));
                                                    if (leftBlock.getBlock().getType() != Material.AIR) {
                                                        moveLocation = leftBlock.clone().add(0.5, 1.0, 0.5);
                                                        safePositionFound = true;
                                                        plugin.getLogger().info("找到安全的层 " + backLayer + " 的左侧方块, 移动到: " +
                                                            moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                                        break;
                                                    }

                                                    // 右侧
                                                    Location rightBlock = backLayerCenter.clone().subtract(perpendicular.clone().multiply(offset));
                                                    if (rightBlock.getBlock().getType() != Material.AIR) {
                                                        moveLocation = rightBlock.clone().add(0.5, 1.0, 0.5);
                                                        safePositionFound = true;
                                                        plugin.getLogger().info("找到安全的层 " + backLayer + " 的右侧方块, 移动到: " +
                                                            moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                                        break;
                                                    }
                                                }

                                                if (safePositionFound) {
                                                    break;
                                                }
                                            }
                                        }

                                        // 如果没有找到安全的位置，则尝试往前看下一层
                                        if (!safePositionFound) {
                                            // 尝试下一层的中心位置
                                            Location nextLayerCenter = startLocation.clone().add(direction.clone().multiply(currentLayer + 1));
                                            if (nextLayerCenter.getBlock().getType() == Material.AIR) {
                                                moveLocation = nextLayerCenter.clone().add(0.5, 1.0, 0.5);
                                                safePositionFound = true;
                                                plugin.getLogger().info("找到下一层的空气位置, 移动到: " +
                                                    moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());
                                            }
                                        }

                                        // 如果还是没有找到安全的位置，则保持当前位置
                                        if (!safePositionFound) {
                                            plugin.getLogger().info("没有找到安全的位置，保持当前位置");
                                            moveLocation = enderman.getLocation().clone();

                                            // 强制跳过当前层，避免卡住
                                            currentLayerBlocksPlaced = currentLayerBlocks.size();
                                            plugin.getLogger().info("强制跳过当前层，避免卡住");

                                            // 显示跳过层的特效
                                            if (enderman.isValid()) {
                                                // 显示传送粒子效果
                                                world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1, 0), 50, 0.5, 1, 0.5, 0.1);
                                                world.spawnParticle(Particle.REVERSE_PORTAL, enderman.getLocation(), 30, 0.3, 0.5, 0.3, 0.05);

                                                // 播放传送声音
                                                world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                                            }
                                        }
                                    }
                                }

                                plugin.getLogger().info("移动末影人到新的一层: " + currentLayer + ", 位置: " +
                                    moveLocation.getBlockX() + ", " + moveLocation.getBlockY() + ", " + moveLocation.getBlockZ());

                                // 流畅移动末影人，使用动画而不是直接传送
                                // 计算从当前位置到目标位置的向量
                                Vector moveVector = moveLocation.toVector().subtract(enderman.getLocation().toVector()).normalize().multiply(0.2);
                                Location finalDestination = moveLocation.clone();

                                // 打印末影人当前位置和目标位置以便于调试
                                plugin.getLogger().info("末影人当前位置: " +
                                    enderman.getLocation().getBlockX() + ", " + enderman.getLocation().getBlockY() + ", " + enderman.getLocation().getBlockZ());
                                plugin.getLogger().info("末影人目标位置: " +
                                    finalDestination.getBlockX() + ", " + finalDestination.getBlockY() + ", " + finalDestination.getBlockZ());

                                // 让末影人面向移动方向
                                Location lookAt = enderman.getLocation().clone().setDirection(moveVector);
                                enderman.teleport(lookAt);

                                // 使用更真实的动画模拟末影人移动
                                new BukkitRunnable() {
                                    int steps = 0;
                                    final int maxSteps = 10; // 增加移动帧数，使移动更流畅
                                    boolean isWalking = false; // 跟踪末影人是否在行走

                                    @Override
                                    public void run() {
                                        if (!enderman.isValid() || steps >= maxSteps) {
                                            if (enderman.isValid()) {
                                                // 最后一步确保末影人到达目标位置
                                                enderman.teleport(finalDestination);
                                                currentEnderLocation[0] = finalDestination;

                                                // 移除到达音效
                                                // world.playSound(finalDestination, Sound.BLOCK_GRAVEL_STEP, 0.5f, 1.0f);

                                                // 到达目的地后的粒子效果
                                                world.spawnParticle(Particle.PORTAL, finalDestination.clone().add(0, 1, 0), 10, 0.2, 0.5, 0.2, 0.02);

                                                // 移除末影人到达的声音
                                                // world.playSound(finalDestination, Sound.ENTITY_ENDERMAN_AMBIENT, 0.3f, 1.0f);
                                            }
                                            cancel();
                                            return;
                                        }

                                        // 计算当前帧的位置
                                        Location currentPos = enderman.getLocation();
                                        Vector step = moveVector.clone().multiply(0.15); // 减小每步的距离，使移动更流畅
                                        Location nextPos = currentPos.clone().add(step);

                                        // 确保末影人面向移动方向
                                        nextPos.setDirection(moveVector);

                                        // 确保末影人始终在方块上方而不是内部
                                        // 获取当前方块的Y坐标
                                        int blockY = nextPos.getBlockY();
                                        // 确保末影人的Y坐标至少是方块顶部 + 1.0
                                        double minY = blockY + 1.0;

                                        // 如果计算出的Y坐标小于最小允许值，则使用最小允许值
                                        if (nextPos.getY() < minY) {
                                            nextPos.setY(minY);
                                        }

                                        // 模拟行走动作 - 上下跳动
                                        if (steps % 2 == 0) {
                                            // 偶数步微微上跳
                                            nextPos.add(0, 0.05, 0);
                                            isWalking = true;
                                        } else {
                                            // 奇数步微微下降，但不会低于最小高度
                                            if (nextPos.getY() - 0.05 >= minY) {
                                                nextPos.add(0, -0.05, 0);
                                            }
                                            isWalking = false;
                                        }

                                        // 移动末影人
                                        enderman.teleport(nextPos);
                                        currentEnderLocation[0] = nextPos;

                                        // 每一步都有移动的视觉效果，移除听觉效果
                                        if (isWalking) {
                                            // 移除脚步声
                                            // world.playSound(nextPos, Sound.BLOCK_GRAVEL_STEP, 0.2f, 1.0f);

                                            // 在脚下显示小粒子
                                            world.spawnParticle(Particle.CLOUD, nextPos.clone().add(0, 0.1, 0), 1, 0.1, 0.0, 0.1, 0.01);
                                        }

                                        // 每三步显示粒子效果，移除末影人的环境音
                                        if (steps % 3 == 0) {
                                            // 移除末影人声音
                                            // world.playSound(nextPos, Sound.ENTITY_ENDERMAN_AMBIENT, 0.1f, 1.2f);

                                            // 在末影人周围显示传送粒子
                                            world.spawnParticle(Particle.PORTAL, nextPos.clone().add(0, 1, 0), 3, 0.2, 0.5, 0.2, 0.01);
                                        }

                                        steps++;
                                    }
                                }.runTaskTimer(plugin, 0L, 2L); // 每2刻执行一次，模拟流畅移动

                                // 在移动后等待一下，下一次运行才放置方块
                                lastPlacedLocation = null;
                                return;
                            } else {
                                // 如果上一层还没有建造完成，则跳过这个方块
                                plugin.getLogger().info("上一层还没有建造完成，跳过这个方块");
                                currentBlockIndex++;
                                return;
                            }
                        }
                    }

                    // 检查当前层是否有障碍物，如果有，则跳到下一层
                    int obstacleCount = 0;
                    int remainingBlocksInLayer = 0;
                    boolean frontBlocked = false; // 检查前方是否被阻挡

                    // 计算当前层中剩余的方块数量和障碍物数量
                    for (int i = currentBlockIndex; i < bridgeLocations.size(); i++) {
                        Location loc = bridgeLocations.get(i);
                        int locLayer;
                        if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                            locLayer = Math.abs(loc.getBlockX() - startLocation.getBlockX());
                        } else {
                            locLayer = Math.abs(loc.getBlockZ() - startLocation.getBlockZ());
                        }

                        // 如果这个方块属于当前层
                        if (locLayer == currentLayer) {
                            remainingBlocksInLayer++;
                            // 检查这个位置是否已经有方块
                            if (loc.getBlock().getType() != Material.AIR) {
                                obstacleCount++;

                                // 检查这个方块是否在末影人前方
                                if (i == currentBlockIndex) {
                                    frontBlocked = true;
                                    plugin.getLogger().info("末影人前方被阻挡！");
                                }
                            }
                        } else {
                            // 如果不属于当前层，则跳出循环
                            break;
                        }
                    }

                    // 如果前方被阻挡或当前层的障碍物超过了30%，则跳到下一层
                    if (frontBlocked || (obstacleCount > 0 && (double)obstacleCount / remainingBlocksInLayer > 0.3)) {
                        plugin.getLogger().info("当前层障碍物过多或前方被阻挡，跳到下一层。障碍物数量: " + obstacleCount + ", 总方块数: " + remainingBlocksInLayer);

                        // 将当前层的所有方块标记为已建造
                        currentLayerBlocksPlaced = currentLayerBlocks.size();

                        // 跳过当前层的所有方块
                        while (currentBlockIndex < bridgeLocations.size()) {
                            Location loc = bridgeLocations.get(currentBlockIndex);
                            int locLayer;
                            if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                                locLayer = Math.abs(loc.getBlockX() - startLocation.getBlockX());
                            } else {
                                locLayer = Math.abs(loc.getBlockZ() - startLocation.getBlockZ());
                            }

                            if (locLayer == currentLayer) {
                                currentBlockIndex++;
                            } else {
                                break;
                            }
                        }

                        // 显示跳过层的特效
                        if (enderman.isValid()) {
                            // 显示传送粒子效果
                            world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1, 0), 50, 0.5, 1, 0.5, 0.1);
                            world.spawnParticle(Particle.REVERSE_PORTAL, enderman.getLocation(), 30, 0.3, 0.5, 0.3, 0.05);

                            // 播放传送声音
                            world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                        }

                        // 返回，下一次运行时将处理下一层
                        return;
                    }

                    // 检查当前层是否已经完成
                    if (currentLayerBlocksPlaced >= currentLayerBlocks.size()) {
                        plugin.getLogger().info("当前层已经完成，准备进入下一层");
                        return;
                    }

                    // 更新当前层已建造的方块数量
                    currentLayerBlocksPlaced++;

                    try {
                        // 放置方块
                        Block block = blockLocation.getBlock();

                        // 检查方块是否为空气
                        if (block.getType() != Material.AIR) {
                            plugin.getLogger().info("方块不是空气，跳过: " + block.getType().name());

                            // 计算跳过的方块数量
                            int skippedBlocks = 1;
                            int consecutiveObstacles = 1; // 连续障碍物计数

                            // 检查这一层中还有多少方块可以放置
                            int remainingAirBlocks = 0;
                            for (int i = currentBlockIndex + 1; i < bridgeLocations.size(); i++) {
                                Location loc = bridgeLocations.get(i);
                                int locLayer;
                                if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                                    locLayer = Math.abs(loc.getBlockX() - startLocation.getBlockX());
                                } else {
                                    locLayer = Math.abs(loc.getBlockZ() - startLocation.getBlockZ());
                                }

                                // 如果这个方块属于当前层
                                if (locLayer == currentLayer) {
                                    if (loc.getBlock().getType() == Material.AIR) {
                                        remainingAirBlocks++;
                                        consecutiveObstacles = 0; // 重置连续障碍物计数
                                    } else {
                                        consecutiveObstacles++;
                                        // 如果连续障碍物超过3个，直接跳到下一层
                                        if (consecutiveObstacles >= 3) {
                                            plugin.getLogger().info("检测到连续" + consecutiveObstacles + "个障碍物，直接跳到下一层");
                                            remainingAirBlocks = 0; // 强制跳到下一层
                                            break;
                                        }
                                    }
                                } else {
                                    break;
                                }
                            }

                            // 如果这一层中没有空气方块可以放置，或者连续障碍物过多，则将整层标记为已完成
                            if (remainingAirBlocks == 0 || consecutiveObstacles >= 3) {
                                plugin.getLogger().info("当前层没有空气方块可以放置或连续障碍物过多，跳到下一层");

                                // 将当前层的所有方块标记为已建造
                                currentLayerBlocksPlaced = currentLayerBlocks.size();

                                // 跳过当前层的所有方块
                                while (currentBlockIndex < bridgeLocations.size()) {
                                    Location loc = bridgeLocations.get(currentBlockIndex);
                                    int locLayer;
                                    if (Math.abs(direction.getX()) > Math.abs(direction.getZ())) {
                                        locLayer = Math.abs(loc.getBlockX() - startLocation.getBlockX());
                                    } else {
                                        locLayer = Math.abs(loc.getBlockZ() - startLocation.getBlockZ());
                                    }

                                    if (locLayer == currentLayer) {
                                        currentBlockIndex++;
                                    } else {
                                        break;
                                    }
                                }

                                // 显示跳过层的特效
                                if (enderman.isValid()) {
                                    // 显示传送粒子效果
                                    world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1, 0), 50, 0.5, 1, 0.5, 0.1);
                                    world.spawnParticle(Particle.REVERSE_PORTAL, enderman.getLocation(), 30, 0.3, 0.5, 0.3, 0.05);

                                    // 播放传送声音
                                    world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                                }
                            } else {
                                // 如果还有空气方块可以放置，则只跳过当前方块
                                currentBlockIndex++;
                                plugin.getLogger().info("跳过当前方块，还有 " + remainingAirBlocks + " 个空气方块可以放置");
                            }

                            lastPlacedLocation = blockLocation.clone();
                            return;
                        }

                        // 保留放置音效
                        world.playSound(blockLocation, Sound.BLOCK_STONE_PLACE, 0.5f, 1.0f);

                        // 始终使用队伍颜色的羊毛
                        Material teamWool = getTeamColoredWool(teamColorData);
                        plugin.getLogger().info("将要放置的羊毛颜色: " + teamWool.name());

                        // 对于所有方块，使用队伍颜色的羊毛
                        block.setType(teamWool);

                        // 不需要在桥的尾部放置玩家头颅

                        // 标记方块为玩家放置，这样游戏结束时会被清理
                        arena.setBlockPlayerPlaced(block, true);

                        // 打印方块放置信息以便于调试
                        plugin.getLogger().info("成功放置方块: " + blockLocation.getBlockX() + ", " + blockLocation.getBlockY() + ", " + blockLocation.getBlockZ());

                        // 检查末影人是否在中间位置（第3格）
                        // 如果不在，尝试将其移回中间位置
                        Location currentPos = enderman.getLocation();
                        // 增加Y坐标到1.0，确保末影人站在方块上方而不是内部
                        Location centerPos = currentLayerCenter.clone().add(0.5, 1.0, 0.5);

                        // 如果末影人与中心位置的水平距离超过1格，则将其移回中心
                        double distanceToCenter = Math.sqrt(
                            Math.pow(currentPos.getX() - centerPos.getX(), 2) +
                            Math.pow(currentPos.getZ() - centerPos.getZ(), 2));

                        if (distanceToCenter > 1.0) {
                            plugin.getLogger().info("末影人偏离中心位置过远，将其移回中心。当前距离: " + distanceToCenter);
                            // 在下一次放置方块前将末影人移回中心位置
                            enderman.teleport(centerPos);
                            currentEnderLocation[0] = centerPos;
                        }

                        // 确保末影人的Y坐标始终在方块上方而不是内部
                        if (enderman.getLocation().getY() < (currentLayerCenter.getBlockY() + 1.0)) {
                            Location adjustedPos = enderman.getLocation().clone();
                            adjustedPos.setY(currentLayerCenter.getBlockY() + 1.0);
                            enderman.teleport(adjustedPos);
                            currentEnderLocation[0] = adjustedPos;
                            plugin.getLogger().info("末影人高度调整到: " + adjustedPos.getY());
                        }

                        // 增强末影人放置方块的动作效果
                        // 先让末影人看向要放置的方块
                        Location lookLocation = blockLocation.clone().add(0.5, 0.5, 0.5);
                        Vector lookDirection = lookLocation.toVector().subtract(enderman.getLocation().toVector()).normalize();
                        Location lookAt = enderman.getLocation().clone().setDirection(lookDirection);
                        enderman.teleport(lookAt);

                        // 移除末影人放置方块的所有声音
                        // world.playSound(enderman.getLocation(), Sound.BLOCK_WOOL_PLACE, 0.3f, 1.0f);

                        // 增强手部动作效果 - 启用AI并让末影人有更多动作
                        enderman.setAI(true);

                        // 模拟末影人拿起方块并放置的动作
                        // 更真实的手部动作模拟
                        new BukkitRunnable() {
                            private int step = 0;
                            private final int totalSteps = 8; // 增加动作步骤

                            @Override
                            public void run() {
                                if (!enderman.isValid() || step >= totalSteps) {
                                    if (enderman.isValid()) {
                                        enderman.setAI(false);
                                    }
                                    cancel();
                                    return;
                                }

                                // 模拟更复杂的手部动作
                                switch (step) {
                                    case 0: // 开始伸手
                                        // 向方块方向微微伸出
                                        Location loc1 = enderman.getLocation().add(lookDirection.clone().multiply(0.15));
                                        enderman.teleport(loc1);
                                        // 移除末影人声音
                                        // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_AMBIENT, 0.2f, 1.2f);
                                        // 显示小粒子效果
                                        world.spawnParticle(Particle.PORTAL, enderman.getLocation().add(0, 1.5, 0), 3, 0.1, 0.1, 0.1, 0.01);
                                        break;
                                    case 1: // 继续伸手
                                        // 再向方块方向伸出
                                        Location loc2 = enderman.getLocation().add(lookDirection.clone().multiply(0.1));
                                        enderman.teleport(loc2);
                                        // 在末影人手部位置显示粒子
                                        Location handPos = enderman.getLocation().add(lookDirection.clone().multiply(0.5)).add(0, 1.5, 0);
                                        world.spawnParticle(Particle.PORTAL, handPos, 5, 0.05, 0.05, 0.05, 0.01);
                                        break;
                                    case 2: // 拿起方块
                                        // 微微下蹲
                                        Location loc3 = enderman.getLocation().add(0, -0.1, 0);
                                        enderman.teleport(loc3);
                                        // 在方块位置显示粒子，模拟拿起方块
                                        world.spawnParticle(Particle.PORTAL, blockLocation.clone().add(0.5, 0.5, 0.5), 10, 0.2, 0.2, 0.2, 0.05);
                                        // 移除拿起方块的声音
                                        // world.playSound(blockLocation, Sound.ENTITY_ENDERMAN_HURT, 0.3f, 1.5f);
                                        break;
                                    case 3: // 继续拿起方块动作
                                        // 站直身体
                                        Location loc4 = enderman.getLocation().add(0, 0.1, 0);
                                        enderman.teleport(loc4);
                                        // 在末影人手部位置显示更多粒子
                                        handPos = enderman.getLocation().add(lookDirection.clone().multiply(0.5)).add(0, 1.5, 0);
                                        world.spawnParticle(Particle.PORTAL, handPos, 8, 0.1, 0.1, 0.1, 0.02);
                                        break;
                                    case 4: // 准备放置方块
                                        // 向方块位置移动
                                        Location loc5 = enderman.getLocation().add(lookDirection.clone().multiply(0.2));
                                        enderman.teleport(loc5);
                                        // 在方块位置显示更多粒子
                                        world.spawnParticle(Particle.PORTAL, blockLocation.clone().add(0.5, 0.5, 0.5), 15, 0.25, 0.25, 0.25, 0.05);
                                        break;
                                    case 5: // 放置方块
                                        // 微微下蹲
                                        Location loc6 = enderman.getLocation().add(0, -0.1, 0);
                                        enderman.teleport(loc6);
                                        // 在方块位置显示大量粒子，模拟放置方块
                                        world.spawnParticle(Particle.PORTAL, blockLocation.clone().add(0.5, 0.5, 0.5), 20, 0.3, 0.3, 0.3, 0.1);
                                        // 保留放置方块的声音
                                        world.playSound(blockLocation, Sound.BLOCK_WOOL_PLACE, 0.5f, 1.0f);
                                        break;
                                    case 6: // 放置后站直
                                        // 站直身体
                                        Location loc7 = enderman.getLocation().add(0, 0.1, 0);
                                        enderman.teleport(loc7);
                                        // 在方块位置显示少量粒子
                                        world.spawnParticle(Particle.PORTAL, blockLocation.clone().add(0.5, 0.5, 0.5), 5, 0.1, 0.1, 0.1, 0.02);
                                        break;
                                    case 7: // 回到原位置
                                        // 向后微微移动，回到原位置
                                        Location loc8 = enderman.getLocation().subtract(lookDirection.clone().multiply(0.2));
                                        enderman.teleport(loc8);
                                        // 移除完成声音
                                        // world.playSound(enderman.getLocation(), Sound.ENTITY_ENDERMAN_AMBIENT, 0.2f, 0.8f);
                                        break;
                                }

                                step++;
                            }
                        }.runTaskTimer(plugin, 1L, 3L); // 每3刻执行一次，增加动作流畅度

                        // 延迟关闭AI，让动作持续时间更长
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (enderman.isValid()) {
                                    enderman.setAI(false);
                                }
                            }
                        }.runTaskLater(plugin, 25L); // 增加到25刻，让动作持续时间更长

                        // 更新上一个放置的方块位置
                        lastPlacedLocation = blockLocation.clone();
                    } catch (Exception e) {
                        // 捕获并记录任何异常
                        plugin.getLogger().severe("放置方块时发生异常: " + e.getMessage());
                        e.printStackTrace();
                    } finally {
                        // 无论如何，移动到下一个方块
                        currentBlockIndex++;
                    }
                }
            }.runTaskTimer(plugin, 10L, buildDelay[0]); // 初始延迟设置为10刻，给末影人一些准备时间

            player.sendMessage("§a成功召唤末影建筑师！它将为你建造一座桥梁。");

            // 消耗物品
            takeItem();
        }

        private List<Location> calculateBridgeLocations(Location startLocation, Vector direction, int length, int width, boolean hasRailings, Player player, boolean usePlayerHead, boolean placePlayerHeadAtEnd) {
            List<Location> locations = new ArrayList<>();

            plugin.getLogger().info("计算桥梁位置: 起始点=" + startLocation.getBlockX() + "," + startLocation.getBlockY() + "," + startLocation.getBlockZ() + ", 方向=" + direction.getX() + "," + direction.getY() + "," + direction.getZ());

            // 强制将方向转换为四个基本方向之一（北、南、东、西）
            // 这样可以确保桥梁是直线的
            double x = direction.getX();
            double z = direction.getZ();
            if (Math.abs(x) > Math.abs(z)) {
                // 主要是x方向
                direction = new Vector(x > 0 ? 1 : -1, 0, 0);
            } else {
                // 主要是z方向
                direction = new Vector(0, 0, z > 0 ? 1 : -1);
            }
            plugin.getLogger().info("标准化后的方向: " + direction.getX() + "," + direction.getY() + "," + direction.getZ());

            // 计算垂直于方向的向量
            Vector perpendicular = new Vector(-direction.getZ(), 0, direction.getX()).normalize();
            plugin.getLogger().info("垂直向量: " + perpendicular.getX() + "," + perpendicular.getY() + "," + perpendicular.getZ());

            // 计算桥梁的起始位置（中心点）
            Location centerStart = startLocation.clone();

            // 修改: 将起始位置向前移动一格
            // 确保桥梁从点击的方块前面开始建造
            centerStart.add(direction.clone()); // 将起始位置向前移动一格
            plugin.getLogger().info("起始位置设置在点击的方块前面: " + centerStart.getBlockX() + "," + centerStart.getBlockY() + "," + centerStart.getBlockZ());

            // 按照每一段的顺序生成桥梁位置
            for (int i = 0; i < length; i++) {
                // 计算当前段的中心位置
                Location centerLocation = centerStart.clone().add(direction.clone().multiply(i));

                // 计算左右两侧的位置（共五个位置）
                Location pos1 = centerLocation.clone().add(perpendicular.clone().multiply(2)); // 最左侧
                Location pos2 = centerLocation.clone().add(perpendicular.clone().multiply(1)); // 左侧
                Location pos3 = centerLocation.clone(); // 中间
                Location pos4 = centerLocation.clone().subtract(perpendicular.clone().multiply(1)); // 右侧
                Location pos5 = centerLocation.clone().subtract(perpendicular.clone().multiply(2)); // 最右侧

                plugin.getLogger().info("第" + i + "段桥梁位置: 五个位置分别为: " +
                    pos1.getBlockX() + "," + pos1.getBlockY() + "," + pos1.getBlockZ() + " | " +
                    pos2.getBlockX() + "," + pos2.getBlockY() + "," + pos2.getBlockZ() + " | " +
                    pos3.getBlockX() + "," + pos3.getBlockY() + "," + pos3.getBlockZ() + " | " +
                    pos4.getBlockX() + "," + pos4.getBlockY() + "," + pos4.getBlockZ() + " | " +
                    pos5.getBlockX() + "," + pos5.getBlockY() + "," + pos5.getBlockZ());

                // 添加所有五个位置的底层方块（桥面）
                locations.add(pos1.clone());
                locations.add(pos2.clone());
                locations.add(pos3.clone());
                locations.add(pos4.clone());
                locations.add(pos5.clone());

                // 根据要求，只有第1格和第5格高出一格作为栏杆，第3格（中间位置）不高出
                if (hasRailings) {
                    // 最左侧栏杆（第1格）
                    Location leftRailing = pos1.clone().add(0, 1, 0);
                    locations.add(leftRailing);

                    // 最右侧栏杆（第5格）
                    Location rightRailing = pos5.clone().add(0, 1, 0);
                    locations.add(rightRailing);

                    // 特别注意: 确保不会在第3格（中间位置）添加高出的方块
                    // 这里不需要添加任何代码，只是为了强调这一点
                }
            }

            // 打印桥梁位置信息以便于调试
            plugin.getLogger().info("生成桥梁位置数量: " + locations.size());
            for (int i = 0; i < Math.min(10, locations.size()); i++) {
                Location loc = locations.get(i);
                plugin.getLogger().info("桥梁位置 " + i + ": " + loc.getBlockX() + ", " + loc.getBlockY() + ", " + loc.getBlockZ());
            }

            // 不需要在桥的尾部放置玩家头颅
            // 移除这部分代码

            return locations;
        }

        private byte getTeamColorData(Team team) {
            // 根据队伍名称或颜色返回对应的羊毛颜色数据值
            String teamName = team.getDisplayName().toLowerCase();

            // 打印队伍名称以便于调试
            plugin.getLogger().info("检测到队伍名称: " + teamName);

            // 尝试使用队伍的DyeColor来确定颜色
            try {
                org.bukkit.DyeColor dyeColor = team.getDyeColor();
                plugin.getLogger().info("队伍的DyeColor: " + dyeColor.name());

                switch (dyeColor) {
                    case RED: return 14;
                    case BLUE: return 11;
                    case LIME: return 5;
                    case YELLOW: return 4;
                    case LIGHT_BLUE: return 3;
                    case PINK: return 6;
                    case GRAY: return 8;
                    case WHITE: return 0;
                    case ORANGE: return 1;
                    case MAGENTA: return 2;
                    case CYAN: return 9;
                    case PURPLE: return 10;
                    case BROWN: return 12;
                    case GREEN: return 13;
                    case BLACK: return 15;
                    default: break;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("无法获取队伍的DyeColor: " + e.getMessage());
            }

            // 如果无法使用DyeColor，尝试使用队伍名称
            if (teamName.contains("red")) return 14; // 红色
            if (teamName.contains("blue")) return 11; // 蓝色
            if (teamName.contains("green")) return 5; // 绿色
            if (teamName.contains("yellow")) return 4; // 黄色
            if (teamName.contains("aqua") || teamName.contains("cyan")) return 3; // 青色
            if (teamName.contains("pink")) return 6; // 粉色
            if (teamName.contains("gray")) return 8; // 灰色
            if (teamName.contains("white")) return 0; // 白色

            // 检查队伍显示名称的首字母或简写
            if (teamName.startsWith("r") || teamName.startsWith("红")) return 14; // 红色
            if (teamName.startsWith("b") || teamName.startsWith("蓝")) return 11; // 蓝色
            if (teamName.startsWith("g") || teamName.startsWith("绿")) return 5; // 绿色
            if (teamName.startsWith("y") || teamName.startsWith("黄")) return 4; // 黄色
            if (teamName.startsWith("a") || teamName.startsWith("c") || teamName.startsWith("青")) return 3; // 青色
            if (teamName.startsWith("p") || teamName.startsWith("粉")) return 6; // 粉色
            if (teamName.startsWith("gr") || teamName.startsWith("灰")) return 8; // 灰色
            if (teamName.startsWith("w") || teamName.startsWith("白")) return 0; // 白色

            // 默认返回白色
            plugin.getLogger().warning("无法识别队伍颜色，使用默认白色");
            return 0;
        }

        private Material getTeamColoredWool(byte colorData) {
            // 根据颜色数据返回对应的羊毛材质
            Material woolMaterial;

            switch (colorData) {
                case 14: woolMaterial = Material.RED_WOOL; break;
                case 11: woolMaterial = Material.BLUE_WOOL; break;
                case 5: woolMaterial = Material.LIME_WOOL; break; // 使用LIME_WOOL代替GREEN_WOOL以匹配游戏中的亮绿色
                case 4: woolMaterial = Material.YELLOW_WOOL; break;
                case 3: woolMaterial = Material.LIGHT_BLUE_WOOL; break;
                case 6: woolMaterial = Material.PINK_WOOL; break;
                case 8: woolMaterial = Material.GRAY_WOOL; break;
                case 0: woolMaterial = Material.WHITE_WOOL; break;
                case 1: woolMaterial = Material.ORANGE_WOOL; break;
                case 2: woolMaterial = Material.MAGENTA_WOOL; break;
                case 9: woolMaterial = Material.CYAN_WOOL; break;
                case 10: woolMaterial = Material.PURPLE_WOOL; break;
                case 12: woolMaterial = Material.BROWN_WOOL; break;
                case 13: woolMaterial = Material.GREEN_WOOL; break;
                case 15: woolMaterial = Material.BLACK_WOOL; break;
                default: woolMaterial = Material.WHITE_WOOL; break;
            }

            // 打印所选的羊毛颜色以便于调试
            plugin.getLogger().info("选择的羊毛颜色: " + woolMaterial.name() + " (颜色数据: " + colorData + ")");
            return woolMaterial;
        }
    }
}
