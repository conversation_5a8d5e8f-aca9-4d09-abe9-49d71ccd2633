package de.marcely.bedwars.api.game.spawner;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.SpawnerDropEvent;
import de.marcely.bedwars.api.world.hologram.HologramEntity;
import de.marcely.bedwars.tools.location.XYZ;
import java.util.Collection;
import org.bukkit.entity.Item;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * Represents a single (existing) spawner inside an arena.
 * This is used to periodically spawn items.
 */
public interface Spawner {

  /**
   * Returns the arena in which the spawner was added.
   *
   * @return The arena on which the spawner has been added
   */
  Arena getArena();

  /**
   * Returns where the spawner has been added.
   *
   * @return The location of the spawner
   */
  XYZ getLocation();

  /**
   * Returns the type of the spawner.
   *
   * @return The drop type
   */
  DropType getDropType();

  /**
   * Returns the time in seconds it takes until it drops something again.
   *
   * @return Duration in seconds until it drops something
   */
  double getCurrentDropDuration();

  /**
   * Use {@link SpawnerDurationModifier}s to modify the time until something gets dropped.
   * Plugin goes through them from bottom to top to calculate {@link #getCurrentDropDuration()}.
   * <p>
   * 	It's NOT safe to modify this List.
   * <p>
   * 	Default ones only initiate with the start of a match.
   *
   * @return The list containing all modifiers for calculating the drop duration
   */
  List<SpawnerDurationModifier> getDropDurationModifiers();

  /**
   * Adds a new modifier to the top of {@link #getDropDurationModifiers()}.
   * Through this you're able to change the duration until something gets dropped.
   * <p>
   * 	It's safe to have multiple modifiers with the same name, but it's not recommended to do so.
   * 	Additionally it's not a must for the id to follow a specific format. It's recommended to follow plugin:action_name
   * <p>
   * 	Keep in mind that they get reset with every start of a match
   *
   * @param id The unique id of what this spawner is doing
   * @param plugin The plugin that's invoking this
   * @param operation What mathematical operation shall be done with the previous number to obtain the new one
   * @param value Used in combination with <code>operation</code> to calculate the final number (in seconds)
   * @return The new modifier that has been to the top of {@link #getDropDurationModifiers()}
   */
  SpawnerDurationModifier addDropDurationModifier(String id, Plugin plugin, SpawnerDurationModifier.Operation operation, double value);

  /**
   * Removes an existing modifier.
   * <p>
   * 	Modifiers are being used to modify the time until something gets dropped.
   *
   * @param modifier The modifier instance that shall be removed
   * @return <code>true</code> if has been found and removed
   */
  boolean removeDropDurationModifier(SpawnerDurationModifier modifier);

  /**
   * Tries to locate a modifier that has been added to this spawner by it's id.
   * <p>
   * 	It's possible that there are multiple modifiers with an id, this method will only return one of them.
   * <p>
   * 	Default ones only initiate with the start of a match.
   *
   * @param id The id that the modifier has
   * @return The modifier instance that has the given id. <code>null</code> if none has been found
   */
  @Nullable SpawnerDurationModifier getDropDurationModifier(String id);

  /**
   * Returns the time in ticks of the internal clock that's being used for this spawner.
   * The returned value might differ from other spawners. It's being used to determinate when the next drop should occur.
   * It does not increase when the game is not running and gets reset when the game end.
   *
   * @return The internal time of this spawner in ticks
   */
  int getInternalClock();

  /**
   * Returns the time in ticks until the spawner drops something again
   *
   * @return The time in ticks that needs to pass until something drops
   */
  int getRemainingNextDropTime();

  /**
   * While generally all hologram lines are basically the same you may make them unique per spawner using this spawner using {@link #setOverridingHologramLines(String[])}.
   * This method returns the currently set ones, or <code>null</code> if none has been set.
   *
   * @return The lines that (possibly) will be shown above the spawners during a game instead of the global variant
   */
  @Nullable String[] getOverridingHologramLines();

  /**
   * While generally all hologram lines are basically the same you may make them unique per spawner using this spawner using this method.
   * You may pass <code>null</code> to instead again use the default one.
   * <p>
   * 	Keep in mind that these are specific to this spawner and that they'll disappear after the arena or the spawner gets unloaded.
   *
   * @param lines The new lines that (possibly) will be shown above the spawners during a game instead of the global variant
   */
  void setOverridingHologramLines(@Nullable String[] lines);

  /**
   * Make the spawner do its thing.
   * <p>
   * 	This will do the exact same thing as if the game would drop it naturally.
   * 	By this {@link SpawnerDropEvent} and everything else is also getting called.
   * <p>
   * 	Keep in mind that properties, such as {@link DropType#getMaxNearbyItems()}, can prevent the thing from being spawned
   */
  default void drop() {
    drop(false, null);
  }

  /**
   * Make the spawner do its thing.
   * <p>
   * 	This will do the exact same thing as if the game would drop it naturally.
   * 	By this {@link SpawnerDropEvent} and everything else is also getting called.
   * <p>
   * 	Keep in mind that properties, such as {@link DropType#getMaxNearbyItems()}, can prevent the thing from being spawned
   *
   * @param overrideLimit weather or not the spawn limit should be overridden
   */
  default void drop(boolean overrideLimit) {
    drop(overrideLimit, null);
  }

  /**
   * Make the spawner do its thing.
   * <p>
   * 	This will do the exact same thing as if the game would drop it naturally.
   * 	By this {@link SpawnerDropEvent} and everything else is also getting called.	 *
   * <p>
   * 	Keep in mind that properties, such as {@link DropType#getMaxNearbyItems()}, can prevent the thing from being spawned
   *
   * @param overrideLimit weather or not the spawn limit should be overridden
   * @param droppingMaterials an array of ItemStacks the spawner should drop. Note that only ItemStacks native to the spawner will count towards the maxNearbyItems count (or gen cap) of this spawner
   */
  void drop(boolean overrideLimit, @Nullable ItemStack[] droppingMaterials);

  /**
   * Counts all the items that are currently uncollected in the spawner.
   * <p>
   * 	Note: this count only includes items native to this spawner that have never been picked up by a player before.
   * </p>
   * <p>
   *  For performance reasons, it might be off than the actual count.
   *  MBedwars won't do more calculations than it needs to for its own purposes.
   *  Use {@link #getDroppedItems()} to get the actual items, in case you need a precise number.
   * </p>
   *
   * @return return the number of items in the spawner waiting for pickup
   */
  int getNearbyItemsCount();

  /**
   * Returns all the items that are currently uncollected in the spawner.
   * <p>
   *   It may be empty if there is no need for MBedwars's purposes due to configurations.
   * </p>
   *
   * @return The items in the spawner waiting for pickup
   * @throws java.lang.IllegalStateException If no items are being tracked
   */
  Collection<Item> getDroppedItems();

  /**
   * Change the max nearby items for this spawner
   * <p>
   * By default, the spawner is using the
   * value attached to the DropType, but you can change it with this method
   * </p>
   *
   * @param max The number of items that can be in the spawner at the same time.
   */
  void setMaxNearbyItems(int max);

  /**
   * Returns the max amount of spawner items that can be in this spawner before it stops spawning new items
   *
   * @return The max amount of items that can be in a spawner at once.
   */
  int getMaxNearbyItems();

  /**
   * Gets the hologram that is used to display i.a. the remaining time and the block.
   * <p>
   *   May be <code>null</code> if e.g. the round hasn't started yet or it has been disabled.
   * </p>
   *
   * @return Get the visual hologram of this spawner. May be <code>null</code>
   */
  @Nullable
  HologramEntity getHologram();

  /**
   * Returns if the spawner still exists on the arena.
   *
   * @return It's existence
   */
  boolean exists();

  /**
   * Tries to remove the spawner from the arena.
   * <p>
   * 	Can fail when the spawner already doesn't exist.
   *
   * @return <code>true</code> if it was successful
   */
  boolean remove();
}