package de.marcely.bedwars.api.configuration;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.List;

/**
 * Contains API for getting and modifying MBedwars configurations.
 */
@SuppressWarnings("deprecation")
public interface ConfigurationAPI {

  /**
   * Gets all config names in the main config file.
   *
   * @return A list of all the configs
   */
  Collection<String> getAllNames();

  /**
   * Get the description of a certain config.
   *
   * @param name The name of the config value in the config
   * @return The description of the config
   * @throws IllegalArgumentException if the passed config name does not exist in the MBedwars config
   */
  @Nullable List<String> getDescription(String name) throws IllegalArgumentException;

  /**
   * Get the object that is currently set for this config
   *
   * @param name The name of the config value in the config
   * @return The value that is current set
   * @throws IllegalArgumentException if the passed config name does not exist in the MBedwars config
   */
  Object getValue(String name) throws IllegalArgumentException;

  /**
   * Updates the config value stored in memory. This will NOT save the user's config.
   * The value will be reset to the configured value if the plugin is reloaded, or the server is restarted.
   *
   * @param name  The name of the config you want to change
   * @param value The new value for that config
   * @throws IllegalArgumentException if the passed config name does not exist in the MBedwars config, or if the object is not the correct data type
   */
  void setValue(String name, Object value) throws IllegalArgumentException;

  /**
   * Returns the data type used for a certian Config.
   *
   * @param name the name of the config
   * @return the class (data type) that a config value is stored as
   * @throws IllegalArgumentException if the passed config name does not exist in the MBedwars config
   */
  @Nullable Class<?> getValueType(String name) throws IllegalArgumentException;

  /**
   * Saves a config file based on the data currently loaded in memory.
   * This method must be called from the main thread.
   *
   * @param file the file type that is being re-saved
   * @throws IllegalStateException if this method is run outside the primary thread
   */
  void saveNow(ConfigFile file) throws IllegalStateException;

  /**
   * Returns the global ConfigurationAPI instance.
   *
   * @return The global ConfigurationAPI instance
   */
  static ConfigurationAPI get() {
    return BedwarsAPILayer.INSTANCE.getConfigurationAPI();
  }
}
