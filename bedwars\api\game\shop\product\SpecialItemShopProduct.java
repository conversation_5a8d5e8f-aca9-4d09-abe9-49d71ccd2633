package de.marcely.bedwars.api.game.shop.product;

import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import org.jetbrains.annotations.Nullable;

public interface SpecialItemShopProduct extends ItemShopProduct {

  /**
   * Returns the special item that will be given on purchase.
   * <br>
   * Might be <code>null</code> when no special item has been registered with the given id.
   * Use {@link #getSpecialItem()} to obtain the id of the special item.
   *
   * @return The special item. <code>null</code> when it hasn't been registered yet
   */
  @Nullable SpecialItem getSpecialItem();

  /**
   * Returns the id of the special item that will be given on purchase.
   *
   * @return The id of the special item
   */
  String getSpecialItemId();

  /**
   * Set the {@link SpecialItem} that shall be given on purchase
   *
   * @param item The new SpecialItem
   */
  void setSpecialItem(SpecialItem item);

  /**
   * Set the id of the {@link SpecialItem} that shall be given.
   * <br>
   * Preferbly should be {@link SpecialItem#getId()}.
   *
   * @param specialItemId The new id of the special item
   */
  void setSpecialItemId(String specialItemId);
}
