package de.marcely.bedwars.api.game.upgrade.layout;

import org.bukkit.plugin.Plugin;

public interface UpgradeShopLayout {

  /**
   * Returns the type of the layout to make it easier to differentiate to default ones
   *
   * @return The type of layout
   */
  default UpgradeShopLayoutType getType() {
    return UpgradeShopLayoutType.PLUGIN;
  }

  /**
   * Returns the name of the layout that will be shown inter alia in the shop config
   *
   * @return The name of the UpgradeShopLayout
   */
  String getName();

  /**
   * Returns whether the layout is in beta state
   * <p>
   *  Beta meaning that the layout hasn't been completely finished yet or hasn't been tested
   * </p>
   *
   * @return If it's in beta state
   */
  boolean isBeta();

  /**
   * Returns the plugin that has created the layout
   *
   * @return The plugin that created the layout
   */
  Plugin getPlugin();

  /**
   * Returns the handler of it
   *
   * @return The handler of the layout
   */
  UpgradeShopLayoutHandler getHandler();
}
