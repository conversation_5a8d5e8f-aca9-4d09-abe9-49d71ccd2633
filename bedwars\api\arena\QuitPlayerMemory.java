package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.game.shop.BuyGroup;
import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.api.player.PlayerStats;
import de.marcely.bedwars.tools.Validate;
import java.time.Duration;
import java.time.Instant;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.Inventory;
import org.bukkit.metadata.MetadataStoreBase;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.metadata.Metadatable;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * Gets constructed whenever a player leaves a running match.
 * <p>
 *   Used to keep track of all prior players who already have left.
 *   Also used to store data for a player's rejoin capability.
 * </p>
 */
public class QuitPlayerMemory implements Metadatable {

  private final UUID uuid;
  private final String username;

  private Team team;
  private boolean rejoinPermitted;
  private boolean bypassTraps;
  private Instant lastJoinTime, quitTime;
  private Duration playTime;
  private BukkitTask soloRejoinTask;
  private int shopResourcesSpentAmount;
  private final Map<BuyGroup, Integer> buyGroupLevels = new IdentityHashMap<>();
  private final Set<ShopItem> oneTimePurchaseBoughtItems = new HashSet<>();
  private final Inventory playerPrivateInventory;
  private final Map<String, Number> gameStats = new HashMap<>();

  private final MetadataStoreBase<QuitPlayerMemory> metadataStore;

  public QuitPlayerMemory(
      OfflinePlayer player,
      Team team,
      boolean rejoinPermitted,
      Instant lastJoinTime,
      Instant quitTime,
      Duration playTime,
      boolean bypassTraps,
      Inventory playerPrivateInventory,
      @Nullable BukkitTask soloRejoinTask,
      int shopResourcesSpentAmount) {
    this(player.getUniqueId(), player.getName(), team, rejoinPermitted, lastJoinTime, quitTime, playTime, bypassTraps, playerPrivateInventory, soloRejoinTask, shopResourcesSpentAmount);
  }

  public QuitPlayerMemory(
      UUID uniqueId,
      String username,
      Team team,
      boolean rejoinPermitted,
      Instant lastJoinTime,
      Instant quitTime,
      Duration playTime,
      boolean bypassTraps,
      Inventory playerPrivateInventory,
      @Nullable BukkitTask soloRejoinTask,
      int shopResourcesSpentAmount) {
    Validate.notNull(uniqueId, "uniqueId");
    Validate.notNull(username, "username");
    Validate.notNull(team, "team");
    Validate.notNull(playerPrivateInventory, "playerPrivateInventory");
    Validate.notNull(lastJoinTime, "lastJoinTime");
    Validate.notNull(quitTime, "quitTime");

    this.uuid = uniqueId;
    this.username = username;
    this.team = team;
    this.rejoinPermitted = rejoinPermitted;
    this.playerPrivateInventory = playerPrivateInventory;
    this.bypassTraps = bypassTraps;
    this.lastJoinTime = lastJoinTime;
    this.quitTime = quitTime;
    this.playTime = playTime;
    this.soloRejoinTask = soloRejoinTask;
    this.shopResourcesSpentAmount = shopResourcesSpentAmount;
    this.metadataStore = new MetadataStoreBase<QuitPlayerMemory>() {
      protected String disambiguate(QuitPlayerMemory arena, String key) {
        return key;
      }
    };
  }

  /**
   * Returns the uuid ({@link OfflinePlayer#getUniqueId()}) of the player.
   *
   * @return The uniqueId of the player
   */
  public UUID getUniqueId() {
    return this.uuid;
  }

  /**
   * Returns the name ({@link OfflinePlayer#getName()}) of the player.
   *
   * @return The username of the player
   */
  public String getUsername() {
    return this.username;
  }

  /**
   * Returns the team to which he belongs to.
   *
   * @return The team of the player
   */
  public Team getTeam() {
    return this.team;
  }

  /**
   * Set the team to which the player belongs to.
   *
   * @param team The new team of the player
   */
  public void setTeam(Team team) {
    Validate.notNull(team, "team");

    this.team = team;
  }

  /**
   * Returns if the player is able to rejoin the match.
   * <p>
   *   A player may not be permitted if e.g. his team's elimination
   *   was the cause for him to quit.
   * </p>
   *
   * @return if the player can rejoin
   */
  public boolean isRejoinPermitted() {
    return this.rejoinPermitted;
  }

  /**
   * Set if the player is able to rejoin the match.
   * <p>
   *   A player may not be permitted if e.g. his team's elimination
   *   was the cause for him to quit.
   * </p>
   *
   * @param rejoinPermitted if the player can rejoin
   */
  public void setRejoinPermitted(boolean rejoinPermitted) {
    this.rejoinPermitted = rejoinPermitted;
  }

  /**
   * Set if the player is able to bypass traps.
   *
   * @return if the player can bypass traps
   */
  public boolean getBypassTraps() {
    return bypassTraps;
  }

  /**
   * Set if the player is able to bypass traps.
   *
   * @param bypassTraps if the player can bypass traps
   */
  public void setBypassTraps(boolean bypassTraps) {
    this.bypassTraps = bypassTraps;
  }

  /**
   * Returns the time when the player has the last (re)joined the match.
   *
   * @return The previous time the player joined the arena
   */
  public Instant getLastJoinTime() {
    return this.lastJoinTime;
  }

  /**
   * Set the time when the player has the last time (re)joined the match
   *
   * @param time The new previous time the player joined the arena
   */
  public void setLastJoinTime(Instant time) {
    Validate.notNull(time, "time");

    this.lastJoinTime = time;
  }

  /**
   * Returns the time when the player has quit the arena.
   *
   * @return The time the player decided to leave the match
   */
  public Instant getQuitTime() {
    return this.quitTime;
  }

  /**
   * Set the time when the player has the last time quit the arena.
   *
   * @param time The new time the player decided to leave the match
   */
  public void setQuitTime(Instant time) {
    Validate.notNull(time, "time");

    this.quitTime = time;
  }

  /**
   * Returns the duration that the player has been within the match.
   * <p>
   *     The returned value may not match {@link #getQuitTime()} minus {@link #getLastJoinTime()} as the player may rejoined multiple times.
   * </p>
   *
   * @return The play time of the player
   */
  public Duration getPlayTime() {
    return this.playTime;
  }

  /**
   * Set the total play time the player has been within the match.
   *
   * @param playTime The new play time
   */
  public void setPlayTime(Duration playTime) {
    Validate.notNull(playTime, "playTime");

    this.playTime = playTime;
  }

  /**
   * Returns a map containing all the corresponding buy-group levels of the player.
   * Not all existing buy-groups may be present.
   * <p>
   *     It is safe to modify the entries of the Map.
   * </p>
   *
   * @return A map storing the buy-groups of the player
   */
  public Map<BuyGroup, Integer> getBuyGroupLevels() {
    return this.buyGroupLevels;
  }

  /**
   * Returns the players private inventory, used for ender chests in some configurations.
   * <p>
   *     It is safe to modify the contents of the Inventory.
   * </p>
   *
   * @return The private inventory belong to the player
   */
  public Inventory getPlayerPrivateInventory() {
    return this.playerPrivateInventory;
  }

  /**
   * Returns all ShopItems the player has bought which have {@link ShopItem#isOneTimePurchase()} set to true.
   * <p>
   *     It is safe to modify the entries of the Set.
   * </p>
   *
   * @return All one-time-purchase items that have been bought
   */
  public Set<ShopItem> getOneTimePurchaseBoughtItems() {
    return this.oneTimePurchaseBoughtItems;
  }

  /**
   * Returns the task used to eliminate a team if no players rejoin in the configured time.
   * <p>
   *     If you dont want this team to be eliminated if the player does not rejoin, simply cancel this task.
   * </p>
   *
   * @return The task that will eliminate the players team if he leaves the team empty. <code>null</code> if there were other players on the team still
   */
  @Nullable
  public BukkitTask getSoloRejoinTask(){
    return this.soloRejoinTask;
  }

  /**
   * Set the task used to eliminate a team if no players rejoin in the configured time.
   * <p>
   *   Note: This doesn't cancel the current task if it exists.
   *   Make sure to cancel it before setting a new one.
   * </p>
   *
   * @param task The task that will eliminate the players team if he leaves the team empty. <code>null</code> if there were other players on the team still
   */
  public void setSoloRejoinTask(@Nullable BukkitTask task){
    this.soloRejoinTask = task;
  }

  /**
   * Returns the internal map that represents the game stats of the player.
   * <p>
   *  Note that they won't be added to the actual stats thereafter.
   *  They only exist for representiation purposes.
   * </p>
   *
   * @return The game stats of the player
   * @see PlayerStats#getGameStats()
   */
  public Map<String, Number> getGameStats() {
    return this.gameStats;
  }

  /**
   * Returns the amount of resources the player has spent in the shop.
   * <p>
   *   This number is used for the {@link de.marcely.bedwars.api.player.DefaultPlayerAchievement#SPEND_200_RESOURCES} achievement.
   * </p>
   *
   * @return The amount of resources the player has spent
   */
  public int getShopResourcesSpentAmount() {
    return this.shopResourcesSpentAmount;
  }

  /**
   * Set the amount of resources the player has spent in the shop.
   * <p>
   *   This number is used for the {@link de.marcely.bedwars.api.player.DefaultPlayerAchievement#SPEND_200_RESOURCES} achievement.
   * </p>
   *
   * @param amount The new amount of resources the player has spent
   */
  public void setShopResourcesSpentAmount(int amount) {
    this.shopResourcesSpentAmount = amount;
  }

  @Override
  public List<MetadataValue> getMetadata(String key) {
    return this.metadataStore.getMetadata(this, key);
  }

  @Override
  public boolean hasMetadata(String key) {
    return this.metadataStore.hasMetadata(this, key);
  }

  @Override
  public void removeMetadata(String key, Plugin plugin) {
    this.metadataStore.removeMetadata(this, key, plugin);
  }

  @Override
  public void setMetadata(String key, MetadataValue value) {
    this.metadataStore.setMetadata(this, key, value);
  }
}
