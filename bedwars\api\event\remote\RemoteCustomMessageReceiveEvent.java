package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemoteServer;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import de.marcely.bedwars.api.remote.RemoteAPI;

/**
 * This event is being called when another server has sent a custom message to this server.
 * <p>
 *   This system is useful when you want to communicate between servers.
 * </p>
 * <p>
 *   Custom messages can be sent using:<br>
 *   - {@link RemoteServer#sendCustomMessage(String, byte[])}<br>
 *   - {@link RemoteAPI#broadcastCustomMessage(String, byte[])}
 * </p>
 * <p>
 *   Keep in mind that this event is async.
 * </p>
 */
public class RemoteCustomMessageReceiveEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemoteServer sender;
  private final String channel;
  private final byte[] payload;

  public RemoteCustomMessageReceiveEvent(RemoteServer sender, String channel, byte[] payload) {
    super(true);

    this.sender = sender;
    this.channel = channel;
    this.payload = payload;
  }

  /**
   * Get the server that has sent this message.
   *
   * @return The server that has sent the custom message to us
   */
  public RemoteServer getSender() {
    return this.sender;
  }

  /**
   * Gets the channel parameter that has been passed into the send method on the other server.
   * <p>
   *   The <code>channel</code> parameter is useful as it allows you to be distant from other plugins that make use of the messaging system
   *   as well. You may e.g. insert the name of your plugin. Note that its max length is 16 bytes (1) and that it will be encoded as UTF-8.
   *   You may later use {@link RemoteCustomMessageReceiveEvent#getChannel()} to identify the channel again.
   * </p>
   *
   * @return The channel in which the messages communicate
   */
  public String getChannel() {
    return this.channel;
  }

  /**
   * Gets the unmodified payload.
   * <p>
   *   You may use {@link String#String(byte[])} to turn it into a String.
   * </p>
   *
   * @return The actual message that has been sent
   */
  public byte[] getPayload() {
    return this.payload;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
