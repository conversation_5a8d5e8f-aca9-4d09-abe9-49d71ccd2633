package cn.acebrand.acevotemode.listener;

import cn.acebrand.acevotemode.AceVoteMode;
import org.bukkit.entity.LivingEntity;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;

/**
 * 幸运方块怪物监听器
 * 处理幸运方块生成的怪物死亡事件
 */
public class LuckyBlockMonsterListener implements Listener {

    private final AceVoteMode plugin;

    public LuckyBlockMonsterListener(AceVoteMode plugin) {
        this.plugin = plugin;
    }


    /**
     * 处理实体死亡事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();

        // 检查是否是幸运方块生成的怪物
        if (isLuckyBlockMonster(entity)) {
            // 清空掉落物品
            event.getDrops().clear();

            // 设置经验为0
            event.setDroppedExp(0);

            plugin.getLogger().info("幸运方块怪物死亡，已清空掉落物品和经验: " + entity.getCustomName());
        }
    }

    /**
     * 检查是否是幸运方块生成的怪物
     */
    private boolean isLuckyBlockMonster(LivingEntity entity) {
        // 检查是否有幸运方块怪物的元数据标记
        if (entity.hasMetadata("lucky_block_monster")) {
            return true;
        }

        // 检查自定义名称是否包含幸运方块怪物的前缀
        String customName = entity.getCustomName();
        if (customName != null) {
            return customName.contains("§c愤怒的") ||
                    customName.contains("愤怒的") ||
                    customName.contains("僵尸") ||
                    customName.contains("骷髅") ||
                    customName.contains("蜘蛛") ||
                    customName.contains("苦力怕") ||
                    customName.contains("猪人") ||
                    customName.contains("凋零骷髅") ||
                    customName.contains("烈焰人");
        }

        return false;
    }
}
