package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.player.PlayerProperties;
import de.marcely.bedwars.api.remote.RemoteServer;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called whenever a value of a property has changed in a {@link PlayerProperties}, set or removed.
 * <p>
 * 	It's possible that this event might get called async.
 * </p>
 */
public class PlayerPropertyChangeEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final PlayerProperties playerProperties;
  private final boolean fromRemote;
  private final String key;
  private final String oldValue;
  private String newValue;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerPropertyChangeEvent(
      PlayerProperties playerProperties,
      boolean fromRemote,
      boolean async,
      String key,
      String previous,
      String after) {

    super(async);

    this.playerProperties = playerProperties;
    this.fromRemote = fromRemote;
    this.key = key;
    this.oldValue = previous;
    this.newValue = after;
  }

  /**
   * Get the {@link PlayerProperties} in which the new value might get set
   *
   * @return The PlayerProperties instance involved in this event
   */
  public PlayerProperties getProperties() {
    return this.playerProperties;
  }

  /**
   * Get whether a non-local server has caused the change.
   * <p>
   *   May be <code>true</code> when the ProxySync addon is used.
   * </p>
   *
   * @return <code>true</code> if a non-local server initiated the change
   */
  public boolean isFromRemoteServer() {
    return this.fromRemote;
  }

  /**
   * Get the key of the property of who the value might change
   *
   * @return The key of the property
   */
  public String getKey() {
    return this.key;
  }

  /**
   * Returns the previous value that was set.
   * <p>
   *     It is possible that it is null. In that case, it wasn't a part of the properties map.
   * </p>
   *
   * @return The previous value, <code>null</code> when it didn't exist before
   */
  @Nullable
  public String getOldValue() {
    return this.oldValue;
  }

  /**
   * Get the new value that's on being planned to be set.
   * <p>
   *     It is possible that the new value may be null, which means that it is getting removed.
   * </p>
   *
   * @return The new value. May be <code>null</code>, in which case it gets removed
   */
  @Nullable
  public String getNewValue() {
    return this.newValue;
  }

  /**
   * Modify the new value to be something else.
   * <p>
   *     Set it to <code>null</code> to remove it.
   * </p>
   *
   * @param number The new "new value". May be <code>null</code>
   */
  public void setNewValue(@Nullable String number) {
    Validate.notNull(number, "number");

    this.newValue = number;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
