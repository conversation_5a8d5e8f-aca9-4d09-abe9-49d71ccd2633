package de.marcely.bedwars.api.event;

import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.plugin.Plugin;

/**
 * Gets called after this plugin tried to create a hook with another plugin.
 */
public class PluginPostHookEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Plugin plugin;
  private final boolean successful;

  public PluginPostHookEvent(Plugin plugin, boolean successful) {
    this.plugin = plugin;
    this.successful = successful;
  }

  /**
   * Returns the plugin to which we tried to hook ourself into.
   *
   * @return The plugin we tried to hook into
   */
  public Plugin getPlugin() {
    return this.plugin;
  }

  /**
   * Returns whether we were successful with creating a hook to another plugin.
   * <p>
   *     It fails in case there was an error while performing the hook registration.
   * </p>
   *
   * @return <code>true</code> when we are now successfully hooked with the given plugin
   */
  public boolean wasSuccessful() {
    return this.successful;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
