package de.marcely.bedwars.api.game.shop.layout;

import org.bukkit.plugin.Plugin;

/**
 * A ShopLayout is what's dealing with the look-and-feel of a shop.<br>
 * By default MBedwars delivers types such as {@link ShopLayoutType#HYPIXEL}.<br>
 * This instance only represents the info of a layout. The actual logic is located in {@link ShopLayoutHandler}.
 */
public interface ShopLayout {

  /**
   * Returns the type of the layout to make it easier to differentiate to default ones
   *
   * @return The type
   */
  default ShopLayoutType getType() {
    return ShopLayoutType.PLUGIN;
  }

  /**
   * Returns the name of the layout that will be shown inter alia in the shop config
   *
   * @return The name
   */
  String getName();

  /**
   * Returns whether or not the layout is in beta state<br>
   * Beta meaning that the layout hasn't been completely finished yet or hasn't been tested
   *
   * @return If it's in beta state
   */
  boolean isBeta();

  /**
   * Returns the plugin that has created the layout
   *
   * @return The plugin that created the layout
   */
  Plugin getPlugin();

  /**
   * Returns the handler of it
   *
   * @return The handler of the layout
   */
  ShopLayoutHandler getHandler();
}
