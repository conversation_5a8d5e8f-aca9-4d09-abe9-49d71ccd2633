package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 恐怖降临模式定时任务管理器
 * 负责管理Boss生成定时器和怪物生成周期
 */
public class TerrorTimerManager {

    private final AceVoteMode plugin;
    private final TerrorDescentMode terrorMode;

    // 竞技场定时器任务
    private final Map<Arena, TimerData> arenaTimers = new ConcurrentHashMap<>();

    public TerrorTimerManager(AceVoteMode plugin, TerrorDescentMode terrorMode) {
        this.plugin = plugin;
        this.terrorMode = terrorMode;
    }

    /**
     * 定时器数据类
     */
    private static class TimerData {
        BukkitTask bossSpawnTask;
        BukkitTask monsterSpawnTask;
        BukkitTask countdownTask;
        long gameStartTime;
        int nextBossSpawnTime;

        TimerData(long gameStartTime, int bossInterval) {
            this.gameStartTime = gameStartTime;
            this.nextBossSpawnTime = bossInterval;
        }
    }

    /**
     * 启动定时器
     */
    public void startTimer(Arena arena) {
        if (arenaTimers.containsKey(arena)) {
            return; // 已经启动过了
        }

        long gameStartTime = System.currentTimeMillis();
        int bossSpawnInterval = terrorMode.getConfig().getInt("boss.spawn-interval", 600); // 默认10分钟

        TimerData timerData = new TimerData(gameStartTime, bossSpawnInterval);
        arenaTimers.put(arena, timerData);

        // 启动Boss生成定时器
        startBossSpawnTimer(arena, timerData, bossSpawnInterval);

        // 启动怪物生成定时器（更频繁）
        startMonsterSpawnTimer(arena, timerData);

        // 启动倒计时提醒任务
        startCountdownTask(arena, timerData);

        plugin.getLogger().info("恐怖降临定时器已在竞技场 " + arena.getName() + " 启动");
    }

    /**
     * 启动Boss生成定时器
     */
    private void startBossSpawnTimer(Arena arena, TimerData timerData, int intervalSeconds) {
        timerData.bossSpawnTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!terrorMode.isArenaActive(arena)) {
                    cancel();
                    return;
                }

                // 生成Boss（BossManager会自动检查数量限制）
                boolean success = terrorMode.getBossManager().spawnBoss(arena);
                if (success) {
                    plugin.getLogger().info("定时Boss已在竞技场 " + arena.getName() + " 生成");
                } else {
                    // 检查是否是因为Boss数量限制而失败
                    int aliveBossCount = terrorMode.getBossManager().getAliveBossCount(arena);
                    if (aliveBossCount > 0) {
                        plugin.getLogger().info("竞技场 " + arena.getName() + " 已有 " + aliveBossCount + " 个Boss存活，跳过生成");
                    } else {
                        plugin.getLogger().warning("定时Boss生成失败在竞技场 " + arena.getName());
                    }
                }

                // 同时生成怪物
                terrorMode.getMonsterSpawnManager().spawnMonstersAtResourcePoints(arena);

                // 更新下次Boss生成时间
                timerData.nextBossSpawnTime += intervalSeconds;
            }
        }.runTaskTimer(plugin, intervalSeconds * 20L, intervalSeconds * 20L);
    }

    /**
     * 启动怪物生成定时器
     */
    private void startMonsterSpawnTimer(Arena arena, TimerData timerData) {
        // 怪物每2分钟生成一次（比Boss频繁）
        int monsterInterval = 120; // 2分钟

        timerData.monsterSpawnTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!terrorMode.isArenaActive(arena)) {
                    cancel();
                    return;
                }

                // 生成怪物
                terrorMode.getMonsterSpawnManager().spawnMonstersAtResourcePoints(arena);
            }
        }.runTaskTimer(plugin, monsterInterval * 20L, monsterInterval * 20L);
    }

    /**
     * 启动倒计时提醒任务
     */
    private void startCountdownTask(Arena arena, TimerData timerData) {
        timerData.countdownTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!terrorMode.isArenaActive(arena)) {
                    cancel();
                    return;
                }

                // 计算距离下次Boss生成的时间
                long currentTime = System.currentTimeMillis();
                long elapsedSeconds = (currentTime - timerData.gameStartTime) / 1000;
                long timeUntilNextBoss = timerData.nextBossSpawnTime - elapsedSeconds;

                // 多个时间点提醒（可配置）
                if (timeUntilNextBoss == 300) { // 5分钟前
                    broadcastCountdown(arena, "5分钟", "&a");
                } else if (timeUntilNextBoss == 180) { // 3分钟前
                    broadcastCountdown(arena, "3分钟", "&2");
                } else if (timeUntilNextBoss == 120) { // 2分钟前
                    broadcastCountdown(arena, "2分钟", "&e");
                } else if (timeUntilNextBoss == 60) { // 1分钟前
                    broadcastCountdown(arena, "1分钟", "&6");
                } else if (timeUntilNextBoss == 30) { // 30秒前
                    broadcastCountdown(arena, "30秒", "&c");
                } else if (timeUntilNextBoss == 15) { // 15秒前
                    broadcastCountdown(arena, "15秒", "&c");
                } else if (timeUntilNextBoss == 10) { // 10秒前
                    broadcastCountdown(arena, "10秒", "&4");
                } else if (timeUntilNextBoss <= 5 && timeUntilNextBoss > 0) { // 5秒倒计时
                    broadcastCountdown(arena, timeUntilNextBoss + "秒", "&4");
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒检查一次
    }

    /**
     * 广播倒计时消息
     */
    private void broadcastCountdown(Arena arena, String timeLeft, String colorCode) {
        String message = colorCode + "&l【恐怖降临】&f Boss将在 " + colorCode + timeLeft + "&f 后降临！";
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

        for (Player player : arena.getPlayers()) {
            player.sendMessage(coloredMessage);
            // 播放音效提醒
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.0f);
        }
    }

    /**
     * 停止定时器
     */
    public void stopTimer(Arena arena) {
        TimerData timerData = arenaTimers.remove(arena);
        if (timerData != null) {
            // 取消所有任务
            if (timerData.bossSpawnTask != null) {
                timerData.bossSpawnTask.cancel();
            }
            if (timerData.monsterSpawnTask != null) {
                timerData.monsterSpawnTask.cancel();
            }
            if (timerData.countdownTask != null) {
                timerData.countdownTask.cancel();
            }

            plugin.getLogger().info("恐怖降临定时器已在竞技场 " + arena.getName() + " 停止");
        }
    }

    /**
     * 获取距离下次Boss生成的时间（秒）
     */
    public long getTimeUntilNextBoss(Arena arena) {
        TimerData timerData = arenaTimers.get(arena);
        if (timerData == null) {
            return -1;
        }

        long currentTime = System.currentTimeMillis();
        long elapsedSeconds = (currentTime - timerData.gameStartTime) / 1000;
        return Math.max(0, timerData.nextBossSpawnTime - elapsedSeconds);
    }

    /**
     * 获取游戏已进行时间（秒）
     */
    public long getGameElapsedTime(Arena arena) {
        TimerData timerData = arenaTimers.get(arena);
        if (timerData == null) {
            return 0;
        }

        long currentTime = System.currentTimeMillis();
        return (currentTime - timerData.gameStartTime) / 1000;
    }

    /**
     * 手动触发Boss生成
     */
    public boolean triggerBossSpawn(Arena arena) {
        if (!terrorMode.isArenaActive(arena)) {
            return false;
        }

        TimerData timerData = arenaTimers.get(arena);
        if (timerData == null) {
            return false;
        }

        // 生成Boss
        boolean success = terrorMode.getBossManager().spawnBoss(arena);
        if (success) {
            // 同时生成怪物
            terrorMode.getMonsterSpawnManager().spawnMonstersAtResourcePoints(arena);

            // 重置定时器
            int bossSpawnInterval = terrorMode.getConfig().getInt("boss.spawn-interval", 600);
            timerData.nextBossSpawnTime = (int) (getGameElapsedTime(arena) + bossSpawnInterval);

            plugin.getLogger().info("手动触发Boss生成在竞技场 " + arena.getName());
        }

        return success;
    }

    /**
     * 手动触发怪物生成
     */
    public void triggerMonsterSpawn(Arena arena) {
        if (!terrorMode.isArenaActive(arena)) {
            return;
        }

        terrorMode.getMonsterSpawnManager().spawnMonstersAtResourcePoints(arena);
        plugin.getLogger().info("手动触发怪物生成在竞技场 " + arena.getName());
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        // 重新启动所有活跃的定时器
        for (Arena arena : arenaTimers.keySet()) {
            if (terrorMode.isArenaActive(arena)) {
                stopTimer(arena);
                startTimer(arena);
            }
        }
    }

    /**
     * 获取定时器状态信息
     */
    public String getTimerStatus(Arena arena) {
        TimerData timerData = arenaTimers.get(arena);
        if (timerData == null) {
            return "定时器未启动";
        }

        long elapsedTime = getGameElapsedTime(arena);
        long timeUntilBoss = getTimeUntilNextBoss(arena);

        return String.format("游戏时间: %d分%d秒 | 下次Boss: %d分%d秒",
                elapsedTime / 60, elapsedTime % 60,
                timeUntilBoss / 60, timeUntilBoss % 60);
    }

    /**
     * 检查定时器是否活跃
     */
    public boolean isTimerActive(Arena arena) {
        return arenaTimers.containsKey(arena);
    }

    /**
     * 暂停定时器
     */
    public void pauseTimer(Arena arena) {
        TimerData timerData = arenaTimers.get(arena);
        if (timerData != null) {
            if (timerData.bossSpawnTask != null) {
                timerData.bossSpawnTask.cancel();
                timerData.bossSpawnTask = null;
            }
            if (timerData.monsterSpawnTask != null) {
                timerData.monsterSpawnTask.cancel();
                timerData.monsterSpawnTask = null;
            }
            if (timerData.countdownTask != null) {
                timerData.countdownTask.cancel();
                timerData.countdownTask = null;
            }
        }
    }

    /**
     * 恢复定时器
     */
    public void resumeTimer(Arena arena) {
        TimerData timerData = arenaTimers.get(arena);
        if (timerData != null) {
            int bossSpawnInterval = terrorMode.getConfig().getInt("boss.spawn-interval", 600);

            // 重新计算剩余时间并启动任务
            long timeUntilBoss = getTimeUntilNextBoss(arena);
            if (timeUntilBoss > 0) {
                startBossSpawnTimer(arena, timerData, (int) timeUntilBoss);
            }

            startMonsterSpawnTimer(arena, timerData);
            startCountdownTask(arena, timerData);
        }
    }
}
