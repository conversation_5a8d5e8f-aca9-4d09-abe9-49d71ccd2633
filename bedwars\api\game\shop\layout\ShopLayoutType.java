package de.marcely.bedwars.api.game.shop.layout;

import de.marcely.bedwars.api.unsafe.ShopLayoutTypeWrapper;
import org.jetbrains.annotations.Nullable;

@SuppressWarnings("deprecation")
public enum ShopLayoutType {

  NORMAL,
  HYPIXEL,
  HIVEMC,
  GOMMEHD,
  REWINSIDE,
  MINESUCHT,
  BERGWERKLABS,
  HYPIXEL_V2,

  PLUGIN;

  private final ShopLayoutTypeWrapper wrapper = null;

  /**
   * Returns the corresponding layout to this type.<br>
   * Might return null if the type is {@link #PLUGIN}
   *
   * @return The layout instance of this type
   */
  public @Nullable ShopLayout getLayout() {
    return this.wrapper.getLayout();
  }
}
