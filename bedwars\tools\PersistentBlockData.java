package de.marcely.bedwars.tools;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.bukkit.DyeColor;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.jetbrains.annotations.Nullable;

/**
 * This class allows you to obtain, store and restore a blocks data.
 * <p>
 *     This class is pretty similar to todays Bukkit's BlockData API.
 *     The reason this exists is to add the same or similar functionality to older versions of spigot, as they don't have that functionality.
 * </p>
 */
public interface PersistentBlockData {

  /**
   * Returns the material of the block.
   *
   * @return The material of the block
   */
  Material getMaterial();

  /**
   * Places the data for a block at a specific location with applying phyics.
   *
   * @param block The block that shall be replaced with this data
   */
  default void place(Block block) {
    place(block, true);
  }

  /**
   * Places the data for a block at a specific location.
   *
   * @param block The block that shall be replaced with this data
   * @param applyPhysics Doesn't calculate physics for this and surrounding blocks. May safe performance
   */
  void place(Block block, boolean applyPhysics);

  /**
   * Transforms the data into a string which later can be reloaded with {@link #parse(String)}.
   * <p>
   *     Keep in mind that it's likely possible that you want be able to load a parsed string in 1.8 - 1.12 within 1.13+ and vice versa.
   * </p>
   *
   * @return A string representing all daa stored in this instance
   */
  String getAsString();

  /**
   * Returns if the given instance is similar to this one.
   *
   * @param data The other instance we want to compare with
   * @return <code>true</code> when they match
   */
  boolean matches(PersistentBlockData data);

  /**
   * Returns whether the stored data matches the current data of the given block.
   *
   * @param block The block that we want to match
   * @return <code>true</code> when they are basically the same
   */
  boolean matches(Block block);

  /**
   * Tries to dye this block and possibly returns a new instance with the blocked dyed in the given color.
   *
   * @param color The new color we want it to have
   * @return A new instance with the new color, or this one
   */
  PersistentBlockData getDyedData(DyeColor color);

  /**
   * Tries to parse a previously encoded string using {@link #getAsString()}.
   * <p>
   *     Keep in mind that it's likely possible that you want be able to load a parsed string in 1.8 - 1.12 within 1.13+ and vice versa.
   * </p>
   *
   * @param str The string we want to parse
   * @return The parsed instance. <code>null</code> if it failed
   */
  static @Nullable PersistentBlockData parse(String str) {
    Validate.notNull(str, "string");

    return BedwarsAPILayer.INSTANCE.blockDataParse(str);
  }

  /**
   * Fetches all the data from the block and constructs a new instance from that
   *
   * @param block The block we want to read from
   * @return The instance representing the data of the block at this given moment
   */
  static PersistentBlockData fromBlock(Block block) {
    Validate.notNull(block, "block");

    return BedwarsAPILayer.INSTANCE.blockDataFromBlock(block);
  }

  /**
   * Constructs a new instance given by a material
   *
   * @param mat The material
   * @return A new instance with only the given material at its containing data
   */
  static PersistentBlockData fromMaterial(Material mat) {
    Validate.notNull(mat, "material");

    return BedwarsAPILayer.INSTANCE.blockDataFromMaterial(mat);
  }
}
