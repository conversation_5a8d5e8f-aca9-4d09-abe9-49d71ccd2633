package cn.acebrand.acevotemode.manager;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gui.VoteResultGUI;
import cn.acebrand.acevotemode.model.GameMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 投票结果管理器
 * 负责管理投票结果的展示和游戏模式的应用
 */
public class VoteResultManager {
    
    private final AceVoteMode plugin;
    private final Map<Arena, VoteResultGUI> activeGUIs;
    
    public VoteResultManager(AceVoteMode plugin) {
        this.plugin = plugin;
        this.activeGUIs = new ConcurrentHashMap<>();
    }
    
    /**
     * 显示投票结果并选择模式（在大厅阶段）
     */
    public void showVoteResultAndSelectMode(Arena arena) {
        // 如果已经有活跃的GUI，先清理
        VoteResultGUI existingGUI = activeGUIs.get(arena);
        if (existingGUI != null) {
            existingGUI.cleanup();
        }

        // 创建新的投票结果GUI
        VoteResultGUI resultGUI = new VoteResultGUI(plugin, arena);
        activeGUIs.put(arena, resultGUI);

        // 显示给所有玩家
        resultGUI.showToAllPlayers();

        // 延迟关闭GUI（不应用模式，等待游戏开始时应用）
        new BukkitRunnable() {
            @Override
            public void run() {
                // 只清理GUI，不应用模式
                resultGUI.cleanup();
                activeGUIs.remove(arena);

                plugin.getLogger().info("Vote result GUI closed for arena: " + arena.getName() +
                        ", selected mode: " + (resultGUI.getFinalMode() != null ? resultGUI.getFinalMode().getPlainName() : "None"));
            }
        }.runTaskLater(plugin, 120L); // 6秒后关闭GUI
    }

    /**
     * 显示投票结果并应用游戏模式（已弃用，保留兼容性）
     */
    @Deprecated
    public void showVoteResultAndApplyMode(Arena arena) {
        showVoteResultAndSelectMode(arena);
    }
    
    /**
     * 应用游戏模式
     */
    private void applyGameMode(Arena arena, GameMode selectedMode) {
        if (selectedMode == null) {
            plugin.getLogger().warning("No game mode selected for arena: " + arena.getName());
            return;
        }
        
        try {
            // 激活游戏模式
            boolean activated = plugin.getGameModeManager().activateGameMode(arena, selectedMode.getId());
            
            if (activated) {
                plugin.getLogger().info("Successfully applied game mode '" + selectedMode.getId() +
                        "' to arena: " + arena.getName());

                // 向玩家发送模式启动的华丽消息（全局升级模式除外）
                if (!isGlobalUpgradeMode(selectedMode.getId())) {
                    sendModeStartMessage(arena, selectedMode);
                }

            } else {
                plugin.getLogger().severe("Failed to apply game mode '" + selectedMode.getId() + 
                        "' to arena: " + arena.getName());
                
                // 发送错误消息给玩家
                for (org.bukkit.entity.Player player : arena.getPlayers()) {
                    player.sendMessage(org.bukkit.ChatColor.RED + "游戏模式应用失败，将使用默认模式");
                }
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Error applying game mode to arena " + arena.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 清理指定竞技场的投票结果GUI
     */
    public void cleanupArena(Arena arena) {
        VoteResultGUI gui = activeGUIs.remove(arena);
        if (gui != null) {
            gui.cleanup();
        }
    }
    
    /**
     * 清理所有投票结果GUI
     */
    public void cleanup() {
        for (VoteResultGUI gui : activeGUIs.values()) {
            gui.cleanup();
        }
        activeGUIs.clear();
    }
    
    /**
     * 检查竞技场是否有活跃的投票结果GUI
     */
    public boolean hasActiveGUI(Arena arena) {
        return activeGUIs.containsKey(arena);
    }

    /**
     * 检查是否是全局升级模式（不需要显示华丽启动消息的模式）
     */
    private boolean isGlobalUpgradeMode(String modeId) {
        // 只有使用全局升级系统且没有特殊功能的模式才不显示华丽消息
        // 目前只有 "global" 和 "default" 这种纯全局升级模式
        return "global".equals(modeId) || "default".equals(modeId);
    }

    /**
     * 发送模式启动的华丽消息（教程说明）
     */
    private void sendModeStartMessage(Arena arena, GameMode selectedMode) {
        // 从配置文件获取消息
        String border = plugin.getConfigManager().getMessage("mode-start.border");
        String titleTemplate = plugin.getConfigManager().getMessage("mode-start.title");
        String description = plugin.getConfigManager().getMessage("mode-start.descriptions." + selectedMode.getId());

        // 如果没有找到特定模式的描述，使用默认描述
        if (description.contains("消息未找到")) {
            description = plugin.getConfigManager().getMessage("mode-start.descriptions.default");
        }

        // 替换标题中的模式名称
        String title = titleTemplate.replace("{mode}", selectedMode.getPlainName());

        // 向所有玩家发送消息
        for (org.bukkit.entity.Player player : arena.getPlayers()) {
            player.sendMessage("");
            player.sendMessage(border);
            player.sendMessage(title);

            // 处理多行描述信息
            String[] lines = description.split("\n");
            for (String line : lines) {
                player.sendMessage(line);
            }

            player.sendMessage(border);
            player.sendMessage("");
        }
    }
}
