# 怪物群体召唤事件配置
# 坏事件 - 召唤一大堆怪物

# 事件基本信息
event:
  name: "MONSTER_HORDE"
  type: "BAD"
  weight: 20
  enabled: true

# 消息设置
messages:
  # 是否发送事件消息到聊天栏
  send_message: true
  # 消息前缀
  message_prefix: "§c[幸运方块] §f"
  # 事件消息
  event_message: "§c怪物大军来袭！准备战斗！"

# 怪物群组配置
monster_groups:
  # 普通怪物组（权重60）
  normal_group:
    weight: 60
    monsters:
      zombie:
        count: 5
        type: "ZOMBIE"
        display_name: "僵尸"
        health: 20.0
        speed_multiplier: 1.1
        equipment_chance: 30  # 装备概率
      skeleton:
        count: 5
        type: "SKELETON"
        display_name: "骷髅"
        health: 20.0
        speed_multiplier: 1.1
        equipment_chance: 30
      spider:
        count: 5
        type: "SPIDER"
        display_name: "蜘蛛"
        health: 16.0
        speed_multiplier: 1.2
      creeper:
        count: 2
        type: "CREEPER"
        display_name: "苦力怕"
        health: 20.0
        explosion_power: 3.0
        charged_chance: 10  # 闪电苦力怕概率
  
  # 地狱怪物组（权重40）
  nether_group:
    weight: 40
    monsters:
      piglin:
        count: 5
        type: "PIGLIN"
        display_name: "猪人"
        health: 16.0
        speed_multiplier: 1.0
        angry: true  # 是否愤怒状态
      wither_skeleton:
        count: 5
        type: "WITHER_SKELETON"
        display_name: "凋零骷髅"
        health: 20.0
        speed_multiplier: 1.0
        equipment_chance: 50
      blaze:
        count: 5
        type: "BLAZE"
        display_name: "烈焰人"
        health: 20.0
        speed_multiplier: 1.0

# 全局怪物设置
global_settings:
  # 生成范围（以玩家为中心的半径，方块）
  spawn_radius: 10
  # 怪物存活时间（秒，0为永久）
  lifetime: 120
  # 是否攻击同队玩家
  attack_teammates: false
  # 是否白天燃烧（设为false防止白天燃烧）
  burn_in_daylight: false
  # 怪物自定义名称前缀
  name_prefix: "§c愤怒的"
