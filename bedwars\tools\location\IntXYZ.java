package de.marcely.bedwars.tools.location;

import java.util.HashMap;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.configuration.serialization.ConfigurationSerializable;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;

/**
 * Represents a 3-dimensional int position (i.a. block coord)
 */
@EqualsAndHashCode
@ToString
public class IntXYZ implements Cloneable, ConfigurationSerializable {

  protected int x, y, z;

  public IntXYZ() {
    this(0, 0, 0);
  }

  public IntXYZ(Location loc) {
    this(loc.getBlockX(), loc.getBlockY(), loc.getBlockZ());
  }

  public IntXYZ(Vector vec) {
    this(vec.getBlockX(), vec.getBlockY(), vec.getBlockZ());
  }

  public IntXYZ(XYZ xyz) {
    this(xyz.getBlockX(), xyz.getBlockY(), xyz.getBlockZ());
  }

  public IntXYZ(Block block) {
    this(block.getX(), block.getY(), block.getZ());
  }

  public IntXYZ(int x, int y, int z) {
    this.x = x;
    this.y = y;
    this.z = z;
  }

  /**
   * Returns the x-coordinate
   *
   * @return The x value
   */
  public int getX() {
    return this.x;
  }

  /**
   * Returns the y-coordinate
   *
   * @return The y value
   */
  public int getY() {
    return this.y;
  }

  /**
   * Returns the z-coordinate
   *
   * @return The z value
   */
  public int getZ() {
    return this.z;
  }

  /**
   * Sets the x-coordinate
   *
   * @param x The new x-coordinate
   * @return This IntXYZ instance
   */
  public IntXYZ setX(int x) {
    this.x = x;

    return this;
  }

  /**
   * Sets the y-coordinate
   *
   * @param y The new y-coordinate
   * @return This IntXYZ instance
   */
  public IntXYZ setY(int y) {
    this.y = y;

    return this;
  }

  /**
   * Sets the z-coordinate
   *
   * @param z The new z-coordinate
   * @return This IntXYZ instance
   */
  public IntXYZ setZ(int z) {
    this.z = z;

    return this;
  }

  /**
   * Sets the new coordinates
   *
   * @param x The new x-coordinate
   * @param y The new y-coordinate
   * @param z The new z-coordinate
   * @return This IntXYZ instance
   */
  public IntXYZ set(int x, int y, int z) {
    this.x = x;
    this.y = y;
    this.z = z;

    return this;
  }

  /**
   * Copies and sets the xyz coordinates from the given object
   *
   * @param xyz The object from which it shall be taken from
   * @return This IntXYZ instance
   */
  public IntXYZ set(XYZ xyz) {
    this.x = xyz.getBlockX();
    this.y = xyz.getBlockY();
    this.z = xyz.getBlockZ();

    return this;
  }

  /**
   * Copies and sets the xyz coordinates from the given object
   *
   * @param loc The object from which it shall be taken from
   * @return This IntXYZ instance
   */
  public IntXYZ set(Location loc) {
    this.x = loc.getBlockX();
    this.y = loc.getBlockY();
    this.z = loc.getBlockZ();

    return this;
  }


  /**
   * Copies and sets the xyz coordinates from the given block
   *
   * @param block The object from which it shall be taken from
   * @return This IntXYZ instance
   */
  public IntXYZ set(Block block) {
    this.x = block.getX();
    this.y = block.getY();
    this.z = block.getZ();

    return this;
  }

  /**
   * Adds the x-coordinates
   *
   * @param x The added x-coordinate
   * @param y The added y-coordinate
   * @param z The added z-coordinate
   * @return This IntXYZ instance
   */
  public IntXYZ add(int x, int y, int z) {
    this.x += x;
    this.y += y;
    this.z += z;

    return this;
  }

  public IntXYZ add(IntXYZ xyz) {
    this.x += xyz.x;
    this.y += xyz.y;
    this.z += xyz.z;

    return this;
  }

  public IntXYZ subtract(int x, int y, int z) {
    this.x -= x;
    this.y -= y;
    this.z -= z;

    return this;
  }

  public IntXYZ subtract(IntXYZ xyz) {
    this.x -= xyz.x;
    this.y -= xyz.y;
    this.z -= xyz.z;

    return this;
  }

  public IntXYZ multiply(int amount) {
    this.x *= amount;
    this.y *= amount;
    this.z *= amount;

    return this;
  }

  public IntXYZ multiply(int x, int y, int z) {
    this.x *= x;
    this.y *= y;
    this.z *= z;

    return this;
  }

  /**
   * Sets all coordinates to 0
   *
   * @return This IntXYZ instance
   */
  public IntXYZ zero() {
    this.x = 0;
    this.y = 0;
    this.z = 0;

    return this;
  }

  /**
   * Get whether all coordinates are set to 0
   *
   * @return <code>true</code> if X, Y and Z are set to 0
   */
  public boolean isZero() {
    return this.x == 0 && this.y == 0 && this.z == 0;
  }

  public double distance(IntXYZ o) {
    return Math.sqrt(distanceSquared(o));
  }

  public double distanceSquared(IntXYZ o) {
    return NumberConversions.square(x - o.x) +
        NumberConversions.square(y - o.y) + NumberConversions.square(z - o.z);
  }

  /**
   * Converts this XYZ object to a new Location object
   *
   * @param world The world needed for creating the Location
   * @return A new location whose coordinates where taken from this object
   */
  public Location toLocation(World world) {
    return new Location(world, this.x, this.y, this.z);
  }

  /**
   * Converts this XYZ object to a new Vector object
   *
   * @return A new vector whose coordinates where taken from this object
   */
  public Vector toVector() {
    return new Vector(this.x, this.y, this.z);
  }

  /**
   * Get the Bukkit block that you can find at the given position.
   *
   * @param world The world in which we want to look up the block
   * @return The matching block
   */
  public Block getBlock(World world) {
    return world.getBlockAt(this.x, this.y, this.z);
  }

  /**
   * Check whether the block coords match.
   *
   * @param xyz The other object to compare with
   * @return <code>true</code> if the floored coordinates match
   */
  public boolean equalsBlockCoords(XYZ xyz) {
    return this.x == xyz.getBlockX() && this.y == xyz.getBlockY() && this.z == xyz.getBlockZ();
  }

  /**
   * Check whether the block coords match.
   *
   * @param loc The other object to compare with
   * @return <code>true</code> if the floored coordinates match
   */
  public boolean equalsBlockCoords(Location loc) {
    return this.x == loc.getBlockX() && this.y == loc.getBlockY() && this.z == loc.getBlockZ();
  }

  /**
   * Check whether the block coords match.
   *
   * @param block The other object to compare with
   * @return <code>true</code> if the floored coordinates match
   */
  public boolean equalsBlockCoords(Block block) {
    return this.x == block.getX() && this.y == block.getY() && this.z == block.getZ();
  }

  @Override
  public IntXYZ clone() {
    return new IntXYZ(this.x, this.y, this.z);
  }

  @Override
  public Map<String, Object> serialize() {
    final Map<String, Object> data = new HashMap<>();

    data.put("x", this.x);
    data.put("y", this.y);
    data.put("z", this.z);

    return data;
  }

  /**
   * Required method for deserialization
   *
   * @param data map to deserialize
   * @return deserialized xyz instance
   */
  public static IntXYZ deserialize(Map<String, Object> data) {
    return new IntXYZ(
        NumberConversions.toInt(data.get("x")),
        NumberConversions.toInt(data.get("y")),
        NumberConversions.toInt(data.get("z")));
  }
}
