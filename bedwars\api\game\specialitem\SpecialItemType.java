package de.marcely.bedwars.api.game.specialitem;

import org.jetbrains.annotations.Nullable;

public enum SpecialItemType {

  TELEPORTER("Teleporter"),
  MINI_SHOP("MiniShop"),
  RESCUE_PLATFORM("RescuePlatform"),
  TNT_SHEEP("TNTSheep"),
  MAGNETIC_SHOES("MagnetShoes"),
  TRAP("Trap"),
  BRIDGE("Bridge"),
  GUARD_DOG("GuardDog"),
  TRACKER("Tracker"),
  FIREBALL("Fireball"),
  MAGIC_MILK("MagicMilk"),

  PLUGIN(null);

  private final String id;
  private final SpecialItem item = null;

  SpecialItemType(@Nullable String id) {
    this.id = id;
  }

  /**
   * Returns the SpecialItem instance for the given type.
   * <p>
   * Can return <code>null</code> if there's none.
   *
   * @return The corresponding item
   */
  public @Nullable SpecialItem getItem() {
    return this.item;
  }

  /**
   * Returns the internal id of the type.
   * <p>
   * Will return <code>null</code> when it's {@link #PLUGIN}.
   *
   * @return The id of the type
   */
  public @Nullable String getId() {
    return this.id;
  }
}
