package cn.acebrand.acevotemode.events.good;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.entity.Player;
import org.bukkit.Location;

/**
 * 资源奖励事件 - 好事件
 */
public class ResourceBonusEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    
    public ResourceBonusEvent(AceVoteMode plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // TODO: 实现资源奖励逻辑
        player.sendMessage("§a[幸运方块] §f你获得了资源奖励！");
        plugin.getLogger().info("执行资源奖励事件: " + player.getName());
    }
    
    @Override
    public String getName() {
        return "RESOURCE_BONUS";
    }
    
    @Override
    public EventType getType() {
        return EventType.GOOD;
    }
    
    @Override
    public int getWeight() {
        return 12; // 权重
    }
}
