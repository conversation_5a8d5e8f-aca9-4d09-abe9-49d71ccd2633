package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.player.PlayerStats;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called whenever a value of a stat has changed in a {@link PlayerStats}.
 * <p>
 * 	It's possible that this event might get called async.
 * 	Keep in mind that this event might get called twice, one for the normal stats and again for the game stats.
 * </p>
 */
public class PlayerStatChangeEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final PlayerStats playerStats;
  private final boolean fromRemote;
  private final String key;
  private final Number oldValue;
  private Number newValue;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerStatChangeEvent(
      PlayerStats playerStats,
      boolean fromRemote,
      boolean async,
      String key,
      Number previous,
      Number after) {

    super(async);

    this.playerStats = playerStats;
    this.fromRemote = fromRemote;
    this.key = key;
    this.oldValue = previous;
    this.newValue = after;
  }

  /**
   * Get the {@link PlayerStats} in which the new value might get set
   *
   * @return The PlayerStats instance involved in this event
   */
  public PlayerStats getStats() {
    return this.playerStats;
  }

  /**
   * Get whether a non-local server has caused the change.
   * <p>
   *   May be <code>true</code> when the ProxySync addon is used.
   * </p>
   *
   * @return <code>true</code> if a non-local server initiated the change
   */
  public boolean isFromRemoteServer() {
    return this.fromRemote;
  }

  /**
   * Get the key of the stat of who the value might change
   *
   * @return The key of the stat
   */
  public String getKey() {
    return this.key;
  }

  /**
   * Returns the previous value that was set
   *
   * @return The previous value, <code>0</code> when none was declared before
   */
  public Number getOldValue() {
    return this.oldValue;
  }

  /**
   * Get the new value that's on being planned to be set
   *
   * @return The new value
   */
  public Number getNewValue() {
    return this.newValue;
  }

  /**
   * Modify the new value to be something else
   *
   * @param number The new "new value"
   */
  public void setNewValue(Number number) {
    Validate.notNull(number, "number");

    this.newValue = number;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
