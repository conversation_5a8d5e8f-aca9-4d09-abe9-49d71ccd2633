package cn.acebrand.mbedwarsprop.config;

import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class ItemConfigManager {

    private final Plugin plugin;
    private final Map<String, ItemConfig> itemConfigs = new HashMap<>();
    private final File itemsFolder;

    public ItemConfigManager(Plugin plugin) {
        this.plugin = plugin;

        // 创建items文件夹
        this.itemsFolder = new File(plugin.getDataFolder(), "items");
        if (!this.itemsFolder.exists()) {
            this.itemsFolder.mkdirs();
        }

        // 加载所有道具配置
        loadAllItemConfigs();
    }

    public void loadAllItemConfigs() {
        itemConfigs.clear();

        // 保存默认配置文件
        saveDefaultItemConfigs();

        // 加载所有配置文件
        File[] configFiles = itemsFolder.listFiles((dir, name) -> name.endsWith(".yml"));
        if (configFiles == null) {
            plugin.getLogger().warning("无法读取items文件夹中的配置文件");
            return;
        }

        for (File configFile : configFiles) {
            String itemId = configFile.getName().replace(".yml", "");
            loadItemConfig(itemId, configFile);
        }

        plugin.getLogger().info("已加载 " + itemConfigs.size() + " 个道具配置");
    }

    private void saveDefaultItemConfigs() {
        // 保存所有默认道具配置
        saveDefaultItemConfig("string_trap");
        saveDefaultItemConfig("sea_lantern");
        saveDefaultItemConfig("emerald_ore");
        saveDefaultItemConfig("team_cake");
        saveDefaultItemConfig("fragile_bedrock");
        saveDefaultItemConfig("ender_builder");
        saveDefaultItemConfig("jump_pad");
        saveDefaultItemConfig("resource_booster");
        saveDefaultItemConfig("defense_tower");
        saveDefaultItemConfig("memory_anchor");
    }

    private void saveDefaultItemConfig(String itemId) {
        File configFile = new File(itemsFolder, itemId + ".yml");

        if (!configFile.exists()) {
            // 尝试从资源中加载默认配置
            InputStream inputStream = plugin.getResource("items/" + itemId + ".yml");
            if (inputStream != null) {
                try {
                    // 从资源中加载配置
                    FileConfiguration config = YamlConfiguration
                            .loadConfiguration(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                    // 保存到文件
                    config.save(configFile);
                    plugin.getLogger().info("已创建默认配置文件: " + itemId + ".yml");
                } catch (IOException e) {
                    plugin.getLogger().log(Level.SEVERE, "无法保存默认配置文件: " + itemId + ".yml", e);
                    // 尝试创建一个空的配置文件
                    try {
                        YamlConfiguration emptyConfig = new YamlConfiguration();
                        emptyConfig.set("enabled", true);
                        emptyConfig.set("name", "&c" + itemId);
                        emptyConfig.set("material", "STONE");
                        emptyConfig.save(configFile);
                        plugin.getLogger().info("已创建空的配置文件: " + itemId + ".yml");
                    } catch (IOException ex) {
                        plugin.getLogger().log(Level.SEVERE, "无法创建空的配置文件: " + itemId + ".yml", ex);
                    }
                }
            } else {
                plugin.getLogger().warning("找不到默认配置资源: items/" + itemId + ".yml");
                // 创建一个空的配置文件
                try {
                    YamlConfiguration emptyConfig = new YamlConfiguration();
                    emptyConfig.set("enabled", true);
                    emptyConfig.set("name", "&c" + itemId);
                    emptyConfig.set("material", "STONE");
                    emptyConfig.save(configFile);
                    plugin.getLogger().info("已创建空的配置文件: " + itemId + ".yml");
                } catch (IOException e) {
                    plugin.getLogger().log(Level.SEVERE, "无法创建空的配置文件: " + itemId + ".yml", e);
                }
            }
        }
    }

    private void loadItemConfig(String itemId, File configFile) {
        try {
            FileConfiguration config = YamlConfiguration.loadConfiguration(configFile);

            // 检查道具是否启用
            if (!config.getBoolean("enabled", true)) {
                plugin.getLogger().info("道具 " + itemId + " 已禁用，跳过加载");
                return;
            }

            // 基本信息
            String name = config.getString("name", "&c" + itemId);
            List<String> lore = config.getStringList("lore");

            // 尝试获取材质
            Material material;
            String materialName = config.getString("material", "STONE").toUpperCase();

            // 处理材质名称兼容性
            if (materialName.equals("SKULL_ITEM")) {
                materialName = "PLAYER_HEAD";
            } else if (materialName.equals("WOOL")) {
                materialName = "WHITE_WOOL";
            }

            try {
                material = Material.valueOf(materialName);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("道具 " + itemId + " 的材质 " + materialName + " 无效，使用默认值 STONE");
                material = Material.STONE;
            }

            // 效果设置
            Map<String, EffectConfig> effects = new HashMap<>();

            // 尝试从 effects 部分加载效果
            ConfigurationSection effectsSection = config.getConfigurationSection("effects");
            if (effectsSection != null) {
                for (String effectKey : effectsSection.getKeys(false)) {
                    // 如果效果是一个部分
                    if (effectsSection.isConfigurationSection(effectKey)) {
                        ConfigurationSection effectSection = effectsSection.getConfigurationSection(effectKey);
                        int level = effectSection.getInt("level", 0);
                        int duration = effectSection.getInt("duration", 5);
                        String value = effectSection.getString("value", "");

                        if (!value.isEmpty()) {
                            effects.put(effectKey, new EffectConfig(level, duration, value));
                        } else {
                            effects.put(effectKey, new EffectConfig(level, duration));
                        }
                    }
                    // 兼容旧格式
                    else if (effectKey.endsWith("-level") || effectKey.endsWith("-duration")) {
                        String baseKey = effectKey.substring(0, effectKey.lastIndexOf("-"));
                        if (!effects.containsKey(baseKey)) {
                            int level = effectsSection.getInt(baseKey + "-level", 0);
                            int duration = effectsSection.getInt(baseKey + "-duration", 5);
                            effects.put(baseKey, new EffectConfig(level, duration));
                        }
                    }
                    // 如果效果是一个简单值
                    else {
                        Object value = effectsSection.get(effectKey);
                        if (value != null) {
                            if (value instanceof Number) {
                                int level = ((Number) value).intValue();
                                // 特殊处理持续时间字段
                                if (effectKey.equals("duration")) {
                                    effects.put(effectKey, new EffectConfig(0, level));
                                } else {
                                    effects.put(effectKey, new EffectConfig(level, 0));
                                }
                            } else if (value instanceof String) {
                                effects.put(effectKey, new EffectConfig(0, 0, value.toString()));
                            }
                        }
                    }
                }
            }

            // 创建道具配置
            ItemConfig itemConfig = new ItemConfig(
                    itemId,
                    name,
                    lore,
                    material,
                    effects);

            itemConfigs.put(itemId, itemConfig);
            plugin.getLogger().info("已加载道具配置: " + itemId);

        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "加载道具 " + itemId + " 配置时出错", e);
        }
    }

    public ItemConfig getItemConfig(String itemId) {
        return itemConfigs.get(itemId);
    }

    public static class ItemConfig {
        private final String id;
        private final String name;
        private final List<String> lore;
        private final Material material;
        private final Map<String, EffectConfig> effects;

        public ItemConfig(String id, String name, List<String> lore, Material material,
                Map<String, EffectConfig> effects) {
            this.id = id;
            this.name = name;
            this.lore = lore;
            this.material = material;
            this.effects = effects;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public List<String> getLore() {
            return lore;
        }

        public Material getMaterial() {
            return material;
        }

        public Map<String, EffectConfig> getEffects() {
            return effects;
        }

        public EffectConfig getEffect(String effectName) {
            return effects.get(effectName);
        }
    }

    public static class EffectConfig {
        private final int level;
        private final int duration;
        private String stringValue;
        private boolean enabled = true;

        public EffectConfig(int level, int duration) {
            this.level = level;
            this.duration = duration;
        }

        public EffectConfig(int level, int duration, String stringValue) {
            this.level = level;
            this.duration = duration;
            this.stringValue = stringValue;
        }

        public int getLevel() {
            return level;
        }

        public int getDuration() {
            return duration;
        }

        public boolean isEnabled() {
            // 如果level为0且duration为0，且stringValue为null或空，则认为是禁用的
            // 否则，默认为启用
            if (level == 0 && duration == 0 && (stringValue == null || stringValue.isEmpty())) {
                return false;
            }
            return enabled;
        }

        public String getValue() {
            return stringValue;
        }

        @Override
        public String toString() {
            return stringValue != null ? stringValue : String.valueOf(level);
        }
    }
}
