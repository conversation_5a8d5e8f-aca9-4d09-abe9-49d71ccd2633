package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import lombok.Getter;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when an arena stopped or cancelled the regeneration
 */
public class ArenaRegenerationStopEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final CommandSenderWrapper sender;

  public ArenaRegenerationStopEvent(Arena arena, @Nullable CommandSenderWrapper sender) {
    this.arena = arena;
    this.sender = sender;
  }

  /**
   * Returns the person/sender who stopped it.
   *
   * <p>
   * 	Can be <code>null</code> if not a person (or e.g. nobody on this server) caused it.
   * </p>
   *
   * @return The person/sender behind this
   */
  @Nullable
  public CommandSender getSender() {
    return this.sender != null ? this.sender.getCommandSender() : null;
  }

  /**
   * Returns the person/sender who stopped it.
   * <p>
   *     May be <code>null</code> if not a person caused it.
   * </p>
   *
   * @return The person/sender behind this
   */
  @Nullable
  public CommandSenderWrapper getSenderWrapped() {
    return this.sender;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}