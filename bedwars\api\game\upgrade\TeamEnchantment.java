package de.marcely.bedwars.api.game.upgrade;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.tools.Validate;
import java.util.function.Predicate;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * Represents an enchantment that is being applied to all items of a specific type for all team members
 */
public class TeamEnchantment {

  private final Target target;
  private final Predicate<ItemStack> targetPredicate;
  private final String targetId;
  private final Enchantment enchantment;
  private final int level;

  /**
   * @param target The item type it should target
   * @param ench The enchantment
   * @param lvl The level of the enchantment
   */
  public TeamEnchantment(Target target, Enchantment ench, int lvl) {
    Validate.notNull(target, "target");
    Validate.notNull(ench, "ench");
    Validate.isTrue(target != Target.CUSTOM, "target cannot be CUSTOM");

    this.target = target;
    this.targetPredicate = mat -> BedwarsAPILayer.INSTANCE.isApplicable(target, mat);
    this.targetId = target.name();
    this.enchantment = ench;
    this.level = lvl;
  }

  /**
   * <p>
   *   You generally should use {@link #TeamEnchantment(Target, Enchantment, int)} instead.
   *   This constructor, however, allows you to be more precise with the items that should be enchanted.
   * </p>
   * <p>
   *   The targetPredicate should always return the same result for the same material.
   *   If it does not, then unexpected behavior might occur.
   * </p>
   *
   * @param targetPredicate The predicate that determines if an item should be enchanted
   * @param targetId The id of the target. Multiple enchantments with the same targetId will be combined into one
   * @param ench The enchantment
   * @param lvl The level of the enchantment
   */
  public TeamEnchantment(Predicate<ItemStack> targetPredicate, String targetId, Enchantment ench, int lvl) {
    Validate.notNull(targetPredicate, "targetPredicate");
    Validate.notNull(targetId, "targetId");
    Validate.notNull(ench, "ench");

    this.target = Target.CUSTOM;
    this.targetPredicate = targetPredicate;
    this.targetId = targetId;
    this.enchantment = ench;
    this.level = lvl;
  }

  /**
   * Get the broad item target this enchantment should target.
   *
   * @return The items for which the enchantment gets applied on
   */
  public Target getTarget() {
    return this.target;
  }

  /**
   * Get whether this enchantment is applicable to the given material.
   * <p>
   *   E.g. if the target is {@link Target#PICKAXE} and the material is {@link Material#DIAMOND_PICKAXE},
   *   this method would return <code>true</code>. If the material would however be {@link Material#DIRT},
   *   this method would return <code>false</code>.
   * </p>
   *
   * @param is The item to test
   * @return <code>true</code> if the enchantment is applicable, <code>false</code> otherwise
   */
  public boolean isApplicable(ItemStack is) {
    Validate.notNull(is, "is");

    return this.targetPredicate.test(is);
  }

  /**
   * Get the id of the target.
   * <p>
   *   If a player buys an enchantment with the same targetId multiple times, then the older
   *   one with the equal targetId will be replaced by the newer one.
   * </p>
   * <p>
   *   Built-in targets (those that are not {@link Target#CUSTOM}) will return
   *   {@link Target#name()}.
   * </p>
   *
   * @return The id of the target
   */
  public String getTargetId() {
    return this.targetId;
  }

  /**
   * Get the enchantment that shall be applied on the items.
   *
   * @return The enchantment that gets applied
   */
  public Enchantment getEnchantment() {
    return this.enchantment;
  }

  /**
   * Get the level of the enchantment that gets applied
   *
   * @return The level of the enchantment that gets applied
   */
  public int getLevel() {
    return this.level;
  }



  /**
   * The type of item a {@link TeamEnchantment} shall target
   */
  public enum Target {

    /**
     * E.g. {@link Material#DIAMOND_PICKAXE}
     */
    PICKAXE,

    /**
     * E.g. {@link Material#DIAMOND_AXE}
     */
    AXE,

    /**
     * E.g. {@link Material#DIAMOND_SWORD}
     */
    SWORD,

    /**
     * E.g. {@link Material#LEATHER_HELMET}
     */
    ARMOR,

    /**
     * E.g. The Mace Weapon
     */
    MACE,

    /**
     * A custom predicate has been passed to the TeamEnchantment
     */
    CUSTOM;

    @Nullable
    public static Target byName(String name) {
      for (Target target : values()) {
        if (name.equalsIgnoreCase(target.name()))
          return target;
      }

      return null;
    }

    /**
     * Get the {@link Target} by the material of the item used
     *
     * @param mat The material
     * @return The target or null if not found
     * @deprecated Will be removed in the future. Use {@link TeamEnchantment#isApplicable(ItemStack)} instead
     */
    @Nullable
    @Deprecated
    public static Target byMaterial(Material mat) {
      final ItemStack is = new ItemStack(mat);

      for (Target target : values()) {
        if (BedwarsAPILayer.INSTANCE.isApplicable(target, is))
          return target;
      }

      return null;
    }
  }
}
