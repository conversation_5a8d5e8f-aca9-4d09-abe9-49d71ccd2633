package de.marcely.bedwars.tools.gui;

/**
 * Helper for easily aligning items in a GUI
 */
public enum CenterFormat {

  /**
   * Item: X, Air: O<br>
   * <b>Example with 3 items:</b> O O O X X X O O O<br>
   * <b>Example with 2 items:</b> O O O X X O O O O
   */
  CENTRALIZED,

  /**
   * Item: X, Air: O<br>
   * <b>Example with 3 items:</b> O O O X X X O O O<br>
   * <b>Example with 2 items:</b> O O O X O X O O O
   */
  CENTRALIZED_EVEN,

  /**
   * Item: X, Air: O<br>
   * Example with 3 items:<br>
   * O X O X O X O X O
   */
  ALIGNED;

  public int calculate(int index, int amount, int min, int max) {
    switch (this) {
      case CENTRALIZED:
        return getSlotCenterCentralized(index, amount, min, max);
      case CENTRALIZED_EVEN:
        return getSlotCenterCentralizedEven(index, amount, min, max);
      case ALIGNED:
        return getSlotCenterAligned(index, amount, min, max);
    }

    return index;
  }

  private static int getSlotCenterCentralized(int index, int amount, int min, int max) {
    return (int) ((double) (max - min) / 2 - amount / 2 + index) + min;
  }

  private static int getSlotCenterCentralizedEven(int index, int amount, int min, int max) {
    final int commonSlot = getSlotCenterCentralized(index, amount, min, max);

    if (amount == 9)
      return commonSlot;
    if (amount%2 == 1)
      return commonSlot;
    if (index < amount/2)
      return commonSlot;

    return commonSlot+1;
  }

  private static int getSlotCenterAligned(int index, int amount, int min, int max) {
    if (min == 0 && max == 9)
      return getSlotCenterAligned9(index, amount);
    else if (max - min == 3)
      return getSlotCenterAligned3(index, amount) + min;
    else
      return (int) ((double) index / amount * (max - min) + 0.5D) + min;
  }

  private static int getSlotCenterAligned3(int index, int amount) {
    switch (amount) {
      case 1:
        return 1;
      case 2:
        switch (index) {
          case 0:
            return 0;
          case 1:
            return 2;
        }
      default:
        return index;
    }
  }

  private static int getSlotCenterAligned9(int index, int amount) {
    switch (amount) {
      case 1:
        return 4;
      case 2:
        switch (index) {
          case 0:
            return 3;
          case 1:
            return 5;
        }
      case 3:
        switch (index) {
          case 0:
            return 1;
          case 1:
            return 4;
          case 2:
            return 7;
        }
      case 4:
        switch (index) {
          case 0:
            return 0;
          case 1:
            return 3;
          case 2:
            return 5;
          case 3:
            return 8;
        }
      case 5:
        switch (index) {
          case 0:
            return 0;
          case 1:
            return 2;
          case 2:
            return 4;
          case 3:
            return 6;
          case 4:
            return 8;
        }
      case 6:
        switch (index) {
          case 0:
            return 0;
          case 1:
            return 1;
          case 2:
            return 3;
          case 3:
            return 5;
          case 4:
            return 7;
          case 5:
            return 8;
        }
      case 7:
        switch (index) {
          case 0:
            return 0;
          case 1:
            return 2;
          case 2:
            return 3;
          case 3:
            return 4;
          case 4:
            return 5;
          case 5:
            return 6;
          case 6:
            return 8;
        }
      case 8:
      case 9:
        return index;
      default:
        return -1;
    }
  }
}
