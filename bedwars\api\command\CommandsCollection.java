package de.marcely.bedwars.api.command;

import org.jetbrains.annotations.Nullable;

import java.util.Collection;

/**
 * A collection of sub commands<br>
 * The default handler is the HelpCommandHandler<br>
 * It'll display all commands, similar to /bw and /bw arena
 */
public interface CommandsCollection extends SubCommand {

  /**
   * Returns the command that have been already added to this collection
   *
   * @return The added commands
   */
  Collection<SubCommand> getCommands();

  /**
   * Add a command to this collection
   *
   * @param name The name of the command
   * @return <code>true</code> if it hasn't already been added
   */
  @Nullable SubCommand addCommand(String name);

  /**
   * Adds a new collection to this collection
   *
   * @param name The name of the collection
   * @return <code>true</code> if it hasn't already been added
   */
  @Nullable CommandsCollection addCommandsCollection(String name);

  /**
   * Removes the command from the collection
   *
   * @param command The command that shall be removed
   * @return <code>false</code> if its not not in the collection
   */
  boolean removeCommand(SubCommand command);

  /**
   * Returns the sub command that has a name equal to the given <code>name</code> parameter
   * <p>
   * May also look for aliases when <code>deep</code> is set to true, but names still have a higher priority
   *
   * @param name The name that with which we want to look up
   * @param deep Whether or not it should match the aliases
   * @return The sub command with that name (or alias)
   */
  @Nullable SubCommand getCommand(String name, boolean deep);

  /**
   * Returns the sub command that has that name or an alias with that name
   *
   * @param name The name that with which we want to look up
   * @return The sub command with that name or alias
   */
  default @Nullable SubCommand getCommand(String name) {
    return getCommand(name, true);
  }

  /**
   * Returns the command that will be executed when the player only executes the command on this collection level and not deeper
   * <p>
   * The command must not implement {@link HelpCommandHandler}
   *
   * @return The command that will be executed when no parameter is given
   */
  CommandHandler getHelpCommand();

  /**
   * Set the command that shall be executed when the player only executes the command on this collection level and not deeper
   * <p>
   * The command must not implement {@link HelpCommandHandler}
   *
   * @param cmd The new command that will be executed when no parameter is given
   */
  void setHelpCommand(CommandHandler cmd);
}