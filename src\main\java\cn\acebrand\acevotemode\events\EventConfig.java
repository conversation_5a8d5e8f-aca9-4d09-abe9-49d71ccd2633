package cn.acebrand.acevotemode.events;

/**
 * 事件配置类
 * 包含事件执行所需的所有配置参数
 */
public class EventConfig {
    private final String eventType;
    private final double timeSeconds;
    private final String message;
    private final boolean showMessage;
    private final int dragonCount;
    private final boolean destroyGenerators;
    private final int gameEndCountdown;
    // 龙配置参数
    private final double dragonSpeed;
    private final double dragonBlockDestroyRadius;
    private final boolean spawnDefaultDragon;
    private final boolean disableDragonDeathSound;
    
    public EventConfig(String eventType, double timeSeconds, String message, boolean showMessage) {
        this(eventType, timeSeconds, message, showMessage, 1, true, 30, 0.8, 2.0, false, true);
    }

    public EventConfig(String eventType, double timeSeconds, String message, boolean showMessage,
                      int dragonCount, boolean destroyGenerators, int gameEndCountdown) {
        this(eventType, timeSeconds, message, showMessage, dragonCount, destroyGenerators, gameEndCountdown, 0.8, 2.0, false, true);
    }

    public EventConfig(String eventType, double timeSeconds, String message, boolean showMessage,
                      int dragonCount, boolean destroyGenerators, int gameEndCountdown,
                      double dragonSpeed, double dragonBlockDestroyRadius, boolean spawnDefaultDragon, boolean disableDragonDeathSound) {
        this.eventType = eventType;
        this.timeSeconds = timeSeconds;
        this.message = message;
        this.showMessage = showMessage;
        this.dragonCount = dragonCount;
        this.destroyGenerators = destroyGenerators;
        this.gameEndCountdown = gameEndCountdown;
        this.dragonSpeed = dragonSpeed;
        this.dragonBlockDestroyRadius = dragonBlockDestroyRadius;
        this.spawnDefaultDragon = spawnDefaultDragon;
        this.disableDragonDeathSound = disableDragonDeathSound;
    }
    
    public String getEventType() {
        return eventType;
    }
    
    public double getTimeSeconds() {
        return timeSeconds;
    }
    
    public String getMessage() {
        return message;
    }
    
    public boolean isShowMessage() {
        return showMessage;
    }
    
    public int getDragonCount() {
        return dragonCount;
    }
    
    public boolean isDestroyGenerators() {
        return destroyGenerators;
    }
    
    public int getGameEndCountdown() {
        return gameEndCountdown;
    }

    public double getDragonSpeed() {
        return dragonSpeed;
    }

    public double getDragonBlockDestroyRadius() {
        return dragonBlockDestroyRadius;
    }

    public boolean isSpawnDefaultDragon() {
        return spawnDefaultDragon;
    }

    public boolean isDisableDragonDeathSound() {
        return disableDragonDeathSound;
    }
}
