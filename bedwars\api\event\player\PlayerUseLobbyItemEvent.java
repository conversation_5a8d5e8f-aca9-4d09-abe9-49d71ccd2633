package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.lobby.LobbyItem;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.ItemStack;

/**
 * Gets called whenever a player uses a {@link LobbyItem}
 */
public class PlayerUseLobbyItemEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final LobbyItem lobbyItem;
  @Getter
  private final Arena arena;
  private final ItemStack item;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerUseLobbyItemEvent(Player player, LobbyItem lobbyItem, Arena arena, ItemStack item) {
    super(player);

    this.lobbyItem = lobbyItem;
    this.arena = arena;
    this.item = item;
  }

  /**
   * Returns the {@link LobbyItem} that was used by the player.
   *
   * @return The item involved in this event
   */
  public LobbyItem getLobbyItem() {
    return this.lobbyItem;
  }

  /**
   * Returns the ItemStack on which has been clicked.
   *
   * @return The item on which has been clicked
   */
  public ItemStack getItem() {
    return this.item;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
