package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.event.player.PlayerDataPurgeEvent;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.tools.CloseableIterator;
import java.util.Set;
import org.bukkit.OfflinePlayer;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;

public interface PlayerDataAPI {

  /**
   * Tries to fetch the stats of the player.
   * <p>
   * 	Result may gets cached for quick access in the future.
   * 	Callback will never return a null value.
   * </p>
   *
   * @param player The player who owns the stats
   * @param callback Stats will be given to this callback
   */
  default void getStats(OfflinePlayer player, Consumer<PlayerStats> callback) {
    getStats(player.getUniqueId(), callback);
  }

  /**
   * Tries to fetch the stats of the player.
   * <p>
   * 	Result may gets cached for quick access in the future.
   * 	Callback will never return a null value.
   * </p>
   *
   * @param id The UUID of the player who owns the stats
   * @param callback Stats will be given to this callback
   */
  void getStats(UUID id, Consumer<PlayerStats> callback);

  /**
   *
   * Tries to fetch the stats of the player given by its name.
   * <p>
   *  Casing of the name doesn't have to be exactly equally.
   * 	Result may gets cached for quick access in the future.
   * 	Callback might return a null value in case there is no info about a player with that given name.
   * </p>
   *
   * @param name The name of the player
   * @param callback Stats will be given to this callback
   */
  void getStatsByName(String name, Consumer<Optional<PlayerStats>> callback);

  /**
   * Tries to get stats from the cache.
   * <p>
   *   Returns an empty optional when they haven't been loaded yet.
   *   This method also does not automatically load them, you must call {@link #getStats(OfflinePlayer, Consumer)} to explicitly load them.
   * </p>
   *
   * @param player The player who owns the properties
   * @return The stats of the player. Maybe empty if they aren't being cached
   */
  default Optional<PlayerStats> getStatsCached(OfflinePlayer player) {
    return getStatsCached(player.getUniqueId());
  }

  /**
   * Tries to get stats from the cache.
   * <p>
   *   Returns an empty optional when they haven't been loaded yet.
   *   This method also does not automatically load them, you must call {@link #getStats(UUID, Consumer)} to explicitly load them.
   * </p>
   *
   * @param id The UUID of the player who owns the properties
   * @return The stats of the player. Maybe empty if they aren't being cached
   */
  Optional<PlayerStats> getStatsCached(UUID id);

  /**
   * Gets a collection of all currently cached player stats.
   *
   * @return An immutable collection of currently cached achievements
   */
  Collection<PlayerStats> getCachedStats();

  /**
   * Tries to fetch the achievements info of the player.
   * <p>
   * 	Result may gets cached for quick access in the future.
   * 	Callback will never return a null value.
   * </p>
   *
   * @param player The player who owns the achievements
   * @param callback Result will be given to this callback
   */
  default void getAchievements(OfflinePlayer player, Consumer<PlayerAchievements> callback) {
    getAchievements(player.getUniqueId(), callback);
  }

  /**
   * Tries to fetch the achievement info of the player.
   * <p>
   * 	Result may gets cached for quick access in the future.
   * 	Callback will never return a null value.
   * </p>
   *
   * @param id The UUID of the player who owns the achievements
   * @param callback Result will be given to this callback
   */
  void getAchievements(UUID id, Consumer<PlayerAchievements> callback);

  /**
   * Tries to get achievements from the cache.
   * <p>
   *   Returns an empty optional when they haven't been loaded yet.
   *   This method also does not automatically load them, you must call {@link #getAchievements(OfflinePlayer, Consumer)} to explicitly load them.
   * </p>
   *
   * @param player The player who owns the properties
   * @return The achievements of the player. Maybe empty if they aren't being cached
   */
  default Optional<PlayerAchievements> getAchievementsCached(OfflinePlayer player) {
    return getAchievementsCached(player.getUniqueId());
  }

  /**
   * Tries to get achievements from the cache.
   * <p>
   *   Returns an empty optional when they haven't been loaded yet.
   *   This method also does not automatically load them, you must call {@link #getAchievements(UUID, Consumer)} to explicitly load them.
   * </p>
   *
   * @param id The UUID of the player who owns the properties
   * @return The achievements of the player. Maybe empty if they aren't being cached
   */
  Optional<PlayerAchievements> getAchievementsCached(UUID id);

  /**
   * Gets a collection of all currently cached properties achievements.
   *
   * @return An immutable collection of currently cached achievements
   */
  Collection<PlayerAchievements> getCachedAchievements();

  /**
   * Tries to fetch the properties of the player.
   * <p>
   * 	Result may gets cached for quick access in the future.
   * 	Callback will never return a null value.
   * </p>
   *
   * @param player The player who owns the properties
   * @param callback Result will be given to this callback
   */
  default void getProperties(OfflinePlayer player, Consumer<PlayerProperties> callback) {
    getProperties(player.getUniqueId(), callback);
  }

  /**
   * Tries to fetch the properties of the player.
   * <p>
   * 	Result may gets cached for quick access in the future.
   * 	Callback will never return a null value.
   * </p>
   *
   * @param id The UUID of the player who owns the properties
   * @param callback Result will be given to this callback
   */
  void getProperties(UUID id, Consumer<PlayerProperties> callback);

  /**
   * Tries to get properties from the cache.
   * <p>
   *   Returns an empty optional when they haven't been loaded yet.
   *   This method also does not automatically load them, you must call {@link #getProperties(OfflinePlayer, Consumer)} to explicitly load them.
   * </p>
   *
   * @param player The player who owns the properties
   * @return The properties of the player. Maybe empty if they aren't being cached
   */
  default Optional<PlayerProperties> getPropertiesCached(OfflinePlayer player) {
    return getPropertiesCached(player.getUniqueId());
  }

  /**
   * Tries to get properties from the cache.
   * <p>
   *   Returns an empty optional when they haven't been loaded yet.
   *   This method also does not automatically load them, you must call {@link #getProperties(UUID, Consumer)} to explicitly load them.
   * </p>
   *
   * @param id The UUID of the player who owns the properties
   * @return The properties of the player. Maybe empty if they aren't being cached
   */
  Optional<PlayerProperties> getPropertiesCached(UUID id);

  /**
   * Gets a collection of all currently cached player properties.
   *
   * @return An immutable collection of currently cached properties
   */
  Collection<PlayerProperties> getCachedProperties();

  /**
   * Returns all registered data sets for stats.
   *
   * @return All registered data sets
   */
  Collection<PlayerStatSet> getRegisteredStatSets();

  /**
   * Searches for the data set with that id and returns it.
   *
   * @param id The id of that data set
   * @return Its instance
   */
  Optional<PlayerStatSet> getStatsDataSet(String id);

  /**
   * Tries to register this data set and make it display everywhere in the plugin.
   *
   * @param dataSet The data set that shall be added
   * @throws IllegalStateException If there's already a data set with the same id
   */
  void registerStatSet(PlayerStatSet dataSet);

  /**
   * Tries to register this data set.
   *
   * @param dataSet The data set that shall be removed
   * @return <code>true</code> if it has been unregistered/removed, otherwise <code>false</code>
   */
  boolean unregisterStatSet(PlayerStatSet dataSet);

  /**
   * Returns the stats set with the given id.
   *
   * @param id This id of the stats set we are trying to get
   * @return the stats set with the corresponding id. Returns <code>null</code> if there is no stats set with the given id
   */
  @Nullable
  PlayerStatSet getStatsSet(String id);

  /**
   * Fetches all uuids of players that are known for this plugin.
   * <p>
   *     This method may not match the OfflinePlayer ones and likely contains less or more than there.
   *     MBedwars only stores uuids of relevant players.
   *     A player is relevant in case there is any info available that needs to be stored for either of its stats, achievements or properties.
   * </p>
   * <p>
   *     This fetch is not being executed async on a separate thread. It will be done in the same thread in which this method is being called!!
   * </p>
   * <p>
   *     Also it is very important that you call {@link CloseableIterator#close()} once you are done.
   * </p>
   *
   * @return An iterator for going through all known uuids
   */
  CloseableIterator<UUID> fetchAllStoredUUIDs();

  /**
   * Purges stored and cached data from a certain player. DANGEROUS METHOD!
   * <p>
   *   Calls {@link PlayerDataPurgeEvent}. Plugins using the API may cancel it if they want to.
   * </p>
   *
   * @param uuid The uuid of the player
   * @param stats Whether stats shall be purged
   * @param achievements Whether achievements shall be purged
   * @param properties Whether properties shall be purged
   * @param cacheOnly true: Only local cache updated, false: Local cache of all servers, permanent database get affected
   * @param callback Optional callback. Gets called on the main thread. Includes info whether it was successful or not
   */
  void purgePlayerData(UUID uuid, boolean stats, boolean achievements, boolean properties, boolean cacheOnly, @Nullable Consumer<Boolean> callback);

  /**
   * Purges stored and cached data from ALL (!!) players. VERY DANGEROUS METHOD!
   * <p>
   *   Calls {@link PlayerDataPurgeEvent}. Plugins using the API may cancel it if they want to.
   * </p>
   *
   * @param stats Whether stats shall be purged
   * @param achievements Whether achievements shall be purged
   * @param properties Whether properties shall be purged
   * @param cacheOnly true: Only local cache updated, false: Local cache of all servers, permanent database get affected
   * @param callback Optional callback. Gets called on the main thread. Includes info whether it was successful or not
   */
  void purgeAllPlayerData(boolean stats, boolean achievements, boolean properties, boolean cacheOnly, @Nullable Consumer<Boolean> callback);

  /**
   * Purges stored and cached data from ALL (!!) players. VERY DANGEROUS METHOD!
   * <p>
   *   Calls {@link PlayerDataPurgeEvent}. Plugins using the API may cancel it if they want to.
   * </p>
   *
   * @param statSetIds The ids of the stat sets that shall be purged
   * @param achievements Whether achievements shall be purged
   * @param properties Whether properties shall be purged
   * @param cacheOnly true: Only local cache updated, false: Local cache of all servers, permanent database get affected
   * @param callback Optional callback. Gets called on the main thread. Includes info whether it was successful or not
   */
  void purgeAllPlayerData(Set<String> statSetIds, boolean achievements, boolean properties, boolean cacheOnly, @Nullable Consumer<Boolean> callback);

  /**
   * Fetches players at the given ranks at the leaderboard.
   * <p>
   *   This causes a direct call to the database. It only makes use of what it is currently saved.
   *   Meaning that the rank might be slightly off due to temporal factors, as the local cache of all
   *   players possibly hasn't been pushed yet to the database.
   * </p>
   *
   * @param statSet The stat set according to which we want to order the players
   * @param minRank The minimum rank that we want to fetch. Must be at least 1
   * @param maxRank The maximum rank that we want to fetch
   * @param callback The callback that is being used to deliver the result. Always on the main thread
   * @throws IllegalArgumentException In case minRank is less than 1 or greater than maxRank
   * @throws IllegalArgumentException If the stat set is not registered
   */
  void fetchLeaderboard(PlayerStatSet statSet, int minRank, int maxRank, Consumer<LeaderboardFetchResult> callback);

  /**
   * Fetches the position of a player on the leaderboard.
   * <p>
   *   This causes a direct call to the database. It only makes use of what it is currently saved.
   *   Meaning that the rank might be slightly off due to temporal factors, as the local cache of all
   *   players possibly hasn't been pushed yet to the database.
   * </p>
   *
   * @param playerUUID The UUID of the player
   * @param statSet The stat set according to which we want to order the players
   * @param callback The callback that is being used to deliver the result. Always on the main thread
   */
  void fetchLeaderboardPosition(UUID playerUUID, PlayerStatSet statSet, Consumer<Integer> callback);

  /**
   * Fetches the position of a player on the leaderboard.
   * <p>
   *   This causes a direct call to the database. It only makes use of what it is currently saved.
   *   Meaning that the rank might be slightly off due to temporal factors, as the local cache of all
   *   players possibly hasn't been pushed yet to the database.
   * </p>
   *
   * @param player Tthe player
   * @param statSet The stat set according to which we want to order the players
   * @param callback The callback that is being used to deliver the result. Always on the main thread
   */
  default void fetchLeaderboardPosition(OfflinePlayer player, PlayerStatSet statSet, Consumer<Integer> callback) {
    fetchLeaderboardPosition(player.getUniqueId(), statSet, callback);
  }

  /**
   * Tries to look-up the UUID of a player by its name in the database.
   * <p>
   *   Also checks among online players, just to be sure and avoid unnecessary database calls.
   *   Note that this does not execute a call to Mojang's API for fetching the UUID.
   *   It only checks what we know, and in case he never joined the server/network, it will return an empty optional.
   *   The use of this method is recommended, as it properly supports cracked/offline players on networks,
   *   which {@link org.bukkit.Bukkit#getOfflinePlayer(String)} does not.
   * </p>
   * <p>
   *   Depending on the implementation of the database, username might be case-sensitive or not.
   * </p>
   *
   * @param username The name of the player
   * @param callback The callback that is being used to deliver the result. Always on the main thread
   */
  public void getUUIDByName(String username, Consumer<Optional<UUID>> callback);

  /**
   * Returns whether the plugin is currently connected to a SQL database.
   *
   * @return <code>true</code> when a stable database connection exists
   */
  boolean isSQLServiceActive();

  /**
   * Opens a new database connection (or takes one from the connection pool).
   * <p>
   *     Make sure to close it!
   * </p>
   *
   * @return <code>null</code> when it isn't connected to any database, otherwise the connection
   * @throws SQLException An error that possibly occurred during that
   */
  @Nullable
  Connection openSQLConnection() throws SQLException;

  /**
   * Get an achievement by its id.
   *
   * @param id The id of the achievement
   * @return The achievement with the given id. Returns <code>null</code> if there is no achievement with the given id
   */
  @Nullable
  PlayerAchievement getAchievementTypeById(String id);

  /**
   * Get all existing achievements.
   *
   * @return A collection of all existing achievements
   */
  Collection<PlayerAchievement> getRegisteredAchievementTypes();

  /**
   * Registers a new custom achievement type.
   * <p>
   * 	You may only use the following chars for the id: a{@literal -}z, 0{@literal -}9, :, _
   * </p>
   * <p>
   *   Finally, you must manually grant the achievement at the given event using
   *   {@link PlayerAchievements#earn(PlayerAchievement)}.
   * </p>
   *
   * @param id The id of the achievement type. Must be unique
   * @param plugin The plugin that created this achievement type
   * @param name The name of this achievement that'll get displayed e.g. when a player earns it
   * @param description The description that contains info about earning it of this achievement that'll get displayed e.g. when a player earns it
   * @return The achievement type that has been registered. Returns <code>null</code> if an achievement with the same id already exists
   * @throws IllegalArgumentException If the id is invalid
   * @throws IllegalArgumentException If the plugin is MBedwars itself
   */
  @Nullable
  PlayerAchievement registerAchievementType(String id, Plugin plugin, Message name, Message description);

  /**
   * Returns the global PlayerDataAPI instance.
   *
   * @return The global PlayerDataAPI instance
   */
  static PlayerDataAPI get() {
    return BedwarsAPILayer.INSTANCE.getPlayerDataAPI();
  }
}
