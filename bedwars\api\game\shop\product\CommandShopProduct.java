package de.marcely.bedwars.api.game.shop.product;

public interface CommandShopProduct extends ShopProduct {

  /**
   * Returns the command that will be executed on purchase
   *
   * @return The command
   */
  String getCommand();

  /**
   * Set the command that shall be executed on purchase
   *
   * @param command The new command
   */
  void setCommand(String command);

  /**
   * Returns if the command will be executed as the player or as the console
   *
   * @return <code>false</code>: Gets executed as if the player would type it in, <code>true</code>: Gets executed as if the console sent it
   */
  boolean isAsConsole();

  /**
   * Define if the command should be executed as if the console typed it or as if the buyer typed it.
   * <br>
   * <code>true: </code>as if the console typed it
   * <br>
   * <code>false: </code>as if the buyer typed it
   *
   * @param asConsole Either true or false
   */
  void setAsConsole(boolean asConsole);
}
