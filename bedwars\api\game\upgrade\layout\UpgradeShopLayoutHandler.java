package de.marcely.bedwars.api.game.upgrade.layout;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.upgrade.Upgrade;
import de.marcely.bedwars.api.game.upgrade.UpgradeLevel;
import de.marcely.bedwars.api.game.upgrade.UpgradeState;
import de.marcely.bedwars.api.player.PlayerProperties;
import de.marcely.bedwars.tools.gui.GUI;
import de.marcely.bedwars.tools.gui.GUIItem;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;

import java.util.List;
import org.jetbrains.annotations.Nullable;

public interface UpgradeShopLayoutHandler {


  /**
   * Gets called when layout is getting opened or when a player changed the page.
   * <p>
   *   This method is being called on a separate thread!
   * </p>
   *
   * @param event The event of it containing information
   * @return The GUI that shall be shown to the player
   */
  GUI build(OpenEvent event);

  /**
   * Read the user-defined configurations from the layouts config file.
   *
   * @param section The section that you previously added your configs into
   * @see #writeConfigProperties(ConfigurationSection)
   */
  default void readConfigProperties(ConfigurationSection section) { }

  /**
   * Write the user-defined configurations into the layouts config file.
   * <p>
   *   Make sure to set the values as the values that were previously read
   *   using #readConfigs(ConfigurationSection) to make sure they stay with an update.
   *   If it hasn't been called before, set your default values.
   * </p>
   *
   * @param section Write your stuff into this section
   */
  default void writeConfigProperties(ConfigurationSection section) { }


  /**
   * Used to pass information to {@link UpgradeShopLayoutHandler#build(OpenEvent)}
   */
  interface OpenEvent {

    /**
     * Returns the layout that has been opened
     *
     * @return The layout
     */
    UpgradeShopLayout getLayout();

    /**
     * Returns the player that opened it
     *
     * @return The player
     */
    Player getPlayer();

    /**
     * Returns the arena the player is a part of.
     *
     * @return The arena of the player. May be <code>null</code> of he's in none
     */
    @Nullable
    Arena getArena();

    /**
     * Returns the already loaded and ready to use player properties instance of the player
     *
     * @return The PlayerProperties instance of the given player
     */
    PlayerProperties getPlayerProperties();

    /**
     * Returns the title that has been configured by the player.<br>
     * That title shall be used when creating the GUI
     *
     * @return The title that shall be used in the GUI
     */
    String getDefaultGUITitle();

    /**
     * Gets all the upgrade levels that should be displayed in the GUI
     *
     * @return All the upgrade levels that need to be displayed
     */
    List<UpgradeLevel> getUpgradeLevels();

    /**
     * Returns the upgrade state corresponding to the team this gui is selling upgrades for.
     * <p>
     *   May construct a fake instance in case the player is not within an arena or team.
     * </p>
     *
     * @return The current upgrade still
     */
    UpgradeState getUpgradeState();

    /**
     * The default value is null
     *
     * @return Data that can be passed around
     */
    Object getData();

    /**
     * Returns data that can be used during the session
     *
     * @return Data that can be passed around
     * @param def The default value which will be taken if none is set
     * @param <T> (Unsafe operation:) Automatically casts the intern data object to the given generic
     */
    <T> T getData(T def);

    /**
     * Will reopen the current GUI
     */
    void refresh();

    /**
     * Will reopen the current GUI
     *
     * @param data new data
     */
    void refresh(Object data);

    /**
     * returns the upgrade state of a specified upgrade
     *
     * @param upgrade the upgrade we are checking
     * @return the current UpgradeState
     */
    Upgrade.State getUpgradeState(Upgrade upgrade);

    /**
     * Builds a GUIItem that can be used to set the upgrade inside the GUI
     *
     * @param upgradeLevel the UpgradeLevel this GUIItem will be responsible for
     * @return The item that shall be placed inside the GUI
     */
    GUIItem build(UpgradeLevel upgradeLevel);

    /**
     * Builds a GUIItem that can be used to set the upgrade inside the GUI
     * the data is only passed if the purchase was successful
     *
     * @param upgradeLevel the UpgradeLevel this GUIItem will be responsible for
     * @param data new data passed to the shop layout when the upgrade level is purchases
     * @return The item that shall be placed inside the GUI
     */
    GUIItem build(UpgradeLevel upgradeLevel, Object data);

  }
}
