package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.tools.location.ImmutableLocation;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a player places a bed into an arena during set-up
 */
public class PlayerAddBedEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Team team;
  private final ImmutableLocation location;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerAddBedEvent(Player player, Arena arena, Team team, Location location) {
    super(player);

    this.arena = arena;
    this.team = team;
    this.location = new ImmutableLocation(location);
  }

  /**
   * Returns the team of the bed.
   *
   * @return The team
   */
  public Team getTeam() {
    return this.team;
  }

  /**
   * Returns the new bed location.
   *
   * @return The bed location
   */
  public ImmutableLocation getLocation() {
    return this.location;
  }


  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
