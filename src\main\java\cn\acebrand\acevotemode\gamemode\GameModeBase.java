package cn.acebrand.acevotemode.gamemode;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;

/**
 * 游戏模式基础类
 * 所有游戏模式都应该继承此类
 */
public abstract class GameModeBase {
    
    protected final AceVoteMode plugin;
    protected final String modeId;
    protected final String modeName;
    protected FileConfiguration config;
    protected File configFile;
    
    public GameModeBase(AceVoteMode plugin, String modeId, String modeName) {
        this.plugin = plugin;
        this.modeId = modeId;
        this.modeName = modeName;
        
        // 创建配置文件
        createConfigFile();
        loadConfig();
    }
    
    /**
     * 创建配置文件
     */
    private void createConfigFile() {
        File gameModeFolder = new File(plugin.getDataFolder(), "gamemodes");
        if (!gameModeFolder.exists()) {
            gameModeFolder.mkdirs();
        }

        configFile = new File(gameModeFolder, modeId + ".yml");
        if (!configFile.exists()) {
            try {
                configFile.createNewFile();
                // 先加载配置，然后创建默认配置
                loadConfig();
                createDefaultConfig();
            } catch (IOException e) {
                plugin.getLogger().severe("Failed to create config file for " + modeId + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * 加载配置文件
     */
    protected void loadConfig() {
        config = YamlConfiguration.loadConfiguration(configFile);
    }
    
    /**
     * 保存配置文件
     */
    protected void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save config file for " + modeId + ": " + e.getMessage());
        }
    }
    
    /**
     * 重载配置文件
     */
    public void reloadConfig() {
        loadConfig();
        onConfigReload();
    }
    
    /**
     * 创建默认配置 - 子类需要实现
     */
    protected abstract void createDefaultConfig();
    
    /**
     * 配置重载时调用 - 子类可以重写
     */
    protected void onConfigReload() {
        // 默认实现为空
    }
    
    /**
     * 游戏开始时调用
     */
    public abstract void onGameStart(Arena arena);
    
    /**
     * 游戏结束时调用
     */
    public abstract void onGameEnd(Arena arena);
    
    /**
     * 玩家加入游戏时调用
     */
    public void onPlayerJoin(Player player, Arena arena) {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 玩家离开游戏时调用
     */
    public void onPlayerQuit(Player player, Arena arena) {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 获取模式ID
     */
    public String getModeId() {
        return modeId;
    }
    
    /**
     * 获取模式名称
     */
    public String getModeName() {
        return modeName;
    }
    
    /**
     * 获取配置文件
     */
    public FileConfiguration getConfig() {
        return config;
    }
    
    /**
     * 检查模式是否启用
     */
    public boolean isEnabled() {
        return config.getBoolean("enabled", true);
    }
    
    /**
     * 获取模式描述
     */
    public String getDescription() {
        return config.getString("description", "No description available");
    }
}
