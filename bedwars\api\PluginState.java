package de.marcely.bedwars.api;

import org.bukkit.plugin.Plugin;

/**
 * Run states of a plugin
 */
public enum PluginState {

  /**
   * The plugin is currently at {@link Plugin#onLoad()}
   */
  LOADING,

  /**
   * The plugin is currently at {@link Plugin#onEnable()}
   */
  ENABLING,

  /**
   * The plugin successfully started and is running now
   */
  RUNNING,

  /**
   * The plugin tried to start, but failed
   */
  START_FAILED,

  /**
   * The plugin is currently disabling itself
   */
  DISABLING,

  /**
   * The plugin stopped
   */
  DISABLED;

  /**
   * Returns is it's safe to create a BukkitTask during this state
   *
   * @return If it's safe to create a BukkitTask during this state
   */
  public boolean isTaskable() {
    switch (this) {
      case ENABLING:
      case RUNNING:
        return true;

      default:
        return false;
    }
  }
}
