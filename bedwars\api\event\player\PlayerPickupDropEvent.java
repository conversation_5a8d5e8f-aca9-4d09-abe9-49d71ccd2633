package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.spawner.DropType;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Item;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called whenever a player picks up a spawner item.
 * <p>
 *     Note that this even gets called when an item is being picked up that hasn't been dropped by a spawner, but instead by e.g. a player.
 * </p>
 */
public class PlayerPickupDropEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final DropType dropType;
  private final Item item;
  private final boolean fromSpawner;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerPickupDropEvent(Player who, Arena arena, DropType dropType, Item item, boolean fromSpawner) {
    super(who);

    this.arena = arena;
    this.dropType = dropType;
    this.item = item;
    this.fromSpawner = fromSpawner;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  /**
   * Returns the potential {@link DropType} of the spawner that dropped this item.
   *
   * @return The drop type of the item
   */
  public DropType getDropType() {
    return this.dropType;
  }

  /**
   * Returns the item that the player is trying to pick up.
   *
   * @return The item that is being picked up
   */
  public Item getItem() {
    return this.item;
  }

  /**
   * Get whether it has never picked up before or whether a player just dropped it and picked it up again.
   *
   * @return <code>true</code> if it has been dropped by a spawner (and nobody picked it up before), <code>false</code> if it has been dropped by something else.
   */
  public boolean isFromSpawner() {
    return this.fromSpawner;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
