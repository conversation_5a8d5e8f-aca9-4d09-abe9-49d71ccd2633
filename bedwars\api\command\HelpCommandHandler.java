package de.marcely.bedwars.api.command;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import org.bukkit.ChatColor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public abstract class HelpCommandHandler implements CommandHandlerWrappedSender {

  private static final int ENTRIES_PER_PAGE = 7;

  private CommandsCollection collection;

  @Override
  public void onRegister(SubCommand collection) {
    this.collection = (CommandsCollection) collection;
  }

  @Override
  public void onFire(CommandSenderWrapper sender, String fullUsage, String[] args) {
    Integer page = 1;

    if (args.length >= 1) {
      page = BedwarsAPI.getHelper().parseInt(args[0]);

      if (page == null)
        page = 1;
    }

    // display commands
    final SubCommand[] permittedCommands = getPermittedCommands(sender).toArray(SubCommand[]::new);
    SubCommand[] display = new SubCommand[0];
    final int min = Math.max(Math.min((page - 1) * ENTRIES_PER_PAGE, permittedCommands.length), 0);
    final int max = Math.min(page * ENTRIES_PER_PAGE, permittedCommands.length);
    final int pages = (int) Math.ceil(permittedCommands.length / ((double) ENTRIES_PER_PAGE));
    final String label = getBukkitLabel(fullUsage);

    if (max - min > 0)
      display = Arrays.copyOfRange(permittedCommands, min, max);

    // header
    sender.sendMessage(ChatColor.YELLOW +
        " ---- " + ChatColor.GOLD + Message.buildByKey("Help").done(sender) +
        ChatColor.YELLOW + " -- " + ChatColor.GOLD + Message.buildByKey("Page").done(sender) + " " +
        page + ChatColor.RED + "/" + ChatColor.GOLD + pages + ChatColor.YELLOW + " ----");

    sender.sendMessage(ChatColor.GRAY + this.collection.getFullName(this.getBukkitLabel(fullUsage)) + " " + ChatColor.DARK_GRAY + "[" + Message.buildByKey("Page").done(sender) + "]");

    // easter egg
    if (page < 0 && this.collection.getName().equals("backup")) {
      final int rows = 5;
      final int entriesPerRow = 5;
      final int maxE = rows * entriesPerRow;
      final String chars = "☺☻";
      final int amount = page < -maxE ? Math.max(0, page + maxE * 2) : -page;

      // empty
      for (int i = 0; i <= ENTRIES_PER_PAGE - Math.ceil(amount / entriesPerRow); i++)
        sender.sendMessage("");

      // print
      if (amount == maxE)
        sender.sendMessage(ChatColor.ITALIC + "Greetings, Marcel.");

      String buffer = "";

      for (int i = 0; i < amount; i++) {
        buffer += chars.charAt(i % chars.length());

        if (buffer.length() == entriesPerRow) {
          sender.sendMessage(buffer);
          buffer = "";
        }
      }

      if (!buffer.isEmpty())
        sender.sendMessage(buffer);

      return;
    }

    // empty
    for (int i = 0; i <= ENTRIES_PER_PAGE - display.length; i++)
      sender.sendMessage("");

    // commands
    for (SubCommand cmd : display) {
      String usage = cmd.getUsage(sender);

      if (cmd instanceof CommandsCollection)
        usage = "[...]";

      String line = ChatColor.DARK_AQUA + cmd.getFullName(label);

      if (!usage.isEmpty())
        line += " " + ChatColor.AQUA + usage;

      if (cmd.hasContentAmount()) {
        final CommandHandler handler = cmd.getHandler();
        Integer cAmount = null;

        if (handler instanceof CommandHandlerWrappedSender)
          cAmount = ((CommandHandlerWrappedSender) handler).getContentAmount(sender);
        else if (sender.getCommandSender() != null)
          cAmount = handler.getContentAmount(sender.getCommandSender());

        if (cAmount != null)
          line += ChatColor.GRAY + " (" + BedwarsAPI.getHelper().formatNumber(cAmount) + ")";
      }

      sender.sendMessage(line);
    }
  }

  @Override
  public List<String> onAutocomplete(CommandSenderWrapper sender, String[] args) {
    final List<String> options = new ArrayList<>();
    final List<String> permittedCommands = getPermittedCommands(sender)
        .map(cmd -> cmd.getName())
        .collect(Collectors.toList());

    options.addAll(permittedCommands);

    for (int i = 1; i <= Math.ceil(permittedCommands.size() / ((double) ENTRIES_PER_PAGE)); i++)
      options.add("" + i);

    return options;
  }

  private Stream<SubCommand> getPermittedCommands(CommandSenderWrapper sender) {
    return this.collection.getCommands().stream()
        .filter(cmd -> cmd.isVisible() && (cmd.getPermission() == null || sender.hasPermission(cmd.getPermission())));
  }
}
