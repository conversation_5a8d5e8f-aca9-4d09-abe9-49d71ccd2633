package de.marcely.bedwars.api.arena.picker.condition.variable;

import de.marcely.bedwars.api.arena.picker.condition.ArenaConditionComparisonOperator;

/**
 * Represents a value that may be used to compare.
 * <p>
 *     It may be manually constructed by using either its inheritances or {@link ArenaConditionVariable}.
 * </p>
 * @param <T> The actual value that it is holding. E.g. {@link String} for {@link ArenaConditionVariableValueString}
 */
public abstract class ArenaConditionVariableValue<T> {

  /**
   * {@link ArenaConditionVariableValueNumber}
   */
  public static final byte TYPE_NUMBER = 0;

  /**
   * {@link ArenaConditionVariableValueString}
   */
  public static final byte TYPE_STRING = 1;

  protected final T entry;

  public ArenaConditionVariableValue(T entry) {
    this.entry = entry;
  }

  /**
   * Returns the actual data that this wrapper is holding.
   *
   * @return The data that this instance represents
   */
  public T getEntry() {
    return this.entry;
  }

  /**
   * The type of this class.
   * <p>
   *     You may find them as fields within this class, e.g. {@link #TYPE_NUMBER}.
   * </p>
   *
   * @return The type of this equal
   */
  public abstract byte getType();

  /**
   * Does a {@link ArenaConditionComparisonOperator#EQUAL} operation.
   *
   * @param entry The second value of the same type
   * @return <code>true</code> in case the operation succeded
   */
  public abstract boolean equal(T entry);

  /**
   * Does a {@link ArenaConditionComparisonOperator#SIMILAR} operation.
   *
   * @param entry The second value of the same type
   * @return <code>true</code> in case the operation succeded
   */
  public abstract boolean similar(T entry);

  /**
   * Does a {@link ArenaConditionComparisonOperator#NOT_EQUAL} operation.
   *
   * @param entry The second value of the same type
   * @return <code>true</code> in case the operation succeded
   */
  public abstract boolean notEqual(T entry);

  /**
   * Does a {@link ArenaConditionComparisonOperator#GREATER_THAN} operation.
   *
   * @param entry The second value of the same type
   * @return <code>true</code> in case the operation succeded
   */
  public abstract boolean greaterThan(T entry);

  /**
   * Does a {@link ArenaConditionComparisonOperator#LESS_THAN} operation.
   *
   * @param entry The second value of the same type
   * @return <code>true</code> in case the operation succeded
   */
  public abstract boolean lessThan(T entry);

  /**
   * Does a {@link ArenaConditionComparisonOperator#GREATER_THAN_OR_EQUAL} operation.
   *
   * @param entry The second value of the same type
   * @return <code>true</code> in case the operation succeded
   */
  public abstract boolean greaterThanOrEqual(T entry);

  /**
   * Does a {@link ArenaConditionComparisonOperator#LESS_THAN_OR_EQUAL} operation.
   *
   * @param entry The second value of the same type
   * @return <code>true</code> in case the operation succeded
   */
  public abstract boolean lessThanOrEqual(T entry);

  /**
   * Automatically executes the correct operation method given the passed operator.
   * <p>
   *     Additionally does an unsafe casting from a generic value object.
   * </p>
   *
   * @param operator The operation that shall be done
   * @param secondValue The second value
   * @return <code>true</code> in case the operation succeded
   */
  @SuppressWarnings("unchecked")
  public boolean check(ArenaConditionComparisonOperator operator, ArenaConditionVariableValue<?> secondValue) {
    switch (operator) {
      case EQUAL:
        return equal((T) secondValue.entry);
      case SIMILAR:
        return similar((T) secondValue.entry);
      case NOT_EQUAL:
        return notEqual((T) secondValue.entry);
      case GREATER_THAN:
        return greaterThan((T) secondValue.entry);
      case LESS_THAN:
        return lessThan((T) secondValue.entry);
      case GREATER_THAN_OR_EQUAL:
        return greaterThanOrEqual((T) secondValue.entry);
      case LESS_THAN_OR_EQUAL:
        return lessThanOrEqual((T) secondValue.entry);

      default:
        throw new IllegalStateException("Operator " + operator + " not supported");
    }
  }
}
