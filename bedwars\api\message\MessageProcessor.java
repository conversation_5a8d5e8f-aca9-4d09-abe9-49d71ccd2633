package de.marcely.bedwars.api.message;

import de.marcely.bedwars.api.BedwarsAPI;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * Handles the post-processing of a message.
 *
 * <p>
 *   Its tasks (at least for the default processor) are:<br>
 *   - Replacing placeholders<br>
 *   - Replacing message placeholders<br>
 *   - Replacing chat color codes<br>
 *   - Adding a prefix to the message<br>
 * </p>
 *
 * @see MessageAPI#setProcessor(MessageProcessor)
 */
public abstract class MessageProcessor {

  /**
   * Returns the plugin that's owning this processor
   *
   * @return The plugin that created this processor
   */
  public abstract Plugin getPlugin();

  /**
   * Returns if it's the MessageProcessor provided by MBedwars
   *
   * @return <code>true</code> if it's the MessageProcessor provided by MBedwars
   */
  public final boolean isDefault() {
    return BedwarsAPI.getMessageAPI().getDefaultProcessor() == this;
  }

  /**
   * The default processor processes it in the following way:<br>
   * 1. Get the message in the language of the sender<br>
   * 2. Replace the message placeholders (these which are surrounded with a %)<br>
   * 3. Replaces the placeholders (these which are surrounded with { and })<br>
   * 4. Replaces the chat color codes
   *
   * @param rawMessage The message that was fetched from the Message object you can work with
   * @param messageObj The Message object that contains informations about the object
   * @param locale The locale of the target. Keep in mind that rawMessage has already been translated into the given locale
   * @return The final String
   */
  public abstract String process(String rawMessage, Message messageObj, @Nullable String locale);

  /**
   * Same as {@link #process(String, Message, String)} put fetches the locale from the given target
   *
   * @param target Will fetch the locale from this target. May be null
   * @param messageObj Contains info about the message
   * @param forceDefaultLang <code>true</code> means that it should use the default language independent of the configuration of the user
   * @return The final String
   */
  public String process(@Nullable CommandSender target, Message messageObj, boolean forceDefaultLang) {
    String locale = null;

    if (!forceDefaultLang && target instanceof Player)
      locale = MessageAPI.get().getLocaleFetcher().fetchAsString((Player) target);

    return process(messageObj.getRawMessage(locale), messageObj, locale);
  }
}
