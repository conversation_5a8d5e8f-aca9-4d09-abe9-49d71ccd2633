package de.marcely.bedwars.api.hook;

/**
 * Represents a generalized category to which a hook may itself assign to.
 */
public enum HookCategory {

  /**
   * A hook that implements {@link PartiesHook}.
   */
  PARTIES,

  /**
   * A hook that adds an interface to maintain a currency.
   */
  CURRENCY,

  /**
   * A hook for the CloudSystem functionality.
   */
  CLOUD_SYSTEM,

  /**
   * A hook that implements {@link NicknamingHook}.
   */
  NICKNAMING,

  /**
   * Something that can't be generalized into the other categories.
   */
  MISCELLANEOUS;
}
