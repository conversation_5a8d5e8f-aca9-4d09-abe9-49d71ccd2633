package cn.acebrand.acevotemode.events.neutral;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.Location;

import java.io.File;
import java.util.List;
import java.util.Random;

/**
 * 无事发生事件 - 中立事件
 */
public class NothingHappensEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    
    public NothingHappensEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File neutralDir = new File(eventsDir, "neutral");
            if (!neutralDir.exists()) {
                neutralDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(neutralDir, "nothing_happens.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/neutral/nothing_happens.yml", false);
                plugin.getLogger().info("已生成无事发生事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载无事发生事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载无事发生事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送随机消息
        String message = getRandomMessage();
        if (message != null && !message.isEmpty()) {
            player.sendMessage(message);
        }
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了无事发生事件");
    }
    
    /**
     * 获取随机消息
     */
    private String getRandomMessage() {
        if (config == null) return "§e[幸运方块] §f什么都没有发生...";
        
        List<String> messages = config.getStringList("messages.random_messages");
        if (messages.isEmpty()) {
            return config.getString("messages.default", "§e[幸运方块] §f什么都没有发生...");
        }
        
        return messages.get(random.nextInt(messages.size()));
    }
    
    @Override
    public String getName() {
        return "NOTHING_HAPPENS";
    }
    
    @Override
    public EventType getType() {
        return EventType.NEUTRAL;
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 40) : 40;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
}
