package cn.acebrand.acevotemode.gamemode;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.api.game.shop.price.ShopPrice;
import de.marcely.bedwars.api.game.shop.price.SpawnerItemShopPrice;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;

/**
 * 炮爷出击模式
 * 商店中火焰弹和TNT的价格减半
 */
public class ArtilleryMasterMode extends GameModeBase {
    
    private static ArtilleryMasterMode instance;
    
    // 存储原始价格，用于恢复
    private final Map<Arena, Map<String, Map<ShopPrice, Integer>>> originalPrices = new HashMap<>();
    
    public ArtilleryMasterMode(AceVoteMode plugin) {
        super(plugin, "artillery-master", "炮爷出击");
        instance = this;
    }
    
    /**
     * 获取实例（用于PAPI）
     */
    public static ArtilleryMasterMode getInstance() {
        return instance;
    }
    
    @Override
    protected void createDefaultConfig() {
        // 添加配置文件头部注释
        addConfigComments();

        // 基础配置
        config.set("enabled", true);
        config.set("description", "火焰弹和TNT价格减半，享受爆炸的艺术！");

        // 价格修改配置
        config.set("price-reduction.fire-ball.enabled", true);
        config.set("price-reduction.fire-ball.discount", 0.5);  // 50%折扣
        config.set("price-reduction.tnt.enabled", true);
        config.set("price-reduction.tnt.discount", 0.5);        // 50%折扣

        // 目标物品ID配置（可能需要根据实际商店配置调整）
        config.set("target-items.fire-ball", new String[]{"fire_ball", "fireball", "火焰弹"});
        config.set("target-items.tnt", new String[]{"tnt", "TNT", "炸药"});

        // 消息配置
        config.set("messages.mode-start", "&c&l炮爷出击模式已启动！");
        config.set("messages.mode-description", "&7火焰弹和TNT价格减半，享受爆炸的艺术！");
        config.set("messages.price-modified", "&a商店价格已调整！火焰弹和TNT现在更便宜了！");

        saveConfig();

        // 保存后添加详细注释到文件
        addDetailedCommentsToFile();
    }
    
    @Override
    protected void onConfigReload() {
        // 重载时重新应用价格修改到所有活跃的竞技场
        for (Arena arena : originalPrices.keySet()) {
            applyPriceModifications(arena);
        }
    }
    
    @Override
    public void onGameStart(Arena arena) {
        
        // 检查是否已经启动过，防止重复执行
        if (originalPrices.containsKey(arena)) {
            return;
        }

        // 向所有玩家发送模式开始消息
        String startMessage = config.getString("messages.mode-start", "&c&l炮爷出击模式已启动！");
        String description = config.getString("messages.mode-description", "&7火焰弹和TNT价格减半，享受爆炸的艺术！");

        for (Player player : arena.getPlayers()) {
            player.sendMessage(translateColors(startMessage));
            player.sendMessage(translateColors(description));
        }

        // 应用价格修改
        applyPriceModifications(arena);
        
        // 通知玩家价格已修改
        String priceMessage = config.getString("messages.price-modified", "&a商店价格已调整！火焰弹和TNT现在更便宜了！");
        for (Player player : arena.getPlayers()) {
            player.sendMessage(translateColors(priceMessage));
        }
    }

    @Override
    public void onGameEnd(Arena arena) {
        
        // 恢复原始价格
        restoreOriginalPrices(arena);
        
        // 清理数据
        originalPrices.remove(arena);
    }

    @Override
    public void onPlayerJoin(Player player, Arena arena) {
        // 新加入的玩家也应该看到修改后的价格（价格修改是全局的，无需特殊处理）
        if (originalPrices.containsKey(arena)) {
            String priceMessage = config.getString("messages.price-modified", "&a商店价格已调整！火焰弹和TNT现在更便宜了！");
            player.sendMessage(translateColors(priceMessage));
        }
    }

    @Override
    public void onPlayerQuit(Player player, Arena arena) {
        // 玩家离开时无需特殊处理
    }

    /**
     * 应用价格修改
     */
    private void applyPriceModifications(Arena arena) {
        
        Map<String, Map<ShopPrice, Integer>> arenaOriginalPrices = new HashMap<>();
        
        // 获取所有商店物品
        for (ShopItem shopItem : GameAPI.get().getShopItems()) {
            if (isTargetItem(shopItem)) {
                
                Map<ShopPrice, Integer> itemOriginalPrices = new HashMap<>();
                
                // 修改该物品的所有价格
                for (ShopPrice price : shopItem.getPrices()) {
                    if (price instanceof SpawnerItemShopPrice) {
                        SpawnerItemShopPrice spawnerPrice = (SpawnerItemShopPrice) price;
                        int originalAmount = spawnerPrice.getGeneralAmount();
                        
                        // 保存原始价格
                        itemOriginalPrices.put(price, originalAmount);
                        
                        // 计算新价格（减半）
                        double discount = getDiscountForItem(shopItem);
                        int newAmount = Math.max(1, (int) (originalAmount * discount));
                        
                        // 应用新价格
                        if (spawnerPrice.setGeneralAmount(newAmount)) {
                        } else {
                        }
                    }
                }
                
                if (!itemOriginalPrices.isEmpty()) {
                    arenaOriginalPrices.put(shopItem.getId(), itemOriginalPrices);
                }
            }
        }
        
        originalPrices.put(arena, arenaOriginalPrices);
    }

    /**
     * 恢复原始价格
     */
    private void restoreOriginalPrices(Arena arena) {
        
        Map<String, Map<ShopPrice, Integer>> arenaOriginalPrices = originalPrices.get(arena);
        if (arenaOriginalPrices == null) {
            return;
        }
        
        int restoredCount = 0;
        for (Map.Entry<String, Map<ShopPrice, Integer>> itemEntry : arenaOriginalPrices.entrySet()) {
            String itemId = itemEntry.getKey();
            Map<ShopPrice, Integer> itemPrices = itemEntry.getValue();
            
            ShopItem shopItem = GameAPI.get().getShopItemById(itemId);
            if (shopItem != null) {
                for (Map.Entry<ShopPrice, Integer> priceEntry : itemPrices.entrySet()) {
                    ShopPrice price = priceEntry.getKey();
                    int originalAmount = priceEntry.getValue();
                    
                    if (price.setGeneralAmount(originalAmount)) {
                        restoredCount++;
                    }
                }
            }
        }
        
    }

    /**
     * 检查是否是目标物品
     */
    private boolean isTargetItem(ShopItem shopItem) {
        String itemName = shopItem.getName().toLowerCase();
        String itemId = shopItem.getId().toLowerCase();
        
        // 检查火焰弹
        String[] fireBallNames = config.getStringList("target-items.fire-ball").toArray(new String[0]);
        for (String name : fireBallNames) {
            if (itemName.contains(name.toLowerCase()) || itemId.contains(name.toLowerCase())) {
                return true;
            }
        }
        
        // 检查TNT
        String[] tntNames = config.getStringList("target-items.tnt").toArray(new String[0]);
        for (String name : tntNames) {
            if (itemName.contains(name.toLowerCase()) || itemId.contains(name.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取物品的折扣率
     */
    private double getDiscountForItem(ShopItem shopItem) {
        String itemName = shopItem.getName().toLowerCase();
        String itemId = shopItem.getId().toLowerCase();
        
        // 检查火焰弹
        String[] fireBallNames = config.getStringList("target-items.fire-ball").toArray(new String[0]);
        for (String name : fireBallNames) {
            if (itemName.contains(name.toLowerCase()) || itemId.contains(name.toLowerCase())) {
                return config.getDouble("price-reduction.fire-ball.discount", 0.5);
            }
        }
        
        // 检查TNT
        String[] tntNames = config.getStringList("target-items.tnt").toArray(new String[0]);
        for (String name : tntNames) {
            if (itemName.contains(name.toLowerCase()) || itemId.contains(name.toLowerCase())) {
                return config.getDouble("price-reduction.tnt.discount", 0.5);
            }
        }
        
        return 1.0; // 默认无折扣
    }

    /**
     * 颜色代码转换
     */
    private String translateColors(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 添加配置文件注释
     */
    private void addConfigComments() {
        // 这里可以添加配置文件的头部注释
    }

    /**
     * 添加详细注释到配置文件
     */
    private void addDetailedCommentsToFile() {
        // 这里可以添加更详细的配置说明
    }
}
