package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.tools.Validate;
import de.marcely.bedwars.tools.gui.VillagerOffer;
import de.marcely.bedwars.tools.gui.type.VillagerGUI;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * This event is getting called when the player wants to do a transaction within a {@link VillagerGUI}
 */
public class PlayerTradeVillagerGUIEvent extends PlayerEvent implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final VillagerGUI gui;
  private final Inventory inventory;
  private final VillagerOffer offer;
  private final ItemStack givenResult, givenPrice1, givenPrice2;
  private final boolean isShiftClick, isLeftClick;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerTradeVillagerGUIEvent(Player who, VillagerGUI gui, Inventory inv, VillagerOffer offer,
      ItemStack givenResult, ItemStack givenPrice1, @Nullable ItemStack givenPrice2,
      boolean isShiftClick, boolean isLeftClick) {
    super(who);

    Validate.notNull(who, "who");
    Validate.notNull(gui, "gui");
    Validate.notNull(inv, "inv");
    Validate.notNull(offer, "offer");
    Validate.notNull(givenResult, "givenResult");
    Validate.notNull(givenPrice1, "givenPrice1");

    this.gui = gui;
    this.inventory = inv;
    this.offer = offer;
    this.givenResult = givenResult;
    this.givenPrice1 = givenPrice1;
    this.givenPrice2 = givenPrice2;
    this.isShiftClick = isShiftClick;
    this.isLeftClick = isLeftClick;
  }

  /**
   * Returns the GUI that was opened and in which the player performs his transaction.
   *
   * @return The GUI
   */
  public VillagerGUI getGUI() {
    return this.gui;
  }

  /**
   * Returns Bukkit's inventory on which has been clicked.
   *
   * @return The inventory
   */
  public Inventory getInventory() {
    return this.inventory;
  }

  /**
   * Returns the offer involved in this transaction
   *
   * @return The offer
   */
  public VillagerOffer getOffer() {
    return this.offer;
  }

  /**
   * Returns the items that will be given if this even passes.
   *
   * @return The items that will be given to the player
   */
  public ItemStack getGivenResult() {
    return this.givenResult.clone();
  }

  /**
   * Returns the first price that was offered by the player.
   *
   * @return The item that the player inserted as the first price
   */
  public ItemStack getFirstGivenPrice() {
    return this.givenPrice1.clone();
  }

  /**
   * Returns the second price that was offered by the player.
   * Might be <code>null</code> if the offer doesn't require it
   *
   * @return The item that the player inserted as the second price
   */
  public @Nullable ItemStack getSecondGivenPrice() {
    return this.givenPrice2 != null ? this.givenPrice2.clone() : null;
  }

  /**
   * Gets whether the ClickType for this event indicates that the key was pressed down when the click was made.
   *
   * @return <code>true</code> if he pressed shift while clicking on it
   */
  public boolean isShiftClick() {
    return this.isShiftClick;
  }

  /**
   * Gets whether or not the ClickType for this event represents a left click.
   *
   * @return <code>true</code> if he clicked the left mouse button
   */
  public boolean isLeftClick() {
    return this.isLeftClick;
  }

  /**
   * Gets whether or not the ClickType for this event represents a right click.
   *
   * @return <code>true</code> if he clicked the right mouse button
   */
  public boolean isRightClick() {
    return !this.isLeftClick;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
