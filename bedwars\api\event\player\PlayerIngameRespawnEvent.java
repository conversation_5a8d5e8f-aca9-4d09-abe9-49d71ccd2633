package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a player has respawned in a game
 */
public class PlayerIngameRespawnEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private boolean givingItems;
  private boolean givingEffects;

  public PlayerIngameRespawnEvent(Player player, Arena arena, boolean givingItems, boolean givingEffects) {
    super(player);

    this.givingItems = givingItems;
    this.givingEffects = givingEffects;
    this.arena = arena;
  }

  /**
   * Returns whether spawn items will be given to the player respawning.
   *
   * @return whether spawn items will be given to the player
   */
  public boolean isGivingItems() {
    return givingItems;
  }

  /**
   * Returns whether effects will be given to the player respawning.
   * <p>
   *   This only references effects related to the <code>giveeffects-on-respawn</code> config.
   * </p>
   *
   * @return whether ffects will be given to the player
   */
  public boolean isGivingEffects() {
    return givingEffects;
  }

  /**
   * Set whether or not spawn items will be given to the player when respawning.
   *
   * @param givingItems whether or not spawn items will be given to the player
   */
  public void setGivingItems(boolean givingItems) {
    this.givingItems = givingItems;
  }

  /**
   * Set whether or not effects will be given to the player when respawning.
   *
   * @param givingEffects whether or not effects will be given to the player
   */
  public void setGivingEffects(boolean givingEffects) {
    this.givingEffects = givingEffects;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}

