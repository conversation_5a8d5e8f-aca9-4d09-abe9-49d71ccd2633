package cn.acebrand.acevotemode.manager;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.event.arena.ArenaStatusChangeEvent;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.spawner.SpawnerDurationModifier;
import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.GameAPI;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 全局升级管理器
 * 管理钻石和绿宝石的全局升级事件（除了无限火力和火力不足模式）
 */
public class GlobalUpgradeManager implements Listener {
    
    private final AceVoteMode plugin;
    private FileConfiguration config;
    private final Map<Arena, UpgradeState> arenaUpgradeStates = new HashMap<>();
    private final Map<Arena, Long> arenaStartTimes = new HashMap<>();
    private final List<UpgradeInfo> upgradeSequence = new ArrayList<>();
    
    public GlobalUpgradeManager(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 加载配置
     */
    private void loadConfig() {
        File configFile = new File(plugin.getDataFolder(), "global-upgrades.yml");
        
        if (!configFile.exists()) {
            createDefaultConfig(configFile);
        }
        
        config = YamlConfiguration.loadConfiguration(configFile);
        loadUpgradeSequence();
    }
    
    /**
     * 创建默认配置
     */
    private void createDefaultConfig(File configFile) {
        try {
            configFile.getParentFile().mkdirs();
            configFile.createNewFile();
            
            FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(configFile);
            
            // 基础配置
            defaultConfig.set("enabled", true);
            defaultConfig.set("description", "全局钻石和绿宝石升级事件系统");
            defaultConfig.set("upgrade-messages-enabled", true);  // 是否启用升级消息提示
            
            // 升级序列配置
            // 使用相对时间逻辑（每个升级完成后等待指定时间）
            defaultConfig.set("upgrade-sequence.1.type", "diamond");
            defaultConfig.set("upgrade-sequence.1.tier", 2);
            defaultConfig.set("upgrade-sequence.1.time", 360);          // 游戏开始后等待360秒（6分钟）
            defaultConfig.set("upgrade-sequence.1.interval", 30);       // 每30秒生成
            defaultConfig.set("upgrade-sequence.1.amount", 1);          // 每次1个
            defaultConfig.set("upgrade-sequence.1.message", "&b钻石生成器已升级到 &c2级&b！");
            defaultConfig.set("upgrade-sequence.1.show-message", true); // 是否显示此事件的聊天消息

            defaultConfig.set("upgrade-sequence.2.type", "emerald");
            defaultConfig.set("upgrade-sequence.2.tier", 2);
            defaultConfig.set("upgrade-sequence.2.time", 240);          // 第1个升级完成后等待240秒（4分钟）
            defaultConfig.set("upgrade-sequence.2.interval", 60);       // 每60秒生成
            defaultConfig.set("upgrade-sequence.2.amount", 1);          // 每次1个
            defaultConfig.set("upgrade-sequence.2.message", "&a绿宝石生成器已升级到 &c2级&a！");
            defaultConfig.set("upgrade-sequence.2.show-message", true); // 是否显示此事件的聊天消息

            defaultConfig.set("upgrade-sequence.3.type", "diamond");
            defaultConfig.set("upgrade-sequence.3.tier", 3);
            defaultConfig.set("upgrade-sequence.3.time", 300);          // 第2个升级完成后等待300秒（5分钟）
            defaultConfig.set("upgrade-sequence.3.interval", 20);       // 每20秒生成
            defaultConfig.set("upgrade-sequence.3.amount", 2);          // 每次2个
            defaultConfig.set("upgrade-sequence.3.message", "&b钻石生成器已升级到 &c3级&b！");
            defaultConfig.set("upgrade-sequence.3.show-message", true); // 是否显示此事件的聊天消息

            defaultConfig.set("upgrade-sequence.4.type", "emerald");
            defaultConfig.set("upgrade-sequence.4.tier", 3);
            defaultConfig.set("upgrade-sequence.4.time", 300);          // 第3个升级完成后等待300秒（5分钟）
            defaultConfig.set("upgrade-sequence.4.interval", 40);       // 每40秒生成
            defaultConfig.set("upgrade-sequence.4.amount", 2);          // 每次2个
            defaultConfig.set("upgrade-sequence.4.message", "&a绿宝石生成器已升级到 &c3级&a！");
            defaultConfig.set("upgrade-sequence.4.show-message", true); // 是否显示此事件的聊天消息
            
            // 自定义事件配置
            defaultConfig.set("custom-events.5.type", "dragon");
            defaultConfig.set("custom-events.5.time", 240);             // 第4个升级完成后等待240秒（4分钟）
            defaultConfig.set("custom-events.5.message", "&c&l末影龙已降临战场！");
            defaultConfig.set("custom-events.5.show-message", true);
            defaultConfig.set("custom-events.5.dragon-count", 1);       // 每队生成1条龙
            defaultConfig.set("custom-events.5.destroy-generators", true); // 是否摧毁生成器
            defaultConfig.set("custom-events.5.dragon-speed", 0.8);     // 龙的速度（标准速度）
            defaultConfig.set("custom-events.5.dragon-block-destroy-radius", 2.0); // 方块摧毁半径
            defaultConfig.set("custom-events.5.spawn-default-dragon", true); // 生成默认龙
            defaultConfig.set("custom-events.5.disable-dragon-death-sound", true); // 禁用龙死亡音效

            defaultConfig.set("custom-events.6.type", "bed-destroy");
            defaultConfig.set("custom-events.6.time", 180);             // 龙事件后等待180秒（3分钟）
            defaultConfig.set("custom-events.6.message", "&4&l所有床铺已被摧毁！");
            defaultConfig.set("custom-events.6.show-message", true);

            defaultConfig.set("custom-events.7.type", "game-end");
            defaultConfig.set("custom-events.7.time", 300);             // 床摧毁后等待300秒（5分钟）
            defaultConfig.set("custom-events.7.message", "&c&l游戏即将结束！");
            defaultConfig.set("custom-events.7.show-message", true);
            defaultConfig.set("custom-events.7.game-end-countdown", 60); // 游戏结束倒计时60秒

            // 排除的模式
            defaultConfig.set("excluded-modes", Arrays.asList("unlimited-fire", "low-fire"));

            // 添加注释
            defaultConfig.setComments("enabled", Arrays.asList(
                "是否启用全局升级系统",
                "true = 启用, false = 禁用"
            ));

            defaultConfig.setComments("upgrade-messages-enabled", Arrays.asList(
                "是否在聊天栏显示升级消息",
                "true = 显示升级消息, false = 不显示升级消息"
            ));

            defaultConfig.set("upgrade-sound-enabled", true);
            defaultConfig.setComments("upgrade-sound-enabled", Arrays.asList(
                "是否播放升级音效",
                "true = 播放音效, false = 不播放音效"
            ));

            defaultConfig.setComments("upgrade-sequence", Arrays.asList(
                "升级序列配置（使用相对时间逻辑）",
                "time: 相对于上一个升级完成后的等待时间（秒）",
                "interval: 升级后的生成间隔（秒）",
                "amount: 升级后的生成数量",
                "message: 升级时的广播消息",
                "show-message: 是否显示此事件的聊天消息"
            ));

            defaultConfig.setComments("excluded-modes", Arrays.asList(
                "排除的游戏模式列表",
                "这些模式有自己的升级系统，不会使用全局升级"
            ));
            
            defaultConfig.save(configFile);
            plugin.getLogger().info("Created default global upgrades config");
            
        } catch (IOException e) {
        }
    }
    
    /**
     * 加载升级序列
     */
    private void loadUpgradeSequence() {
        upgradeSequence.clear();

        // 加载升级序列
        if (config.getConfigurationSection("upgrade-sequence") != null) {
            for (String key : config.getConfigurationSection("upgrade-sequence").getKeys(false)) {
                String path = "upgrade-sequence." + key;

                try {
                    String resourceType = config.getString(path + ".type");
                    int tier = config.getInt(path + ".tier");
                    double timeSeconds = config.getDouble(path + ".time");
                    int interval = config.getInt(path + ".interval");
                    int amount = config.getInt(path + ".amount");
                    String message = config.getString(path + ".message", "");
                    boolean showMessage = config.getBoolean(path + ".show-message", true);

                    UpgradeInfo upgrade = new UpgradeInfo(resourceType, tier, timeSeconds, interval, amount, message, showMessage);
                    upgradeSequence.add(upgrade);

                } catch (Exception e) {
                }
            }
        }

        // 加载自定义事件
        if (config.getConfigurationSection("custom-events") != null) {
            for (String key : config.getConfigurationSection("custom-events").getKeys(false)) {
                String path = "custom-events." + key;

                try {
                    String eventType = config.getString(path + ".type");
                    double timeSeconds = config.getDouble(path + ".time");
                    String message = config.getString(path + ".message", "");
                    boolean showMessage = config.getBoolean(path + ".show-message", true);
                    int dragonCount = config.getInt(path + ".dragon-count", 1);
                    boolean destroyGenerators = config.getBoolean(path + ".destroy-generators", false);
                    int gameEndCountdown = config.getInt(path + ".game-end-countdown", 30);
                    double dragonSpeed = config.getDouble(path + ".dragon-speed", 0.8);
                    double dragonBlockDestroyRadius = config.getDouble(path + ".dragon-block-destroy-radius", 2.0);
                    boolean spawnDefaultDragon = config.getBoolean(path + ".spawn-default-dragon", false);
                    boolean disableDragonDeathSound = config.getBoolean(path + ".disable-dragon-death-sound", true);

                    UpgradeInfo customEvent = new UpgradeInfo(eventType, timeSeconds, message, showMessage, dragonCount, destroyGenerators, gameEndCountdown, dragonSpeed, dragonBlockDestroyRadius, spawnDefaultDragon, disableDragonDeathSound);
                    upgradeSequence.add(customEvent);

                } catch (Exception e) {
                }
            }
        }

        // 使用TreeMap来确保按数字顺序加载，而不是按时间排序
        Map<Integer, UpgradeInfo> orderedUpgrades = new TreeMap<>();

        // 重新组织升级序列，按配置中的索引顺序
        upgradeSequence.clear();

        // 重新加载升级序列，按索引顺序
        if (config.getConfigurationSection("upgrade-sequence") != null) {
            for (String key : config.getConfigurationSection("upgrade-sequence").getKeys(false)) {
                String path = "upgrade-sequence." + key;
                try {
                    int index = Integer.parseInt(key);
                    String resourceType = config.getString(path + ".type");
                    int tier = config.getInt(path + ".tier");
                    double timeSeconds = config.getDouble(path + ".time");
                    int interval = config.getInt(path + ".interval");
                    int amount = config.getInt(path + ".amount");
                    String message = config.getString(path + ".message", "");
                    boolean showMessage = config.getBoolean(path + ".show-message", true);

                    UpgradeInfo upgrade = new UpgradeInfo(resourceType, tier, timeSeconds, interval, amount, message, showMessage);
                    orderedUpgrades.put(index, upgrade);
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to parse upgrade-sequence." + key);
                }
            }
        }

        // 重新加载自定义事件，按索引顺序
        if (config.getConfigurationSection("custom-events") != null) {
            for (String key : config.getConfigurationSection("custom-events").getKeys(false)) {
                String path = "custom-events." + key;
                try {
                    int index = Integer.parseInt(key);
                    String eventType = config.getString(path + ".type");
                    double timeSeconds = config.getDouble(path + ".time");
                    String message = config.getString(path + ".message", "");
                    boolean showMessage = config.getBoolean(path + ".show-message", true);
                    int dragonCount = config.getInt(path + ".dragon-count", 1);
                    boolean destroyGenerators = config.getBoolean(path + ".destroy-generators", false);
                    int gameEndCountdown = config.getInt(path + ".game-end-countdown", 30);
                    double dragonSpeed = config.getDouble(path + ".dragon-speed", 0.8);
                    double dragonBlockDestroyRadius = config.getDouble(path + ".dragon-block-destroy-radius", 2.0);
                    boolean spawnDefaultDragon = config.getBoolean(path + ".spawn-default-dragon", false);
                    boolean disableDragonDeathSound = config.getBoolean(path + ".disable-dragon-death-sound", true);

                    UpgradeInfo customEvent = new UpgradeInfo(eventType, timeSeconds, message, showMessage, dragonCount, destroyGenerators, gameEndCountdown, dragonSpeed, dragonBlockDestroyRadius, spawnDefaultDragon, disableDragonDeathSound);
                    orderedUpgrades.put(index, customEvent);
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to parse custom-events." + key);
                }
            }
        }

        // 按索引顺序添加到升级序列
        for (UpgradeInfo upgrade : orderedUpgrades.values()) {
            upgradeSequence.add(upgrade);
        }

        plugin.getLogger().info("Loaded " + upgradeSequence.size() + " upgrade events for global upgrade system in correct order");

    }
    
    /**
     * 监听竞技场状态变化
     */
    @EventHandler
    public void onArenaStatusChange(ArenaStatusChangeEvent event) {
        Arena arena = event.getArena();
        ArenaStatus newStatus = event.getNewStatus();
        
        if (newStatus == ArenaStatus.RUNNING) {
            // 游戏开始，延迟检查是否需要启动全局升级（让游戏模式先激活）
            // 但是只检查一次，避免重复启动
            if (!arenaUpgradeStates.containsKey(arena)) {
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (shouldUseGlobalUpgrades(arena) && !arenaUpgradeStates.containsKey(arena)) {
                        startGlobalUpgrades(arena);
                    }
                }, 5L); // 延迟5个tick（0.25秒）
            }
        } else if (newStatus == ArenaStatus.LOBBY || newStatus == ArenaStatus.STOPPED) {
            // 游戏结束，清理升级状态
            cleanupUpgradeState(arena);
        }
    }
    
    /**
     * 检查是否应该使用全局升级
     */
    private boolean shouldUseGlobalUpgrades(Arena arena) {
        if (!config.getBoolean("enabled", true)) {
            return false;
        }

        // 检查当前竞技场是否使用了排除的模式
        GameModeManager gameModeManager = plugin.getGameModeManager();
        if (gameModeManager != null) {
            String currentMode = gameModeManager.getCurrentMode(arena);
            List<String> excludedModes = config.getStringList("excluded-modes");

            if (currentMode != null) {
                if (excludedModes.contains(currentMode)) {
                    return false;
                }
            }
        } else {
        }

        return true;
    }
    
    /**
     * 启动全局升级
     */
    private void startGlobalUpgrades(Arena arena) {
        if (upgradeSequence.isEmpty()) {
            return;
        }

        // 检查是否已经有升级状态，如果有则先清理
        UpgradeState existingState = arenaUpgradeStates.get(arena);
        if (existingState != null) {
            existingState.cancelAllTasks();
        }

        // 记录游戏开始时间
        arenaStartTimes.put(arena, System.currentTimeMillis());

        UpgradeState state = new UpgradeState();
        arenaUpgradeStates.put(arena, state);

        // 开始第一个升级的调度
        scheduleNextGlobalUpgrade(arena, 0);

        // 设置全息显示更新任务
        BukkitTask hologramTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (arena.getStatus() != ArenaStatus.RUNNING) {
                    this.cancel();
                    return;
                }
                updateHolograms(arena);
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒更新一次
        
        state.addTask(hologramTask);
    }

    /**
     * 安排下一个全局升级（按顺序）
     */
    private void scheduleNextGlobalUpgrade(Arena arena, int upgradeIndex) {
        UpgradeState state = arenaUpgradeStates.get(arena);
        if (state == null || upgradeIndex >= upgradeSequence.size()) {
            return; // 没有更多升级或状态无效
        }

        UpgradeInfo nextUpgrade = upgradeSequence.get(upgradeIndex);

        // time 是相对于当前时间的延迟时间（秒）
        long delayMs = (long)(nextUpgrade.timeSeconds * 1000);
        long delayTicks = delayMs / 50; // 50ms = 1 tick

        // 计算升级的绝对时间
        long upgradeAbsoluteTime = System.currentTimeMillis() + delayMs;

        BukkitTask task = Bukkit.getScheduler().runTaskLater(plugin, () -> {
            executeUpgrade(arena, nextUpgrade);
            // 升级完成后，安排下一个升级
            scheduleNextGlobalUpgrade(arena, upgradeIndex + 1);
        }, delayTicks);

        state.addTask(task);
        state.setNextUpgrade(nextUpgrade, upgradeAbsoluteTime);
    }

    /**
     * 执行升级
     */
    private void executeUpgrade(Arena arena, UpgradeInfo upgradeInfo) {
        try {
            // 检查是否是自定义事件
            if (!"upgrade".equals(upgradeInfo.eventType)) {
                executeCustomEvent(arena, upgradeInfo);
                return;
            }

            // 升级所有对应类型的生成器
            DropType dropType = GameAPI.get().getDropTypeById(upgradeInfo.resourceType);
            if (dropType != null) {
                for (Spawner spawner : arena.getSpawners()) {
                    if (spawner.getDropType().equals(dropType)) {
                        // 移除旧的修改器
                        SpawnerDurationModifier oldModifier = spawner.getDropDurationModifier("acevotemode:global-upgrade");
                        if (oldModifier != null) {
                            spawner.removeDropDurationModifier(oldModifier);
                        }

                        // 添加新的修改器
                        spawner.addDropDurationModifier(
                            "acevotemode:global-upgrade",
                            plugin,
                            SpawnerDurationModifier.Operation.SET,
                            upgradeInfo.interval
                        );

                        // 更新全息显示
                        updateSpawnerHologram(spawner, upgradeInfo.tier);
                    }
                }
            }

            // 广播升级消息和音效
            if (upgradeInfo.showMessage && !upgradeInfo.message.isEmpty()) {
                for (Player player : arena.getPlayers()) {
                    player.sendMessage(ChatColor.translateAlternateColorCodes('&', upgradeInfo.message));
                }
            }

            // 播放升级音效（如果启用）
            if (config.getBoolean("upgrade-sound-enabled", true)) {
                playUpgradeSound(arena);
            }

        } catch (Exception e) {
        }
    }

    /**
     * 执行自定义事件
     */
    private void executeCustomEvent(Arena arena, UpgradeInfo upgradeInfo) {
        try {
            // 创建事件配置
            cn.acebrand.acevotemode.events.EventConfig eventConfig =
                new cn.acebrand.acevotemode.events.EventConfig(
                    upgradeInfo.eventType,
                    upgradeInfo.timeSeconds,
                    upgradeInfo.message,
                    upgradeInfo.showMessage,
                    upgradeInfo.dragonCount,
                    upgradeInfo.destroyGenerators,
                    upgradeInfo.gameEndCountdown,
                    upgradeInfo.dragonSpeed,
                    upgradeInfo.dragonBlockDestroyRadius,
                    upgradeInfo.spawnDefaultDragon,
                    upgradeInfo.disableDragonDeathSound
                );

            // 执行自定义事件
            plugin.getCustomEventManager().executeEvent(upgradeInfo.eventType, arena, eventConfig);

        } catch (Exception e) {
        }
    }

    /**
     * 更新生成器全息显示
     */
    private void updateSpawnerHologram(Spawner spawner, int tier) {
        String tierDisplay = getTierRomanDisplay(tier);
        String resourceType = spawner.getDropType().getId();
        String colorCode = getResourceColorCode(resourceType);

        String[] customLines = new String[] {
            ChatColor.translateAlternateColorCodes('&', colorCode + spawner.getDropType().getConfigName()),
            ChatColor.translateAlternateColorCodes('&', "&e等级 " + tierDisplay),
            ChatColor.translateAlternateColorCodes('&', "&7全局升级已激活"),
            ChatColor.translateAlternateColorCodes('&', "&7下次生成: &c{time}")
        };

        spawner.setOverridingHologramLines(customLines);
    }

    /**
     * 更新所有相关生成器的全息显示
     */
    private void updateHolograms(Arena arena) {
        try {
            for (Spawner spawner : arena.getSpawners()) {
                String typeId = spawner.getDropType().getId();
                if ("diamond".equals(typeId) || "emerald".equals(typeId)) {
                    // 检查是否有全局升级修改器
                    SpawnerDurationModifier modifier = spawner.getDropDurationModifier("acevotemode:global-upgrade");
                    if (modifier != null) {
                        // 获取当前等级
                        int currentTier = getCurrentTier(arena, typeId);
                        updateSpawnerHologram(spawner, currentTier);
                    }
                }
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }

    /**
     * 获取当前等级
     */
    private int getCurrentTier(Arena arena, String resourceType) {
        long currentTime = System.currentTimeMillis();
        long gameStartTime = getGameStartTime(arena);
        long elapsedSeconds = (currentTime - gameStartTime) / 1000;

        int maxTier = 1;
        for (UpgradeInfo upgrade : upgradeSequence) {
            if (upgrade.resourceType.equals(resourceType) && elapsedSeconds >= upgrade.timeSeconds) {
                maxTier = Math.max(maxTier, upgrade.tier);
            }
        }

        return maxTier;
    }

    /**
     * 获取游戏开始时间
     */
    private long getGameStartTime(Arena arena) {
        return arenaStartTimes.getOrDefault(arena, System.currentTimeMillis());
    }

    /**
     * 获取等级的罗马数字显示
     */
    private String getTierRomanDisplay(int tier) {
        switch (tier) {
            case 1: return "I";
            case 2: return "II";
            case 3: return "III";
            case 4: return "IV";
            case 5: return "V";
            default: return String.valueOf(tier);
        }
    }

    /**
     * 获取资源颜色代码
     */
    private String getResourceColorCode(String resourceType) {
        switch (resourceType.toLowerCase()) {
            case "diamond": return "&b";
            case "emerald": return "&a";
            case "gold": return "&6";
            case "iron": return "&f";
            default: return "&7";
        }
    }

    /**
     * 清理升级状态
     */
    private void cleanupUpgradeState(Arena arena) {
        UpgradeState state = arenaUpgradeStates.remove(arena);
        if (state != null) {
            state.cancelAllTasks();
        }

        // 清理开始时间记录
        arenaStartTimes.remove(arena);

        // 恢复生成器原始状态
        restoreSpawners(arena);

        // 清理自定义事件任务
        plugin.getCustomEventManager().cleanupArenaTasks(arena);
    }

    /**
     * 恢复生成器原始状态
     */
    private void restoreSpawners(Arena arena) {
        try {
            for (Spawner spawner : arena.getSpawners()) {
                // 移除全局升级修改器
                SpawnerDurationModifier modifier = spawner.getDropDurationModifier("acevotemode:global-upgrade");
                if (modifier != null) {
                    spawner.removeDropDurationModifier(modifier);
                }

                // 恢复原始全息显示
                spawner.setOverridingHologramLines(null);
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        loadConfig();
    }

    /**
     * 获取下一个升级事件（供占位符使用）
     */
    public UpgradeInfo getNextUpgrade(Arena arena) {
        if (!shouldUseGlobalUpgrades(arena)) {
            return null;
        }

        UpgradeState state = arenaUpgradeStates.get(arena);
        if (state == null) {
            return null;
        }

        long currentTime = System.currentTimeMillis();
        return state.getNextUpgrade(currentTime);
    }

    /**
     * 获取到下一个升级的秒数（供占位符使用）
     */
    public int getSecondsToNextUpgrade(Arena arena) {
        if (!shouldUseGlobalUpgrades(arena)) {
            return 0;
        }

        UpgradeState state = arenaUpgradeStates.get(arena);
        if (state == null) {
            return 0;
        }

        long currentTime = System.currentTimeMillis();
        long nextUpgradeTime = state.getNextUpgradeTime(currentTime);

        if (nextUpgradeTime == 0) {
            return 0;
        }

        return Math.max(0, (int)((nextUpgradeTime - currentTime) / 1000));
    }

    /**
     * 升级状态类
     */
    private static class UpgradeState {
        private final List<BukkitTask> tasks = new ArrayList<>();
        private UpgradeInfo currentUpgrade = null;
        private long nextUpgradeTime = 0;

        public void addTask(BukkitTask task) {
            tasks.add(task);
        }

        public void setNextUpgrade(UpgradeInfo upgrade, long upgradeTime) {
            this.currentUpgrade = upgrade;
            this.nextUpgradeTime = upgradeTime;
        }

        public UpgradeInfo getNextUpgrade(long currentTime) {
            if (currentUpgrade != null && nextUpgradeTime > currentTime) {
                return currentUpgrade;
            }
            return null;
        }

        public long getNextUpgradeTime(long currentTime) {
            if (currentUpgrade != null && nextUpgradeTime > currentTime) {
                return nextUpgradeTime;
            }
            return 0;
        }

        public void cancelAllTasks() {
            for (BukkitTask task : tasks) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            tasks.clear();
            currentUpgrade = null;
            nextUpgradeTime = 0;
        }
    }

    /**
     * 升级信息类
     */
    public static class UpgradeInfo {
        public final String resourceType;
        public final int tier;
        public final double timeSeconds;
        public final int interval;
        public final int amount;
        public final String message;
        public final boolean showMessage;
        public final String eventType;  // 事件类型 ("upgrade", "dragon", "bed-destroy", "game-end")
        public final int dragonCount;   // 龙的数量
        public final boolean destroyGenerators; // 是否摧毁生成器
        public final int gameEndCountdown; // 游戏结束倒计时
        public final double dragonSpeed; // 龙的速度
        public final double dragonBlockDestroyRadius; // 龙的方块摧毁半径
        public final boolean spawnDefaultDragon; // 是否生成默认龙
        public final boolean disableDragonDeathSound; // 是否禁用龙死亡音效

        // 升级事件构造器
        public UpgradeInfo(String resourceType, int tier, double timeSeconds, int interval, int amount, String message, boolean showMessage) {
            this.resourceType = resourceType;
            this.tier = tier;
            this.timeSeconds = timeSeconds;
            this.interval = interval;
            this.amount = amount;
            this.message = message;
            this.showMessage = showMessage;
            this.eventType = "upgrade";
            this.dragonCount = 1;
            this.destroyGenerators = false;
            this.gameEndCountdown = 30;
            this.dragonSpeed = 0.8;
            this.dragonBlockDestroyRadius = 2.0;
            this.spawnDefaultDragon = false;
            this.disableDragonDeathSound = true;
        }

        // 自定义事件构造器（简化版）
        public UpgradeInfo(String eventType, double timeSeconds, String message, boolean showMessage,
                          int dragonCount, boolean destroyGenerators, int gameEndCountdown) {
            this(eventType, timeSeconds, message, showMessage, dragonCount, destroyGenerators, gameEndCountdown, 0.8, 2.0, false, true);
        }

        // 自定义事件构造器（完整版）
        public UpgradeInfo(String eventType, double timeSeconds, String message, boolean showMessage,
                          int dragonCount, boolean destroyGenerators, int gameEndCountdown,
                          double dragonSpeed, double dragonBlockDestroyRadius, boolean spawnDefaultDragon, boolean disableDragonDeathSound) {
            this.eventType = eventType;
            this.timeSeconds = timeSeconds;
            this.message = message;
            this.showMessage = showMessage;
            this.dragonCount = dragonCount;
            this.destroyGenerators = destroyGenerators;
            this.gameEndCountdown = gameEndCountdown;
            this.dragonSpeed = dragonSpeed;
            this.dragonBlockDestroyRadius = dragonBlockDestroyRadius;
            this.spawnDefaultDragon = spawnDefaultDragon;
            this.disableDragonDeathSound = disableDragonDeathSound;
            // 其他字段设为默认值
            this.resourceType = "";
            this.tier = 0;
            this.interval = 0;
            this.amount = 0;
        }
    }

    /**
     * 播放升级音效
     */
    private void playUpgradeSound(Arena arena) {
        for (Player player : arena.getPlayers()) {
            try {
                // 尝试播放升级音效
                player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_ANVIL_USE, 1.0f, 1.2f);
            } catch (Exception e) {
                try {
                    // 备用音效
                    player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.5f);
                } catch (Exception ex) {
                    // 如果都失败了，忽略音效错误
                }
            }
        }
    }
}
