package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.attribute.Attribute;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.Set;

/**
 * 怪物群体召唤事件
 * 召唤不同类型的怪物群体
 */
public class MonsterHordeEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;

    public MonsterHordeEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(badDir, "monster_horde.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/monster_horde.yml", false);
                plugin.getLogger().info("已生成怪物群体召唤事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载怪物群体召唤事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载怪物群体召唤事件配置失败: " + e.getMessage());
        }
    }

    @Override
    public EventType getType() {
        return EventType.BAD;
    }

    @Override
    public String getName() {
        return "MONSTER_HORDE";
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 20) : 20;
    }

    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }

    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }

        // 获取全局设置
        double spawnRadius = config != null ? config.getDouble("global_settings.spawn_radius", 10.0) : 10.0;
        int lifetime = config != null ? config.getInt("global_settings.lifetime", 120) : 120;
        boolean attackTeammates = config != null ? config.getBoolean("global_settings.attack_teammates", false) : false;
        boolean burnInDaylight = config != null ? config.getBoolean("global_settings.burn_in_daylight", false) : false;
        String namePrefix = config != null ? config.getString("global_settings.name_prefix", "§c愤怒的") : "§c愤怒的";

        // 获取玩家队伍信息
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        Team playerTeam = arena != null ? arena.getPlayerTeam(player) : null;

        // 选择怪物组
        String selectedGroup = selectMonsterGroup();
        if (selectedGroup == null) {
            plugin.getLogger().warning("无法选择怪物组");
            return;
        }

        // 生成怪物
        List<LivingEntity> monsters = spawnMonsterGroup(selectedGroup, player.getLocation(),
                spawnRadius, burnInDaylight, namePrefix);

        // 设置生存时间
        if (lifetime > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    for (LivingEntity monster : monsters) {
                        if (monster.isValid()) {
                            monster.remove();
                        }
                    }
                }
            }.runTaskLater(plugin, lifetime * 20L);
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 触发了怪物群体召唤事件，生成了 " +
                monsters.size() + " 只怪物（" + selectedGroup + "组）");
    }

    /**
     * 选择怪物组
     */
    private String selectMonsterGroup() {
        if (config == null)
            return null;

        ConfigurationSection groupsSection = config.getConfigurationSection("monster_groups");
        if (groupsSection == null)
            return null;

        // 计算总权重
        int totalWeight = 0;
        Set<String> groupNames = groupsSection.getKeys(false);
        for (String groupName : groupNames) {
            totalWeight += groupsSection.getInt(groupName + ".weight", 1);
        }

        // 随机选择
        int roll = random.nextInt(totalWeight);
        int current = 0;

        for (String groupName : groupNames) {
            current += groupsSection.getInt(groupName + ".weight", 1);
            if (roll < current) {
                return groupName;
            }
        }

        return groupNames.iterator().next(); // 回退选择
    }

    /**
     * 生成怪物组
     */
    private List<LivingEntity> spawnMonsterGroup(String groupName, Location center, double radius,
            boolean burnInDaylight, String namePrefix) {
        List<LivingEntity> monsters = new ArrayList<>();

        if (config == null)
            return monsters;

        ConfigurationSection groupSection = config.getConfigurationSection("monster_groups." + groupName + ".monsters");
        if (groupSection == null)
            return monsters;

        for (String monsterKey : groupSection.getKeys(false)) {
            ConfigurationSection monsterConfig = groupSection.getConfigurationSection(monsterKey);
            if (monsterConfig == null)
                continue;

            String entityType = monsterConfig.getString("type", "ZOMBIE");
            String displayName = monsterConfig.getString("display_name", monsterKey);
            int count = monsterConfig.getInt("count", 1);
            double health = monsterConfig.getDouble("health", 20.0);
            double speedMultiplier = monsterConfig.getDouble("speed_multiplier", 1.0);

            for (int i = 0; i < count; i++) {
                Location spawnLocation = getRandomSpawnLocation(center, radius);
                if (spawnLocation != null) {
                    try {
                        EntityType type = EntityType.valueOf(entityType);
                        LivingEntity entity = (LivingEntity) spawnLocation.getWorld().spawnEntity(spawnLocation, type);

                        // 设置基本属性
                        setupMonsterBasics(entity, health, speedMultiplier, burnInDaylight, namePrefix + displayName);

                        // 设置特殊属性
                        setupSpecialAttributes(entity, monsterConfig);

                        monsters.add(entity);

                    } catch (Exception e) {
                        plugin.getLogger().warning("生成怪物失败: " + entityType + " - " + e.getMessage());
                    }
                }
            }
        }

        return monsters;
    }

    /**
     * 获取随机生成位置
     */
    private Location getRandomSpawnLocation(Location center, double radius) {
        for (int attempts = 0; attempts < 10; attempts++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = random.nextDouble() * radius;
            double x = center.getX() + Math.cos(angle) * distance;
            double z = center.getZ() + Math.sin(angle) * distance;

            Location spawnLoc = new Location(center.getWorld(), x, center.getY(), z);

            // 找到安全的Y坐标
            for (int y = (int) center.getY(); y < center.getY() + 10; y++) {
                spawnLoc.setY(y);
                if (spawnLoc.getBlock().getType().isAir() &&
                        spawnLoc.clone().add(0, 1, 0).getBlock().getType().isAir()) {
                    return spawnLoc;
                }
            }
        }
        return center; // 如果找不到合适位置，就在玩家位置生成
    }

    /**
     * 设置怪物基本属性
     */
    private void setupMonsterBasics(LivingEntity entity, double health, double speedMultiplier,
            boolean burnInDaylight, String customName) {
        // 设置生命值
        try {
            entity.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(health);
            entity.setHealth(health);
        } catch (Exception e) {
            // 兼容旧版本
            entity.setMaxHealth(health);
            entity.setHealth(health);
        }

        // 设置移动速度
        try {
            entity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED)
                    .setBaseValue(
                            entity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).getBaseValue() * speedMultiplier);
        } catch (Exception e) {
            plugin.getLogger().warning("无法设置怪物移动速度");
        }

        // 设置自定义名称
        entity.setCustomName(customName);
        entity.setCustomNameVisible(true);

        // 标记为幸运方块怪物（用于死亡时不掉落物品和经验）
        entity.setMetadata("lucky_block_monster", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

        // 防止远离时消失
        entity.setRemoveWhenFarAway(true);

        // 防止白天燃烧
        if (!burnInDaylight && entity instanceof Zombie) {
            // 给僵尸戴头盔防止燃烧
            Zombie zombie = (Zombie) entity;
            zombie.getEquipment().setHelmet(new ItemStack(Material.LEATHER_HELMET));
        } else if (!burnInDaylight && entity instanceof Skeleton) {
            // 给骷髅戴头盔防止燃烧
            Skeleton skeleton = (Skeleton) entity;
            skeleton.getEquipment().setHelmet(new ItemStack(Material.LEATHER_HELMET));
        }
    }

    /**
     * 设置特殊属性
     */
    private void setupSpecialAttributes(LivingEntity entity, ConfigurationSection config) {
        // 苦力怕特殊设置
        if (entity instanceof Creeper) {
            Creeper creeper = (Creeper) entity;
            float explosionPower = (float) config.getDouble("explosion_power", 3.0);
            int chargedChance = config.getInt("charged_chance", 0);

            creeper.setExplosionRadius((int) explosionPower);

            if (random.nextInt(100) < chargedChance) {
                creeper.setPowered(true);
            }
        }

        // 猪人特殊设置
        else if (entity instanceof Piglin) {
            Piglin piglin = (Piglin) entity;
            boolean angry = config.getBoolean("angry", true);

            if (angry) {
                // 猪人会自然地攻击没有穿金装备的玩家
                // 我们可以设置一些属性让它更加敌对
                piglin.setAdult();
                piglin.setImmuneToZombification(false);
                // 设置目标为附近的玩家（如果可能）
                try {
                    // 尝试设置AI目标，但这可能在某些版本中不可用
                    piglin.setTarget(null); // 清除当前目标，让它重新寻找目标
                } catch (Exception e) {
                    // 忽略错误，继续执行
                }
            }
        }

        // 装备设置
        int equipmentChance = config.getInt("equipment_chance", 0);
        if (equipmentChance > 0 && random.nextInt(100) < equipmentChance) {
            giveRandomEquipment(entity);
        }
    }

    /**
     * 给怪物随机装备
     */
    private void giveRandomEquipment(LivingEntity entity) {
        if (entity.getEquipment() == null)
            return;

        // 随机武器
        if (random.nextBoolean()) {
            Material[] weapons = { Material.WOODEN_SWORD, Material.STONE_SWORD, Material.IRON_SWORD };
            entity.getEquipment().setItemInMainHand(new ItemStack(weapons[random.nextInt(weapons.length)]));
        }

        // 随机护甲
        if (random.nextBoolean()) {
            Material[] helmets = { Material.LEATHER_HELMET, Material.CHAINMAIL_HELMET, Material.IRON_HELMET };
            entity.getEquipment().setHelmet(new ItemStack(helmets[random.nextInt(helmets.length)]));
        }
    }

    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null)
            return true;
        return config.getBoolean("messages.send_message", true);
    }

    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null)
            return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }

    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null)
            return "§c怪物大军来袭！准备战斗！";
        return config.getString("messages.event_message", "§c怪物大军来袭！准备战斗！");
    }
}
