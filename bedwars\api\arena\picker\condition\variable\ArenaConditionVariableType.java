package de.marcely.bedwars.api.arena.picker.condition.variable;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.remote.RemoteAPI;
import de.marcely.bedwars.api.remote.RemoteArena;
import org.jetbrains.annotations.Nullable;

import java.util.function.Function;

/**
 * A type of the variable. Differentiate between default and custom ones.
 */
public enum ArenaConditionVariableType {

  /**
   * {@link Arena#getPlayersPerTeam()}
   */
  PLAYERS_PER_TEAM(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber(localArena.getPlayersPerTeam());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueNumber(remoteArena.getPlayersPerTeam());
      }
  ),

  /**
   * Amount of {@link Arena#getEnabledTeams()}
   */
  TEAMS(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber(localArena.getEnabledTeams().size());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueNumber(remoteArena.getEnabledTeams().size());
      }
  ),

  /**
   * {@link Arena#getMinPlayers()}
   */
  MIN_PLAYERS(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber(localArena.getMinPlayers());
      }, remoteArena -> {
    return new ArenaConditionVariableValueNumber(remoteArena.getMinPlayers());
  }
  ),

  /**
   * {@link Arena#getMaxPlayers()}
   */
  MAX_PLAYERS(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber(localArena.getMaxPlayers());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueNumber(remoteArena.getMaxPlayers());
      }
  ),

  /**
   * Amount of {@link Arena#getPlayers()}
   */
  INGAME_PLAYERS(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber(localArena.getPlayers().size());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueNumber(remoteArena.getPlayersCount());
      }
  ),

  /**
   * Amount until {@link Arena#getMaxPlayers()} is reached
   */
  REMAINING_SLOTS(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber(localArena.getMaxPlayers() - localArena.getPlayers().size());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueNumber(remoteArena.getMaxPlayers() - remoteArena.getPlayersCount());
      }
  ),

  /**
   * Legacy id of {@link Arena#getStatus()}
   */
  STATUS(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber(localArena.getStatus().ordinal()+1);
      },
      remoteArena -> {
        return new ArenaConditionVariableValueNumber(remoteArena.getStatus().ordinal()+1);
      }
  ),

  /**
   * {@link Arena#getName()}
   */
  NAME(
      ArenaConditionVariableValueString.class,
      localArena -> {
        return new ArenaConditionVariableValueString(localArena.getName());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueString(remoteArena.getName());
      }
  ),

  /**
   * {@link Arena#getDisplayName()}
   */
  DISPLAY_NAME(
      ArenaConditionVariableValueString.class,
      localArena -> {
        return new ArenaConditionVariableValueString(localArena.getDisplayName());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueString(remoteArena.getDisplayName());
      }
  ),

  /**
   * {@link Arena#getRegenerationType()}
   */
  TYPE(
      ArenaConditionVariableValueString.class,
      localArena -> {
        return new ArenaConditionVariableValueString(localArena.getRegenerationType().getId());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueString(remoteArena.getRegenerationType().getId());
      }
  ),

  /**
   * {@link Arena#getGameWorldName()}
   */
  GAME_WORLD(
      ArenaConditionVariableValueString.class,
      localArena -> {
        return new ArenaConditionVariableValueString(localArena.getGameWorldName());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueString(remoteArena.getGameWorldName());
      }
  ),

  /**
   * Gets the name of the arena from which it has been cloned.
   * <p>
   *   Returns <code>NOT_CLONED</code> in case it's not a cloned arena.
   * </p>
   */
  CLONED_ARENA_NAME(
      ArenaConditionVariableValueString.class,
      localArena -> {
        if (localArena.getCloneParent() == null)
          return new ArenaConditionVariableValueString("NOT_CLONED");

        return new ArenaConditionVariableValueString(localArena.getCloneParent().getName());
      },
      remoteArena -> {
        final RemoteArena parent = remoteArena.getCloneParent();

        if (parent == null)
          return new ArenaConditionVariableValueString("NOT_CLONED");

        return new ArenaConditionVariableValueString(parent.getRealName());
      }
  ),

  /**
   * If the arena is local: Plugin tries to fetch the BungeeCord channel name and returns that one. If none has been fetched, it returns LOCAL.<br>
   * If the arena is remote: Returns the channel name of the server at which the arena is located at
   */
  SERVER_CHANNEL_NAME(
      ArenaConditionVariableValueString.class,
      localArena -> {
        final String channel = RemoteAPI.get().getLocalServer().getBungeeChannelName();

        return new ArenaConditionVariableValueString(channel != null ? channel : "LOCAL");
      },
      remoteArena -> {
        final String channel = remoteArena.getRemoteServer().getBungeeChannelName();

        return new ArenaConditionVariableValueString(channel != null ? channel : "LOCAL");
      }
  ),

  /**
   * Gets the amount of arenas on the server of the testing arena that has at least 1 player
   */
  SERVER_PLAYING_ARENAS_COUNT(
      ArenaConditionVariableValueNumber.class,
      localArena -> {
        return new ArenaConditionVariableValueNumber((int) RemoteAPI.get().getLocalServer().getArenas().stream()
            .filter(a -> a.getPlayersCount() >= 1)
            .count());
      },
      remoteArena -> {
        return new ArenaConditionVariableValueNumber((int) remoteArena.getRemoteServer().getArenas().stream()
            .filter(a -> a.getPlayersCount() >= 1)
            .count());
      }
  ),

  /**
   * Custom type
   */
  PLUGIN();

  private final Class<? extends ArenaConditionVariableValue<?>> valueClass;
  private final Function<Arena, ArenaConditionVariableValue<?>> localFetcher;
  private final Function<RemoteArena, ArenaConditionVariableValue<?>> remoteFetcher;
  private ArenaConditionVariable<?> instance;

  ArenaConditionVariableType(
      Class<? extends ArenaConditionVariableValue<?>> valueClass,
      Function<Arena, ArenaConditionVariableValue<?>> localFetcher,
      Function<RemoteArena, ArenaConditionVariableValue<?>> remoteFetcher) {

    this.valueClass = valueClass;
    this.localFetcher = localFetcher;
    this.remoteFetcher = remoteFetcher;
  }

  ArenaConditionVariableType() {
    this.valueClass = null;
    this.localFetcher = null;
    this.remoteFetcher = null;
  }

  /**
   * Returns the effective instance of this type.
   * <p>
   *     May also return <code>null</code> in case the plugin has not been initialized yet.
   * </p>
   *
   * @return The instance of this type. <code>null</code> in case it's {@link #PLUGIN}.
   */
  @Nullable
  public ArenaConditionVariable<?> getInstance() {
    return this.instance;
  }
}