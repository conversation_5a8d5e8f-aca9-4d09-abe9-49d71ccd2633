package de.marcely.bedwars.tools.gui;

import de.marcely.bedwars.tools.gui.type.AnvilGUI;
import org.bukkit.entity.Player;

/**
 * A listener for {@link AnvilGUI} that gets called whenever someone clicks the final item or closes the inventory
 */
public interface WriteListener {

  /**
   * Gets called when player is done writing
   *
   * @param player The player who has been writing
   * @param text The text he wrote
   */
  void onWrite(Player player, String text);
}
