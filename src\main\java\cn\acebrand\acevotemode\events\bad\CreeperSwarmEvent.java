package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Creeper;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 苦力怕群体召唤事件
 * 召唤大量苦力怕攻击玩家
 */
public class CreeperSwarmEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    
    public CreeperSwarmEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(badDir, "creeper_swarm.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/creeper_swarm.yml", false);
                plugin.getLogger().info("已生成苦力怕群体召唤事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载苦力怕群体召唤事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载苦力怕群体召唤事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public String getName() {
        return "CREEPER_SWARM";
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 25) : 25;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }
        
        // 获取配置参数
        int spawnCount = config != null ? config.getInt("creepers.spawn_count", 20) : 20;
        int chargedProbability = config != null ? config.getInt("creepers.charged_probability", 15) : 15;
        double spawnRadius = config != null ? config.getDouble("creepers.spawn_radius", 8.0) : 8.0;
        double health = config != null ? config.getDouble("creepers.health", 20.0) : 20.0;
        float explosionPower = (float) (config != null ? config.getDouble("creepers.explosion_power", 3.0) : 3.0);
        double speedMultiplier = config != null ? config.getDouble("creepers.speed_multiplier", 1.2) : 1.2;
        int lifetime = config != null ? config.getInt("creepers.lifetime", 60) : 60;
        boolean attackTeammates = config != null ? config.getBoolean("creepers.attack_teammates", false) : false;
        
        // 获取玩家队伍信息
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        Team playerTeam = arena != null ? arena.getPlayerTeam(player) : null;
        
        // 生成苦力怕
        List<Creeper> creepers = new ArrayList<>();
        for (int i = 0; i < spawnCount; i++) {
            Location spawnLocation = getRandomSpawnLocation(player.getLocation(), spawnRadius);
            if (spawnLocation != null) {
                Creeper creeper = (Creeper) spawnLocation.getWorld().spawnEntity(spawnLocation, EntityType.CREEPER);
                
                // 设置苦力怕属性
                setupCreeper(creeper, health, explosionPower, speedMultiplier, chargedProbability);
                
                // 设置目标（如果不攻击队友）
                if (!attackTeammates && playerTeam != null) {
                    // 这里可以添加逻辑让苦力怕只攻击敌对队伍
                    // 由于BedWars API限制，暂时让苦力怕攻击所有玩家
                }
                
                creepers.add(creeper);
            }
        }
        
        // 设置生存时间
        if (lifetime > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    for (Creeper creeper : creepers) {
                        if (creeper.isValid()) {
                            creeper.remove();
                        }
                    }
                }
            }.runTaskLater(plugin, lifetime * 20L);
        }
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了苦力怕群体召唤事件，生成了 " + creepers.size() + " 只苦力怕");
    }
    
    /**
     * 获取随机生成位置
     */
    private Location getRandomSpawnLocation(Location center, double radius) {
        for (int attempts = 0; attempts < 10; attempts++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = random.nextDouble() * radius;
            double x = center.getX() + Math.cos(angle) * distance;
            double z = center.getZ() + Math.sin(angle) * distance;
            
            Location spawnLoc = new Location(center.getWorld(), x, center.getY(), z);
            
            // 找到安全的Y坐标
            for (int y = (int) center.getY(); y < center.getY() + 10; y++) {
                spawnLoc.setY(y);
                if (spawnLoc.getBlock().getType().isAir() && 
                    spawnLoc.clone().add(0, 1, 0).getBlock().getType().isAir()) {
                    return spawnLoc;
                }
            }
        }
        return center; // 如果找不到合适位置，就在玩家位置生成
    }
    
    /**
     * 设置苦力怕属性
     */
    private void setupCreeper(Creeper creeper, double health, float explosionPower, 
                            double speedMultiplier, int chargedProbability) {
        // 设置生命值
        try {
            creeper.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(health);
            creeper.setHealth(health);
        } catch (Exception e) {
            // 兼容旧版本
            creeper.setMaxHealth(health);
            creeper.setHealth(health);
        }
        
        // 设置爆炸威力
        creeper.setExplosionRadius((int) explosionPower);
        
        // 设置移动速度
        try {
            creeper.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED)
                   .setBaseValue(creeper.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).getBaseValue() * speedMultiplier);
        } catch (Exception e) {
            plugin.getLogger().warning("无法设置苦力怕移动速度");
        }
        
        // 随机设置为闪电苦力怕
        if (random.nextInt(100) < chargedProbability) {
            creeper.setPowered(true);
        }
        
        // 设置自定义名称
        creeper.setCustomName("§c愤怒的苦力怕");
        creeper.setCustomNameVisible(true);
    }
    
    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null) return true;
        return config.getBoolean("messages.send_message", true);
    }
    
    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null) return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }
    
    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null) return "§c苦力怕大军来袭！快跑！";
        return config.getString("messages.event_message", "§c苦力怕大军来袭！快跑！");
    }
}
