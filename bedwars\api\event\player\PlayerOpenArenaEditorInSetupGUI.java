package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.tools.Validate;
import de.marcely.bedwars.tools.gui.ClickListener;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Gets called when player opens an arena editor inside the setup GUI (/bw arena setupgui)
 */
public abstract class PlayerOpenArenaEditorInSetupGUI extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemoteArena arena;

  private List<Button>[] buttons;
  @Getter @Setter
  private boolean cancelled = false;

  @SuppressWarnings("unchecked")
  public PlayerOpenArenaEditorInSetupGUI(Player player, RemoteArena arena) {
    super(player);

    this.arena = arena;
    this.buttons = new List[ButtonCategory.values().length];

    for (int i = 0; i < this.buttons.length; i++)
      this.buttons[i] = new ButtonsList(ButtonCategory.values()[i]);
  }

  protected abstract void setButtonIcon(Button button, ItemStack icon);

  protected abstract void setButtonVisibility(Button button, boolean visible);

  protected abstract boolean buttonExists(Button button);

  /**
   * Returns the local {@link Arena} that is involved in this event.
   * <p>
   *     May be <code>null</code> if the arena isn't local.
   * </p>
   *
   * @return The arena that is involved. May be <code>null</code>
   */
  @Nullable
  public Arena getArena() {
    return this.arena.getLocal();
  }

  /**
   * Returns the arena that is involved in this event.
   *
   * @return The involved arena
   */
  public RemoteArena getRemoteArena() {
    return this.arena;
  }

  /**
   * Returns all buttons that were added to a specific category.
   * <p>
   * It's safe to remove, add and iterate the entries of this list.
   * Those changes are also getting applied to the real world.
   *
   * @param category The category to which the buttons were added
   * @return All existing buttons of a category
   */
  public List<Button> getButtons(ButtonCategory category) {
    Validate.notNull(category, "category");

    return this.buttons[category.ordinal()];
  }

  /**
   * Adds a button to the end of its set category.
   * <p>
   * Use {@link #construct(Plugin, ButtonCategory, ButtonType, ItemStack, ClickListener)} to create a Button instance.
   *
   * @param button The button that shall be added
   */
  public void addButton(Button button) {
    if (button.getCategory().ordinal() >= ButtonCategory.SPAWNERS.ordinal() &&
        !this.arena.getRegenerationType().isNormal())
      return;

    this.buttons[button.getCategory().ordinal()].add(button);
  }

  /**
   * Constructs a new button on which players will be able to click on.
   * <p>
   * Keep in mind that this does not automatically adds it, it's only constructing the object.
   * To add it use {@link #addButton(Button)} or add it to the list in {@link #getButtons(ButtonCategory)}.
   *
   * @param plugin The plugin that created the listener (or this button). This is important so that the button automatically gets removed when the plugin unload
   * @param category The category/row to which the item shall be added to
   * @param type The type of the button. Plugins should use {@link ButtonType#CUSTOM}, but this is not a must. Does effectivelly nothing, but helps plugins to orientate
   * @param icon The icon of the button which players will see
   * @param listener The listener that handles what will happen when the player clicks on it
   * @return The constructed button
   */
  public Button construct(Plugin plugin, ButtonCategory category, ButtonType type, ItemStack icon, ClickListener listener) {
    Validate.notNull(plugin, "plugin");
    Validate.notNull(category, "category");
    Validate.notNull(type, "type");
    Validate.notNull(icon, "icon");
    Validate.notNull(listener, "listener");

    return new Button(plugin, category, type, icon, listener);
  }

  /**
   * Constructs a spliterator.
   * <p>
   * Keep in mind that this does not automatically adds it, it's only constructing the object.
   * To add it use {@link #addButton(Button)} or add it to the list in {@link #getButtons(ButtonCategory)}.
   *
   * @param plugin The plugin that created the listener (or this button). This is important so that the button automatically gets removed when the plugin unload
   * @param category The category/row to which the item shall be added to
   * @return The constructed button
   */
  public Button constructSpliterator(Plugin plugin, ButtonCategory category) {
    Validate.notNull(plugin, "plugin");
    Validate.notNull(category, "category");

    return new Button(plugin, category, ButtonType.SPLITERATOR, null, ClickListener.Silent.INSTANCE);
  }

  private boolean exists(Button button) {
    for (List<Button> list : this.buttons) {
      if (list.contains(button))
        return true;
    }

    return false;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  public class Button {

    private final Plugin plugin;
    private final ButtonCategory category;
    private final ButtonType type;
    private final ClickListener listener;
    private ItemStack icon;
    private boolean visible = true;

    private Button(Plugin plugin, ButtonCategory category, ButtonType type, ItemStack icon, ClickListener listener) {
      this.plugin = plugin;
      this.category = category;
      this.type = type;
      this.icon = icon;
      this.listener = listener;
    }

    /**
     * Returns the plugin that created this button.
     *
     * @return The creator of this plugin
     */
    public Plugin getPlugin() {
      return this.plugin;
    }

    /**
     * Returns the category in which this button is inside.
     *
     * @return The category of this button
     */
    public ButtonCategory getCategory() {
      return this.category;
    }

    /**
     * Returns the buttons type.
     *
     * @return The type of the button
     */
    public ButtonType getType() {
      return this.type;
    }

    /**
     * Returns the listener that handles what will happen when the player clicks on the button.
     *
     * @return The handler of the button
     */
    public @Nullable ClickListener getListener() {
      return this.listener;
    }

    /**
     * Returns a clone of the icon. Might be null, used for spliterators.
     *
     * @return The icon of the button
     */
    public @Nullable ItemStack getIcon() {
      return this.icon != null ? this.icon.clone() : null;
    }

    /**
     * Set the icon of the button.
     * <p>
     * It's possible to dynamically change the icon by invoking this method.
     *
     * @param icon The new icon
     */
    public void setIcon(@Nullable ItemStack icon) {
      this.icon = icon != null ? icon.clone() : icon;

      PlayerOpenArenaEditorInSetupGUI.this.setButtonIcon(this, this.icon);
    }

    /**
     * A button may exist in the GUI, but this does not mean that it's actually visible.
     * <p>
     * It's possible to dynamically hide and show a button.
     * By this the button disappears as if it has been removed.
     *
     * @return If it's visible or not
     */
    public boolean isVisible() {
      return this.visible;
    }

    /**
     * A button may exist in the GUI, but this does not mean that it's actually visible.
     * <p>
     * It's possible to dynamically hide and show a button.
     * By this the button disappears as if it has been removed.
     *
     * @param visible If it shall be visible or hidden
     */
    public void setVisible(boolean visible) {
      if (this.visible == visible)
        return;

      this.visible = visible;

      PlayerOpenArenaEditorInSetupGUI.this.setButtonVisibility(this, visible);
    }

    /**
     * Returns whether or not the button still exists on the GUI.
     * <p>
     * Keep in mind that this will also return false when the GUI has been disposed.
     * While it may be closed it does not automatically mean that is has been disposed
     *
     * @return If the button still exists internally
     */
    public boolean exists() {
      return PlayerOpenArenaEditorInSetupGUI.this.buttonExists(this);
    }

    /**
     * Returns the event in which this button was constructed.
     *
     * @return The event in which this button was initiated
     */
    public PlayerOpenArenaEditorInSetupGUI getEvent() {
      return PlayerOpenArenaEditorInSetupGUI.this;
    }
  }


  public enum ButtonCategory {

    ACTIONS,
    MODIFY,
    SPAWNERS,
    TEAMS
  }

  public enum ButtonType {

    ENABLE,
    DISABLE,
    REGENERATE,
    TELEPORT,
    ENTER,
    ENTER_AS_SPECTATOR,
    DISPLAY_INFO,
    DELETE,

    CONFIGURE,
    SET_LOBBY,
    SET_SPECTATOR_SPAWN,
    SET_GAME_WORLD,
    SET_REGION_CORNERS,
    SAVE_REGION_CORNERS,
    SAVE_BLOCKS,
    PROPERTIES,
    RENAME,

    REMOVE_ALL_SPAWNERS,
    SHOW_SPAWNERS,
    HIDE_SPAWNERS,
    SPAWNER,

    GET_ALL_TEAM_BEDS,
    TEAM,

    SPLITERATOR,
    CUSTOM
  }

  private class ButtonsList extends ArrayList<Button> {

    private static final long serialVersionUID = 1L;

    private final ButtonCategory category;

    private ButtonsList(ButtonCategory category) {
      super();

      this.category = category;
    }

    @Override
    public boolean add(Button button) {
      check(button);

      return super.add(button);
    }

    @Override
    public void add(int index, Button button) {
      check(button);

      super.add(index, button);
    }

    @Override
    public boolean addAll(Collection<? extends Button> coll) {
      for (Button b : coll)
        add(b);

      return true;
    }

    @Override
    public boolean addAll(int index, Collection<? extends Button> coll) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Button set(int index, Button button) {
      check(button);

      return super.set(index, button);
    }

    private void check(Button button) {
      Validate.notNull(button, "button");
      Validate.isTrue(button.getEvent() == PlayerOpenArenaEditorInSetupGUI.this, "Button was initiated in an other event");
      Validate.isTrue(!exists(button), "Button was already been added");
      Validate.isTrue(this.category == button.getCategory(), "Button was added to an category to which it does not correspond");
    }
  }
}
