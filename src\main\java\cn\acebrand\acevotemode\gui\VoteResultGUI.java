package cn.acebrand.acevotemode.gui;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.manager.VoteManager;
import cn.acebrand.acevotemode.model.GameMode;
import cn.acebrand.acevotemode.util.ColorUtil;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 投票结果展示GUI
 * 包含动态滚动效果来展示最终选择的游戏模式
 */
public class VoteResultGUI implements Listener {
    
    private final AceVoteMode plugin;
    private final Arena arena;
    private final Map<Player, Inventory> playerInventories;
    private final Map<Player, BukkitTask> animationTasks;
    
    // 动画配置
    private static final int ANIMATION_DURATION = 100; // 总动画时长（tick）
    private static final int SCROLL_SPEED = 3; // 滚动速度（tick间隔）
    private static final int RESULT_SLOT = 13; // 结果显示槽位（中央）
    
    // 动画状态
    private GameMode finalMode;
    private List<GameMode> candidateModes;
    private String selectionReason;

    // 防止重复发送消息
    private boolean finalResultShown = false;
    
    public VoteResultGUI(AceVoteMode plugin, Arena arena) {
        this.plugin = plugin;
        this.arena = arena;
        this.playerInventories = new ConcurrentHashMap<>();
        this.animationTasks = new ConcurrentHashMap<>();

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // 计算最终模式和候选模式
        calculateResult();
    }
    
    /**
     * 计算投票结果
     */
    private void calculateResult() {
        VoteManager.ArenaVoteData voteData = plugin.getVoteManager().getArenaVoteData(arena);
        Map<String, Integer> voteCounts = voteData.getAllVoteCounts();
        
        if (voteData.getTotalVotes() == 0) {
            // 无人投票：从所有模式中随机选择
            candidateModes = new ArrayList<>(plugin.getConfigManager().getGameModes().values());
            selectionReason = "无人投票，随机选择模式";
        } else {
            // 有投票：获取最高票数的模式
            List<String> topModeIds = voteData.getTopModes();
            candidateModes = new ArrayList<>();
            
            for (String modeId : topModeIds) {
                GameMode mode = plugin.getConfigManager().getGameModes().get(modeId);
                if (mode != null) {
                    candidateModes.add(mode);
                }
            }
            
            if (topModeIds.size() > 1) {
                selectionReason = "票数相同，随机选择模式";
            } else {
                selectionReason = "获胜模式";
            }
        }
        
        // 随机选择最终模式
        if (!candidateModes.isEmpty()) {
            finalMode = candidateModes.get(new Random().nextInt(candidateModes.size()));
        }
    }
    
    /**
     * 为所有玩家显示投票结果GUI
     */
    public void showToAllPlayers() {
        for (Player player : arena.getPlayers()) {
            showToPlayer(player);
        }
    }
    
    /**
     * 为指定玩家显示投票结果GUI
     */
    public void showToPlayer(Player player) {
        if (finalMode == null) {
            player.sendMessage(ChatColor.RED + "无法确定游戏模式！");
            return;
        }
        
        // 创建GUI
        Inventory inventory = Bukkit.createInventory(null, 27,
                translateColor("&6&l游戏模式选择中..."));
        
        // 初始化GUI
        initializeGUI(inventory);
        
        // 保存玩家的GUI
        playerInventories.put(player, inventory);
        
        // 显示GUI
        player.openInventory(inventory);

        // 播放开始音效
        playStartSound(player);

        // 开始动画
        startAnimation(player, inventory);
    }
    
    /**
     * 初始化GUI界面
     */
    private void initializeGUI(Inventory inventory) {
        // 填充边框
        ItemStack borderItem = createItem(Material.GRAY_STAINED_GLASS_PANE, " ", null);
        for (int i = 0; i < 27; i++) {
            if (i < 9 || i >= 18 || i % 9 == 0 || i % 9 == 8) {
                inventory.setItem(i, borderItem);
            }
        }
        
        // 设置标题
        ItemStack titleItem = createItem(Material.PAPER,
                translateColor("&e" + selectionReason),
                Arrays.asList(
                    translateColor("&7正在选择游戏模式..."),
                    translateColor("&7候选模式: " + candidateModes.size() + " 个")
                ));
        inventory.setItem(4, titleItem);
        
        // 设置初始滚动物品
        ItemStack scrollItem = createItem(Material.BARRIER,
                translateColor("&c选择中..."), null);
        inventory.setItem(RESULT_SLOT, scrollItem);
    }
    
    /**
     * 开始动画
     */
    private void startAnimation(Player player, Inventory inventory) {
        BukkitTask task = new BukkitRunnable() {
            private int tick = 0;
            private int currentModeIndex = 0;
            
            @Override
            public void run() {
                if (!player.isOnline() || !playerInventories.containsKey(player)) {
                    cancel();
                    return;
                }
                
                // 滚动动画
                if (tick % SCROLL_SPEED == 0 && tick < ANIMATION_DURATION - 20) {
                    // 显示当前候选模式
                    GameMode currentMode = candidateModes.get(currentModeIndex % candidateModes.size());
                    ItemStack modeItem = createModeItem(currentMode, false);
                    inventory.setItem(RESULT_SLOT, modeItem);

                    // 播放滚动音效
                    playScrollSound(player);

                    currentModeIndex++;
                }
                
                // 动画结束，显示最终结果
                if (tick >= ANIMATION_DURATION) {
                    showFinalResult(inventory);
                    
                    // 延迟关闭GUI并应用模式
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            player.closeInventory();
                            playerInventories.remove(player);
                        }
                    }.runTaskLater(plugin, 60L); // 3秒后关闭
                    
                    cancel();
                    return;
                }
                
                tick++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
        
        animationTasks.put(player, task);
    }
    
    /**
     * 显示最终结果
     */
    private void showFinalResult(Inventory inventory) {
        // 更新标题
        ItemStack titleItem = createItem(Material.EMERALD,
                translateColor("&a已选择游戏模式!"),
                Arrays.asList(
                    translateColor("&7" + selectionReason),
                    translateColor("&e模式: " + finalMode.getPlainName())
                ));
        inventory.setItem(4, titleItem);
        
        // 显示最终模式
        ItemStack finalItem = createModeItem(finalMode, true);
        inventory.setItem(RESULT_SLOT, finalItem);
        
        // 添加装饰效果
        ItemStack effectItem = createItem(Material.FIREWORK_STAR,
                translateColor("&6✦"), null);
        inventory.setItem(12, effectItem);
        inventory.setItem(14, effectItem);
        
        // 发送游戏模式确定消息（只发送一次给所有玩家）
        if (!finalResultShown) {
            finalResultShown = true;
            String confirmMessage = translateColor("&6游戏模式已确定: &e" + finalMode.getPlainName());
            for (Player player : arena.getPlayers()) {
                player.sendMessage(confirmMessage);
            }
        }

        // 播放确定音效（只给有GUI的玩家播放）
        for (Player player : arena.getPlayers()) {
            if (playerInventories.containsKey(player)) {
                playConfirmSound(player);
            }
        }
    }
    
    /**
     * 创建模式物品
     */
    private ItemStack createModeItem(GameMode mode, boolean isFinal) {
        ItemStack item = mode.getIcon().clone();
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            if (isFinal) {
                meta.setDisplayName(translateColor("&a✓ " + mode.getName()));
                List<String> lore = new ArrayList<>();
                lore.add(translateColor("&7最终选择的游戏模式"));
                lore.add("");
                // 对模式描述应用颜色代码转换
                for (String desc : mode.getDescription()) {
                    lore.add(translateColor(desc));
                }
                meta.setLore(lore);
            } else {
                meta.setDisplayName(translateColor("&e" + mode.getName()));
                meta.setLore(Arrays.asList(translateColor("&7候选模式...")));
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * 创建物品
     */
    private ItemStack createItem(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            if (lore != null) {
                meta.setLore(lore);
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * 播放开始音效
     */
    private void playStartSound(Player player) {
        try {
            player.playSound(player.getLocation(),
                    org.bukkit.Sound.BLOCK_NOTE_BLOCK_CHIME, 1.0f, 1.0f);
        } catch (Exception e) {
            // 如果音效播放失败，使用备用音效
            try {
                player.playSound(player.getLocation(),
                        org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 0.8f, 1.0f);
            } catch (Exception ex) {
                // 忽略音效错误
            }
        }
    }

    /**
     * 播放滚动音效
     */
    private void playScrollSound(Player player) {
        try {
            player.playSound(player.getLocation(),
                    org.bukkit.Sound.UI_BUTTON_CLICK, 0.3f, 1.2f);
        } catch (Exception e) {
            // 如果音效播放失败，使用备用音效
            try {
                player.playSound(player.getLocation(),
                        org.bukkit.Sound.BLOCK_NOTE_BLOCK_HARP, 0.3f, 1.2f);
            } catch (Exception ex) {
                // 忽略音效错误
            }
        }
    }

    /**
     * 播放确定音效
     */
    private void playConfirmSound(Player player) {
        try {
            // 播放成功音效
            player.playSound(player.getLocation(),
                    org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);

            // 延迟播放额外的庆祝音效
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                try {
                    player.playSound(player.getLocation(),
                            org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.5f);
                } catch (Exception ex) {
                    // 忽略音效错误
                }
            }, 10L);

        } catch (Exception e) {
            // 如果音效播放失败，使用备用音效
            try {
                player.playSound(player.getLocation(),
                        org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
            } catch (Exception ex) {
                // 忽略音效错误
            }
        }
    }

    /**
     * 转换颜色代码
     */
    private String translateColor(String text) {
        if (text == null) return "";
        return text.replace("&", "§");
    }

    /**
     * 获取最终选择的模式
     */
    public GameMode getFinalMode() {
        return finalMode;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        // 取消所有动画任务
        for (BukkitTask task : animationTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        animationTasks.clear();

        // 关闭所有GUI
        for (Map.Entry<Player, Inventory> entry : playerInventories.entrySet()) {
            Player player = entry.getKey();
            if (player.isOnline()) {
                player.closeInventory();
            }
        }
        playerInventories.clear();

        // 注销事件监听器
        InventoryCloseEvent.getHandlerList().unregister(this);
    }

    /**
     * 防止玩家手动关闭投票结果GUI
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        Player player = (Player) event.getPlayer();

        // 检查是否是我们的投票结果GUI
        if (playerInventories.containsKey(player)) {
            // 检查是否是动画完成后的正常关闭
            if (!finalResultShown) {
                // 动画还没完成，重新打开GUI
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (player.isOnline() && playerInventories.containsKey(player)) {
                        player.openInventory(playerInventories.get(player));
                    }
                }, 1L);
            }
        }
    }
}
