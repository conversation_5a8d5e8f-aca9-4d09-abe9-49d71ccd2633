package de.marcely.bedwars.tools;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.jetbrains.annotations.Nullable;

/**
 * The Either type represents a value of one of two possible types (a disjoint union).
 * The data constructors; Left and Right represent the two possible values.
 *
 * @param <L> Left type
 * @param <R> Right type
 */
public interface Either<L, R> {

  /**
   * Returns if something has been set to the left field.
   * <p>
   * {@link #hasRight()} will return <code>false</code> if this method returns <code>true</code>
   *
   * @return true if this is a left value, false if this is a right value
   */
  boolean hasLeft();

  /**
   * Returns if something has been set to the right field.
   * <p>
   * {@link #hasLeft()} will return <code>false</code> if this method returns <code>true</code>
   *
   * @return true if this is a right value, false if this is a left value
   */
  boolean hasRight();

  /**
   * Retrieve the left value.
   * <p>
   * May return <code>null</code> even when {@link #hasLeft()} returns true.
   * Also returns <code>null</code> if this is a right value.
   *
   * @return The set left value
   */
  @Nullable L left();

  /**
   * Retrieve the right value.
   * <p>
   * May return <code>null</code> even when {@link #hasRight()} returns true.
   * Also returns <code>null</code> if this is a left value.
   *
   * @return The set right value
   */
  @Nullable R right();

  /**
   * Creates a new Either with something set to the left value.
   *
   * @param <L> The type for left
   * @param <R> The type for right
   * @param left The value that shall be set
   * @return The created Either instance
   */
  static <L, R> Either<L, R> left(L left) {
    Validate.notNull(left, "left");

    return new Left<L, R>(left);
  }

  /**
   * Creates a new Either with something set to the right value.
   *
   * @param <L> The type for left
   * @param <R> The type for right
   * @param right The value that shall be set
   * @return The created Either instance
   */
  static <L, R> Either<L, R> right(R right) {
    Validate.notNull(right, "right");

    return new Right<L, R>(right);
  }

  @ToString
  @EqualsAndHashCode
  class Left<L, R> implements Either<L, R> {

    private final L entry;

    public Left(L entry) {
      this.entry = entry;
    }

    @Override
    public boolean hasLeft() {
      return true;
    }

    @Override
    public boolean hasRight() {
      return false;
    }

    @Override
    public L left() {
      return this.entry;
    }

    @Override
    public R right() {
      return null;
    }
  }

  @ToString
  @EqualsAndHashCode
  class Right<L, R> implements Either<L, R> {

    private final R entry;

    public Right(R entry) {
      this.entry = entry;
    }

    @Override
    public boolean hasLeft() {
      return false;
    }

    @Override
    public boolean hasRight() {
      return true;
    }

    @Override
    public L left() {
      return null;
    }

    @Override
    public R right() {
      return this.entry;
    }
  }
}
