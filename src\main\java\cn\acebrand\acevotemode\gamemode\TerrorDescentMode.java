package cn.acebrand.acevotemode.gamemode;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.terror.BossManager;
import cn.acebrand.acevotemode.gamemode.terror.MonsterSpawnManager;
import cn.acebrand.acevotemode.gamemode.terror.TeamEffectManager;
import cn.acebrand.acevotemode.gamemode.terror.TerrorTimerManager;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

import java.util.Arrays;
import cn.acebrand.acevotemode.gamemode.terror.CountdownManager;
import cn.acebrand.acevotemode.gamemode.terror.HealthAnnouncementManager;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 恐怖降临模式
 * 特点：每隔10分钟在中心点生成Boss，绿宝石和钻石资源点生成大量怪物
 * Boss击杀后给予队伍属性效果
 */
public class TerrorDescentMode extends GameModeBase implements Listener {

    // 活跃的竞技场
    private final Set<String> activeArenas = ConcurrentHashMap.newKeySet();

    // 管理器映射
    private final Map<String, CountdownManager> countdownManagers = new ConcurrentHashMap<>();
    private final Map<String, HealthAnnouncementManager> healthManagers = new ConcurrentHashMap<>();

    // 管理器组件
    private BossManager bossManager;
    private MonsterSpawnManager monsterSpawnManager;
    private TeamEffectManager teamEffectManager;
    private TerrorTimerManager timerManager;
    private cn.acebrand.acevotemode.gamemode.terror.BossSkillListener skillListener;

    // 静态实例，用于访问
    private static TerrorDescentMode instance;

    public TerrorDescentMode(AceVoteMode plugin) {
        super(plugin, "terror-descent", "恐怖降临模式");
        instance = this;

        // 初始化管理器
        initializeManagers();

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 初始化所有管理器
     */
    private void initializeManagers() {
        this.bossManager = new BossManager(plugin, this);
        this.monsterSpawnManager = new MonsterSpawnManager(plugin, this);
        this.teamEffectManager = new TeamEffectManager(plugin, this);
        this.timerManager = new TerrorTimerManager(plugin, this);

        // 注册Boss技能监听器
        this.skillListener = new cn.acebrand.acevotemode.gamemode.terror.BossSkillListener(plugin, this);
        Bukkit.getPluginManager().registerEvents(skillListener, plugin);
    }

    @Override
    protected void createDefaultConfig() {
        // 添加配置文件头部注释
        addConfigComments();

        // 基础配置
        config.set("enabled", true);
        config.set("description", "恐怖降临！光明先驱三阶段Boss战斗，击杀Boss获得强大属性效果！");

        // Boss配置
        config.set("boss.spawn-interval", 600); // 10分钟 = 600秒
        config.set("boss.mythic-mob-id", "LightHerald1"); // 使用恐怖降临Boss第一阶段
        config.set("boss.spawn-locations", new String[] {}); // 空数组，需要管理员设置
        config.set("boss.announce-spawn", true);
        config.set("boss.announce-death", true);

        // Boss技能提示配置
        config.set("boss.skill-notifications.enabled", true);
        config.set("boss.skill-notifications.show-minor-skills", false); // 是否显示小技能

        // Boss生成音效配置
        config.set("boss.spawn-sound.enabled", true);
        config.set("boss.spawn-sound.sound", "ENTITY_ENDER_DRAGON_GROWL");
        config.set("boss.spawn-sound.volume", 1.0);
        config.set("boss.spawn-sound.pitch", 0.8);
        config.set("boss.health-multiplier", 1.0);
        config.set("boss.max-count", 1); // 最大Boss数量
        config.set("boss.prevent-spawn-if-alive", true); // 如果有Boss存活则不生成新的
        config.set("boss.damage-multiplier", 1.0);

        // Boss阶段配置 - 适配恐怖降临三阶段Boss
        config.set("boss.phases.phase-1.health-range", "100%");
        config.set("boss.phases.phase-1.name", "光明先驱");
        config.set("boss.phases.phase-1.message", "&e⚡ 光明先驱降临战场！");
        config.set("boss.phases.phase-1.mythic-mob-id", "LightHerald1");
        config.set("boss.phases.phase-1.skills", Arrays.asList("LH1", "LH2", "LH3", "LH4", "LH5", "LH6", "LH7"));

        config.set("boss.phases.phase-2.health-range", "100%");
        config.set("boss.phases.phase-2.name", "光明先驱强化");
        config.set("boss.phases.phase-2.message", "&5🌙 光明先驱进入第二阶段！");
        config.set("boss.phases.phase-2.mythic-mob-id", "LightHerald2");
        config.set("boss.phases.phase-2.skills", Arrays.asList("LK1", "LK2", "LK3", "LK4", "LK5", "LK6", "LK8", "LK9", "LK10", "LK11", "LK12", "LK13"));

        config.set("boss.phases.phase-3.health-range", "100%");
        config.set("boss.phases.phase-3.name", "光明核心");
        config.set("boss.phases.phase-3.message", "&c💀 最终阶段：光明核心觉醒！");
        config.set("boss.phases.phase-3.mythic-mob-id", "LightCore");
        config.set("boss.phases.phase-3.skills", Arrays.asList("LC1", "LC2", "LC2_2", "LC3", "LC4", "LC5", "LC6", "LC6_2", "LC7", "LC8", "LC9", "LC10", "LC11"));

        // Boss版本配置 - 使用您的恐怖降临Boss
        config.set("boss.version", "terror-descent"); // 使用恐怖降临版本
        config.set("boss.terror-descent.enabled", true);
        config.set("boss.terror-descent.phases", 3); // 三个阶段
        config.set("boss.terror-descent.health", 500); // 每阶段500血量
        config.set("boss.terror-descent.auto-progression", true); // 自动阶段切换

        // 召唤物配置 - 适配恐怖降临Boss召唤物
        config.set("boss.minions.light-warrior.mythic-mob-id", "LightWarrior");
        config.set("boss.minions.light-warrior.max-count", 4);
        config.set("boss.minions.light-warrior.spawn-radius", 15);
        config.set("boss.minions.light-warrior.health", 35);
        config.set("boss.minions.light-warrior.damage", 8);

        config.set("boss.minions.spear.mythic-mob-id", "Spear");
        config.set("boss.minions.spear.max-count", 2);
        config.set("boss.minions.spear.spawn-radius", 20);
        config.set("boss.minions.spear.health", 100);
        config.set("boss.minions.spear.damage", 0);

        config.set("boss.minions.beam.mythic-mob-id", "Beam1");
        config.set("boss.minions.beam.max-count", 3);
        config.set("boss.minions.beam.spawn-radius", 12);
        config.set("boss.minions.beam.health", 100);
        config.set("boss.minions.beam.damage", 10);

        // 怪物生成配置
        config.set("monsters.enabled", true);
        config.set("monsters.spawn-on-emerald", true);
        config.set("monsters.spawn-on-diamond", true);
        config.set("monsters.count-per-spawner", 3);
        config.set("monsters.spawn-radius", 5.0);
        config.set("monsters.burn-in-daylight", false);
        config.set("monsters.show-name-tags", true);
        config.set("monsters.name-prefix", "&c恐怖");
        config.set("monsters.lifetime", 300); // 5分钟后自动消失

        // 怪物类型配置 - 降低血量适应玩家1-2排血
        config.set("monsters.types.zombie.enabled", true);
        config.set("monsters.types.zombie.weight", 30);
        config.set("monsters.types.zombie.health", 25.0);
        config.set("monsters.types.zombie.count", 2); // 每次生成的数量

        config.set("monsters.types.skeleton.enabled", true);
        config.set("monsters.types.skeleton.weight", 25);
        config.set("monsters.types.skeleton.health", 20.0);
        config.set("monsters.types.skeleton.count", 2);

        config.set("monsters.types.spider.enabled", true);
        config.set("monsters.types.spider.weight", 20);
        config.set("monsters.types.spider.health", 18.0);
        config.set("monsters.types.spider.count", 3); // 蜘蛛数量多一些

        config.set("monsters.types.creeper.enabled", true);
        config.set("monsters.types.creeper.weight", 15);
        config.set("monsters.types.creeper.health", 22.0);
        config.set("monsters.types.creeper.count", 1); // 苦力怕数量少一些

        config.set("monsters.types.enderman.enabled", true);
        config.set("monsters.types.enderman.weight", 10);
        config.set("monsters.types.enderman.health", 35.0);
        config.set("monsters.types.enderman.count", 1); // 末影人数量少一些

        // 队伍效果配置
        config.set("team-effects.enabled", true);
        config.set("team-effects.duration", 300); // 5分钟
        config.set("team-effects.announce", true);
        config.set("team-effects.stack-effects", false); // 是否叠加效果
        config.set("team-effects.override-existing", true); // 是否覆盖现有效果

        // 效果列表 - 可配置多个属性
        config.set("team-effects.effects.strength.enabled", true);
        config.set("team-effects.effects.strength.level", 2);
        config.set("team-effects.effects.strength.duration", 300); // 单独的持续时间

        config.set("team-effects.effects.speed.enabled", true);
        config.set("team-effects.effects.speed.level", 2);
        config.set("team-effects.effects.speed.duration", 300);

        config.set("team-effects.effects.resistance.enabled", true);
        config.set("team-effects.effects.resistance.level", 1);
        config.set("team-effects.effects.resistance.duration", 300);

        config.set("team-effects.effects.regeneration.enabled", true);
        config.set("team-effects.effects.regeneration.level", 1);
        config.set("team-effects.effects.regeneration.duration", 180); // 3分钟

        config.set("team-effects.effects.haste.enabled", false);
        config.set("team-effects.effects.haste.level", 2);
        config.set("team-effects.effects.haste.duration", 300);

        config.set("team-effects.effects.jump_boost.enabled", false);
        config.set("team-effects.effects.jump_boost.level", 2);
        config.set("team-effects.effects.jump_boost.duration", 300);

        config.set("team-effects.effects.fire_resistance.enabled", false);
        config.set("team-effects.effects.fire_resistance.level", 1);
        config.set("team-effects.effects.fire_resistance.duration", 300);

        config.set("team-effects.effects.water_breathing.enabled", false);
        config.set("team-effects.effects.water_breathing.level", 1);
        config.set("team-effects.effects.water_breathing.duration", 300);

        // 消息配置 - 适配恐怖降临Boss
        config.set("messages.mode-start", "&4&l恐怖降临模式已启动！");
        config.set("messages.mode-description", "&7每10分钟光明先驱降临，资源点怪物横行！");
        config.set("messages.boss-spawn", "&4&l⚡【恐怖降临】⚡ &c光明先驱已经苏醒！");
        config.set("messages.boss-death", "&a&l🎉【史诗胜利】🎉 &f{team} 队伍击败了光明先驱！");
        config.set("messages.boss-void-return", "&4&l⚡【恐怖降临】⚡ &c光明先驱从虚空爬了回来！");
        config.set("messages.boss-spawn-prevented", "&e&l【提醒】&f 当前还有Boss存活，新Boss生成已取消！");
        config.set("messages.monster-spawn", "&e&l【警告】&f 资源点出现了恐怖怪物！");
        config.set("messages.monsters-cleared", "&a&l【净化】&f Boss被击杀，所有恐怖怪物已被清除！");
        config.set("messages.effect-gained", "&b&l【强化】&f 你的队伍获得了强大的属性效果！");
        config.set("messages.effect-details", "&7获得效果: {effects}");
        config.set("messages.welcome", "&c欢迎来到恐怖降临模式！准备迎接挑战！");

        // Boss阶段消息 - 适配三阶段Boss
        config.set("messages.phase-1", "&e⚡ 光明先驱降临战场！");
        config.set("messages.phase-2", "&5🌙 光明先驱进入第二阶段！");
        config.set("messages.phase-3", "&c💀 最终阶段：光明核心觉醒！");

        // 召唤物消息 - 适配恐怖降临召唤物
        config.set("messages.light-warrior-spawn", "&e⚔ 光明战士降临！");
        config.set("messages.spear-spawn", "&6🗡 光明长矛出现！");
        config.set("messages.beam-spawn", "&c🔥 光明光束现身！");

        // 倒计时消息配置
        config.set("messages.countdown.minutes", "&6&l【倒计时】&f Boss将在 &c{minutes}分钟 &f后降临！");
        config.set("messages.countdown.seconds", "&6&l【倒计时】&f Boss将在 &c{seconds}秒 &f后降临！");
        config.set("messages.countdown.final", "&4&l【警告】&f Boss即将降临！所有玩家准备战斗！");

        // 倒计时音效配置
        config.set("messages.countdown.sounds.default", "BLOCK_NOTE_BLOCK_PLING");
        config.set("messages.countdown.sounds.final", "ENTITY_ENDER_DRAGON_GROWL");



        saveConfig();
        addDetailedCommentsToFile();
    }

    @Override
    protected void onConfigReload() {
        // 重新初始化管理器
        if (bossManager != null) {
            bossManager.reloadConfig();
        }
        if (monsterSpawnManager != null) {
            monsterSpawnManager.reloadConfig();
        }
        if (teamEffectManager != null) {
            teamEffectManager.reloadConfig();
        }
        if (timerManager != null) {
            timerManager.reloadConfig();
        }
    }

    @Override
    public void onGameStart(Arena arena) {
        // 检查是否已经启动过
        if (activeArenas.contains(arena.getName())) {
            return;
        }

        // 标记竞技场为活跃状态
        activeArenas.add(arena.getName());

        // 初始化倒计时管理器
        CountdownManager countdownManager = new CountdownManager(plugin, arena, getConfig());
        countdownManagers.put(arena.getName(), countdownManager);

        // 初始化血量公告管理器
        HealthAnnouncementManager healthManager = new HealthAnnouncementManager(plugin, arena, getConfig());
        healthManagers.put(arena.getName(), healthManager);

        // 向所有玩家发送模式开始消息
        String startMessage = config.getString("messages.mode-start", "&4&l恐怖降临模式已启动！");
        String description = config.getString("messages.mode-description", "&7每10分钟Boss降临，资源点怪物横行！");

        for (Player player : arena.getPlayers()) {
            player.sendMessage(translateColors(startMessage));
            player.sendMessage(translateColors(description));
        }

        // 启动定时器管理器
        timerManager.startTimer(arena);

        // 启动Boss生成倒计时
        int spawnInterval = getConfig().getInt("boss.spawn-interval", 600);
        countdownManager.startCountdown(spawnInterval);

        plugin.getLogger().info("恐怖降临模式已在竞技场 " + arena.getName() + " 启动");
    }

    @Override
    public void onGameEnd(Arena arena) {
        // 移除竞技场的活跃状态
        activeArenas.remove(arena.getName());

        // 停止倒计时管理器
        CountdownManager countdownManager = countdownManagers.remove(arena.getName());
        if (countdownManager != null) {
            countdownManager.stopCountdown();
        }

        // 清理血量公告管理器
        HealthAnnouncementManager healthManager = healthManagers.remove(arena.getName());
        if (healthManager != null) {
            healthManager.reset();
        }

        // 停止所有管理器的任务
        timerManager.stopTimer(arena);
        bossManager.cleanupArena(arena);
        monsterSpawnManager.cleanupArena(arena);
        teamEffectManager.cleanupArena(arena);

        plugin.getLogger().info("恐怖降临模式已在竞技场 " + arena.getName() + " 结束");
    }

    @Override
    public void onPlayerJoin(Player player, Arena arena) {
        if (config.getBoolean("display.show-welcome-message", true)) {
            String welcomeMessage = config.getString("messages.welcome", "&c欢迎来到恐怖降临模式！");
            player.sendMessage(translateColors(welcomeMessage));
        }
    }

    @Override
    public void onPlayerQuit(Player player, Arena arena) {
        // 恐怖降临模式无需对离开的玩家进行特殊处理
    }

    /**
     * 颜色代码转换
     */
    private String translateColors(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 添加配置文件注释
     */
    private void addConfigComments() {
        // 恐怖降临模式的配置注释
    }

    /**
     * 添加详细注释到配置文件
     */
    private void addDetailedCommentsToFile() {
        // 恐怖降临模式的详细配置说明
    }

    // Getter方法
    public static TerrorDescentMode getInstance() {
        return instance;
    }

    public BossManager getBossManager() {
        return bossManager;
    }

    public MonsterSpawnManager getMonsterSpawnManager() {
        return monsterSpawnManager;
    }

    public TeamEffectManager getTeamEffectManager() {
        return teamEffectManager;
    }

    public TerrorTimerManager getTimerManager() {
        return timerManager;
    }

    public cn.acebrand.acevotemode.gamemode.terror.BossSkillListener getSkillListener() {
        return skillListener;
    }

    public Set<String> getActiveArenas() {
        return activeArenas;
    }

    /**
     * 检查竞技场是否处于恐怖降临模式
     */
    public boolean isArenaActive(Arena arena) {
        return activeArenas.contains(arena.getName()) && arena.getStatus() == ArenaStatus.RUNNING;
    }

    /**
     * 公共的保存配置方法
     */
    public void saveConfigPublic() {
        saveConfig();
    }

    /**
     * 获取倒计时管理器
     */
    public CountdownManager getCountdownManager(Arena arena) {
        return countdownManagers.get(arena.getName());
    }

    /**
     * 获取血量公告管理器
     */
    public HealthAnnouncementManager getHealthAnnouncementManager(Arena arena) {
        return healthManagers.get(arena.getName());
    }
}
