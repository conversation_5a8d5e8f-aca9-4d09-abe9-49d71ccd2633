package de.marcely.bedwars.api;

import org.bukkit.command.CommandSender;

/**
 * Contains all permissions that are being provided by MBedwars
 */
public enum Permissions {

  /**
   * Grants access to the "/bw stats" command
   */
  COMMAND_STATS(base() + "cmd.stats"),

  /**
   * Grants access to the "/bw join" command
   */
  COMMAND_JOIN(base() + "cmd.join"),

  /**
   * Grants access to the "/bw rejoin" command
   */
  COMMAND_REJOIN(base() + "cmd.rejoin"),

  /**
   * Grants access to the "/bw leave" command
   */
  COMMAND_LEAVE(base() + "cmd.leave"),

  /**
   * Grants access to the "/bw forcestart" command and the forcestart item
   */
  COMMAND_FORCESTART(base() + "cmd.forcestart"),

  /**
   * Grants access to the "/bw kick" command
   */
  COMMAND_KICK(base() + "cmd.kick"),

  /**
   * Grants access to the "/bw locateplayer" command
   */
  COMMAND_LOCATE_PLAYER(base() + "cmd.locateplayer"),

  /**
   * Grants access to admin specific commands and abilities.
   * <p>
   * This includes: reload, arena spawn, sethubpos, tools, backup, addon, checkupdate, debug
   */
  ADMIN(base() + "admin"),

  /**
   * Generally the plugin forbids any user to teleport to an arena when they're not inside playing in it.
   * <p>
   * This has been added as a security measure to prevent players from doing things such as /back or /home.
   */
  TELEPORT_INSIDE_ARENA(base() + "arenatp"),

  /**
   * Similar to {@link #TELEPORT_INSIDE_ARENA}, this forbids players to build inside arenas in which they aren't participating
   */
  BUILD_INSIDE_ARENA(base() + "arenabuild"),

  /**
   * Used for the arena beta feature (Config "beta").
   * <p>
   * By this a message is being displayed that gamemode is still in beta and only players with this permission are able to join an arena.
   */
  BETA_USER(base() + "betauser"),

  /**
   * Players using this permission are able to join full lobbies.
   * <p>
   * By this they'll kick a random player who doesn't have this permission in favor for a new available slot.
   */
  JOIN_FULL_LOBBIES(base() + "joinfull"),

  /**
   * Bypasses command restrictions from the config.
   * <p>
   *   See <code>blocked-commands-mode</code> and <code>blocked-commands</code> in config.yml.
   * </p>
   */
  BYPASS_BLOCKED_COMANDS(base() + "bypassblockedcommands"),

  /**
   * Used for the "amount-perm" config for shop prices.
   * <p>
   * Players using this permission will see the "amount-perm" instead.
   */
  CUSTOM_PRICE_IN_SHOP(base() + "shopcustomprice"),

  /**
   * Used for the "language-per-user-requires-permission" config.
   * <p>
   * If it's enabled then the plugin will only display the messages in the language of the player when they have this permission.
   */
  LANGUAGE_PER_USER(base() + "langperuser"),

  /**
   * Permission for the config "specialitem-requiredpermission".
   * <p>
   * If that config is enabled then only players with this permission can use specific special items.
   * This permission has a parameter which represents the id of the special item.
   */
  SPECIAL_ITEM_USE(base() + "specialitem.%s"),

  /**
   * Permissions to buy specific shop items.
   * <p>
   * Users can set shop items to require a certain permission in order to be purchased.
   * This permission has a parameter which represents the id of the shop item.
   */
  SHOP_ITEM_USE(base() + "shopitem.%s");

  private final String string;

  Permissions(String string) {
    this.string = string;
  }

  /**
   * Returns the permission in Bukkit's String format.
   * <p>
   * A few permissions require parameters to be added. Those will be displayed as "%s" using this method.
   *
   * @return The permission in String format
   */
  public String get() {
    return this.string;
  }

  /**
   * Returns the permission in Bukkit's String format and overrides all parameters.
   *
   * @param params The parameters you want to be added
   * @return The permission in String format with the parameters overwritten
   */
  public String get(String... params) {
    return String.format(this.string, (Object[]) params);
  }

  /**
   * Returns whether or not the sender is granted to this permission.
   *
   * @param sender The person we want to check
   * @return <code>true</code> if he is permitted
   */
  public boolean has(CommandSender sender) {
    if (hasIncludeAdmin(sender))
      return true;

    return sender.hasPermission(this.string);
  }

  /**
   * Returns whether or not the sender is granted to this permission.
   * <p>
   * A few permissions require parameters to be added. Those will be displayed as "%s" using this method.
   *
   * @param sender The person we want to check
   * @param params The parameters you want to be added
   * @return <code>true</code> if he is permitted
   */
  public boolean has(CommandSender sender, String... params) {
    if (hasIncludeAdmin(sender))
      return true;

    return sender.hasPermission(String.format(this.string, (Object[]) params));
  }

  private boolean hasIncludeAdmin(CommandSender sender) {
    if (!(this == BUILD_INSIDE_ARENA || this == TELEPORT_INSIDE_ARENA))
      return false;

    return sender.hasPermission(ADMIN.string);
  }

  private static String base() {
    return "mbedwars.";
  }
}
