package de.marcely.bedwars.api.game.lobby;

import de.marcely.bedwars.api.arena.RegenerationType;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the type of {@link LobbyItemHandler}.
 * <p>
 * Custom ones use {@link #PLUGIN}
 */
public enum LobbyItemHandlerType {

  /**
   * Opens a GUI that displays all achievements that the player has obtained or hasn't obtained yet.
   */
  VIEW_ACHIEVEMENTS("bedwars:view_achievements"),

  /**
   * Decreases lobby timer on use.
   */
  FORCE_START("bedwars:force_start"),

  /**
   * Kicks player out of the arena.
   */
  LEAVE("bedwars:leave"),

  /**
   * Opens a GUI in which the player can select the team in which he wants to be in.
   */
  SELECT_TEAM("bedwars:select_team"),

  /**
   * Used for arenas of the type {@link RegenerationType#VOTING}: Opens a GUI on use in which players can vote in which arena the want to play in.
   */
  VOTE_ARENA("bedwars:vote_arena"),

  /**
   * A custom type created by something accessing the API.
   */
  PLUGIN(null);

  private final String id;

  LobbyItemHandlerType(@Nullable String id) {
    this.id = id;
  }

  /**
   * Returns the id that's being used for the handler.
   * <code>null</code> is being returned when passing {@link #PLUGIN}.
   *
   * @return The id of this handler type
   */
  public @Nullable String getId() {
    return this.id;
  }
}
