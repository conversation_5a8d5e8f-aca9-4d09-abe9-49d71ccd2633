package cn.acebrand.acevotemode;

import cn.acebrand.acevotemode.config.ConfigManager;
import cn.acebrand.acevotemode.events.CustomEventManager;
import cn.acebrand.acevotemode.handler.VoteLobbyItemHandler;
import cn.acebrand.acevotemode.listener.ArenaEventListener;
import cn.acebrand.acevotemode.manager.VoteManager;
import cn.acebrand.acevotemode.manager.GameModeManager;
import cn.acebrand.acevotemode.manager.GlobalUpgradeManager;
import cn.acebrand.acevotemode.manager.GlobalResourceManager;
import cn.acebrand.acevotemode.manager.VoteResultManager;
import cn.acebrand.acevotemode.command.AceVoteModeCommand;
import cn.acebrand.acevotemode.commands.LuckyTestCommand;

import cn.acebrand.acevotemode.placeholder.VoteModePlaceholders;
import cn.acebrand.acevotemode.events.LuckyEventManager;
import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.game.lobby.LobbyItemHandler;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;

/**
 * AceVoteMode - 起床战争投票系统插件
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class AceVoteMode extends JavaPlugin {

    private static AceVoteMode instance;
    private ConfigManager configManager;
    private VoteManager voteManager;
    private GameModeManager gameModeManager;
    private GlobalUpgradeManager globalUpgradeManager;
    private GlobalResourceManager globalResourceManager;
    private VoteResultManager voteResultManager;
    private VoteLobbyItemHandler voteLobbyItemHandler;
    private CustomEventManager customEventManager;
    private LuckyEventManager luckyEventManager;

    @Override
    public void onEnable() {
        instance = this;

        // 启动信息
        getLogger().info("AceVoteMode v" + getDescription().getVersion() + " starting...");

        try {
            // 检查 MBedwars 是否存在
            if (!getServer().getPluginManager().isPluginEnabled("MBedwars")) {
                getLogger().severe("MBedwars plugin not found! Disabling AceVoteMode...");
                getServer().getPluginManager().disablePlugin(this);
                return;
            }

            // 初始化配置管理器
            configManager = new ConfigManager(this);
            configManager.loadConfigs();

            // 生成事件配置文件
            generateEventConfigs();

            // 初始化投票管理器
            voteManager = new VoteManager(this);

            // 初始化游戏模式管理器
            gameModeManager = new GameModeManager(this);

            // 初始化全局升级管理器
            globalUpgradeManager = new GlobalUpgradeManager(this);

            // 初始化全局资源管理器
            globalResourceManager = new GlobalResourceManager(this);

            // 初始化投票结果管理器
            voteResultManager = new VoteResultManager(this);

            // 初始化自定义事件管理器
            customEventManager = new CustomEventManager(this);

            // 初始化幸运方块事件管理器
            luckyEventManager = new LuckyEventManager(this);

            // 等待 Bedwars API 准备就绪
            BedwarsAPI.onReady(() -> {
                try {
                    // 注册大厅物品处理器
                    voteLobbyItemHandler = new VoteLobbyItemHandler(this);

                    boolean registered = GameAPI.get().registerLobbyItemHandler(voteLobbyItemHandler);

                    if (!registered) {
                        getLogger().severe("Failed to register lobby item handler!");
                    } else {
                        getLogger().info("Successfully registered vote item handler");
                    }

                } catch (Exception e) {
                    getLogger().severe("Failed to integrate with Bedwars API: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            // 注册事件监听器
            getServer().getPluginManager().registerEvents(new ArenaEventListener(this), this);
            getServer().getPluginManager()
                    .registerEvents(new cn.acebrand.acevotemode.listener.LuckyBlockMonsterListener(this), this);
            getServer().getPluginManager()
                    .registerEvents(new cn.acebrand.acevotemode.listener.UpgradeListener(this), this);

            // 注册命令
            getCommand("acevotemode").setExecutor(new AceVoteModeCommand(this));

            // 注册测试指令
            LuckyTestCommand luckyTestCommand = new LuckyTestCommand(this);
            getCommand("luckytest").setExecutor(luckyTestCommand);
            getCommand("luckytest").setTabCompleter(luckyTestCommand);

            // 注册恐怖降临Boss管理指令
            cn.acebrand.acevotemode.commands.TerrorBossCommand terrorBossCommand = new cn.acebrand.acevotemode.commands.TerrorBossCommand(
                    this);
            getCommand("terrorboss").setExecutor(terrorBossCommand);
            getCommand("terrorboss").setTabCompleter(terrorBossCommand);

            // 注册恐怖降临测试指令
            cn.acebrand.acevotemode.commands.TerrorTestCommand terrorTestCommand = new cn.acebrand.acevotemode.commands.TerrorTestCommand(
                    this);
            getCommand("terrortest").setExecutor(terrorTestCommand);

            // 注册PlaceholderAPI
            if (getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
                try {
                    new VoteModePlaceholders(this).register();
                    getLogger().info("Successfully registered PlaceholderAPI expansion");
                } catch (Exception e) {
                    getLogger().warning("Failed to register PlaceholderAPI expansion: " + e.getMessage());
                }
            } else {
                getLogger().warning("PlaceholderAPI not found - placeholders will not work");
            }

            getLogger().info("AceVoteMode enabled successfully!");

        } catch (Exception e) {
            getLogger().severe("Critical error during plugin initialization: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }

    @Override
    public void onDisable() {
        // 注销大厅物品处理器
        if (voteLobbyItemHandler != null && voteLobbyItemHandler.isRegistered()) {
            GameAPI.get().unregisterLobbyItemHandler(voteLobbyItemHandler);
        }

        // 清理投票数据
        if (voteManager != null) {
            voteManager.cleanup();
        }

        // 清理游戏模式数据
        if (gameModeManager != null) {
            gameModeManager.cleanup();
        }

        // 清理投票结果数据
        if (voteResultManager != null) {
            voteResultManager.cleanup();
        }

        getLogger().info("AceVoteMode has been disabled!");
    }

    /**
     * 获取插件实例
     */
    public static AceVoteMode getInstance() {
        return instance;
    }

    /**
     * 获取配置管理器
     */
    public ConfigManager getConfigManager() {
        return configManager;
    }

    /**
     * 获取投票管理器
     */
    public VoteManager getVoteManager() {
        return voteManager;
    }

    /**
     * 获取游戏模式管理器
     */
    public GameModeManager getGameModeManager() {
        return gameModeManager;
    }

    /**
     * 获取全局升级管理器
     */
    public GlobalUpgradeManager getGlobalUpgradeManager() {
        return globalUpgradeManager;
    }

    /**
     * 获取全局资源管理器
     */
    public GlobalResourceManager getGlobalResourceManager() {
        return globalResourceManager;
    }

    /**
     * 获取投票结果管理器
     */
    public VoteResultManager getVoteResultManager() {
        return voteResultManager;
    }

    public CustomEventManager getCustomEventManager() {
        return customEventManager;
    }

    /**
     * 生成事件配置文件
     */
    private void generateEventConfigs() {
        try {
            // 创建events目录结构
            File eventsDir = new File(getDataFolder(), "events");
            File goodDir = new File(eventsDir, "good");
            File badDir = new File(eventsDir, "bad");
            File neutralDir = new File(eventsDir, "neutral");

            goodDir.mkdirs();
            badDir.mkdirs();
            neutralDir.mkdirs();

            // 生成好事件配置文件
            saveResourceIfNotExists("events/good/item_reward.yml");

            // 生成坏事件配置文件
            saveResourceIfNotExists("events/bad/lightning_strike.yml");
            saveResourceIfNotExists("events/bad/creeper_swarm.yml");
            saveResourceIfNotExists("events/bad/instant_explosion.yml");
            saveResourceIfNotExists("events/bad/monster_horde.yml");
            saveResourceIfNotExists("events/bad/negative_buff.yml");
            saveResourceIfNotExists("events/bad/anvil_rain.yml");
            saveResourceIfNotExists("events/bad/cobweb_trap.yml");
            saveResourceIfNotExists("events/bad/launch_player.yml");
            saveResourceIfNotExists("events/bad/confiscate_item.yml");
            saveResourceIfNotExists("events/bad/iron_cage.yml");
            saveResourceIfNotExists("events/bad/obsidian_cage.yml");
            saveResourceIfNotExists("events/bad/tnt_spawn.yml");

            // 生成中立事件配置文件
            saveResourceIfNotExists("events/neutral/nothing_happens.yml");
            saveResourceIfNotExists("events/neutral/resource_clear.yml");
            saveResourceIfNotExists("events/neutral/resource_spawner_monster.yml");

            getLogger().info("事件配置文件生成完成");

        } catch (Exception e) {
            getLogger().severe("生成事件配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 如果文件不存在则保存资源文件
     */
    private void saveResourceIfNotExists(String resourcePath) {
        File file = new File(getDataFolder(), resourcePath);
        if (!file.exists()) {
            try {
                saveResource(resourcePath, false);
                getLogger().info("已生成配置文件: " + resourcePath);
            } catch (Exception e) {
                getLogger().warning("无法生成配置文件 " + resourcePath + ": " + e.getMessage());
            }
        }
    }

    /**
     * 重载插件配置
     */
    public void reload() {
        try {
            configManager.loadConfigs();
            voteManager.reload();
            gameModeManager.reloadAllConfigs();
            getLogger().info("AceVoteMode configuration reloaded successfully!");
        } catch (Exception e) {
            getLogger().severe("Failed to reload configuration: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取无限火力模式实例
     */
    public cn.acebrand.acevotemode.gamemode.UnlimitedFireMode getUnlimitedFireMode() {
        return gameModeManager != null ? gameModeManager.getUnlimitedFireMode() : null;
    }

    /**
     * 获取火力不足模式实例
     */
    public cn.acebrand.acevotemode.gamemode.LowFireMode getLowFireMode() {
        return gameModeManager != null ? gameModeManager.getLowFireMode() : null;
    }

}
