package cn.acebrand.acevotemode.listener;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.player.PlayerTriggerUpgradeEvent;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.upgrade.UpgradeLevel;
import de.marcely.bedwars.api.game.upgrade.UpgradeTriggerHandler;
import de.marcely.bedwars.api.game.upgrade.UpgradeTriggerHandlerType;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;

/**
 * 监听玩家购买升级事件，特别是资源点升级
 * 当玩家购买spawner_multiplier升级时，更新我们的自定义生成器速度
 */
public class UpgradeListener implements Listener {

    private final AceVoteMode plugin;

    public UpgradeListener(AceVoteMode plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerTriggerUpgrade(PlayerTriggerUpgradeEvent event) {
        if (event.isCancelled()) {
            return;
        }

        Arena arena = event.getArena();
        Team team = event.getTeam();
        UpgradeLevel upgradeLevel = event.getUpgradeLevel();

        // 检查是否是spawner_multiplier升级
        UpgradeTriggerHandler handler = upgradeLevel.getTriggerHandler();
        if (handler != null && handler.getType() == UpgradeTriggerHandlerType.SPAWNER_MULTIPLIER) {
            plugin.getLogger().info("[升级监听] 检测到队伍 " + team.getDisplayName() + " 购买了资源点升级");

            // 获取升级的倍数
            double amplifier = getSpawnerMultiplier(upgradeLevel);
            if (amplifier > 0) {
                plugin.getLogger().info("[升级监听] 资源点升级倍数: " + amplifier);

                // 更新该队伍附近的自定义生成器
                updateCustomSpawnersForTeam(arena, team, amplifier);
            }
        }
    }

    /**
     * 获取spawner_multiplier的倍数
     */
    private double getSpawnerMultiplier(UpgradeLevel upgradeLevel) {
        try {
            // 直接从UpgradeLevel获取amplifier值
            double amplifier = upgradeLevel.getAmplifier();
            plugin.getLogger().info("[升级监听] 从UpgradeLevel获取到amplifier: " + amplifier);
            return amplifier;

        } catch (Exception e) {
            plugin.getLogger().warning("[升级监听] 获取spawner_multiplier倍数失败: " + e.getMessage());
            return 1.0; // 默认无倍数
        }
    }

    /**
     * 更新队伍附近的自定义生成器
     */
    private void updateCustomSpawnersForTeam(Arena arena, Team team, double amplifier) {
        try {
            // 获取队伍的生成点位置
            de.marcely.bedwars.tools.location.XYZYP teamSpawnXYZYP = arena.getTeamSpawn(team);
            if (teamSpawnXYZYP == null) {
                plugin.getLogger().warning("[升级监听] 无法获取队伍 " + team.getDisplayName() + " 的生成点");
                return;
            }
            org.bukkit.Location teamSpawn = teamSpawnXYZYP.toLocation(arena.getGameWorld());

            // 查找队伍附近的生成器（通常在30格范围内）
            double searchRadius = 30.0;

            for (Spawner spawner : arena.getSpawners()) {
                de.marcely.bedwars.tools.location.XYZ spawnerXYZ = spawner.getLocation();
                org.bukkit.Location spawnerLoc = spawnerXYZ.toLocation(arena.getGameWorld());

                // 检查生成器是否在队伍附近
                if (teamSpawn.distance(spawnerLoc) <= searchRadius) {
                    String resourceType = spawner.getDropType().getId();

                    // 只处理铁锭和金锭
                    if ("iron".equals(resourceType) || "gold".equals(resourceType)) {
                        updateCustomSpawnerSpeed(arena, spawner, resourceType, amplifier);
                    }
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("[升级监听] 更新自定义生成器失败: " + e.getMessage());
        }
    }

    /**
     * 更新单个自定义生成器的速度
     */
    private void updateCustomSpawnerSpeed(Arena arena, Spawner spawner, String resourceType, double amplifier) {
        try {
            // 检查当前游戏模式
            String currentMode = getCurrentGameMode(arena);

            if ("unlimited-fire".equals(currentMode)) {
                // 更新无限火力模式的生成器
                plugin.getUnlimitedFireMode().updateSpawnerSpeed(spawner, resourceType, amplifier);
                plugin.getLogger().info("[升级监听] 已更新无限火力模式 " + resourceType + " 生成器速度，倍数: " + amplifier);

            } else if ("low-fire".equals(currentMode)) {
                // 更新火力不足模式的生成器
                plugin.getLowFireMode().updateSpawnerSpeed(spawner, resourceType, amplifier);
                plugin.getLogger().info("[升级监听] 已更新火力不足模式 " + resourceType + " 生成器速度，倍数: " + amplifier);

            } else {
                // 更新全局模式的生成器
                plugin.getGlobalResourceManager().updateSpawnerSpeed(spawner, resourceType, amplifier);
                plugin.getLogger().info("[升级监听] 已更新全局模式 " + resourceType + " 生成器速度，倍数: " + amplifier);
            }

        } catch (Exception e) {
            plugin.getLogger().warning("[升级监听] 更新生成器速度失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前游戏模式
     */
    private String getCurrentGameMode(Arena arena) {
        try {
            // 检查竞技场是否有特定的游戏模式标记
            // 这里需要根据实际的游戏模式检测逻辑来实现

            // 先检查是否是无限火力模式
            if (plugin.getUnlimitedFireMode().isActiveInArena(arena)) {
                return "unlimited-fire";
            }

            // 再检查是否是火力不足模式
            if (plugin.getLowFireMode().isActiveInArena(arena)) {
                return "low-fire";
            }

            // 默认是全局模式
            return "global";

        } catch (Exception e) {
            plugin.getLogger().warning("[升级监听] 获取游戏模式失败: " + e.getMessage());
            return "global";
        }
    }
}
