LightHerald1:
  Type: VINDICATOR
  Display: '&e⚡ 光明先驱 &7(第一阶段)'
  Health: 500
  Faction: Light
  Damage: 0
  Armor: 0
  DamageModifiers:
  - FALL 0
  BossBar:
    Enabled: true
    Title: '&e⚡ 光明先驱 &7(第一阶段)'
    Range: 100
    Color: RED
    Style: SEGMENTED_20
  Modules:
   ThreatTable: true
  Options:
    PreventOtherDrops: true
    Despawn: false
    MaxCombatDistance: 256
    PreventRandomEquipment: true
    PreventSunburn: true
    AttackSpeed: 0.18
    Collidable: true
    FollowRange: 80
    KnockbackResistance: 100
    MovementSpeed: 0.19
    AttackSpeed: 1
    NoDamageTicks: 10
    NoAI: false
    Silent: true
  Skills:
  - model{modelid=herald_of_light_phase_1;damagetint=false} @Self ~onSpawn
  - model{modelid=herald_of_light_phase_1;damagetint=false} @Self ~onLoad
  - sound{s=entity.ender_dragon.growl;p=0.8;v=2} @Self ~onSpawn
  - sound{s=entity.lightning_bolt.thunder;p=1.2;v=1.5} @Self ~onSpawn
  - state{state=damage} @Self ~onDamaged
  - sound{s=entity.iron_golem.hurt;p=0.7;v=2} @Self ~onDamaged
  - sound{s=entity.blaze.hurt;p=1.8;v=2} @Self ~onDamaged
  - metaskill{s=LH8;sync=true} @self ~onDamaged
  - cancelevent @self ~onAttack
  - randomskill{s=LH1 1, LH2 1, LH3 1, LH4 1, LH5 1, LH6 1, LH7 1} ~onTimer:15
  - skill{s=[
      - damage{a=5} @LivingNearTargetLocation{r=2.5}
    ]} @modelpart{mid=herald_of_light_phase_1;pid=b_mirror} ~onTimer:1
  - skill{s=[
       - damage{a=5} @LivingNearTargetLocation{r=2.5}
     ]} @modelpart{mid=herald_of_light_phase_1;pid=b_mirror2} ~onTimer:1
LightHerald2:
  Type: VINDICATOR
  Display: '&5🌙 光明先驱 &7(第二阶段)'
  Health: 500
  Faction: Light
  Damage: 0
  Armor: 0
  DamageModifiers:
  - FALL 0
  BossBar:
    Enabled: true
    Title: '&5🌙 光明先驱 &7(第二阶段)'
    Range: 100
    Color: RED
    Style: SEGMENTED_20
  Modules:
   ThreatTable: true
  Options:
    PreventOtherDrops: true
    Despawn: false
    MaxCombatDistance: 256
    PreventRandomEquipment: true
    PreventSunburn: true
    AttackSpeed: 0.18
    Collidable: true
    FollowRange: 80
    KnockbackResistance: 100
    MovementSpeed: 0.19
    AttackSpeed: 1
    NoDamageTicks: 10
    NoAI: false
    Silent: true
  Skills:
  - randomskill{s=LK1 1, LK2 1, LK3 1, LK4 1, LK5 1, LK6 1, LH7 1, LK8 1, LK9 1, LK10 1,LK11 1, LK12 1, LK13 1} ~onTimer:15
  - model{modelid=herald_of_light_phase_2;damagetint=false} @Self ~onSpawn
  - model{modelid=herald_of_light_phase_2;damagetint=false} @Self ~onLoad
  - sound{s=entity.ender_dragon.growl;p=1.0;v=2} @Self ~onSpawn
  - sound{s=entity.wither.spawn;p=0.8;v=1.5} @Self ~onSpawn
  - sound{s=entity.iron_golem.hurt;p=0.7;v=2} @Self ~onDamaged
  - sound{s=entity.blaze.hurt;p=1.8;v=2} @Self ~onDamaged
  - sound{s=entity.blaze.death;p=2;v=2} @Self ~onDeath
  - sound{s=entity.iron_golem.death;p=0.7;v=2} @Self ~onDeath
  - state{state=damage} @Self ~onDamaged
  - metaskill{s=LH8;sync=true} @self ~onDamaged
  - gcd{ticks=70} @self ~onSpawn
  - cancelevent @self ~onAttack
  - setAI{ai=false} @self ~onSpawn
  - potion{type=DAMAGE_RESISTANCE;duration=60;level=100} @self ~onSpawn
  - setAI{ai=true;delay=60} @self ~onSpawn
  - skill{s=[
      - damage{a=5} @LivingNearTargetLocation{r=2.5}
    ]} @modelpart{mid=herald_of_light_phase_2;pid=b_mirror} ~onTimer:1
  - skill{s=[
       - damage{a=5} @LivingNearTargetLocation{r=2.5}
     ]} @modelpart{mid=herald_of_light_phase_2;pid=b_mirror2} ~onTimer:1
  - skill{s=[
      - damage{a=8} @LivingNearTargetLocation{r=3}
    ]} @modelpart{mid=herald_of_light_phase_2;pid=b_beam_damage_1} ~onTimer:1
  - skill{s=[
       - damage{a=8} @LivingNearTargetLocation{r=3}
     ]} @modelpart{mid=herald_of_light_phase_2;pid=b_beam_damage_1} ~onTimer:1
LightCore:
  Type: VINDICATOR
  Display: '&c💀 光明核心 &7(最终阶段)'
  Health: 500
  Faction: Light
  Damage: 0
  Armor: 0
  DamageModifiers:
  - FALL 0
  BossBar:
    Enabled: true
    Title: '&c💀 光明核心 &7(最终阶段)'
    Range: 100
    Color: RED
    Style: SEGMENTED_20
  Modules:
   ThreatTable: true
  Options:
    PreventOtherDrops: true
    Despawn: false
    MaxCombatDistance: 256
    PreventRandomEquipment: true
    PreventSunburn: true
    AttackSpeed: 0.18
    Collidable: true
    FollowRange: 80
    KnockbackResistance: 100
    MovementSpeed: 0.13
    AttackSpeed: 1
    NoDamageTicks: 10
    NoAI: false
    Silent: true
  Skills:
  - randomskill{s=LC1 1, LC2 1, LC2_2 1, LC3 1, LC4 1, LC5 1, LC6 1, LC6_2 1, LC7 1, LC8 1, LC9 1, LC10 1, LC11 1} ~onTimer:10
  - metaskill{s=LH8;sync=true} @self ~onDamaged
  - model{modelid=herald_of_light_core;damagetint=false} @Self ~onSpawn
  - model{modelid=herald_of_light_core;damagetint=false} @Self ~onLoad
  - sound{s=entity.ender_dragon.growl;p=1.2;v=2.5} @Self ~onSpawn
  - sound{s=entity.wither.spawn;p=1.0;v=2} @Self ~onSpawn
  - sound{s=entity.lightning_bolt.thunder;p=0.8;v=2} @Self ~onSpawn
  - state{state=damage} @Self ~onDamaged
  - sound{s=block.amethyst_block.hit;p=0;v=2} @Self ~onDamaged
  - sound{s=entity.blaze.hurt;p=1.8;v=2} @Self ~onDamaged
  - sound{s=block.amethyst_block.break;p=0;v=2} @Self ~onDeath
  - sound{s=entity.blaze.death;p=1.8;v=2} @Self ~onDeath
  - cancelevent @self ~onAttack
  - gcd{ticks=245} @self ~onSpawn
  - setAI{ai=false} @self ~onSpawn
  - potion{type=DAMAGE_RESISTANCE;duration=220;level=100} @self ~onSpawn
  - setAI{ai=true;delay=220} @self ~onSpawn
  - skill{s=[
      - damage{a=5} @LivingNearTargetLocation{r=3}
    ]} @modelpart{mid=herald_of_light_core;pid=b_mirror_damage} ~onTimer:1
  - skill{s=[
       - damage{a=5} @LivingNearTargetLocation{r=3}
     ]} @modelpart{mid=herald_of_light_core;pid=b_mirror2_damage} ~onTimer:1
  - skill{s=[
      - damage{a=5} @LivingNearTargetLocation{r=3}
    ]} @modelpart{mid=herald_of_light_core;pid=b_mirror3_damage} ~onTimer:1
  - skill{s=[
       - damage{a=5} @LivingNearTargetLocation{r=3}
     ]} @modelpart{mid=herald_of_light_core;pid=b_mirror4_damage} ~onTimer:1
  - skill{s=[
      - damage{a=8} @LivingNearTargetLocation{r=3}
    ]} @modelpart{mid=herald_of_light_core;pid=b_beam_mirror_damage_1} ~onTimer:1
  - skill{s=[
       - damage{a=8} @LivingNearTargetLocation{r=3}
     ]} @modelpart{mid=herald_of_light_core;pid=b_beam_mirror_damage_2} ~onTimer:1
  - skill{s=[
      - damage{a=8} @LivingNearTargetLocation{r=3}
    ]} @modelpart{mid=herald_of_light_core;pid=b_beam_mirror_damage_3} ~onTimer:1
  - skill{s=[
       - damage{a=8} @LivingNearTargetLocation{r=3}
     ]} @modelpart{mid=herald_of_light_core;pid=b_beam_mirror_damage_4} ~onTimer:1
  - skill{s=[
       - damage{a=20} @LivingNearTargetLocation{r=3}
     ]} @modelpart{mid=herald_of_light_core;pid=b_core_beam_damage} ~onTimer:1
Spear:
 Type: SKELETON
 Display: '&6🗡 光明长矛'
 Health: 100
 Faction: Light
 Options:
  PreventOtherDrops: true
  Despawn: false
  MaxCombatDistance: 256
  Silent: true
  PreventRandomEquipment: true
  PreventSunburn: true
  AttackSpeed: 0.18
  Collidable: false
  NoAI: true
  Invincible: true
 Skills:
 - model{modelid=light_herald_spear;damagetint=false} @Self ~onSpawn
Spear2:
 Type: SKELETON
 Display: '&6🗡 光明长矛'
 Health: 100
 Faction: Light
 Options:
  PreventOtherDrops: true
  Despawn: false
  MaxCombatDistance: 256
  Silent: true
  PreventRandomEquipment: true
  PreventSunburn: true
  AttackSpeed: 0.18
  Collidable: false
  NoAI: true
  Invincible: true
 Skills:
 - model{modelid=light_herald_spear2;damagetint=false} @Self ~onSpawn
 - remove{delay=100} @self ~onSpawn
LightProjectile:
 Type: SKELETON
 Display: Light
 Health: 100
 Faction: Light
 Options:
  PreventOtherDrops: true
  Despawn: false
  MaxCombatDistance: 256
  Silent: true
  PreventRandomEquipment: true
  PreventSunburn: true
  AttackSpeed: 0.18
  Collidable: false
  NoAI: true
  Invincible: true
 Skills:
 - model{modelid=light_projectile;damagetint=false} @Self ~onSpawn
LightWarrior:
  Type: VINDICATOR
  Display: '&e⚔ 光明战士'
  Health: 35
  Faction: Light
  Damage: 0
  Armor: 0
  DamageModifiers:
  - FALL 0
  Modules:
   ThreatTable: true
  Options:
    NoGravity: false
    PreventOtherDrops: true
    Despawn: false
    MaxCombatDistance: 256
    PreventRandomEquipment: true
    PreventSunburn: true
    AttackSpeed: 0.18
    Collidable: false
    FollowRange: 80
    KnockbackResistance: 100
    MovementSpeed: 0.19
    AttackSpeed: 1
    Invincible: false
    NoAI: false
    Silent: true
  Skills:
  - model{modelid=light_warrior;damagetint=false} @Self ~onSpawn
  - sound{s=entity.iron_golem.hurt;p=2;v=2} @Self ~onDamaged
  - sound{s=entity.blaze.hurt;p=1.8;v=2} @Self ~onDamaged
  - model{modelid=light_warrior;damagetint=false} @Self ~onLoad
  - randomskill{s=LW1 1, LW2 1, LW3 1} ~onTimer:10
  - setAI{ai=false} @self ~onSpawn
  - cancelevent @self ~onAttack
  - potion{type=DAMAGE_RESISTANCE;duration=40;level=100} @self ~onSpawn
  - setAI{ai=true;delay=40} @self ~onSpawn
Beam1:
 Type: SKELETON
 Display: '&c🔥 光明光束'
 Health: 100
 Faction: Light
 Options:
  PreventOtherDrops: true
  Despawn: false
  MaxCombatDistance: 256
  Silent: true
  PreventRandomEquipment: true
  PreventSunburn: true
  AttackSpeed: 0.18
  Collidable: false
  NoAI: true
  Invincible: true
 Skills:
 - model{modelid=sun_light_beam;damagetint=false} @Self ~onSpawn
 - damage{a=10;pi=true;pk=true;delay=35;repeat=8;repeatinterval=2} @EIR{r=0.8} ~onSpawn
 - remove{delay=70} @self ~onSpawn
 - sound{s=block.fire.extinguish;p=0;v=1;delay=35;repeat=4;repeatinterval=4} @Self ~onSpawn
 - effect:particles{particle=lava;amount=3;hS=0.3;vS=0.1;speed=0;repeat=8;repeatinterval=2;delay=35} @self ~onSpawn
 - effect:particles{particle=flash;amount=1;hS=0;vS=0;speed=0;repeat=4;repeatinterval=4;delay=35} @self ~onSpawn
Beam2:
 Type: VINDICATOR
 Display: '&c🔥 光明长光束'
 Health: 100
 Faction: Light
 Options:
  PreventOtherDrops: true
  Despawn: false
  MaxCombatDistance: 256
  Silent: true
  PreventRandomEquipment: true
  PreventSunburn: true
  AttackSpeed: 0.18
  Collidable: false
  MovementSpeed: 0.22
  NoAI: false
  Invincible: true
 Skills:
 - model{modelid=sun_light_long_beam;damagetint=false} @Self ~onSpawn
 - damage{a=10;pi=true;pk=true;delay=35;repeat=90;repeatinterval=2} @EIR{r=1.1} ~onSpawn
 - sound{s=block.fire.extinguish;p=0;v=1;delay=35;repeat=22.5;repeatinterval=8} @Self ~onSpawn
 - remove{delay=232} @self ~onSpawn
 - cancelevent @self ~onAttack
 - effect:particles{particle=lava;amount=3;hS=0.3;vS=0.1;speed=0;repeat=90;repeatinterval=2;delay=35} @self ~onSpawn
 - effect:particles{particle=flash;amount=1;hS=0;vS=0;speed=0;repeat=45;repeatinterval=4;delay=35} @self ~onSpawn
     
#IXN4MERTlDVmVGZpl1YqBjevdFVykkeNBzYq50MFRUT4FFVjJ1boh2ap1WV512Q4pEZmVjM