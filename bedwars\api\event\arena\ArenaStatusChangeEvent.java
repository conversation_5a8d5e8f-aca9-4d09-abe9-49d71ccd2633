package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when the status of an arena is about to change
 */
public class ArenaStatusChangeEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final ArenaStatus before;
  private ArenaStatus newStatus;

  public ArenaStatusChangeEvent(Arena arena, ArenaStatus before, ArenaStatus newStatus) {
    this.arena = arena;
    this.before = before;
    this.newStatus = newStatus;
  }

  /**
   * Returns the current status.
   *
   * @return The current status of the arena
   */
  public ArenaStatus getOldStatus() {
    return this.before;
  }

  /**
   * Returns the new status status that should be set.
   *
   * @return The new status
   */
  public ArenaStatus getNewStatus() {
    return this.newStatus;
  }

  /**
   * Set the new status that should be set.
   *
   * @param status The new status
   */
  public void setNewStatus(ArenaStatus status) {
    Validate.notNull(status, "status");

    this.newStatus = status;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
