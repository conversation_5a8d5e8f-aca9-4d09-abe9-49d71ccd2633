package cn.acebrand.acevotemode.events.good;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.entity.Player;
import org.bukkit.Location;

/**
 * 增益效果事件 - 好事件
 */
public class BuffEffectEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    
    public BuffEffectEvent(AceVoteMode plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // TODO: 实现增益效果逻辑
        player.sendMessage("§a[幸运方块] §f你获得了增益效果！");
        plugin.getLogger().info("执行增益效果事件: " + player.getName());
    }
    
    @Override
    public String getName() {
        return "BUFF_EFFECT";
    }
    
    @Override
    public EventType getType() {
        return EventType.GOOD;
    }
    
    @Override
    public int getWeight() {
        return 8; // 权重
    }
}
