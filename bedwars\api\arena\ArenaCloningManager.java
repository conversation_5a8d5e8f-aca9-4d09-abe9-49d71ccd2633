package de.marcely.bedwars.api.arena;

import org.bukkit.World;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * The manager for the so-called arena auto-cloning system.
 * <p>
 *     Note that it is not possible to interact with it while {@link #isActive()} returns false.
 * </p>
 */
public interface ArenaCloningManager {

  /**
   * Gets whether this manager is currently actively processing the cloning of new arenas.
   * <p>
   *     In case this method returns <code>false</code>, the methods under this class, that modify e.g. the queue, won't have any effect.
   * </p>
   * <p>
   *     Whether the manager is active is being determined by the configuration of the "auto-cloning-enabled" config (located within the config.yml file).
   * </p>
   *
   * @return Whether this manager is active or not
   */
  boolean isActive();

  /**
   * Gets all existing arenas that have been cloned.
   *
   * @return An immutable collection of arenas that have been cloned
   */
  Collection<Arena> getClonedArenas();

  /**
   * Gets all worlds whose purpose is to only contain the cloned arenas.
   * <p>
   *     The processor is creating new temporary worlds for the cloned arenas. They automatically get deleted when not needed anymore.
   * </p>
   *
   * @return An immutable collection of worlds used for the cloning system
   */
  Collection<World> getClonePurposeWorlds();

  /**
   * Gets all arenas that are enqueued for the cloning process.
   * <p>
   *     It is legit for it to contain the same arena more than once.
   *     The pure existence of an arena within this Collection is not a promise for the arena to be actually cloned.
   * </p>
   * <p>
   *     A separate thread will be pulling the arenas from the queue.
   * </p>
   *
   * @return An immmutable collection of arenas that may will be cloned in the future
   * @see #addToQueue(Arena)
   * @see #addToQueue(Arena, boolean)
   * @see #removeFromQueue(Arena)
   * @see #getQueueEntries()
   */
  Collection<Arena> getQueue();

  /**
   * Gets all entries that are enqueued for the cloning process.
   * <p>
   *     It is legit for it to contain the same arena more than once.
   *     The pure existence of an arena within this Collection is not a promise for the arena to be actually cloned.
   * </p>
   * <p>
   *     A separate thread will be pulling the arenas from the queue.
   * </p>
   *
   * @return An immmutable collection of arenas that may will be cloned in the future
   * @see #addToQueue(QueuedCloningArena)
   * @see #removeFromQueue(Arena)
   * @see #getQueue()
   */
  Collection<QueuedCloningArena> getQueueEntries();

  /**
   * Adds an arena to the queue.
   * <p>
   *     There are certain limits within the configurations that limit the amount of arenas that may be cloned.
   *     You may overcome that limit using the <code>ignoreLimits</code> parameter.
   * </p>
   * <p>
   *     This method fully supports async operations.<br>
   *     It is also okay for an arena to be added multiple times. It will be cloned that amount of times.
   * </p>
   * <p>
   *     An arena may not be cloned (this method returns <code>false</code>) for the following reasons:<br>
   *     - {@link #isActive()} returns false<br>
   *     - Arena itself is a cloned arena ({@link Arena#isCloned()} returns true)<br>
   *     - Arena is not a "normal" regeneration type ({@link RegenerationType#isNormal()} returns false)<br>
   *     - The status of the arena is {@link ArenaStatus#STOPPED}<br>
   *     - The arena depends on SWM while SWM is not installed<br>
   *     - Arena does not exist<br>
   *     - There are limits preventing it (<code>ignoreLimits</code> is set to false)<br>
   *     - It is a part of the 'auto-cloning-excluded-arenas' config
   * </p>
   * <p>
   *     The pure existence of an arena within this Collection is not a promise for the arena to be actually cloned.
   *     Certain states may have changed with the start of its theoretical cloning process.
   * </p>
   *
   * @param entry The entry shall be added. Create it using its constructors
   * @return <code>true</code> when an arena has been added to the queue. This does not tell whether it actually succeded the cloning! Use the callback for that
   * @see #getQueue()
   * @see #addToQueue(Arena, boolean)
   * @see #removeFromQueue(Arena)
   */
  boolean addToQueue(QueuedCloningArena entry);

  /**
   * Adds an arena to the queue.
   * <p>
   *     There are certain limits within the configurations that limit the amount of arenas that may be cloned.
   *     You may overcome that limit using the <code>ignoreLimits</code> parameter.
   * </p>
   * <p>
   *     This method fully supports async operations.<br>
   *     It is also okay for an arena to be added multiple times. It will be cloned that amount of times.
   * </p>
   * <p>
   *     An arena may not be cloned (this method returns <code>false</code>) for the following reasons:<br>
   *     - {@link #isActive()} returns false<br>
   *     - Arena itself is a cloned arena ({@link Arena#isCloned()} returns true)<br>
   *     - Arena is not a "normal" regeneration type ({@link RegenerationType#isNormal()} returns false)<br>
   *     - The status of the arena is {@link ArenaStatus#STOPPED}<br>
   *     - The arena depends on SWM while SWM is not installed<br>
   *     - Arena does not exist<br>
   *     - There are limits preventing it (<code>ignoreLimits</code> is set to false)<br>
   *     - It is a part of the 'auto-cloning-excluded-arenas' config
   * </p>
   * <p>
   *     The pure existence of an arena within this Collection is not a promise for the arena to be actually cloned.
   *     Certain states may have changed with the start of its theoretical cloning process.
   * </p>
   *
   * @param arena The arena that shall be cloned
   * @param ignoreLimits Whether certain limitations shall be ignored
   * @param callback An optional callback that returns the result of the cloning. In case it succeded, the callback will include the cloned arena. It is always being called on the main thread
   * @return <code>true</code> when an arena has been added to the queue. This does not tell whether it actually succeded the cloning! Use the callback for that
   * @see #getQueue()
   * @see #addToQueue(Arena, boolean)
   * @see #removeFromQueue(Arena)
   */
  default boolean addToQueue(Arena arena, boolean ignoreLimits, @Nullable Consumer<Optional<Arena>> callback) {
    return addToQueue(new QueuedCloningArena(arena, null, ignoreLimits, callback));
  }

  /**
   * Adds an arena to the queue.
   * <p>
   *     There are certain limits within the configurations that limit the amount of arenas that may be cloned.
   *     You may overcome that limit using the <code>ignoreLimits</code> parameter.
   * </p>
   * <p>
   *     This method fully supports async operations.<br>
   *     It is also okay for an arena to be added multiple times. It will be cloned that amount of times.
   * </p>
   * <p>
   *     An arena may not be cloned (this method returns <code>false</code>) for the following reasons:<br>
   *     - {@link #isActive()} returns false<br>
   *     - Arena itself is a cloned arena ({@link Arena#isCloned()} returns true)<br>
   *     - Arena is not a "normal" regeneration type ({@link RegenerationType#isNormal()} returns false)<br>
   *     - The status of the arena is {@link ArenaStatus#STOPPED}<br>
   *     - The arena depends on SWM while SWM is not installed<br>
   *     - Arena does not exist<br>
   *     - There are limits preventing it (<code>ignoreLimits</code> is set to false)<br>
   *     - It is a part of the 'auto-cloning-excluded-arenas' config
   * </p>
   * <p>
   *     The pure existence of an arena within this Collection is not a promise for the arena to be actually cloned.
   *     Certain states may have changed with the start of its theoretical cloning process.
   * </p>
   *
   * @param arena The arena that shall be cloned
   * @param ignoreLimits Whether certain limitations shall be ignored
   * @return <code>true</code> when an arena has been added to the queue. This does not tell whether it actually succeded the cloning!
   * @see #getQueue()
   * @see #addToQueue(Arena, boolean)
   * @see #removeFromQueue(Arena)
   */
  default boolean addToQueue(Arena arena, boolean ignoreLimits) {
    return addToQueue(arena, ignoreLimits, null);
  }

  /**
   * Adds an arena to the queue.
   * <p>
   *     There are certain limits within the configurations that limit the amount of arenas that may be cloned.
   * </p>
   * <p>
   *     This method fully supports async operations.<br>
   *     It is also okay for an arena to be added multiple times. It will be cloned that amount of times.
   * </p>
   * <p>
   *     An arena may not be cloned (this method returns <code>false</code>) for the following reasons:<br>
   *     - {@link #isActive()} returns false<br>
   *     - Arena itself is a cloned arena ({@link Arena#isCloned()} returns true)<br>
   *     - Arena is not a "normal" regeneration type ({@link RegenerationType#isNormal()} returns false)<br>
   *     - The status of the arena is {@link ArenaStatus#STOPPED}<br>
   *     - The arena depends on SWM while SWM is not installed<br>
   *     - Arena does not exist<br>
   *     - There are limits preventing it<br>
   *     - It is a part of the 'auto-cloning-excluded-arenas' config
   * </p>
   * <p>
   *     The pure existence of an arena within this Collection is not a promise for the arena to be actually cloned.
   *     Certain states may have changed with the start of its theoretical cloning process.
   * </p>
   *
   * @param arena The arena that shall be cloned
   * @return <code>true</code> when an arena has been added to the queue. This does not tell whether it actually succeded the cloning!
   * @see #getQueue()
   * @see #addToQueue(Arena)
   * @see #removeFromQueue(Arena)
   */
  default boolean addToQueue(Arena arena) {
    return addToQueue(arena, false, null);
  }

  /**
   * Removes a single instance of an arena from the queue.
   * <p>
   *     This method fully supports async operations.
   * </p>
   *
   * @param arena The arena that shall not be cloned (or once less)
   * @return <code>true</code> in case at least one instance has been found and one has been removed
   * @see #getQueue()
   * @see #addToQueue(Arena)
   * @see #addToQueue(Arena, boolean)
   */
  boolean removeFromQueue(Arena arena);
}
