package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 黑曜石笼子事件
 * 被关到黑曜石笼子中
 */
public class ObsidianCageEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private FileConfiguration config;
    private final List<Location> placedBlocks = new ArrayList<>();

    public ObsidianCageEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(badDir, "obsidian_cage.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/obsidian_cage.yml", false);
                plugin.getLogger().info("已生成黑曜石笼子事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载黑曜石笼子事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载黑曜石笼子事件配置失败: " + e.getMessage());
        }
    }

    @Override
    public EventType getType() {
        return EventType.BAD;
    }

    @Override
    public String getName() {
        return "OBSIDIAN_CAGE";
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 20) : 20;
    }

    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }

    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }

        // 获取配置参数
        String cageMaterial = config != null ? config.getString("obsidian_cage.cage_material", "OBSIDIAN") : "OBSIDIAN";
        int cageSize = config != null ? config.getInt("obsidian_cage.cage_size", 3) : 3;
        int cageHeight = config != null ? config.getInt("obsidian_cage.cage_height", 3) : 3;
        boolean hasRoof = config != null ? config.getBoolean("obsidian_cage.has_roof", true) : true;
        boolean hasFloor = config != null ? config.getBoolean("obsidian_cage.has_floor", true) : true;
        int lifetime = config != null ? config.getInt("obsidian_cage.lifetime", 60) : 60;

        // 获取竞技场
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        if (arena == null) {
            plugin.getLogger().warning("玩家不在竞技场中，无法执行黑曜石笼子事件");
            return;
        }

        // 建造笼子
        buildCage(player, arena, cageMaterial, cageSize, cageHeight, hasRoof, hasFloor);

        // 应用额外效果
        applyAdditionalEffects(player);

        // 设置清理时间
        if (lifetime > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    cleanupBlocks();
                }
            }.runTaskLater(plugin, lifetime * 20L);
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 触发了黑曜石笼子事件");
    }

    /**
     * 建造笼子
     */
    private void buildCage(Player player, Arena arena, String materialName, int size, int height,
            boolean hasRoof, boolean hasFloor) {
        Location playerLoc = player.getLocation();
        Material material;

        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的笼子材料: " + materialName);
            material = Material.OBSIDIAN;
        }

        int halfSize = size / 2;

        // 建造笼子墙壁
        for (int y = 0; y < height; y++) {
            for (int x = -halfSize; x <= halfSize; x++) {
                for (int z = -halfSize; z <= halfSize; z++) {
                    // 只在边缘放置方块
                    if (x == -halfSize || x == halfSize || z == -halfSize || z == halfSize) {
                        Location blockLoc = playerLoc.clone().add(x, y, z);
                        placeBlock(blockLoc, material, arena);
                    }
                }
            }
        }

        // 建造顶部
        if (hasRoof) {
            for (int x = -halfSize; x <= halfSize; x++) {
                for (int z = -halfSize; z <= halfSize; z++) {
                    Location blockLoc = playerLoc.clone().add(x, height, z);
                    placeBlock(blockLoc, material, arena);
                }
            }
        }

        // 建造底部
        if (hasFloor) {
            for (int x = -halfSize; x <= halfSize; x++) {
                for (int z = -halfSize; z <= halfSize; z++) {
                    Location blockLoc = playerLoc.clone().add(x, -1, z);
                    placeBlock(blockLoc, material, arena);
                }
            }
        }
    }

    /**
     * 放置方块
     */
    private void placeBlock(Location location, Material material, Arena arena) {
        Block block = location.getBlock();

        // 只在空气方块中放置
        if (block.getType().isAir()) {
            block.setType(material);

            // 检查是否支持玩家方块标记功能
            if (GameAPI.get().isPlayerBlockMarkingSupported()) {
                // 标记方块为玩家放置的，这样玩家就可以破坏它
                arena.setBlockPlayerPlaced(block, true);
                plugin.getLogger().info("已标记黑曜石笼子方块为玩家放置: " + location);
            } else {
                plugin.getLogger().warning("服务器未启用玩家方块标记功能，黑曜石笼子方块可能无法正常破坏");
            }

            // 记录方块位置
            placedBlocks.add(location);
        }
    }

    /**
     * 应用额外效果
     */
    private void applyAdditionalEffects(Player player) {
        if (config == null)
            return;

        // 失明效果
        if (config.getBoolean("obsidian_cage.additional_effects.blindness.enabled", true)) {
            int level = config.getInt("obsidian_cage.additional_effects.blindness.level", 1);
            int duration = config.getInt("obsidian_cage.additional_effects.blindness.duration", 200);

            PotionEffect blindness = new PotionEffect(PotionEffectType.BLINDNESS, duration, level - 1);
            player.addPotionEffect(blindness);
        }

        // 缓慢效果
        if (config.getBoolean("obsidian_cage.additional_effects.slowness.enabled", true)) {
            int level = config.getInt("obsidian_cage.additional_effects.slowness.level", 2);
            int duration = config.getInt("obsidian_cage.additional_effects.slowness.duration", 400);

            PotionEffect slowness = new PotionEffect(PotionEffectType.SLOW, duration, level - 1);
            player.addPotionEffect(slowness);
        }

        // 虚弱效果
        if (config.getBoolean("obsidian_cage.additional_effects.weakness.enabled", true)) {
            int level = config.getInt("obsidian_cage.additional_effects.weakness.level", 2);
            int duration = config.getInt("obsidian_cage.additional_effects.weakness.duration", 400);

            PotionEffect weakness = new PotionEffect(PotionEffectType.WEAKNESS, duration, level - 1);
            player.addPotionEffect(weakness);
        }

        // 挖掘疲劳效果
        if (config.getBoolean("obsidian_cage.additional_effects.mining_fatigue.enabled", true)) {
            int level = config.getInt("obsidian_cage.additional_effects.mining_fatigue.level", 3);
            int duration = config.getInt("obsidian_cage.additional_effects.mining_fatigue.duration", 600);

            PotionEffect miningFatigue = new PotionEffect(PotionEffectType.SLOW_DIGGING, duration, level - 1);
            player.addPotionEffect(miningFatigue);
        }
    }

    /**
     * 清理方块
     */
    private void cleanupBlocks() {
        for (Location blockLoc : placedBlocks) {
            Block block = blockLoc.getBlock();
            if (block.getType() != Material.AIR) {
                block.setType(Material.AIR);
            }
        }
        placedBlocks.clear();
    }

    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null)
            return true;
        return config.getBoolean("messages.send_message", true);
    }

    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null)
            return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }

    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null)
            return "§c你被困在了坚固的黑曜石笼子里！";
        return config.getString("messages.event_message", "§c你被困在了坚固的黑曜石笼子里！");
    }
}
