package de.marcely.bedwars.tools;

import de.marcely.bedwars.api.remote.RemotePlayer;
import de.marcely.bedwars.api.remote.RemoteServer;
import lombok.AllArgsConstructor;
import lombok.ToString;
import net.md_5.bungee.api.chat.BaseComponent;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.command.CommandSender;
import org.bukkit.command.ConsoleCommandSender;
import org.bukkit.entity.Player;
import org.bukkit.permissions.Permission;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;

/**
 * This class is an alternative to Bukkit's {@link CommandSender} that extends to functionalities, such as forcing all permissions and allows the use of remote players {@link RemotePlayer}.
 * It does this by wrapping those instances.
 *
 * <p>
 *     This is for instance used in commands to permit the remote execution.
 * </p>
 */
public interface CommandSenderWrapper {

  /**
   * Remotes the name of the sender.
   * <p>
   *     For Bukkit Players: {@link CommandSender#getName()}<br>
   *     For Remote Players: {@link RemotePlayer#getName()}<br>
   *     For Console: Console
   * </p>
   *
   * @return The name of the sender
   */
  String getName();

  /**
   * Remotes the uuid of the sender. May be <code>null</code> if it's not supported for the type.
   * <p>
   *     For Bukkit Players: {@link Player#getUniqueId()}<br>
   *     For Remote Players: {@link RemotePlayer#getUniqueId()}
   * </p>
   *
   * @return The uuid of the reference, might be <code>null</code>
   */
  @Nullable UUID getUniqueId();

  /**
   * Returns whether an online player executed this.
   * <p>
   *     If this method returns <code>true</code>, you may use {@link #getBukkitPlayer()}.
   * </p>
   *
   * @return Returns <code>true</code> when an actual {@link Player} is being referenced.
   */
  boolean isBukkitPlayer();

  /**
   * Returns whether it's a player on a remote server.
   * <p>
   *     If this method returns <code>true</code>, you may use {@link #getRemotePlayer()}.
   * </p>
   *
   * @return Returns <code>true</code> when an actual {@link RemotePlayer} is being referenced.
   */
  boolean isRemotePlayer();

  /**
   * Returns whether it was the console that executed the command.
   *
   * @return Returns <code>true</code> when a console is the sender (either remotely and local)
   */
  boolean isConsole();

  /**
   * Returns whether it was a player who executed the command.
   *
   * @return Returns <code>true</code> when a player is the sender (either remotely and local)
   */
  boolean isPlayer();

  /**
   * Returns whether this object is wrapping a sender this is located on this server.
   *
   * @return Returns <code>true</code> when the sender is located on this server
   */
  boolean isLocal();

  /**
   * Returns whether this object is wrapping a sender this is located on another server.
   *
   * @return Returns <code>true</code> when the sender is located on another server
   */
  boolean isRemote();

  /**
   * Returns the object that's being referenced. This may be a {@link CommandSender} or a {@link RemotePlayer}.
   *
   * @return The instance that's being wrapped
   */
  Object getReference();

  /**
   * Returns the {@link CommandSender} that is being wrapped.
   * <p>
   *     Returns <code>null</code> when it is not being wrapped.
   * </p>
   *
   * @return The {@link CommandSender} that is being wrapped. May be <code>null</code>
   */
  CommandSender getCommandSender();

  /**
   * Returns the {@link Player} that is being wrapped.
   * <p>
   *     Returns <code>null</code> when the player is not on this server or when it's not a player.
   * </p>
   *
   * @return The {@link Player} that is being wrapped. May be <code>null</code>
   */
  @Nullable
  Player getBukkitPlayer();

  /**
   * Returns the {@link RemotePlayer} that is being wrapped.
   * <p>
   *     Returns <code>null</code> when the player is not on a remote server.
   * </p>
   *
   * @return The {@link RemotePlayer} that is being wrapped. May be <code>null</code>
   */
  @Nullable
  RemotePlayer getRemotePlayer();

  /**
   * Sends a message to the sender.
   * <p>
   *     For Bukkit Players: {@link CommandSender#sendMessage(String)} ()}<br>
   *     For Remote Players: {@link RemotePlayer#sendMessage(String...)} ()}
   * </p>
   *
   * @param message The message that shall be sent
   */
  void sendMessage(String message);

  /**
   * Sends multiple messages to the sender.
   * <p>
   *     For Bukkit Players: {@link CommandSender#sendMessage(String[])} ()}<br>
   *     For Remote Players: {@link RemotePlayer#sendMessage(String...)} ()}
   * </p>
   *
   * @param messages The messages that shall be sent
   */
  void sendMessage(String... messages);

  /**
   * Sends a single message that persists of the following components.
   * <p>
   *     For Bukkit Players: {@link Player.Spigot#sendMessage(BaseComponent...)} ()}<br>
   *     For Bukkit Misc (only text): {@link CommandSender#sendMessage(String)} ()}<br>
   *     For Remote Players: {@link RemotePlayer#sendMessage(BaseComponent...)} ()}
   * </p>
   *
   * @param components The components the chat message persists of
   */
  void sendMessage(BaseComponent... components);

  /**
   * Returns whether the sender has a given permission.
   * <p>
   *     For Bukkit Players: {@link CommandSender#hasPermission(String)} ()} (<b>Or when <code>hasOP</code> is true: Always true</b>)<br>
   *     For Remote Players: <b>Always true</b>
   * </p>
   *
   * @param name The permission that shall be checked
   */
  boolean hasPermission(String name);

  /**
   * Returns whether the sender has a given permission.
   * <p>
   *     For Bukkit Players: {@link CommandSender#hasPermission(Permission)} ()} (<b>Or when <code>hasOP</code> is true: Always true</b>)<br>
   *     For Remote Players: <b>Always true</b>
   * </p>
   *
   * @param perm The permission that shall be checked
   */
  boolean hasPermission(Permission perm);


  /**
   * Constructs a new instance with a reference of the given sender.
   *
   * @param sender The sender that shall be wrapped
   * @return The wrapped sender
   */
  static CommandSenderWrapper wrap(CommandSender sender) {
    return wrap(sender, false);
  }

  /**
   * Constructs a new instance with a reference of the given sender.
   *
   * @param sender The sender that shall be wrapped
   * @param hasOP <code>true</code>: {@link #hasPermission(String)} always return true; <code>false</code>: uses {@link CommandSender#hasPermission(String)}
   * @return The wrapped sender
   */
  static CommandSenderWrapper wrap(CommandSender sender, boolean hasOP) {
    Validate.notNull(sender, "sender");

    return new BukkitWrapper(sender, hasOP);
  }

  /**
   * Constructs a new instance with a reference of the given player.
   *
   * @param player The player that shall be wrapped
   * @return The wrapped player
   */
  static CommandSenderWrapper wrap(RemotePlayer player) {
    Validate.notNull(player, "player");

    if (player.isLocal() && player.asBukkit().isOnline())
      return wrap(player.asBukkit());

    return new RemotePlayerWrapper(player);
  }

  /**
   * Wraps the console of the given server.
   *
   * @param server The server of which the console shall be wrapped
   * @return The wrapped console
   */
  static CommandSenderWrapper wrapConsole(RemoteServer server) {
    Validate.notNull(server, "server");

    return new RemoteConsoleWrapper(server);
  }


  /**
   * @hidden
   */
  @ToString
  @AllArgsConstructor
  class BukkitWrapper implements CommandSenderWrapper {

    private final CommandSender sender;
    private final boolean hasOP;

    @Override
    public String getName() {
      return this.sender.getName();
    }

    @Override
    public UUID getUniqueId() {
      if (isBukkitPlayer())
        return getBukkitPlayer().getUniqueId();
      else if (isRemotePlayer())
        return getRemotePlayer().getUniqueId();
      else
        return null;
    }

    @Override
    public boolean isBukkitPlayer() {
      return this.sender instanceof Player;
    }

    @Override
    public boolean isRemotePlayer() {
      return false;
    }

    @Override
    public boolean isConsole() {
      return this.sender instanceof ConsoleCommandSender;
    }

    @Override
    public boolean isPlayer() {
      return this.sender instanceof Player;
    }

    @Override
    public boolean isLocal() {
      return true;
    }

    @Override
    public boolean isRemote() {
      return false;
    }

    @Override
    public Object getReference() {
      return this.sender;
    }

    @Override
    public CommandSender getCommandSender() {
      return this.sender;
    }

    @Override
    public @Nullable Player getBukkitPlayer() {
      return isBukkitPlayer() ? (Player) this.sender : null;
    }

    @Override
    public @Nullable RemotePlayer getRemotePlayer() {
      return null;
    }

    @Override
    public void sendMessage(String message) {
      this.sender.sendMessage(message);
    }

    @Override
    public void sendMessage(String... messages) {
      this.sender.sendMessage(messages);
    }

    @Override
    public void sendMessage(BaseComponent... components) {
      Validate.notNull(components, "components");

      if (components.length == 0)
        return;

      if (isBukkitPlayer())
        getBukkitPlayer().spigot().sendMessage(components);
      else
        this.sender.sendMessage(TextComponent.toLegacyText(components));
    }

    @Override
    public boolean hasPermission(String name) {
      return this.hasOP || this.sender.hasPermission(name);
    }

    @Override
    public boolean hasPermission(Permission perm) {
      return this.hasOP || this.sender.hasPermission(perm);
    }

    @Override
    public boolean equals(Object obj) {
      if (obj == null)
        return false;
      if (obj instanceof BukkitWrapper)
        return this == obj || this.sender.equals(((BukkitWrapper) obj).sender);
      if (obj instanceof CommandSender)
        return this.sender.equals(obj);

      return false;
    }
  }

  /**
   * @hidden
   */
  @ToString
  @AllArgsConstructor
  class RemotePlayerWrapper implements CommandSenderWrapper {

    private final RemotePlayer player;

    @Override
    public String getName() {
      return this.player.getName();
    }

    @Override
    public @Nullable UUID getUniqueId() {
      return this.player.getUniqueId();
    }

    @Override
    public boolean isBukkitPlayer() {
      return false;
    }

    @Override
    public boolean isRemotePlayer() {
      return true;
    }

    @Override
    public boolean isConsole() {
      return false;
    }

    @Override
    public boolean isPlayer() {
      return true;
    }

    @Override
    public boolean isLocal() {
      return false;
    }

    @Override
    public boolean isRemote() {
      return true;
    }

    @Override
    public Object getReference() {
      return this.player;
    }

    @Override
    public CommandSender getCommandSender() {
      return null;
    }

    @Override
    public @Nullable Player getBukkitPlayer() {
      return null;
    }

    @Override
    public @Nullable RemotePlayer getRemotePlayer() {
      return this.player;
    }

    @Override
    public void sendMessage(String message) {
      this.player.sendMessage(message);
    }

    @Override
    public void sendMessage(String... messages) {
      this.player.sendMessage(messages);
    }

    @Override
    public void sendMessage(BaseComponent... components) {
      this.player.sendMessage(components);
    }

    @Override
    public boolean hasPermission(String name) {
      return true;
    }

    @Override
    public boolean hasPermission(Permission perm) {
      return true;
    }

    @Override
    public boolean equals(Object obj) {
      if (obj == null)
        return false;
      if (obj instanceof RemotePlayerWrapper)
        return this == obj || ((RemotePlayerWrapper) obj).player.equals(this.player);
      if (obj instanceof RemotePlayer)
        return this.player.equals(obj);

      return false;
    }
  }

  /**
   * @hidden
   */
  @ToString
  @AllArgsConstructor
  class RemoteConsoleWrapper implements CommandSenderWrapper {

    private final RemoteServer server;

    @Override
    public String getName() {
      return "Console";
    }

    @Override
    public @Nullable UUID getUniqueId() {
      return null;
    }

    @Override
    public boolean isBukkitPlayer() {
      return false;
    }

    @Override
    public boolean isRemotePlayer() {
      return false;
    }

    @Override
    public boolean isConsole() {
      return true;
    }

    @Override
    public boolean isPlayer() {
      return false;
    }

    @Override
    public boolean isLocal() {
      return false;
    }

    @Override
    public boolean isRemote() {
      return true;
    }

    @Override
    public Object getReference() {
      return null;
    }

    @Override
    public CommandSender getCommandSender() {
      return null;
    }

    @Override
    public @Nullable Player getBukkitPlayer() {
      return null;
    }

    @Override
    public @Nullable RemotePlayer getRemotePlayer() {
      return null;
    }

    @Override
    public void sendMessage(String message) {
      this.server.sendConsoleMessage(message);
    }

    @Override
    public void sendMessage(String... messages) {
      this.server.sendConsoleMessage(messages);
    }

    @Override
    public void sendMessage(BaseComponent... components) {
      this.server.sendConsoleMessage(TextComponent.toLegacyText(components));
    }

    @Override
    public boolean hasPermission(String name) {
      return true;
    }

    @Override
    public boolean hasPermission(Permission perm) {
      return true;
    }
  }
}