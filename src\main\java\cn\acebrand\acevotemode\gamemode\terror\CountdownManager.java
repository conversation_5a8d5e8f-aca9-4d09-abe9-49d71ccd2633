package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.message.Message;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

/**
 * Boss生成倒计时管理器
 * 支持自定义倒计时时间点和消息
 */
public class CountdownManager {

    private final AceVoteMode plugin;
    private final Arena arena;
    private final ConfigurationSection config;

    private BukkitTask countdownTask;
    private int remainingSeconds;
    private final Set<Integer> announcedTimes = new HashSet<>();

    public CountdownManager(AceVoteMode plugin, Arena arena, ConfigurationSection config) {
        this.plugin = plugin;
        this.arena = arena;
        this.config = config;
    }

    /**
     * 开始倒计时
     */
    public void startCountdown(int totalSeconds) {
        // 停止现有倒计时
        stopCountdown();

        // 检查是否启用倒计时
        if (!config.getBoolean("messages.countdown.enabled", true)) {
            return;
        }

        this.remainingSeconds = totalSeconds;
        this.announcedTimes.clear();

        // 获取需要公告的时间点
        final List<Integer> announceTimes;
        List<Integer> configTimes = config.getIntegerList("messages.countdown.announce-times");
        if (configTimes.isEmpty()) {
            // 默认公告时间点
            announceTimes = Arrays.asList(300, 180, 120, 60, 30, 10, 5, 3, 1);
        } else {
            announceTimes = configTimes;
        }

        // 启动倒计时任务
        countdownTask = new BukkitRunnable() {
            @Override
            public void run() {
                // 检查是否需要公告
                if (announceTimes.contains(remainingSeconds) && !announcedTimes.contains(remainingSeconds)) {
                    announceCountdown(remainingSeconds);
                    announcedTimes.add(remainingSeconds);
                }

                // 倒计时结束
                if (remainingSeconds <= 0) {
                    onCountdownFinished();
                    cancel();
                    return;
                }

                remainingSeconds--;
            }
        }.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次

        plugin.getLogger().info("倒计时已开始：" + totalSeconds + "秒，竞技场：" + arena.getName());
    }

    /**
     * 停止倒计时
     */
    public void stopCountdown() {
        if (countdownTask != null && !countdownTask.isCancelled()) {
            countdownTask.cancel();
            countdownTask = null;
        }
        announcedTimes.clear();
    }

    /**
     * 公告倒计时
     */
    private void announceCountdown(int seconds) {
        String message = formatCountdownMessage(seconds);
        String sound = getCountdownSound(seconds);

        // 发送消息给竞技场内的玩家
        arena.broadcast(Message.build(message));

        // 播放音效
        if (sound != null) {
            for (Player player : arena.getPlayers()) {
                try {
                    Sound soundEnum = Sound.valueOf(sound);
                    player.playSound(player.getLocation(), soundEnum, 1.0f, 1.0f);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("无效的音效名称: " + sound);
                }
            }
        }

        // 发送Title（最后10秒）
        if (seconds <= 10) {
            sendCountdownTitle(seconds);
        }

        plugin.getLogger().info("倒计时公告：" + seconds + "秒，竞技场：" + arena.getName());
    }

    /**
     * 格式化倒计时消息
     */
    private String formatCountdownMessage(int seconds) {
        String template;
        if (seconds >= 60) {
            // 分钟级别
            int minutes = seconds / 60;
            template = config.getString("messages.countdown.minutes", "&6&l【倒计时】&f Boss将在 &c{minutes}分钟 &f后降临！");
            return template.replace("{minutes}", String.valueOf(minutes));
        } else if (seconds <= 5) {
            // 最后几秒
            template = config.getString("messages.countdown.final", "&4&l【警告】&f Boss即将降临！所有玩家准备战斗！");
            return template.replace("{seconds}", String.valueOf(seconds));
        } else {
            // 秒级别
            template = config.getString("messages.countdown.seconds", "&6&l【倒计时】&f Boss将在 &c{seconds}秒 &f后降临！");
            return template.replace("{seconds}", String.valueOf(seconds));
        }
    }

    /**
     * 获取倒计时音效
     */
    private String getCountdownSound(int seconds) {
        if (seconds <= 5) {
            return config.getString("messages.countdown.sounds.final", "ENTITY_ENDER_DRAGON_GROWL");
        } else {
            return config.getString("messages.countdown.sounds.default", "BLOCK_NOTE_BLOCK_PLING");
        }
    }

    /**
     * 发送倒计时Title
     */
    private void sendCountdownTitle(int seconds) {
        String title = "&c&l" + seconds;
        String subtitle = "&f秒后Boss降临！";

        for (Player player : arena.getPlayers()) {
            player.sendTitle(title, subtitle, 10, 20, 10);
        }
    }

    /**
     * 倒计时结束处理
     */
    private void onCountdownFinished() {
        // 发送最终消息
        String finalMessage = config.getString("messages.countdown.messages.final",
                "&4&l【警告】&f Boss即将降临！所有玩家准备战斗！");
        arena.broadcast(Message.build(finalMessage));

        // 播放最终音效
        for (Player player : arena.getPlayers()) {
            player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.8f);
            player.sendTitle("&4&l⚡ 恐怖降临 ⚡", "&c恐怖领主已经苏醒！", 10, 40, 10);
        }

        plugin.getLogger().info("倒计时结束，竞技场：" + arena.getName());
    }

    /**
     * 获取剩余时间
     */
    public int getRemainingSeconds() {
        return remainingSeconds;
    }

    /**
     * 是否正在倒计时
     */
    public boolean isCountingDown() {
        return countdownTask != null && !countdownTask.isCancelled();
    }

    /**
     * 获取倒计时进度百分比
     */
    public double getProgress(int totalSeconds) {
        if (totalSeconds <= 0)
            return 1.0;
        return (double) (totalSeconds - remainingSeconds) / totalSeconds;
    }

    /**
     * 添加自定义倒计时时间点
     */
    public void addAnnouncementTime(int seconds) {
        List<Integer> currentTimes = config.getIntegerList("messages.countdown.announce-times");
        if (!currentTimes.contains(seconds)) {
            currentTimes.add(seconds);
            config.set("messages.countdown.announce-times", currentTimes);
        }
    }

    /**
     * 移除倒计时时间点
     */
    public void removeAnnouncementTime(int seconds) {
        List<Integer> currentTimes = config.getIntegerList("messages.countdown.announce-times");
        currentTimes.remove(Integer.valueOf(seconds));
        config.set("messages.countdown.announce-times", currentTimes);
    }
}
