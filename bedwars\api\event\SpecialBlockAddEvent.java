package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.world.block.SpecialBlock;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when a SpecialBlock is being added.
 * <p>
 * The way of how it got added is not relevant. Meaning it's also getting called when they get loaded, a player adds it, a plugin adds it or whatever
 */
public class SpecialBlockAddEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final SpecialBlock block;

  private boolean cancel = false;

  public SpecialBlockAddEvent(SpecialBlock block) {
    this.block = block;
  }

  /**
   * Returns the block that has been added
   *
   * @return The block that has been added
   */
  public SpecialBlock getBlock() {
    return this.block;
  }

  @Override
  public void setCancelled(boolean bool) {
    this.cancel = bool;
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
