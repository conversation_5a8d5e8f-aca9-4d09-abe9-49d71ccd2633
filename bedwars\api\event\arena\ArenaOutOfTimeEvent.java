package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import lombok.Getter;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when the ingame timer is about to reach zero
 */
public class ArenaOutOfTimeEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;

  private int newTime = 0;

  public ArenaOutOfTimeEvent(Arena arena) {
    this.arena = arena;
  }

  /**
   * Let the game continue running by changing the time.
   * <p>
   * Changing it to 0 or less will stop the arena.
   *
   * @param time The new time
   */
  public void setNewTime(int time) {
    this.newTime = time;
  }

  /**
   * Arena time will be set to this time after this event.
   * <p>
   * This can prevent the game from being stopped.
   *
   * @return The new time. 0 by default
   */
  public int getNewTime() {
    return this.newTime;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
