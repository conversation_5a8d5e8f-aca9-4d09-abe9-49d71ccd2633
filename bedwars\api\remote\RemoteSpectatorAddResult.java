package de.marcely.bedwars.api.remote;

import de.marcely.bedwars.tools.Validate;

import java.util.UUID;

/**
 * Represents the result of a player that tried to get added as a spectator.
 */
public final class RemoteSpectatorAddResult {

  private final UUID playerUUID;
  private final Result result;

  public RemoteSpectatorAddResult(UUID playerUUID, Result result) {
    Validate.notNull(playerUUID, "playerUUID");
    Validate.notNull(result, "result");

    this.playerUUID = playerUUID;
    this.result = result;
  }

  /**
   * The unique id of the player.
   *
   * @return The uuid of the player
   */
  public UUID getPlayerUniqueId() {
    return this.playerUUID;
  }

  /**
   * Gets whether it was successful
   *
   * @return <code>true</code> if it was successful
   */
  public boolean wasSuccessful() {
    return this.result == Result.SUCCESS;
  }

  /**
   * Gets what exactly happend
   *
   * @return The exact result
   */
  public Result getResult() {
    return this.result;
  }

  /**
   * Represents the exact result of what happened to the player that tried to spectate.
   */
  public enum Result {

    /**
     * It all went fine.
     */
    SUCCESS,

    /**
     * The player went offline.
     */
    PLAYER_OFFLINE,

    /**
     * The player is busy with something else, such as getting added as a player.
     */
    ALREADY_MOVING,

    /**
     * The server is full.
     */
    SERVER_FULL,

    /**
     * The arena has been removed during the process.
     */
    ARENA_NOT_EXISTING_ANYMORE,

    /**
     * The arena is not running anymore.
     */
    ARENA_NOT_RUNNING,

    /**
     * A plugin cancelled it using the API.
     */
    PLUGIN_EVENT_CANCELLED,

    /**
     * We tried to contact the server, but we have never received any info.
     */
    TIMEOUT,

    /**
     * We don't know, really.
     */
    UNKNOWN
  }
}