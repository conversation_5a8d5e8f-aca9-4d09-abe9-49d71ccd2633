package de.marcely.bedwars.api.arena.picker;

import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.arena.RegenerationType;
import de.marcely.bedwars.api.remote.RemoteAPI;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.api.remote.RemoteServer;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import org.jetbrains.annotations.Nullable;

/**
 * The type of a selector. Diffferentiate between default and custom ones.
 */
public enum ArenaSelectorType {

  /**
   * Chooses a completely random arena
   */
  RANDOM((arenas, cachedArena) -> {
    if (arenas.isEmpty())
      return null;

    return arenas.get(ThreadLocalRandom.current().nextInt(arenas.size()));
  }),

  /**
   * 1. Filters out all full arenas<br>
   * 2. Searches for the one with the most players<br>
   * 3. Decides for a random one case there are multiple with the equal amount of players
   */
  BEST((arenas, cachedArena) -> {
    if (arenas.isEmpty())
      return null;
    if (arenas.size() == 1)
      return arenas.get(0);

    final RemoteArena lastFallback = arenas.get(0); // in case we end up with no arenas (all full)

    arenas.removeIf(arena -> arena.getPlayersCount() >= arena.getMaxPlayers());

    final int greatestAmount = arenas.stream()
        .map(arena -> arena.getPlayersCount())
        .mapToInt(v -> v)
        .max().orElse(-1);

    // find possible solutions
    arenas.removeIf(arena -> arena.getPlayersCount() != greatestAmount);

    if (arenas.isEmpty())
      return lastFallback;
    if (arenas.size() == 1)
      return arenas.get(0);

    // server auto balancing
    if (RemoteAPI.get().getServers().size() >= 2) {
      final RemoteServer lowestServer = arenas.stream()
          .map(RemoteArena::getRemoteServer)
          .min(Comparator.comparingInt(RemoteServer::getPlayersCount))
          .orElse(null);

      if (lowestServer != null)
        arenas.removeIf(arena -> arena.getRemoteServer() != lowestServer);
    }

    if (cachedArena != null && arenas.contains(cachedArena))
      return cachedArena;
    else
      return arenas.get(ThreadLocalRandom.current().nextInt(arenas.size()));
  }),

  /**
   * Firstly tries to look for a voting arena, then alternatively executes {@link #BEST}
   */
  BEST_PREFER_NON_VOTING((arenas, cachedArena) -> {
    if (arenas.isEmpty())
      return null;

    // look for non-voting arenas
    final List<RemoteArena> nonVoting = arenas.stream()
        .filter(a -> a.getRegenerationType() != RegenerationType.VOTING)
        .collect(Collectors.toCollection(ArrayList::new));

    if (!nonVoting.isEmpty())
      return BEST.instance.run(nonVoting, cachedArena);

    // look for voting
    final List<RemoteArena> voting = arenas.stream()
        .filter(a -> a.getRegenerationType() == RegenerationType.VOTING)
        .collect(Collectors.toCollection(ArrayList::new));

    return BEST.instance.run(voting, cachedArena);
  }),

  /**
   * Firstly tries to look for a running arena, then alternatively executes {@link #BEST}
   */
  BEST_PREFER_RUNNING((arenas, cachedArena) -> {
    if (arenas.isEmpty())
      return null;

    // look for running arenas
    final List<RemoteArena> nonVoting = arenas.stream()
        .filter(a -> a.getStatus() == ArenaStatus.RUNNING)
        .collect(Collectors.toCollection(ArrayList::new));

    if (!nonVoting.isEmpty())
      return BEST.instance.run(nonVoting, cachedArena);

    // look for non-running
    final List<RemoteArena> voting = arenas.stream()
        .filter(a -> a.getStatus() != ArenaStatus.RUNNING)
        .collect(Collectors.toCollection(ArrayList::new));

    return BEST.instance.run(voting, cachedArena);
  }),

  /**
   * All custom added selectors have this type
   */
  PLUGIN();

  private final BiFunction<List<? extends RemoteArena>, RemoteArena, RemoteArena> executor;
  private ArenaSelector instance;

  ArenaSelectorType(BiFunction<List<? extends RemoteArena>, RemoteArena, RemoteArena> executor) {
    this.executor = executor;
  }

  ArenaSelectorType() {
    this.executor = null;
  }

  /**
   * Returns the effective logic object of the given type.
   * <p>
   *     May also return <code>null</code> in case the plugin has not been initialized yet.
   * </p>
   *
   * @return The logic object. <code>null</code> in case it's {@link #PLUGIN}.
   */
  @Nullable
  public ArenaSelector getInstance() {
    return this.instance;
  }
}