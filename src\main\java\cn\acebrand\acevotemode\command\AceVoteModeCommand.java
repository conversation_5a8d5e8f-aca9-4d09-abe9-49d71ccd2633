package cn.acebrand.acevotemode.command;

import cn.acebrand.acevotemode.AceVoteMode;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 主命令处理器
 */
public class AceVoteModeCommand implements CommandExecutor, TabCompleter {
    
    private final AceVoteMode plugin;
    
    public AceVoteModeCommand(AceVoteMode plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                return handleReload(sender);
                
            case "help":
                sendHelp(sender);
                return true;
                
            case "info":
                return handleInfo(sender);
                
            case "version":
                return handleVersion(sender);

            case "test":
                return handleTest(sender);

            default:
                sender.sendMessage(plugin.getConfigManager().getMessage("help"));
                return true;
        }
    }
    
    /**
     * 处理重载命令
     */
    private boolean handleReload(CommandSender sender) {
        if (!sender.hasPermission("acevotemode.admin")) {
            sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
            return true;
        }
        
        try {
            plugin.reload();
            sender.sendMessage(plugin.getConfigManager().getMessage("config-reloaded"));
        } catch (Exception e) {
            sender.sendMessage("§c重载配置时发生错误: " + e.getMessage());
            plugin.getLogger().severe("Error reloading config: " + e.getMessage());
            e.printStackTrace();
        }
        
        return true;
    }
    
    /**
     * 处理信息命令
     */
    private boolean handleInfo(CommandSender sender) {
        if (!sender.hasPermission("acevotemode.admin")) {
            sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
            return true;
        }
        
        sender.sendMessage("§e=== AceVoteMode 信息 ===");
        sender.sendMessage("§7版本: §f" + plugin.getDescription().getVersion());
        sender.sendMessage("§7作者: §f" + plugin.getDescription().getAuthors());
        sender.sendMessage("§7网站: §f" + plugin.getDescription().getWebsite());
        sender.sendMessage("§7可用游戏模式: §f" + plugin.getConfigManager().getGameModes().size());
        
        // 显示游戏模式列表
        if (!plugin.getConfigManager().getGameModes().isEmpty()) {
            sender.sendMessage("§7模式列表:");
            plugin.getConfigManager().getGameModes().values().forEach(mode -> {
                sender.sendMessage("§8  - §f" + mode.getPlainName() + " §7(" + mode.getId() + ")");
            });
        }
        
        return true;
    }
    
    /**
     * 处理版本命令
     */
    private boolean handleVersion(CommandSender sender) {
        sender.sendMessage("§e" + plugin.getName() + " §7v" + plugin.getDescription().getVersion());
        sender.sendMessage("§7作者: §f" + String.join(", ", plugin.getDescription().getAuthors()));
        return true;
    }

    /**
     * 处理测试命令
     */
    private boolean handleTest(CommandSender sender) {
        if (!sender.hasPermission("acevotemode.admin")) {
            sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
            return true;
        }

        sender.sendMessage("§e=== AceVoteMode 测试信息 ===");
        sender.sendMessage("§7插件状态: §a已启用");
        sender.sendMessage("§7配置管理器: §f" + (plugin.getConfigManager() != null ? "已加载" : "未加载"));
        sender.sendMessage("§7投票管理器: §f" + (plugin.getVoteManager() != null ? "已加载" : "未加载"));
        sender.sendMessage("§7可用游戏模式: §f" + plugin.getConfigManager().getGameModes().size());

        // 检查大厅物品处理器
        try {
            de.marcely.bedwars.api.game.lobby.LobbyItemHandler handler =
                de.marcely.bedwars.api.GameAPI.get().getLobbyItemHandler("acevotemode:vote");
            sender.sendMessage("§7大厅物品处理器: §f" + (handler != null ? "已注册" : "未注册"));
        } catch (Exception e) {
            sender.sendMessage("§7大厅物品处理器: §c检查失败 - " + e.getMessage());
        }

        sender.sendMessage("§e========================");
        return true;
    }
    
    /**
     * 发送帮助信息
     */
    private void sendHelp(CommandSender sender) {
        String helpMessage = plugin.getConfigManager().getMessage("help");
        sender.sendMessage(helpMessage);
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList("reload", "help", "info", "version");
            String input = args[0].toLowerCase();
            
            for (String subCommand : subCommands) {
                if (subCommand.startsWith(input)) {
                    // 检查权限
                    if (subCommand.equals("reload") || subCommand.equals("info")) {
                        if (sender.hasPermission("acevotemode.admin")) {
                            completions.add(subCommand);
                        }
                    } else {
                        completions.add(subCommand);
                    }
                }
            }
        }
        
        return completions;
    }
}
