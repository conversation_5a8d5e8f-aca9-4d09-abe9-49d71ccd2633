package de.marcely.bedwars.api.game.upgrade.layout;

import de.marcely.bedwars.api.unsafe.UpgradeShopLayoutTypeWrapper;
import org.jetbrains.annotations.Nullable;

@SuppressWarnings("deprecation")
public enum UpgradeShopLayoutType {

  NORMAL,
  NORMALV2,
  HYPIXEL,
  HYPIXELV2,

  PLUGIN;

  private final UpgradeShopLayoutTypeWrapper wrapper = null;

  /**
   * Returns the corresponding layout to this type.<br>
   * Might return null if the type is {@link #PLUGIN}
   *
   * @return The layout instance of this type
   */
  public @Nullable UpgradeShopLayout getLayout() {
    return this.wrapper.getLayout();
  }
}
