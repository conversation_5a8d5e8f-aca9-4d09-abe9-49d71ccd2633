package de.marcely.bedwars.api.game.scoreboard;

import de.marcely.bedwars.api.arena.ArenaStatus;
import org.jetbrains.annotations.Nullable;

/**
 * The occasion used by {@link ScoreboardHandler} to determinate what exactly shall be shown
 */
public enum ScoreboardType {

  /**
   * In a round ({@link ArenaStatus#RUNNING})
   */
  INGAME,

  /**
   * Waiting in the lobby ({@link ArenaStatus#LOBBY})
   */
  LOBBY,

  /**
   * Game has ended ({@link ArenaStatus#END_LOBBY})
   */
  END_LOBBY,

  /**
   * Spectating the game
   */
  SPECTATOR;

  /**
   * Looks through all ScoreboardTypes and checks which team fits the best to the given name.
   * <p>
   * Ignores upper and lowercase.
   *
   * @param name The name of the ScoreboardType it should look up for
   * @return The type that matches the name. Returns null if it hasn't found any
   */
  public static @Nullable ScoreboardType getByName(String name) {
    for(ScoreboardType type : ScoreboardType.values())
      if(type.name().equalsIgnoreCase(name))
        return type;

    return null;
  }
}