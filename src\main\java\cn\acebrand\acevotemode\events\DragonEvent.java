package cn.acebrand.acevotemode.events;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.tools.location.XYZ;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 末影龙事件
 * 为每个有玩家的队伍生成末影龙，攻击敌方队伍
 */
public class DragonEvent implements CustomEvent {
    
    private static final Random random = new Random();
    
    @Override
    public void execute(AceVoteMode plugin, Arena arena, EventConfig eventConfig) {
        plugin.getLogger().info("Executing dragon event in arena: " + arena.getName());
        
        // 广播消息
        if (eventConfig.isShowMessage()) {
            broadcastMessage(arena, eventConfig.getMessage());
        }
        
        // 播放音效
        playSound(arena, org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 1.0f);
        
        // 获取竞技场中心位置
        Location centerLocation = getArenaMid(arena);
        if (centerLocation == null) {
            plugin.getLogger().warning("Failed to find center location for dragon spawn in arena: " + arena.getName());
            return;
        }
        
        // 为每个有玩家的队伍生成末影龙
        int dragonsSpawned = 0;
        for (Team team : arena.getEnabledTeams()) {
            if (arena.getPlayersInTeam(team).isEmpty()) {
                continue; // 跳过没有玩家的队伍
            }
            
            // 为这个队伍生成指定数量的末影龙
            for (int i = 0; i < eventConfig.getDragonCount(); i++) {
                EnderDragon dragon = spawnDragon(arena, team, centerLocation);
                if (dragon != null) {
                    dragonsSpawned++;
                    // 启动龙的AI任务
                    startDragonAI(plugin, arena, dragon, team, eventConfig);
                }
            }
        }

        // 如果配置了生成默认龙，则生成不属于任何队伍的龙
        if (eventConfig.isSpawnDefaultDragon()) {
            EnderDragon defaultDragon = spawnDefaultDragon(arena, centerLocation);
            if (defaultDragon != null) {
                dragonsSpawned++;
                // 启动默认龙的AI任务（攻击所有玩家）
                startDragonAI(plugin, arena, defaultDragon, null, eventConfig);
            }
        }

        plugin.getLogger().info("Spawned " + dragonsSpawned + " dragons in arena: " + arena.getName());

        // 如果配置了摧毁生成器，则摧毁所有生成器
        if (eventConfig.isDestroyGenerators()) {
            destroyAllGenerators(arena);
        }
    }
    
    @Override
    public String getEventType() {
        return "dragon";
    }
    
    @Override
    public String getEventName() {
        return "末影龙事件";
    }
    
    /**
     * 生成末影龙
     */
    private EnderDragon spawnDragon(Arena arena, Team team, Location centerLocation) {
        try {
            // 在中心位置上方生成龙
            Location spawnLocation = centerLocation.clone().add(0, 10, 0);

            EnderDragon dragon = (EnderDragon) arena.getGameWorld().spawnEntity(spawnLocation, EntityType.ENDER_DRAGON);

            // 设置龙的属性
            dragon.setCustomName("§c" + team.getDisplayName() + " §c的末影龙");
            dragon.setCustomNameVisible(true);
            dragon.setMaxHealth(200.0); // 设置血量
            dragon.setHealth(200.0);

            return dragon;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 生成默认龙（不属于任何队伍）
     */
    private EnderDragon spawnDefaultDragon(Arena arena, Location centerLocation) {
        try {
            // 在中心位置上方生成龙
            Location spawnLocation = centerLocation.clone().add(0, 15, 0);

            EnderDragon dragon = (EnderDragon) arena.getGameWorld().spawnEntity(spawnLocation, EntityType.ENDER_DRAGON);

            // 设置龙的属性
            dragon.setCustomName("§4§l突然死亡末影龙");
            dragon.setCustomNameVisible(true);
            dragon.setMaxHealth(300.0); // 默认龙血量更高
            dragon.setHealth(300.0);

            return dragon;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 启动龙的AI任务
     */
    private void startDragonAI(AceVoteMode plugin, Arena arena, EnderDragon dragon, Team ownerTeam, EventConfig eventConfig) {
        BukkitTask aiTask = new BukkitRunnable() {
            private final Vector velocity = new Vector(0, 0, 0);
            private Player currentTarget = null;
            private int targetSwitchCooldown = 0;

            @Override
            public void run() {
                // 检查龙是否还存活且游戏还在进行
                if (dragon.isDead() || !dragon.isValid() || arena.getStatus() != de.marcely.bedwars.api.arena.ArenaStatus.RUNNING) {
                    this.cancel();
                    return;
                }

                targetSwitchCooldown--;

                // 每tick更新龙的移动
                updateDragonMovement(arena, dragon, ownerTeam, eventConfig);

                // 每tick摧毁附近方块
                if (eventConfig.getDragonBlockDestroyRadius() > 0) {
                    DragonEvent.this.destroyNearbyBlocks(dragon.getLocation(), eventConfig.getDragonBlockDestroyRadius());
                }
            }

            private void updateDragonMovement(Arena arena, EnderDragon dragon, Team ownerTeam, EventConfig eventConfig) {
                Location dragonLocation = dragon.getLocation();

                // 每3秒或当前目标无效时寻找新目标
                if (targetSwitchCooldown <= 0 || currentTarget == null ||
                    !currentTarget.isOnline() || currentTarget.isDead() ||
                    !arena.getPlayers().contains(currentTarget)) {

                    currentTarget = findNearestEnemyPlayer(arena, dragonLocation, ownerTeam);
                    targetSwitchCooldown = 60; // 3秒冷却
                }

                if (currentTarget != null) {
                    Location targetLocation = currentTarget.getLocation();

                    // 检查是否已经接近目标
                    double distance = dragonLocation.distance(targetLocation);
                    if (distance < 5.0) {
                        // 如果太近，寻找新目标
                        currentTarget = null;
                        targetSwitchCooldown = 0;
                        return;
                    }

                    // 计算朝向目标的方向
                    Vector toTarget = targetLocation.toVector().subtract(dragonLocation.toVector()).normalize();

                    // 使用"重力"效果朝向目标
                    Vector gravity = toTarget.multiply(0.05 * eventConfig.getDragonSpeed());
                    velocity.add(gravity);

                    // 限制最大速度
                    double maxSpeed = eventConfig.getDragonSpeed();
                    if (velocity.length() > maxSpeed) {
                        velocity.normalize().multiply(maxSpeed);
                    }

                    // 计算新位置
                    Location newLocation = dragonLocation.clone().add(velocity);

                    // 设置龙朝向目标
                    Vector direction = targetLocation.toVector().subtract(newLocation.toVector());
                    if (direction.length() > 0) {
                        newLocation.setDirection(direction);
                    }

                    // 传送龙到新位置
                    dragon.teleport(newLocation);
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 每tick执行一次

        // 将任务注册到CustomEventManager进行跟踪
        plugin.getCustomEventManager().addActiveTask(arena, aiTask);
    }
    
    /**
     * 寻找最近的敌方玩家
     * @param ownerTeam 龙的所属队伍，如果为null则攻击所有玩家（默认龙）
     */
    private Player findNearestEnemyPlayer(Arena arena, Location dragonLocation, Team ownerTeam) {
        Player nearest = null;
        double nearestDistance = Double.MAX_VALUE;

        for (Player player : arena.getPlayers()) {
            if (player.isOnline() && !player.isDead()) {
                Team playerTeam = arena.getPlayerTeam(player);

                // 如果是默认龙（ownerTeam为null），攻击所有玩家
                // 如果是队伍龙，只攻击敌方队伍的玩家
                boolean isTarget = (ownerTeam == null) ||
                                 (playerTeam != null && !playerTeam.equals(ownerTeam));

                if (isTarget) {
                    double distance = player.getLocation().distance(dragonLocation);
                    if (distance < nearestDistance) {
                        nearest = player;
                        nearestDistance = distance;
                    }
                }
            }
        }

        return nearest;
    }
    
    /**
     * 获取竞技场中心位置
     */
    private Location getArenaMid(Arena arena) {
        World world = arena.getGameWorld();
        if (world == null) {
            return null;
        }
        
        XYZ max = arena.getMaxRegionCorner();
        XYZ min = arena.getMinRegionCorner();
        
        if (min != null && max != null) {
            return new Location(
                world,
                (max.getX() + min.getX()) / 2,
                (max.getY() + min.getY()) / 2 + 20, // 在中心上方20格
                (max.getZ() + min.getZ()) / 2
            );
        }
        
        // 如果无法获取区域边界，使用队伍出生点的平均位置
        return findAverageTeamSpawnLocation(arena, world);
    }
    
    /**
     * 计算所有队伍出生点的平均位置
     */
    private Location findAverageTeamSpawnLocation(Arena arena, World world) {
        List<Location> spawnLocations = new ArrayList<>();

        // 获取所有队伍的出生点
        for (de.marcely.bedwars.api.arena.Team team : arena.getEnabledTeams()) {
            try {
                de.marcely.bedwars.tools.location.XYZYP spawn = arena.getTeamSpawn(team);
                if (spawn != null) {
                    spawnLocations.add(spawn.toLocation(world));
                }
            } catch (Exception e) {
                // 如果获取出生点失败，跳过这个队伍
                continue;
            }
        }

        // 如果没有找到队伍出生点，尝试从玩家位置计算
        if (spawnLocations.isEmpty()) {
            for (Player player : arena.getPlayers()) {
                if (player.isOnline()) {
                    spawnLocations.add(player.getLocation());
                }
            }
        }

        if (spawnLocations.isEmpty()) {
            return world.getSpawnLocation();
        }

        double totalX = 0, totalY = 0, totalZ = 0;
        for (Location loc : spawnLocations) {
            totalX += loc.getX();
            totalY += loc.getY();
            totalZ += loc.getZ();
        }

        return new Location(
            world,
            totalX / spawnLocations.size(),
            totalY / spawnLocations.size() + 20,
            totalZ / spawnLocations.size()
        );
    }
    
    /**
     * 摧毁所有生成器
     */
    private void destroyAllGenerators(Arena arena) {
        for (Spawner spawner : arena.getSpawners()) {
            Location location = spawner.getLocation().toLocation(arena.getGameWorld()).add(0, 3, 0);

            // 向下摧毁5格
            for (int i = 0; i < 5; i++) {
                location.subtract(0, 1, 0);
                if (location.getBlock().getType() != Material.AIR) {
                    location.getBlock().setType(Material.AIR);
                }
            }
        }
    }

    /**
     * 摧毁龙附近的方块
     */
    private void destroyNearbyBlocks(Location location, double radius) {
        if (location == null || location.getWorld() == null) {
            return;
        }

        final double blockX = location.getBlockX() + 0.5;
        final double blockY = location.getBlockY() + 0.5;
        final double blockZ = location.getBlockZ() + 0.5;

        for (double x = blockX - radius; x <= blockX + radius; x++) {
            for (double y = blockY - radius; y <= blockY + radius; y++) {
                for (double z = blockZ - radius; z <= blockZ + radius; z++) {
                    Location blockLocation = new Location(location.getWorld(), x, y, z);
                    org.bukkit.block.Block block = blockLocation.getBlock();

                    // 只摧毁特定类型的方块，避免摧毁重要结构
                    if (block.getType() != Material.AIR &&
                        block.getType() != Material.BEDROCK &&
                        !block.getType().name().contains("SPAWNER") &&
                        !block.getType().name().contains("BED")) {

                        // 检查方块是否在竞技场范围内
                        try {
                            // 这里可以添加更多的保护逻辑
                            block.setType(Material.AIR);
                        } catch (Exception e) {
                            // 忽略无法摧毁的方块
                        }
                    }
                }
            }
        }
    }
}
