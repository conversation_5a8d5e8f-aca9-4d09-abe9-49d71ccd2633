package de.marcely.bedwars.api.game.upgrade;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import java.util.Collection;
import java.util.Queue;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

/**
 * Holds all the information regarding team upgrades.
 */
public interface UpgradeState {

  /**
   * Returns the arena belonging to this state.
   *
   * @return The arena attached to this state
   */
  Arena getArena();

  /**
   * The team that belongs to this state.
   *
   * @return The team attached to this state
   */
  Team getTeam();

  /**
   * Returns the level this team has upgraded this upgrade to.
   * <p>
   *   Starts at 0 if no level has been bought yet.
   * </p>
   *
   * @param upgrade The upgrade we are checking
   * @return The level (how much) this upgrade has been upgraded to
   */
  int getCurrentLevel(Upgrade upgrade);

  /**
   * Returns the UpgradeLevel the teams has reached on this upgrade.
   *
   * @param upgrade The upgrade we are checking
   * @return The UpgradeLevel this upgrade is at, or <code>null</code> if it has not been upgraded
   */
  @Nullable
  UpgradeLevel getCurrentUpgradeLevel(Upgrade upgrade);

  /**
   * Set the new level for the upgrade.
   * <p>
   *   This method does NOT perform any logic. It just changes the internal state.
   *   Use {@link #doUpgrade(UpgradeLevel, Player)} to perform the actual upgrade.
   * </p>
   *
   * @param level The new level we are setting
   * @return The previous upgrade level for the upgrade of the level, or <code>null</code> if it was not set
   * @see #clearUpgrade(Upgrade)
   * @see #doUpgrade(UpgradeLevel, Player)
   */
  @Nullable
  UpgradeLevel setCurrentUpgradeLevel(UpgradeLevel level);

  /**
   * Runs an upgrade for a team to a given level.
   * <p>
   *   Also calls PlayerTriggerUpgradeEvent, which may cancel this.
   *   If it succeds, changes regarding the upgrade will automatically applied
   *   (e.g. improved armor, etc).
   *   You may want to check using {@link Upgrade#isApplicable(Arena)} if the upgrade is
   *   even applicable/usable for the arena.
   * </p>
   *
   * @param level The UpgradeLevel we will attempt to apply
   * @param player The player who ran the upgrade. May be <code>null</code>
   * @return Whether the upgrade was successful
   * @see #setCurrentUpgradeLevel(UpgradeLevel)
   */
  boolean doUpgrade(UpgradeLevel level, @Nullable Player player);

  /**
   * Removes existing upgrades for the specified upgrade.
   * <p>
   *   This method does NOT perform any logic. It just changes the internal state.
   * </p>
   *
   * @param upgrade The upgrade we are resetting
   * @return The previous upgrade level for the upgrade of the level, or <code>null</code> if it was not set
   * @see #clearUpgrade(Upgrade)
   */
  @Nullable
  UpgradeLevel clearUpgrade(Upgrade upgrade);

  /**
   * Returns the next upgrade level players can buy of the specified type.
   *
   * @param upgrade The upgrade we are checking
   * @return The next upgrade level players can buy. <code>null</code> if max was reached
   */
  @Nullable
  UpgradeLevel getNextUpgradeLevel(Upgrade upgrade);

  /**
   * Checks if an upgrade is maxed out.
   *
   * @param upgrade The upgrade we are checking
   * @return If the upgrade is maxed out
   */
  boolean isMaxLevel(Upgrade upgrade);

  /**
   * The upgrade levels that currently effect the team.
   * <p>
   *   This is a read only collection and any specific order isn't granted.
   * </p>
   *
   * @return All upgrades effecting the team
   */
  Collection<UpgradeLevel> getActiveUpgrades();

  /**
   * The traps currently queued for this team.
   * <p>
   *   This is a read-only list in the right order, with first
   *   being the first trap to be triggered.
   * </p>
   *
   * @return The queued traps
   */
  Queue<QueuedTrap> getQueuedTraps();

  /**
   * Adds a trap to the queue.
   *
   * @param trap The trap we are adding to the queue
   * @param force If the trap should be forced into the queue (it might be full)
   * @return Weather or not we were successful in adding it to the queue
   */
  boolean queueTrap(QueuedTrap trap, boolean force);

  /**
   * Removes a queued trap.
   * <p>
   *   This will remove it from the queue and call {@link de.marcely.bedwars.api.event.player.PlayerRemoveTrapEvent}.
   * </p>
   *
   * @param trap The trap we are removing
   * @param player The player who is removing the trap
   * @return Weather we were successful in removing the trap
   */
  boolean removeTrap(QueuedTrap trap, Player player);

  /**
   * Clears the trap queue.
   */
  void clearTrapQueue();

  /**
   * Triggers a trap at a certain base.
   *
   * @param player The player who triggered the trap
   * @param baseTeam The team that will have a trap trigger
   * @param force If we should bypass the trigger cool down
   */
  void triggerTrap(Player player, Team baseTeam, boolean force);

}
