package de.marcely.bedwars.api.unsafe;

import de.marcely.bedwars.api.AddonAPI;
import de.marcely.bedwars.api.BedwarsAddon;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.MigrationProcess;
import de.marcely.bedwars.api.PluginState;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaPersistentStorage;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.arena.picker.ArenaPickerAPI;
import de.marcely.bedwars.api.command.CommandsCollection;
import de.marcely.bedwars.api.configuration.ConfigurationAPI;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import de.marcely.bedwars.api.game.upgrade.TeamEnchantment;
import de.marcely.bedwars.api.hook.Hook;
import de.marcely.bedwars.api.hook.HookAPI;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.api.message.MessageAPI;
import de.marcely.bedwars.api.player.PlayerDataAPI;
import de.marcely.bedwars.api.remote.RemoteAPI;
import de.marcely.bedwars.api.world.WorldStorage;
import de.marcely.bedwars.tools.Helper;
import de.marcely.bedwars.tools.NMSHelper;
import de.marcely.bedwars.tools.PersistentBlockData;
import de.marcely.bedwars.tools.gui.type.AnvilGUI;
import de.marcely.bedwars.tools.gui.type.ChestGUI;
import de.marcely.bedwars.tools.gui.type.VillagerGUI;
import java.io.File;
import java.util.List;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.Nullable;

/**
 *
 * Do NOT use this class!
 * Use BedwarsAPI instead.
 */
@Deprecated
public abstract class BedwarsAPILayer {

  public static BedwarsAPILayer INSTANCE = null;


  public abstract JavaPlugin getPlugin();

  public abstract boolean reload(@Nullable CommandSender sender);

  public abstract PluginState getState();

  public abstract GameAPI getGameAPI();

  public abstract ArenaPickerAPI getArenaPickerAPI();

  public abstract RemoteAPI getRemoteAPI();

  public abstract void setRemoteAPI(RemoteAPI remoteAPI);

  public abstract PlayerDataAPI getPlayerDataAPI();

  public abstract MessageAPI getMessageAPI();

  public abstract AddonAPI getAddonAPI();

  public abstract ConfigurationAPI getConfigurationAPI();

  public abstract HookAPI getHookAPI();

  public abstract NMSHelper getNMSHelper();

  public abstract Helper getHelper();

  public abstract CommandsCollection getRootCommandsCollection();

  public abstract @Nullable Team getTeamByName(String name);

  public abstract Message newMessageInstance(String rawMessage);

  public abstract Message newMessageInstance(List<String> lines);

  public abstract Message newMessageInstance(String key, String def);

  public abstract boolean registerAddon(BedwarsAddon addon);

  public abstract boolean unregisterAddon(BedwarsAddon addon);

  public abstract boolean isRegisteredAddon(BedwarsAddon addon);

  public abstract Inventory open(AnvilGUI gui, Player player);

  public abstract void open(VillagerGUI gui, Player player);

  public abstract void open(ChestGUI gui, Player player);

  public abstract void removeSpecialItemUseSession(SpecialItemUseSession session);

  public abstract boolean takeItemSpecialItemUseSession(SpecialItemUseSession session);

  public abstract File getAddonDataFolder(BedwarsAddon addon);

  public abstract @Nullable WorldStorage getWorldStorage(World world);

  public abstract void onReady(Runnable runn);

  public abstract CommandsCollection getAddonCommandsCollection(BedwarsAddon addon);

  public abstract void playSound(Player player, String soundName);

  public abstract void playSound(Location loc, String soundName);

  public abstract MigrationProcess initMigrationProcess(MigrationProcess.Origin origin);

  public abstract MigrationProcess[] getRunningMigrationProcesses();

  public abstract @Nullable PersistentBlockData blockDataParse(String str);

  public abstract PersistentBlockData blockDataFromBlock(Block block);

  public abstract PersistentBlockData blockDataFromMaterial(Material mat);

  public abstract boolean isApplicable(TeamEnchantment.Target target, ItemStack is);

  public abstract ClassLoader getOriginalClassLoader();

  public abstract boolean isActive(Hook hook);

  // Used by ProxySync !
  public abstract void setPersistentStorage(Arena arena, ArenaPersistentStorage storage);
}