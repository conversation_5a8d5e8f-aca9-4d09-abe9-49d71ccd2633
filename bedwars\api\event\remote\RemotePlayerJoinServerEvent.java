package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemotePlayer;
import de.marcely.bedwars.api.remote.RemoteServer;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when a remote player joined a server on the network.
 * <p>
 *     This does not include the local one.
 *     Keep in mind that this event is async.
 * </p>
 */
public class RemotePlayerJoinServerEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemotePlayer player;

  public RemotePlayerJoinServerEvent(RemotePlayer player) {
    super(true);

    this.player = player;
  }

  /**
   * Gets the player that has entered a server.
   *
   * @return The involved player
   */
  public RemotePlayer getPlayer() {
    return this.player;
  }

  /**
   * Gets the server that he entered.
   *
   * @return The involved server
   */
  public RemoteServer getServer() {
    return this.player.getServer();
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
