package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.player.PlayerAchievement;
import de.marcely.bedwars.api.player.PlayerAchievements;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when a player earns an achievement provided by MBedwars
 * <p>
 * 	It's possible that this event might get called async.
 * </p>
 */
public class PlayerEarnAchievementEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Player player;
  private final PlayerAchievement achievement;
  private final PlayerAchievements achievementsData;

  private boolean cancel = false;

  public PlayerEarnAchievementEvent(Player player, PlayerAchievement achievement, PlayerAchievements achievementsData, boolean async) {
    super(async);

    this.player = player;
    this.achievement = achievement;
    this.achievementsData = achievementsData;
  }

  /**
   * Returns the player who has won the achievement.
   *
   * @return The player involved in this event
   */
  public Player getPlayer() {
    return this.player;
  }

  /**
   * Returns the achievement that the player earned.
   *
   * @return The achievement
   */
  public PlayerAchievement getAchievement() {
    return this.achievement;
  }

  /**
   * Returns an object which contains his earn history.
   *
   * @return The achievements data of the player
   */
  public PlayerAchievements getPlayerAchievementsData() {
    return this.achievementsData;
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public void setCancelled(boolean bool) {
    this.cancel = bool;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
