package cn.acebrand.acevotemode.events.neutral;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.spawner.DropType;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.Location;
import org.bukkit.entity.Item;
import org.bukkit.Material;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 资源清空事件 - 中立事件
 * 清空竞技场中所有钻石/绿宝石资源点的物品
 */
public class ResourceClearEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;

    public ResourceClearEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File neutralDir = new File(eventsDir, "neutral");
            if (!neutralDir.exists()) {
                neutralDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(neutralDir, "resource_clear.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/neutral/resource_clear.yml", false);
                plugin.getLogger().info("已生成资源清空事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载资源清空事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载资源清空事件配置失败: " + e.getMessage());
        }
    }

    @Override
    public void execute(Player player, Location location) {
        // 获取竞技场
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        if (arena == null) {
            plugin.getLogger().warning("玩家不在竞技场中，无法执行资源清空事件");
            return;
        }

        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }

        // 清空资源点
        int clearedItems = clearResourceSpawners(arena);

        // 发送结果消息
        if (clearedItems > 0) {
            String successMessage = getSuccessMessage().replace("{count}", String.valueOf(clearedItems));
            player.sendMessage(successMessage);
        } else {
            String noItemsMessage = getNoItemsMessage();
            player.sendMessage(noItemsMessage);
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 触发了资源清空事件，清空了 " + clearedItems + " 个物品");
    }

    /**
     * 清空资源生成器附近的物品
     */
    private int clearResourceSpawners(Arena arena) {
        int clearedCount = 0;
        double clearRadius = config != null ? config.getDouble("clear.radius", 5.0) : 5.0;

        // 获取要清空的资源类型
        List<String> targetTypes = config != null ? config.getStringList("clear.target_types")
                : List.of("diamond", "emerald");

        // 遍历所有生成器
        plugin.getLogger().info("开始检查生成器，目标类型: " + targetTypes);
        for (Spawner spawner : arena.getSpawners()) {
            DropType dropType = spawner.getDropType();
            String dropTypeId = dropType.getId();

            plugin.getLogger().info("发现生成器类型: " + dropTypeId + " 位置: " + spawner.getLocation());

            // 检查是否是目标资源类型
            if (targetTypes.contains(dropTypeId)) {
                Location spawnerLoc = spawner.getLocation().toLocation(arena.getGameWorld());
                plugin.getLogger().info("匹配的资源生成器: " + dropTypeId + " 位置: " + spawnerLoc);

                // 清空生成器附近的物品
                int cleared = clearItemsNearLocation(spawnerLoc, clearRadius);
                clearedCount += cleared;
                plugin.getLogger().info("在生成器 " + dropTypeId + " 附近清空了 " + cleared + " 个物品");
            }
        }

        plugin.getLogger().info("总共清空了 " + clearedCount + " 个物品");

        return clearedCount;
    }

    /**
     * 清空指定位置附近的物品
     */
    private int clearItemsNearLocation(Location center, double radius) {
        int cleared = 0;

        // 获取附近的所有物品实体
        for (Item item : center.getWorld().getEntitiesByClass(Item.class)) {
            if (item.getLocation().distance(center) <= radius) {
                // 检查是否是目标物品类型
                if (isTargetItem(item)) {
                    item.remove();
                    cleared++;
                }
            }
        }

        return cleared;
    }

    /**
     * 检查是否是目标物品
     */
    private boolean isTargetItem(Item item) {
        Material material = item.getItemStack().getType();

        // 获取要清空的物品类型
        List<String> targetItems = config != null ? config.getStringList("clear.target_items")
                : List.of("DIAMOND", "EMERALD");

        return targetItems.contains(material.name());
    }

    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null)
            return true;
        return config.getBoolean("messages.send_message", true);
    }

    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null)
            return "§e[幸运方块] §f";
        return config.getString("messages.message_prefix", "§e[幸运方块] §f");
    }

    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null)
            return "§c神秘力量清空了资源点！";
        return config.getString("messages.event_message", "§c神秘力量清空了资源点！");
    }

    /**
     * 获取成功消息
     */
    private String getSuccessMessage() {
        if (config == null)
            return "§7清空了 {count} 个珍贵资源！";
        return config.getString("messages.success_message", "§7清空了 {count} 个珍贵资源！");
    }

    /**
     * 获取无物品消息
     */
    private String getNoItemsMessage() {
        if (config == null)
            return "§7资源点已经很干净了...";
        return config.getString("messages.no_items_message", "§7资源点已经很干净了...");
    }

    @Override
    public String getName() {
        return "RESOURCE_CLEAR";
    }

    @Override
    public EventType getType() {
        return EventType.NEUTRAL;
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 30) : 30;
    }

    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
}
