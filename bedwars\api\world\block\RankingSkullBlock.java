package de.marcely.bedwars.api.world.block;

/**
 * A sign which displays informations of a place in a leaderboard
 */
public interface RankingSkullBlock extends SpecialBlock {

  /**
   * Returns the place which will be displayed on the sign
   *
   * @return The place that will be displayed on the sign
   */
  int getPlace();

  /**
   * Set the new place that shall be shown on the sign
   *
   * @param place The new place
   */
  void setPlace(int place);
}