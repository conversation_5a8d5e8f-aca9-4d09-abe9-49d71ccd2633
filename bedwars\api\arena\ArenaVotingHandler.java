package de.marcely.bedwars.api.arena;

import java.util.Collection;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;
import de.marcely.bedwars.api.event.player.PlayerArenaVoteEvent;

/**
 * Represents the handler for the voting system of an arena.
 * <p>
 *   This is only available to arenas with the regeneration type {@link RegenerationType#VOTING}.
 * </p>
 *
 * @see Arena#getVoting()
 */
public interface ArenaVotingHandler {

  /**
   * Get the arena this handler is for.
   *
   * @return The arena backing this handler
   */
  Arena getArena();

  /**
   * Get the pool of arenas that are currently actively being voted for.
   *
   * @return The active pool
   */
  Collection<Arena> getActivePool();

  /**
   * Get the pool of theoretical arenas that may be voted for.
   * <p>
   *   This method does not check for the state of the arenas etc.
   *   Meaning it is possible that an arena is in the total pool but not in the active pool.
   *   <br>
   *   Main purpose of this method is to be displayed as a showcase within the configuration page
   *   of the Setup GUI.
   * </p>
   *
   * @return The total arenas that may theoretically be voted for
   */
  Collection<Arena> getTotalPool();

  /**
   * Get all the players who are voting for an arena.
   *
   * @param arena The arena from the active pool
   * @return The players who are voting for the arena. May be <code>null</code> if it is not a part of the active pool
   */
  @Nullable
  Collection<Player> getVoters(Arena arena);

  /**
   * Get the choice of a player.
   *
   * @param player The player who is voting
   * @return The arena the player is voting for or <code>null</code> if the player hasn't voted yet
   */
  @Nullable
  Arena getChoice(Player player);

  /**
   * Set the choice of a player.
   * <p>
   *   May fail if an API cancels the {@link PlayerArenaVoteEvent}.
   * </p>
   *
   * @param player The player who is voting
   * @param arena The arena the player shall vote for or <code>null</code> to unset the vote
   * @return <code>true</code> if the vote was successfully changed
   * @throws IllegalArgumentException If the player isn't a part of the arena
   * @throws IllegalArgumentException If the arena is not in the active pool
   */
  boolean setChoice(Player player, @Nullable Arena arena);

  /**
   * Unset the choice of a player.
   * <p>
   *   May fail if an API cancels the {@link PlayerArenaVoteEvent}.
   * </p>
   *
   * @param player The player who is voting
   * @return <code>true</code> if the vote was successfully changed
   */
  boolean unsetChoice(Player player);

  /**
   * Manually execute the re-calculation of the entries within the active pool.
   * <p>
   *   This method is already called automatically when needed. The event
   *   {@link de.marcely.bedwars.api.event.arena.ArenaVotingPoolUpdateEvent} is called
   *   with this method as well.
   * </p>
   */
  void updateActivePool();
}
