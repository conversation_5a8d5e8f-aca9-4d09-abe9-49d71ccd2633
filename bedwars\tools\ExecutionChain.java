package de.marcely.bedwars.tools;

import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import org.bukkit.Bukkit;
import org.bukkit.plugin.Plugin;

/**
 * Simplifies execution of multiple async or time-dependant tasks.
 */
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ExecutionChain {

  private final Plugin plugin;
  private final Queue<Runnable> tasks = new LinkedList<>();
  private Consumer<Exception> errorHandler;
  private volatile boolean isRunning;
  private volatile Runnable cancelListener;

  /**
   * Adds a task to the chain that will be executed after the one before.
   *
   * @param runn The task to be executed
   * @return The next chain object you work with
   */
  public ExecutionChain then(Runnable runn) {
    return chain(finish -> {
      runn.run();
      finish.run();
    });
  }

  /**
   * Adds a task to the chain that will be executed after the one before.
   *
   * @param runn The task to be executed
   * @return The next chain object you work with
   */
  public ExecutionChain thenThrowing(ThrowingRunnable runn) {
    return chain(finish -> {
      runn.run();
      finish.run();
    });
  }

  /**
   * Adds a consumer-task that is dependant on a future arrival of an object.
   *
   * @param consumer Returns the consumer that you pass to the supplier
   * @param handler  Your task that you want to execute with the received object
   * @return The next chain object you work with
   */
  public <T> ExecutionChain thenConsumer(ThrowingConsumer<Consumer<T>> consumer, ThrowingConsumer<T> handler) {
    return chain(finish -> {
      consumer.accept(obj -> {
        try {
          handler.accept(obj);
          finish.run();
        } catch (Exception ex) {
          handle(ex);
        }
      });
    });
  }

  /**
   * Execute a task whose finish might not be instant.
   *
   * @param looseRunn The task to be executed. Returns a runnable that you must run once you are done
   * @return The next chain object you work with
   */
  public ExecutionChain thenLoose(ThrowingBiConsumer<Runnable, Consumer<Exception>> looseRunn) {
    final AtomicBoolean called = new AtomicBoolean(false); // avoid calling twice

    return chain(finish -> {
      looseRunn.accept(() -> {
        if (!called.getAndSet(true))
          finish.run();
      }, (ex) -> {
        if (!called.getAndSet(true))
          this.handle(ex);
      });
    });
  }

  /**
   * Synchronizes the upcoming chain with the main thread.
   *
   * @return The next chain object you work with
   */
  public ExecutionChain sync() {
    return chain(finish -> {
      Helper.get().synchronize(finish);
    });
  }

  /**
   * Synchronizes the upcoming chain with the main thread after a certain amount of ticks.
   *
   * @param ticks The amount of ticks to wait
   * @return The next chain object you work with
   */
  public ExecutionChain syncLater(long ticks) {
    return chain(finish -> {
      Bukkit.getScheduler().runTaskLater(this.plugin, finish, ticks);
    });
  }

  /**
   * Runs the upcoming chain in Bukkit's async thread-pool.
   *
   * @return The next chain object you work with
   */
  public ExecutionChain async() {
    return chain(finish -> {
      Bukkit.getScheduler().runTaskAsynchronously(this.plugin, finish);
    });
  }

  /**
   * Runs the upcoming chain in Bukkit's async thread-pool after a certain amount of ticks.
   *
   * @param ticks The amount of ticks to wait
   * @return The next chain object you work with
   */
  public ExecutionChain asyncLater(long ticks) {
    return chain(finish -> {
      Bukkit.getScheduler().runTaskLaterAsynchronously(this.plugin, finish, ticks);
    });
  }

  /**
   * Set the error handler for all the tasks in the chain.
   *
   * @param errorHandler The consumer that gets called in case of an exception in any task
   * @return The next chain object you work with
   */
  public ExecutionChain error(Consumer<Exception> errorHandler) {
    if (this.errorHandler != null)
      throw new IllegalStateException("Error handler already set");

    this.errorHandler = errorHandler;

    return this;
  }

  /**
   * Runs the chain of tasks.
   *
   * @return The next chain object you work with. Only use is to {@link #cancel()}
   * @throws java.lang.IllegalStateException If no tasks were added to the chain
   * @throws java.lang.IllegalStateException If the chain is already running
   */
  public ExecutionChain run() {
    if (this.isRunning)
      throw new IllegalStateException("Chain is already running");

    this.isRunning = true;

    final Runnable first = this.tasks.poll();

    if (first != null)
      first.run();
    else
      throw new IllegalStateException("No tasks added");

    return this;
  }

  /**
   * Tries to cancel the chain of tasks.
   * <p>
   * This will only cancel the execution of further tasks in the chain, and not the one that is already running.
   * </p>
   */
  public void cancel() {
    this.isRunning = false;
  }

  /**
   * Tries to cancel the chain of tasks.
   * <p>
   * This will only cancel the execution of further tasks in the chain, and not the one that is already running.
   * </p>
   *
   * @param callback Gets called after the last task has been processed
   */
  public void cancel(Runnable callback) {
    this.cancelListener = callback;
    this.isRunning = false;
  }

  /**
   * Checks if the chain is running right now.
   * <p>
   * It isn't running if it either has been cancelled, has finished or never hasn't been run so far.
   * </p>
   *
   * @return <code>true</code> if the chain is running
   */
  public boolean isRunning() {
    return this.isRunning;
  }

  protected ExecutionChain chain(ThrowingConsumer<Runnable> runn) {
    this.tasks.add(() -> {
      if (!this.isRunning) {
        onEnd();
        return;
      }

      try {
        runn.accept(() -> {
          final Runnable next = this.tasks.poll();

          if (next != null)
            next.run();
          else
            onEnd();
        });
      } catch (Exception ex) {
        handle(ex);
      }
    });

    return this;
  }

  private void onEnd() {
    this.isRunning = false;

    if (this.cancelListener != null)
      this.cancelListener.run();
  }

  protected void handle(Exception ex) {
    if (this.errorHandler != null)
      this.errorHandler.accept(ex);
    else
      ex.printStackTrace();
  }

  /**
   * Initializes the chain.
   *
   * @param plugin The plugin that is running the chain
   * @return The object to which you add the first task
   */
  public static ExecutionChain init(Plugin plugin) {
    return new ExecutionChain(plugin);
  }
}
