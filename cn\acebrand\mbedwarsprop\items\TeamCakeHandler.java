package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;

public class TeamCakeHandler implements SpecialItemUseHandler {

    private final Plugin plugin;
    private final ItemConfigManager.ItemConfig config;

    public TeamCakeHandler(Plugin plugin, ItemConfigManager.ItemConfig config) {
        this.plugin = plugin;
        this.config = config;
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    private class Session extends SpecialItemUseSession {

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            // 不需要特殊清理
        }

        protected void run() {
            Player player = getEvent().getPlayer();

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(player);
            if (arena == null) {
                player.sendMessage("§c你必须在游戏中才能使用神奇的蛋糕！");
                stop();
                return;
            }

            // 获取玩家所在的队伍
            Team team = arena.getPlayerTeam(player);
            if (team == null) {
                player.sendMessage("§c你必须在一个队伍中才能使用神奇的蛋糕！");
                stop();
                return;
            }

            // 获取配置
            double healthPercent = 1.0;
            int foodAmount = 20;
            boolean saturation = true;
            int saturationDuration = 10;
            int saturationLevel = 0;
            boolean showParticles = true;

            if (config != null) {
                ItemConfigManager.EffectConfig healthConfig = config.getEffect("health-percent");
                ItemConfigManager.EffectConfig foodConfig = config.getEffect("food-amount");
                ItemConfigManager.EffectConfig saturationConfig = config.getEffect("saturation");
                ItemConfigManager.EffectConfig saturationDurationConfig = config.getEffect("saturation-duration");
                ItemConfigManager.EffectConfig saturationLevelConfig = config.getEffect("saturation-level");
                ItemConfigManager.EffectConfig particlesConfig = config.getEffect("particles");

                if (healthConfig != null) {
                    healthPercent = healthConfig.getLevel() / 100.0;
                    if (healthPercent > 1.0) healthPercent = 1.0;
                    if (healthPercent < 0.0) healthPercent = 0.0;
                }

                if (foodConfig != null) {
                    foodAmount = foodConfig.getLevel();
                    if (foodAmount > 20) foodAmount = 20;
                    if (foodAmount < 0) foodAmount = 0;
                }

                if (saturationConfig != null) {
                    saturation = saturationConfig.getLevel() > 0;
                }

                if (saturationDurationConfig != null) {
                    saturationDuration = saturationDurationConfig.getDuration();
                }

                if (saturationLevelConfig != null) {
                    saturationLevel = saturationLevelConfig.getLevel();
                }

                if (particlesConfig != null) {
                    showParticles = particlesConfig.getLevel() > 0;
                }
            }

            // 为队伍中的所有玩家恢复生命值和饥饿度
            // 获取队伍中的所有玩家
            for (Player teamPlayer : arena.getPlayers()) {
                // 检查玩家是否在同一个队伍
                if (!team.equals(arena.getPlayerTeam(teamPlayer))) {
                    continue;
                }
                if (teamPlayer.isOnline()) {
                    // 恢复生命值
                    double maxHealth = teamPlayer.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue();
                    double newHealth = Math.min(teamPlayer.getHealth() + (maxHealth * healthPercent), maxHealth);
                    teamPlayer.setHealth(newHealth);

                    // 恢复饥饿度
                    teamPlayer.setFoodLevel(Math.min(teamPlayer.getFoodLevel() + foodAmount, 20));

                    // 添加饱和效果
                    if (saturation) {
                        teamPlayer.addPotionEffect(new PotionEffect(
                                PotionEffectType.SATURATION,
                                saturationDuration * 20,
                                saturationLevel));
                    }

                    // 播放音效
                    teamPlayer.playSound(teamPlayer.getLocation(), Sound.ENTITY_PLAYER_BURP, 1.0f, 1.0f);

                    // 显示粒子效果
                    if (showParticles) {
                        showHealingParticles(teamPlayer);
                    }

                    // 发送消息
                    if (teamPlayer.equals(player)) {
                        teamPlayer.sendMessage("§a你使用了神奇的蛋糕，恢复了全队的生命值和饥饿度！");
                    } else {
                        teamPlayer.sendMessage("§a队友 " + player.getName() + " 使用了神奇的蛋糕，恢复了你的生命值和饥饿度！");
                    }
                }
            }

            // 消耗物品
            takeItem();
        }

        private void showHealingParticles(final Player player) {
            new BukkitRunnable() {
                private int count = 0;

                @Override
                public void run() {
                    if (count >= 20 || !player.isOnline()) {
                        cancel();
                        return;
                    }

                    // 在玩家周围显示治疗粒子
                    player.getWorld().spawnParticle(
                            Particle.HEART,
                            player.getLocation().add(0, 1, 0),
                            3,
                            0.5, 0.5, 0.5,
                            0);

                    count++;
                }
            }.runTaskTimer(plugin, 0L, 5L);
        }
    }
}
