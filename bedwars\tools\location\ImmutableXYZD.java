package de.marcely.bedwars.tools.location;

import org.bukkit.Location;
import org.bukkit.util.Vector;

/**
 * Extends {@link XYZD} and forbids any modifications done to it
 */
public class ImmutableXYZD extends XYZD {

  public ImmutableXYZD(Location loc) {
    super(loc);
  }

  public ImmutableXYZD(Vector vec) {
    super(vec);
  }

  public ImmutableXYZD(XYZ xyz) {
    super(xyz);
  }

  public ImmutableXYZD(XYZD xyz) {
    super(xyz);
  }

  public ImmutableXYZD() {
    super();
  }

  public ImmutableXYZD(double x, double y, double z) {
    super(x, y, z);
  }

  public ImmutableXYZD(double x, double y, double z, Direction dir) {
    super(x, y, z, dir);
  }

  @Override
  public XYZD setX(double x) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD setY(double y) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD setZ(double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD set(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD set(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD set(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD add(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD add(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD add(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD add(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD subtract(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD subtract(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD subtract(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD subtract(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD multiply(double amount) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD multiply(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD zero() {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZD setDirection(Direction dir) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }
}
