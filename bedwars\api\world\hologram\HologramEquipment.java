package de.marcely.bedwars.api.world.hologram;

import de.marcely.bedwars.tools.NMSHelper;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the externally visible equipment inventory of a hologram.
 */
public interface HologramEquipment {

  /**
   * Get the skin that is being represented by this instance.
   *
   * @return The skin instance for which the items will be displayed
   */
  HologramSkin getSkin();

  /**
   * Set an item at a certain slot.
   *
   * @param slot The slot at which the item shall be put to
   * @param is The item that shall be placed. May be <code>null</code> if it's supposed to be air
   */
  void setItem(Slot slot, @Nullable ItemStack is);

  /**
   * Get an item that has been put at a certain slot.
   *
   * @param slot The slot at which the item has been put at
   * @return The item that has been placed. Maybe be <code>null</code> if it's air
   */
  @Nullable
  ItemStack getItem(Slot slot);



  /**
   * Represents a slot in a holograms equipment inventory.
   */
  public enum Slot {

    /**
     * The head slot.
     */
    HELMET,
    /**
     * The breast slot.
     */
    CHESTPLATE,
    /**
     * The legs slot.
     */
    LEGGINGS,
    /**
     * The feet slot.
     */
    BOOTS,
    /**
     * Right hand.
     */
    MAIN_HAND,
    /**
     * Left hand (1.9+ only).
     */
    OFF_HAND;

    /**
     * Returns whether the current spigot software supports the slot type.
     * <p>
     *   E.g. {@link #OFF_HAND} only is being supported on 1.9+.
     *   The method would return <code>false</code> on 1.8.8.
     * </p>
     *
     * @return <code>true</code> when this slot may be used on the currently used spigot software
     */
    public boolean isSupported() {
      switch (this) {
        case OFF_HAND:
          return NMSHelper.get().getVersion() >= 9;
        default:
          return true;
      }
    }

    /**
     * Returns the 1.8 NMS id that is being used for the given slot.
     * <p>
     *   There shouldn't be a use for this for most developers.
     *   Mainly used for internal purposes.
     * </p>
     *
     * @return The 1.8 NMS id for this slot
     */
    public int getInternalIdV8() {
      switch (this) {
        case HELMET:
          return 4;
        case CHESTPLATE:
          return 3;
        case LEGGINGS:
          return 2;
        case BOOTS:
          return 1;
        case MAIN_HAND:
          return 0;
        default:
          throw new UnsupportedOperationException("Not supported for " + this);
      }
    }

    /**
     * Returns the 1.9+ NMS id that is being used for the given slot.
     * <p>
     *   There shouldn't be a use for this for most developers.
     *   Mainly used for internal purposes.
     * </p>
     *
     * @return The 1.9+ NMS id for this slot
     */
    public String getInternalIdV9() {
      switch (this) {
        case HELMET:
          return "HEAD";
        case CHESTPLATE:
          return "CHEST";
        case LEGGINGS:
          return "LEGS";
        case BOOTS:
          return "FEET";
        case MAIN_HAND:
          return "MAINHAND";
        case OFF_HAND:
          return "OFFHAND";
        default:
          throw new UnsupportedOperationException("Not supported for " + this);
      }
    }

    /**
     * Returns the 1.17+ NMS id that is being used for the given slot.
     * <p>
     *   There shouldn't be a use for this for most developers.
     *   Mainly used for internal purposes.
     * </p>
     *
     * @return The 1.17+ NMS id for this slot
     */
    public String getInternalIdV17() {
      switch (this) {
        case HELMET:
          return "head";
        case CHESTPLATE:
          return "chest";
        case LEGGINGS:
          return "legs";
        case BOOTS:
          return "feet";
        case MAIN_HAND:
          return "mainhand";
        case OFF_HAND:
          return "offhand";
        default:
          throw new UnsupportedOperationException("Not supported for " + this);
      }
    }
  }
}
