package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.remote.RemoteArena;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when player closes an arena editor inside the setup GUI (/bw arena setupgui)
 */
public class PlayerCloseArenaEditorInSetupGUI extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemoteArena arena;

  public PlayerCloseArenaEditorInSetupGUI(Player player, RemoteArena arena) {
    super(player);

    this.arena = arena;
  }

  /**
   * Returns the local {@link Arena} that is involved in this event.
   * <p>
   *     May be <code>null</code> if the arena isn't local.
   * </p>
   *
   * @return The arena that is involved. May be <code>null</code>
   */
  @Nullable
  public Arena getArena() {
    return this.arena.getLocal();
  }

  /**
   * Returns the arena that is involved in this event.
   *
   * @return The involved arena
   */
  public RemoteArena getRemoteArena() {
    return this.arena;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
