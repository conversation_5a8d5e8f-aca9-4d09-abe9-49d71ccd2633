package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called after a player has changed his team.
 * <p>
 *     Note that plugins may also change the team during the match/end lobby using the API.
 * </p>
 */
public class PlayerTeamChangeEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Team oldTeam, newTeam;

  public PlayerTeamChangeEvent(Player player, Arena arena, Team oldTeam, Team newTeam) {
    super(player);

    this.arena = arena;
    this.oldTeam = oldTeam;
    this.newTeam = newTeam;
  }

  /**
   * Returns the previous team of the player.
   * <code>null</code> meaning that he wasn't in a team before.
   *
   * @return The previous team of the player, might be <code>null</code>
   */
  public @Nullable Team getOldTeam() {
    return this.oldTeam;
  }

  /**
   * Returns the new team to which the player has been moved to.
   * <code>null</code> meaning that he's not in a team (Only possible during lobby phase !).
   *
   * @return His new team, might be <code>null</code>
   */
  public @Nullable Team getNewTeam() {
    return this.newTeam;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
