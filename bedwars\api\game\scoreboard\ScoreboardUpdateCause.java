package de.marcely.bedwars.api.game.scoreboard;

import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.player.PlayerStats;
import org.bukkit.entity.Player;

/**
 * What part of the scoreboard shall be updated
 */
public enum ScoreboardUpdateCause {

  /**
   * Complete update.
   * <code>data</code> is always null
   */
  COMPLETE_REFRESH,

  /**
   * Gets called every second when the state of the arena is {@link ArenaStatus#RUNNING} or {@link ArenaStatus#LOBBY}.
   * MBedwars will naturally call it in a separate thread, it's though not promised that it'll be done at all times.
   * <code>data</code> is always null
   */
  TICK,

  /**
   * Player has joined the arena.
   * <code>data</code> is the involved {@link Player}
   */
  PLAYER_ADD,

  /**
   * Player has left the arena.
   * <code>data</code> is the involved {@link Player}
   */
  PLAYER_REMOVE,

  /**
   * Stats of a player have changed.
   * <code>data</code> is the {@link PlayerStats} (never the game stats!) of the player
   */
  PLAYER_STATS_UPDATE,

  /**
   * Player has changed his team.
   * <code>data</code> is the involved {@link Player}
   */
  PLAYER_TEAM_CHANGE,

  /**
   * Bed of a team has been destroyed.
   * <code>data</code> is the involved {@link Team}
   */
  BED_DESTROY,

  /**
   * Bed of a team has been revived (by e.g. a plugin).
   * <code>data</code> is the involved {@link Team}
   */
  BED_REVIVE,

  /**
   * Update PlaceholderAPI placeholders
   */
  PAPI_PLACEHOLDERS
}
