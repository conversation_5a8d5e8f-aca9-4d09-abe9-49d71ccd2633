# 资源点怪物生成事件配置
# 中立事件 - 在钻石/绿宝石资源点生成怪物群

# 事件基本信息
event:
  name: "RESOURCE_SPAWNER_MONSTER"
  type: "NEUTRAL"
  weight: 30
  enabled: true

# 生成配置
spawn:
  # 目标资源生成器类型（注意：使用小写）
  target_spawner_types:
    - "diamond"
    - "emerald"
  
  # 生成半径（以资源点为中心）
  radius: 3.0
  
  # 怪物名称前缀
  name_prefix: "§c愤怒的"
  
  # 怪物生存时间（秒，0为永久）
  lifetime: 300  # 5分钟
  
  # 白天是否燃烧
  burn_in_daylight: false

# 怪物组配置
monster_groups:
  # 主世界怪物组
  overworld:
    weight: 50
    zombie:
      type: "ZOMBIE"
      display_name: "僵尸"
      count: 5
      health: 20.0
    skeleton:
      type: "SKELETON"
      display_name: "骷髅"
      count: 5
      health: 20.0
    spider:
      type: "SPIDER"
      display_name: "蜘蛛"
      count: 5
      health: 16.0
    creeper:
      type: "CREEPER"
      display_name: "苦力怕"
      count: 2
      health: 20.0
  
  # 地狱怪物组
  nether:
    weight: 50
    piglin:
      type: "PIGLIN"
      display_name: "猪人"
      count: 5
      health: 16.0
    wither_skeleton:
      type: "WITHER_SKELETON"
      display_name: "凋零骷髅"
      count: 5
      health: 20.0
    blaze:
      type: "BLAZE"
      display_name: "烈焰人"
      count: 5
      health: 20.0

# 消息设置
messages:
  # 是否发送事件消息到聊天栏
  send_message: true
  # 消息前缀
  message_prefix: "§e[幸运方块] §f"
  # 事件消息
  event_message: "§c资源点出现了怪物！"
  # 成功消息
  success_message: "§7怪物群已在资源点生成！"
  # 无资源点消息
  no_spawner_message: "§7找不到合适的资源点..."
