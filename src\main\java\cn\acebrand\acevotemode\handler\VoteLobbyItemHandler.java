package cn.acebrand.acevotemode.handler;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gui.VoteGUI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.game.lobby.LobbyItem;
import de.marcely.bedwars.api.game.lobby.LobbyItemHandler;
import org.bukkit.entity.Player;

/**
 * 投票大厅物品处理器
 * 处理玩家在等待大厅中点击投票物品的逻辑
 */
public class VoteLobbyItemHandler extends LobbyItemHandler {
    
    private final AceVoteMode plugin;
    
    public VoteLobbyItemHandler(AceVoteMode plugin) {
        super("acevotemode:vote", plugin);
        this.plugin = plugin;
    }
    
    @Override
    public void handleUse(Player player, Arena arena, LobbyItem item) {
        // 检查权限
        if (!player.hasPermission("acevotemode.vote")) {
            player.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
            return;
        }
        
        // 检查竞技场状态
        if (arena.getStatus() != ArenaStatus.LOBBY) {
            return;
        }
        
        // 检查投票是否已关闭
        if (plugin.getVoteManager().getArenaVoteData(arena).isVotingClosed()) {
            player.sendMessage(plugin.getConfigManager().getMessage("vote-closed"));
            return;
        }
        
        // 打开投票GUI
        VoteGUI voteGUI = new VoteGUI(plugin, player, arena);
        voteGUI.open();
    }
    
    @Override
    public boolean isVisible(Player player, Arena arena, LobbyItem item) {
        // 只在大厅状态下显示
        if (arena.getStatus() != ArenaStatus.LOBBY) {
            return false;
        }

        // 检查权限
        if (!player.hasPermission("acevotemode.vote")) {
            return false;
        }

        // 检查是否有可用的游戏模式
        return !plugin.getConfigManager().getGameModes().isEmpty();
    }
}
