package de.marcely.bedwars.api.game.shop.product;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.game.shop.price.ShopPriceType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

public interface ItemShopProduct extends ShopProduct {

  /**
   * Returns the itemstacks that will be given to the player.
   *
   * @return The items that this product holds
   */
  ItemStack[] getItemStacks();

  /**
   * Replace the product item.
   *
   * @param is The new product item
   * @throws UnsupportedOperationException When executing this for a type other than {@link ShopProductType#ITEM}
   */
  void setItemStack(ItemStack is);

  /**
   * Returns an array of all the items that are being given on a purchase.
   * Keep in mind that not all types of products give items. An empty array will be returned in that case.
   *
   * <p>
   *  The team and the arena parameters are optional.
   *  Some product features won't be applied if they're <code>null</code>.
   *  This includes: automatic dying and enchantments gained from upgrades
   * </p>
   *
   * @param player The player to which the item shall be given to. Names will also be automatically translated into the language of this player
   * @param team The team in which the player is in. Can be <code>null</code>
   * @param arena The arena in which the player is in. Can be <code>null</code>
   * @param multiplier How many times this product shall be given to the player. Should be at least 1, or otherwise no effect will be seen
   * @return All items that will be given on a purchase
   */
  ItemStack[] getGivingItems(Player player, @Nullable Team team, @Nullable Arena arena, int multiplier);
}
