package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.BedwarsAPI;
import java.time.Duration;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Called when the configurations of the plugin got loaded.
 * <p>
 * This always gets called after the plugin started or when someone performed a reload.<br>
 * Do NOT use this as an alternative for {@link BedwarsAPI#onReady(Runnable)}.
 */
public class ConfigsLoadEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final CommandSender initiator;
  private final Duration time;
  private final boolean isStartup;

  public ConfigsLoadEvent(@Nullable CommandSender initiator, Duration time, boolean isStartup) {
    this.initiator = initiator;
    this.time = time;
    this.isStartup = isStartup;
  }

  /**
   * Returns the initiator of the load event.
   * <p>
   * It may be <code>null</code> when it was started programmatically.
   *
   * @return The sender that initiated the loading
   */
  public @Nullable CommandSender getInitiator() {
    return this.initiator;
  }

  /**
   * Returns the time it took to load the configurations from 0 to complete in milliseconds.
   *
   * @return Returns the duration it took to load the configs in milliseconds.
   */
  public Duration getTime() {
    return this.time;
  }

  /**
   * Returns whether this event is being executed for the first time due to the loading that occured with starting up the plugin.
   *
   * @return <code>true</code> when it got called because the plugin finished loading all configs during the start up sequence
   */
  public boolean isStartup() {
    return this.isStartup;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
