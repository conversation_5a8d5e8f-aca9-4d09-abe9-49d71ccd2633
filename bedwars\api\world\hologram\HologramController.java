package de.marcely.bedwars.api.world.hologram;

import com.google.gson.JsonObject;
import org.bukkit.entity.Player;

/**
 * The HologramController is basically the brain of the hologram.
 * It handles what exactly is supposed to happen when interacting the hologram and stores additional information that are needed to process those actions.
 * <p>
 *  Storing informations is for instance required for the team selector. In this case it'll store the currently selected team.
 *  To read/write use {@link #serialize(JsonObject)} or {@link #deserialize(JsonObject)}.
 * </p>
 */
public interface HologramController {

  /**
   * Returns the type of this controller.
   *
   * @return The type of the controller
   */
  HologramControllerType getType();

  /**
   * Returns the hologram to which this controller is (possibly) bound to.
   * <p>
   *     It's possible that the hologram has a new controller. Use {@link #isActive()} to verify that this one is currently the active one.
   * </p>
   *
   * @return The hologram to which this controller instance is (possibly) bound to
   */
  HologramEntity getHologram();

  /**
   * Returns if the controller is still bound to an existing hologram.
   *
   * @return If it is still active
   */
  boolean isActive();

  /**
   * Simulate as if a player would right-click on him
   *
   * @param player The player who touched him
   */
  void handleInteract(Player player);

  /**
   * A controller might contain some properties of the hologram.
   * Use this method to apply those properties, such as the display name, to the hologram.
   */
  void applyProperties();

  /**
   * Puts all the permanent data of this controller into a json object.
   * <p>
   *     For instance this'd add the team of the teamselectstatue if the type is {@link HologramControllerType#TEAM_SELECTOR}
   * </p>
   *
   * @param json The json to which it shall be added to
   * @throws Exception Errors that might occur during that
   */
  void serialize(JsonObject json) throws Exception;

  /**
   * Reads all the permanent data of this controller from a json object that was previously added with {@link #serialize(JsonObject)}.
   * <p>
   *     For instance this'd read and update the team of the teamselectstatue if the type is {@link HologramControllerType#TEAM_SELECTOR}
   * </p>
   *
   * @param json The json from which it shall be read from
   * @throws Exception Errors that might occur during that
   */
  void deserialize(JsonObject json) throws Exception;
}