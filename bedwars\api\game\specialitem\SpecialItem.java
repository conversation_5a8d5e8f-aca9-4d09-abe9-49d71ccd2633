package de.marcely.bedwars.api.game.specialitem;

import de.marcely.bedwars.api.GameAPI;
import org.bukkit.command.CommandSender;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the type of SpecialItem and not one for every given item to the player.
 * Meaning there's only one global instance of every type.
 * <p>
 * Use {@link GameAPI#registerSpecialItem(String, Plugin, String, ItemStack)} to initiate a new one.
 */
public interface SpecialItem {

  /**
   * Returns the type to make it easier for you to differentiate it
   *
   * @return The type of this special item
   */
  SpecialItemType getType();

  /**
   * Returns the id that's being used in e.g. the shop config
   *
   * @return The id of this item
   */
  String getId();

  /**
   * Same as {@link #getName(CommandSender)}, but uses the default language
   *
   * @return The name of the item in the default language
   */
  default String getName() {
    return getName(null);
  }

  /**
   * Returns the name of this item in the language of the given sender.<br>
   * If sender is null then it'll use the default language instead
   *
   * @param sender The person from which it should look up the language. Null if it should take the default language
   * @return The name of the item in the language of the sender
   */
  String getName(@Nullable CommandSender sender);

  /**
   * Returns the item that the player will receive once he purchases the item.
   * <p>
   *     Keep in mind that this is not the original ItemStack.
   *     The plugin may add its own tags to make it unique and more identifiable from others.
   *     Use {@link #getOriginalItemStack()} if you look to obtain the original one.
   * </p>
   *
   * @return The ItemStack of the item that's useable ingame
   */
  ItemStack getItemStack();

  /**
   * Returns the original ItemStack that's being passed during initiation.
   * <p>
   *     The returned ItemStack is not usable ingame.
   *     Use {@link #getItemStack()} instead if that's what you're looking for.
   * </p>
   *
   * @return The original, non-modified ItemStack
   */
  ItemStack getOriginalItemStack();

  /**
   * Returns whether or not the given {@link ItemStack} is running this SpecialItem when used.
   *
   * @return <code>true</code> if it's the one for this SpecialItem. Otherwise <code>false</code>
   */
  boolean isSpecialItem(ItemStack itemStack);

  /**
   * Returns the plugin that created the item
   *
   * @return The plugin that created this special item
   */
  Plugin getPlugin();

  /**
   * Returns the handler that's been called whenever someone uses an item<br>
   * Uses {@link DeadSpecialItemUseHandler} as the default one
   *
   * @return The use handler of the special item
   */
  SpecialItemUseHandler getHandler();

  /**
   * Set the handler that's been called whenever someone uses the item.
   *
   * @param handler The new use handler
   */
  void setHandler(SpecialItemUseHandler handler);

  /**
   * Returns whether this item has been registered using {@link GameAPI#registerSpecialItem(SpecialItem)}
   *
   * @return Whether this item is registered
   */
  default boolean isRegistered() {
    return GameAPI.get().getSpecialItems().contains(this);
  }
}
