package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import lombok.Getter;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when the lobby timer reaches 0, but still before the initiation of the round
 */
public class ArenaPreparingStartEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;

  private int countdown = 0;

  public ArenaPreparingStartEvent(Arena arena) {
    this.arena = arena;
  }

  /**
   * Returns the current value of the timer/countdown of the lobby.
   * <p>
   * Is 0 unless an other plugin has changed it by {@link #setCountdown(int)}
   *
   * @return The time remaining until the round starts
   */
  public int getCountdown() {
    return this.countdown;
  }

  /**
   * Set the new timer/countdown value.
   * <p>
   * Changing this anything larger 0 causes the round to not get started
   * and resets the lobby timer back to the given value.
   *
   * @param countdown The new value for the lobby timer
   */
  public void setCountdown(int countdown) {
    this.countdown = countdown;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
