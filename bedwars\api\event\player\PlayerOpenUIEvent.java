package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.game.lobby.LobbyItemHandlerType;
import de.marcely.bedwars.api.game.spectator.SpectatorItemHandlerType;
import de.marcely.bedwars.tools.gui.type.ChestGUI;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a misc variant of a UI is being shown
 */
public class PlayerOpenUIEvent extends PlayerEvent implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final ChestGUI gui;
  private final UIType type;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerOpenUIEvent(Player player, ChestGUI gui, UIType type) {
    super(player);

    this.gui = gui;
    this.type= type;
  }

  /**
   * Get the rendered GUI that'd be shown.
   *
   * @return The GUI that is about to be shown
   */
  public ChestGUI getGUI() {
    return this.gui;
  }

  /**
   * Get the type of the UI.
   *
   * @return The UI type
   */
  public UIType getType() {
    return this.type;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }

  /**
   * Get the type of the UI that can be shown.
   */
  public enum UIType {

    /**
     * UI shown with either {@link LobbyItemHandlerType#VIEW_ACHIEVEMENTS} and "/bw achievements".
     */
    ACHIEVEMENTS,

    /**
     * UI shown with {@link LobbyItemHandlerType#SELECT_TEAM}.
     */
    LOBBY_SELECT_TEAM,

    /**
     * UI shown with {@link LobbyItemHandlerType#VOTE_ARENA}.
     */
    LOBBY_VOTE_ARENA,

    /**
     * UI shown with {@link SpectatorItemHandlerType#VIEW_PLAYERS}.
     */
    SPECTATOR_VIEW_PLAYERS
  }
}
