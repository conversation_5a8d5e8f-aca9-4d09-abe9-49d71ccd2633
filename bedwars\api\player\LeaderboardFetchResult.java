package de.marcely.bedwars.api.player;

import de.marcely.bedwars.tools.Validate;
import java.util.function.Consumer;
import org.jetbrains.annotations.Nullable;

/**
 * Result object of {@link PlayerDataAPI#fetchLeaderboard(PlayerStatSet, int, int, Consumer)}
 */
public class LeaderboardFetchResult {

  private final PlayerStatSet statSet;
  private final int min, max;
  private final PlayerStats[] stats;
  private final PlayerProperties[] properties;

  public LeaderboardFetchResult(PlayerStatSet statSet, int minRank, int maxRank, PlayerStats[] stats, PlayerProperties[] properties) {
    Validate.isTrue(minRank <= maxRank, "min is greater than max");
    Validate.isTrue((maxRank-minRank+1) == stats.length, "stats has an invalid length");
    Validate.isTrue((maxRank-minRank+1) == properties.length, "properties has an invalid length");
    Validate.notNull(statSet, "statSet");

    this.statSet = statSet;
    this.min = minRank;
    this.max = maxRank;
    this.stats = stats;
    this.properties = properties;
  }

  /**
   * Get the stat set that was used to order the players.
   *
   * @return The stat set with which the players were ordered
   */
  public PlayerStatSet getStatSet() {
    return this.statSet;
  }

  /**
   * Gets the minimum rank that has been fetched with this result.
   * <p>
   *   Starts at 1 (= best player).
   * </p>
   *
   * @return The minimum rank that can be obtained
   */
  public int getMinRank() {
    return this.min;
  }

  /**
   * Gets the maximum rank that has been fetched with this result.
   *
   * @return The maximum rank that can be obtained
   */
  public int getMaxRank() {
    return this.max;
  }

  /**
   * Obtains the stats of the player at the given rank.
   * <p>
   *   May be <code>null</code> if no player has reached the given rank yet.
   * </p>
   *
   * @param rank The rank we want to check
   * @return The stats of the player that is at the given rank. May be <code>null</code>
   * @throws IllegalArgumentException In case the rank parameter is out of bounds (must be between min and max)
   */
  @Nullable
  public PlayerStats getStatsAtRank(int rank) {
    final int index = getIndex(rank);

    if (index == -1)
      throw new IllegalArgumentException("rank is out of bounds (must be between " + this.min + " and " + this.max + ")");

    return this.stats[index];
  }

  /**
   * Obtains the properties of the player at the given rank.
   * <p>
   *   May be <code>null</code> if no player has reached the given rank yet.
   * </p>
   *
   * @param rank The rank we want to check
   * @return The properties of the player that is at the given rank. May be <code>null</code>
   * @throws IllegalArgumentException In case the rank parameter is out of bounds (must be between min and max)
   */
  @Nullable
  public PlayerProperties getPropertiesAtRank(int rank) {
    final int index = getIndex(rank);

    if (index == -1)
      throw new IllegalArgumentException("rank is out of bounds (must be between " + this.min + " and " + this.max + ")");

    return this.properties[index];
  }

  private int getIndex(int rank) {
    if (rank < this.min || rank > this.max)
      return -1;

    return rank - this.min;
  }
}
