package de.marcely.bedwars.api.arena;

/**
 * The reason why a player rejoined an arena.
 */
public enum RejoinPlayerCause {

  /**
   * He used a command (/mbedwars rejoin).
   */
  COMMAND,

  /**
   * He tried to join the arena directly (e.g. /mbedwars join or arenasgui).
   */
  JOIN_DIRECTLY,

  /**
   * The match ended and players that died during the match - thus become spectators - get re-added to the end lobby.
   * <p>
   *     This is a special conditon.
   *     Unlike other causes, the status of the arena must match {@link ArenaStatus#END_LOBBY}.
   * </p>
   */
  END_LOBBY,

  /**
   * A plugin made him rejoin using the API.
   */
  PLUGIN,

  /**
   * We received a request from a remote server (proxy), but couldn't identify the cause.
   */
  UNKNOWN;
}
