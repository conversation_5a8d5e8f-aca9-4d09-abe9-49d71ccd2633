package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import lombok.Getter;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called after an arena has been unloaded.
 * <p>
 *   This includes i.a. a deletion ({@link ArenaDeleteEvent}, not cancelled) or a server shutdown.
 * </p>
 */
public class ArenaUnloadEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;

  public ArenaUnloadEvent(Arena arena) {
    this.arena = arena;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
