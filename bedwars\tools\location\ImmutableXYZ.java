package de.marcely.bedwars.tools.location;

import org.bukkit.Location;
import org.bukkit.util.Vector;

/**
 * Extends {@link XYZ} and forbids any modifications done to it
 */
public class ImmutableXYZ extends XYZ {

  public ImmutableXYZ() {
    super(0, 0, 0);
  }

  public ImmutableXYZ(Location loc) {
    super(loc.getX(), loc.getY(), loc.getZ());
  }

  public ImmutableXYZ(Vector vec) {
    super(vec.getX(), vec.getY(), vec.getZ());
  }

  public ImmutableXYZ(XYZ xyz) {
    this(xyz.getX(), xyz.getY(), xyz.getZ());
  }

  public ImmutableXYZ(double x, double y, double z) {
    super(x, y, z);
  }

  @Override
  public XYZ setX(double x) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ setY(double y) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ setZ(double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ set(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ set(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ set(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ add(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ add(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ add(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ add(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ subtract(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ subtract(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ subtract(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ subtract(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ multiply(double amount) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ multiply(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZ zero() {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }
}
