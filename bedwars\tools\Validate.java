package de.marcely.bedwars.tools;

import org.jetbrains.annotations.Nullable;

public class Validate {

  private Validate() {
  }

  public static void notNull(@Nullable Object obj, String param) {
    if (obj == null)
      throw new IllegalArgumentException("The passed parameter " + param + " is null");
  }

  public static void notNullDeep(@Nullable Object[] array, String param) {
    for (int i = 0; i < array.length; i++)
      notNull(array[i], param + "[" + i + "]");
  }

  public static void isTrue(boolean expression, String message) {
    if (!expression)
      throw new IllegalArgumentException(message);
  }
}
