package de.marcely.bedwars.api.game.shop.price;

import org.bukkit.inventory.ItemStack;

public interface ItemShopPrice extends ShopPrice {

  /**
   * Returns cloned itemstacks that will be taken from the player.
   * <p>
   *   The reason that this method returns an array is that other types may have multiple (such as {@link ShopPriceType#SPAWNER_ITEM}).
   * </p>
   *
   * @return The itemstacks that this price represents
   */
  ItemStack[] getItemStacks();

  /**
   * Replace the price item.
   *
   * @param is The new price item
   * @throws UnsupportedOperationException When executing this for a type other than {@link ShopPriceType#ITEM}
   */
  void setItemStack(ItemStack is);
}
