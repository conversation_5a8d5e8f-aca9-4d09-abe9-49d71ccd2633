package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.shop.product.ShopProduct;
import de.marcely.bedwars.api.game.spawner.CustomDropTypeHandler;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * Called when the items that a player shall receive from a shop product are determined.
 *
 * @see ShopProduct#getGivingItems(Player, Team, Arena, int)
 * @see ShopProduct#give(Player, Team, Arena, int)
 */
public class PlayerShopProductGivingDetermineEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Team team;
  private final Arena arena;
  private final int multiplier;
  private final ShopProduct product;

  private ItemStack[] givingItems;
  private ExecutableCommand[] executingCommands;
  private CustomDropTypeHandler callingDropTypeHandler;

  public PlayerShopProductGivingDetermineEvent(
      Player player,
      @Nullable Team team,
      @Nullable Arena arena,
      int multiplier,
      ShopProduct product,
      ItemStack[] givingItems,
      ExecutableCommand[] executingCommands,
      @Nullable CustomDropTypeHandler callingDropTypeHandler) {

    super(player);

    this.team = team;
    this.arena = arena;
    this.multiplier = multiplier;
    this.product = product;
    this.givingItems = givingItems;
    this.executingCommands = executingCommands;
    this.callingDropTypeHandler = callingDropTypeHandler;
  }

  /**
   * Get the team the player is in.
   *
   * @return The team the player is in. May be <code>null</code> if the player is not in a team.
   */
  @Nullable
  public Team getTeam() {
    return this.team;
  }

  /**
   * Get the arena the player is in.
   *
   * @return The arena the player is in. May be <code>null</code> if the player is not in an arena.
   */
  @Nullable
  public Arena getArena() {
    return this.arena;
  }

/**
   * Get the multiplier of the shop product.
   *
   * @return How many times this product shall be given to the player. Should be at least 1, or otherwise no effect will be seen.
   */
  public int getMultiplier() {
    return this.multiplier;
  }

  /**
   * Get the shop product that the player is trying to buy.
   *
   * @return The shop product involved in this event.
   */
  public ShopProduct getShopProduct() {
    return this.product;
  }

  /**
   * Get the items that the player shall receive.
   * <p>
   *   The multiplier is already applied to the items.
   * </p>
   *
   * @return The items that the player shall receive.
   */
  public ItemStack[] getGivingItems() {
    return this.givingItems;
  }

  /**
   * Set the items that the player shall receive.
   * <p>
   *   The multiplier is already applied to the items.
   * </p>
   *
   * @param givingItems The items that the player shall receive.
   */
  public void setGivingItems(ItemStack[] givingItems) {
    Validate.notNullDeep(givingItems, "givingItems");

    this.givingItems = givingItems;
  }

  /**
   * Get the commands that shall be executed.
   *
   * @return The commands that shall be executed.
   */
  public ExecutableCommand[] getExecutingCommands() {
    return this.executingCommands;
  }

  /**
   * Set the commands that shall be executed.
   *
   * @param executingCommands The commands that shall be executed.
   */
  public void setExecutingCommands(ExecutableCommand[] executingCommands) {
    Validate.notNullDeep(executingCommands, "executingCommands");

    this.executingCommands = executingCommands;
  }

  /**
   * Get the custom drop type handlers that shall be called.
   *
   * @return The custom drop type handlers that shall be called. May be <code>null</code> if none shall be called
   */
  @Nullable
  public CustomDropTypeHandler getCallingDropTypeHandler() {
    return this.callingDropTypeHandler;
  }

  /**
   * Set the custom drop type handlers that shall be called.
   *
   * @param callingDropTypeHandler The custom drop type handlers that shall be called. May be <code>null</code> if none shall be called
   */
  public void setCallingDropTypeHandler(@Nullable CustomDropTypeHandler callingDropTypeHandler) {
    this.callingDropTypeHandler = callingDropTypeHandler;
  }


  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  /**
   * Represents that may be executed in the future.
   */
  public static class ExecutableCommand {

    private final String command;
    private final boolean asConsole;

    /**
     * Create a new executable command.
     *
     * @param command The command to be executed without the leading slash.
     * @param asConsole Whether the command shall be executed as console.
     */
    public ExecutableCommand(String command, boolean asConsole) {
      this.command = command;
      this.asConsole = asConsole;
    }

    /**
     * Get the command to be executed.
     *
     * @return The command to be executed without the leading slash.
     */
    public String getCommand() {
      return this.command;
    }

    /**
     * Whether the command shall be executed as console.
     *
     * @return Whether the command shall be executed as console.
     */
    public boolean isAsConsole() {
      return this.asConsole;
    }
  }
}
