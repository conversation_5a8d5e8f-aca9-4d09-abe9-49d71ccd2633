package de.marcely.bedwars.api.world.hologram.controller;

import de.marcely.bedwars.api.world.hologram.HologramController;
import de.marcely.bedwars.api.world.hologram.HologramControllerType;
import de.marcely.bedwars.api.world.hologram.HologramEntity;

/**
 * Extending API if {@link HologramControllerType#RANKING_STATUE} is used.
 * <p>
 *   Cast {@link HologramEntity#getController()} to this class to access the methods.
 * </p>
 */
public interface RankingStatueController extends HologramController {

  /**
   * Get the place of the player in the ranking.
   *
   * @return The place of the player in the ranking, may be a number between 1 and 3
   */
  int getPlace();

  /**
   * Set the place of the player in the ranking.
   * <p>
   *   Make sure to call {@link #applyProperties()} after changing the place to update the hologram.
   * </p>
   * <p>
   *   Must be between 1 and 3.
   * </p>
   *
   * @param place The place of the player in the ranking
   */
  void setPlace(int place);
}
