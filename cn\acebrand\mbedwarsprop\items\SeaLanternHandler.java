package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.attribute.Attribute;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

public class SeaLanternHandler implements SpecialItemUseHandler {

    private final Plugin plugin;
    private final ItemConfigManager.ItemConfig config;
    private final Map<Location, HealingBlock> healingBlocks = new HashMap<>();

    public SeaLanternHandler(Plugin plugin, ItemConfigManager.ItemConfig config) {
        this.plugin = plugin;
        this.config = config;
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    private class Session extends SpecialItemUseSession {

        private Block placedBlock;
        private HealingBlock healingBlock;

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            if (this.placedBlock == null)
                return;

            // 如果会话被强制停止，清理方块
            if (this.healingBlock != null) {
                this.healingBlock.cancel();
                healingBlocks.remove(this.placedBlock.getLocation());
            }

            this.placedBlock.setType(Material.STONE);
        }

        protected void run() {
            final Block clickedBlock = getEvent().getClickedBlock();
            final BlockFace clickedBlockFace = getEvent().getClickedBlockFace();

            // 检查是否有有效的目标方块
            if (clickedBlock == null || clickedBlockFace != BlockFace.UP) {
                getEvent().getPlayer().sendMessage("§c请指向一个有效的方块顶部来放置海景灯！");
                stop();
                return;
            }

            // 获取放置位置（目标方块上方）
            Block placeBlock = clickedBlock.getRelative(clickedBlockFace);

            // 检查放置位置是否为空气
            if (placeBlock.getType() != Material.AIR) {
                getEvent().getPlayer().sendMessage("§c无法在此处放置海景灯！");
                stop();
                return;
            }

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(getEvent().getPlayer());
            if (arena == null) {
                stop();
                return;
            }

            // 放置海景灯
            placeBlock.setType(Material.SEA_LANTERN);
            this.placedBlock = placeBlock;

            // 标记方块为玩家放置，这样游戏结束时会被清理
            arena.setBlockPlayerPlaced(placeBlock, true);

            // 获取配置值
            double healthAmount = 1.0;
            int foodAmount = 1;
            int range = 1;
            int duration = 30;

            if (config != null) {
                // 获取生命值恢复量
                ItemConfigManager.EffectConfig healthConfig = config.getEffect("health");
                if (healthConfig != null) {
                    healthAmount = healthConfig.getLevel();
                }

                // 获取饿饿度恢复量
                ItemConfigManager.EffectConfig foodConfig = config.getEffect("food");
                if (foodConfig != null) {
                    foodAmount = foodConfig.getLevel();
                }

                // 获取范围
                ItemConfigManager.EffectConfig rangeConfig = config.getEffect("range");
                if (rangeConfig != null) {
                    range = rangeConfig.getLevel();
                }

                // 获取持续时间
                ItemConfigManager.EffectConfig durationConfig = config.getEffect("duration");
                if (durationConfig != null) {
                    // 尝试使用 getDuration() 方法
                    int durationValue = durationConfig.getDuration();
                    if (durationValue > 0) {
                        duration = durationValue;
                    } else {
                        // 如果 duration 为 0，尝试使用 getLevel() 方法
                        int level = durationConfig.getLevel();
                        if (level > 0) {
                            duration = level;
                        }
                    }
                }

                // 输出调试信息
                plugin.getLogger().info("海景灯持续时间: " + duration + " 秒");
            }

            // 创建治疗方块
            this.healingBlock = new HealingBlock(
                    placeBlock,
                    getEvent().getPlayer().getUniqueId(),
                    arena,
                    healthAmount,
                    foodAmount,
                    range,
                    duration
            );

            // 添加到治疗方块列表
            healingBlocks.put(placeBlock.getLocation(), this.healingBlock);

            // 启动治疗任务
            this.healingBlock.start();

            getEvent().getPlayer().sendMessage("§a成功放置海景灯医疗站！将持续" + duration + "秒");

            // 消耗物品
            takeItem();
        }
    }

    private class HealingBlock {
        private final Block block;
        private final UUID ownerUUID;
        private final Arena arena;
        private final double healthAmount;
        private final int foodAmount;
        private final int range;
        private final int duration;
        private BukkitTask task;
        private boolean isCancelled = false;

        public HealingBlock(Block block, UUID ownerUUID, Arena arena, double healthAmount, int foodAmount, int range, int duration) {
            this.block = block;
            this.ownerUUID = ownerUUID;
            this.arena = arena;
            this.healthAmount = healthAmount;
            this.foodAmount = foodAmount;
            this.range = range;
            this.duration = duration;
        }

        public void start() {
            final AtomicInteger remainingSeconds = new AtomicInteger(this.duration);

            this.task = new BukkitRunnable() {
                @Override
                public void run() {
                    // 如果方块被破坏或游戏结束
                    if (isCancelled || block.getType() != Material.SEA_LANTERN || remainingSeconds.decrementAndGet() <= 0) {
                        // 变成石头
                        block.setType(Material.STONE);
                        healingBlocks.remove(block.getLocation());
                        cancel();
                        return;
                    }

                    // 治疗范围内的队友
                    for (Player player : block.getWorld().getPlayers()) {
                        // 检查玩家是否在范围内
                        if (player.getLocation().distance(block.getLocation()) <= range) {
                            // 获取玩家所在的竞技场
                            Arena playerArena = GameAPI.get().getArenaByPlayer(player);

                            // 检查是否在同一个竞技场
                            if (playerArena != null && playerArena.equals(arena)) {
                                // 获取玩家和方块所有者的队伍
                                Team playerTeam = playerArena.getPlayerTeam(player);
                                Player owner = plugin.getServer().getPlayer(ownerUUID);
                                Team ownerTeam = owner != null ? playerArena.getPlayerTeam(owner) : null;

                                // 如果是同一个队伍的玩家或者是方块的所有者
                                if (playerTeam != null && (ownerTeam == null || playerTeam.equals(ownerTeam) || player.getUniqueId().equals(ownerUUID))) {
                                    // 恢复生命值
                                    double maxHealth = player.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue();
                                    double newHealth = Math.min(player.getHealth() + healthAmount, maxHealth);
                                    player.setHealth(newHealth);

                                    // 恢复饥饿度
                                    int newFoodLevel = Math.min(player.getFoodLevel() + foodAmount, 20);
                                    player.setFoodLevel(newFoodLevel);
                                }
                            }
                        }
                    }
                }
            }.runTaskTimer(plugin, 20L, 20L); // 每秒执行一次
        }

        public void cancel() {
            if (this.task != null && !this.task.isCancelled()) {
                this.task.cancel();
            }
            this.isCancelled = true;
        }
    }
}
