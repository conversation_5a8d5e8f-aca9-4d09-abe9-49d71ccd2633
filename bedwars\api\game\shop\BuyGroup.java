package de.marcely.bedwars.api.game.shop;

import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Set;

/**
 * A BuyGroup is a system that allows a tier-like upgrading system in the shop
 */
public interface BuyGroup extends Cloneable {

  /**
   * Returns the name of this group.
   *
   * @return The name
   */
  String getName();

  /**
   * Returns a set with all levels.
   *
   * @return A set with all added levels
   */
  Set<Integer> getLevels();

  /**
   * Returns the lowest level of the {@link #getLevels()} Set.
   * <p>
   * 	This is also automatically the starting level.
   * </p>
   *
   * @return The lowest level of this group
   */
  int getLowestLevel();

  /**
   * Returns the highest level of the {@link #getLevels()} Set.
   * <p>
   * 	The max level the player can purchase.
   * </p>
   *
   * @return The highest level of this group
   */
  int getHighestLevel();

  /**
   * Returns a map with all items at that level.
   *
   * @param level The level on which we're checking it
   * @return A collection with all items on that level in this buy group
   */
  @Nullable Collection<? extends ShopItem> getItems(int level);

  /**
   * Whether items shall be displayed as a single one in the shop GUI.
   * <p>
   *     If <code>true</code>, then only the item that the player can buy on the given level will be shown.
   * </p>
   *
   * @return Whether the items shall be stacked in the shop GUI
   */
  boolean isStacked();

  /**
   * Define whether items shall be displayed as a single one in the shop GUI.
   * <p>
   *     If <code>true</code>, then only the item that the player can buy on the given level will be shown.
   * </p>
   *
   * @param stacked <code>true</code> if the items be stacked in the shop GUI
   */
  void setStacked(boolean stacked);

  /**
   * Checks whether this instance is a clone (as if it has been cloned using {@link #clone()}).
   * <p>
   *  BuyGroups are cloned in some cases (e.g. when a shop is opened) to allow you to modify the shop without affecting other arenas.
   * </p>
   *
   * @return whether this BuyGroup is a clone
   * @see #clone()
   * @see #getOriginal()
   */
  boolean isClone();

  /**
   * Returns the original "non-cloned" instance.
   * <p>
   *     This will return the original instance from which the clone has been created from.
   *     In case {@link #isClone()} returns false, the same instance is being returned.
   * </p>
   *
   * @return The original non-cloned instance
   * @see #isClone()
   * @see #clone()
   */
  BuyGroup getOriginal();

  /**
   * Returns a clone of this instance.
   *
   * @return A clone of this instance
   * @see #isClone()
   * @see #getOriginal()
   */
  BuyGroup clone();
}