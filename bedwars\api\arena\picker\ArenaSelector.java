package de.marcely.bedwars.api.arena.picker;

import de.marcely.bedwars.api.remote.RemoteArena;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * A selector effectively decides for the arena that shall be choosen given by a Collection.
 */
public interface ArenaSelector {

  /**
   * The type of this selector.
   *
   * @return The type
   */
  default ArenaSelectorType getType() {
    return ArenaSelectorType.PLUGIN;
  }

  /**
   * The plugin that created this instance.
   *
   * @return The plugin behind this
   */
  Plugin getPlugin();

  /**
   * The name of this selector.
   * <p>
   *     Must be in the following format: a-z, _, @, :<br>
   *     All characters also must be lower-cased
   * </p>
   *
   * @return The name
   */
  String getName();

  /**
   * Make this selector do its thing using this method.
   * <p>
   *     It is legal for him to apply changes to the <code>arenas</code> list (for performance reasons).
   * </p>
   *
   * @param arenas The arenas from which it shall decide from
   * @param cachedArena The previously choosen arena. Maybe be <code>null</code>
   * @return The arena that has been choosen. May be <code>null</code>
   */
  @Nullable
  RemoteArena run(List<? extends RemoteArena> arenas, @Nullable RemoteArena cachedArena);
}
