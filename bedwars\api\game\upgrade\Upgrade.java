package de.marcely.bedwars.api.game.upgrade;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.arena.picker.condition.ArenaConditionGroup;
import java.util.List;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a type of upgrade that holds all possible levels to which one may upgrade to
 */
public interface Upgrade {

  /**
   * The id used in the config for this upgrade
   *
   * @return the upgrade's id
   */
  String getId();

  /**
   * Weather or not this upgrade is repeatable. Repeatable upgrades
   * typically only have one upgrade level that can be purchased repeatedly
   *
   * @return whether the upgrade is repeatable
   */
  boolean isRepeatable();

  /**
   * Weather or not this upgrade auto-scales its pricing. Price auto scaling only
   * works if an upgrade is also a repeatable upgrade.
   *
   * @return whetherthe upgrade's price auto scales
   */
  boolean isPriceAutoScaling();

  /**
   * The ArenaConditionGroup that controls what arenas this Upgrade is visible in.
   *
   * @return The condition that states the arenas this Upgrade will be available in. <code>null</code> if the Upgrade should be displayed in every arena.
   */
  @Nullable ArenaConditionGroup getRestriction();

  /**
   * Lets you restrict this Upgrade, so only be available in certain arenas
   *
   * @param restriction The condition that states the arenas this Upgrade will be available in. <code>null</code> if the Upgrade should be displayed in every arena.
   */
  void setRestriction(@Nullable ArenaConditionGroup restriction);

  /**
   * Get whether this upgrade can even be applied to the given arena
   * <p>
   *   Only case where it's not applicable is if {@link #getRestriction()}
   *   is not <code>null</code> and the arena does not match the condition
   * </p>
   *
   * @param arena the arena to check
   * @return <code>true</code> if the upgrade can be applied to the arena, <code>false</code> otherwise
   */
  boolean isApplicable(Arena arena);

  /**
   * Gets the upgrade levels at a certain level
   *
   * @param level what level you are trying to get
   * @return Gets the upgrade level
   */
  @Nullable
  UpgradeLevel getLevel(int level);

  /**
   * Returns all UpgradeLevels attached to this upgrade
   *
   * @return returns all UpgradeLevels for this upgrade
   */
  List<UpgradeLevel> getLevels();

  /**
   * Returns the first level of this upgrade
   *
   * @return the first level of this upgrade
   */
  UpgradeLevel getMinLevel();

  /**
   * Returns the maximum level in this upgrade
   *
   * @return the maximum level of this upgrade
   */
  UpgradeLevel getMaxLevel();

  /**
   * Returns the current state of a player that indicates whether he is able to upgrade at this given moment.
   *
   * @param player The player looking to buy the upgrade
   * @param arena The arena the player is trying to buy the upgrade in
   * @param team The team the player is trying to buy the  upgrade for
   * @return The state for this specific case
   */
  State getState(Player player, Arena arena, Team team);


  /**
   * Represents the current upgrade state of a player
   */
  enum State {

    /**
     * He is missing materials
     */
    NOT_UPGRADEABLE,

    /**
     * He is theoretically able to upgrade
     */
    UPGRADEABLE,

    /**
     * He reached the max level
     */
    MAXIMUM
  }
}
