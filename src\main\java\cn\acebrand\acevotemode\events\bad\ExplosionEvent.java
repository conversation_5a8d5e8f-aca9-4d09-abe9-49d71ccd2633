package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.entity.Player;
import org.bukkit.Location;

/**
 * 爆炸事件 - 坏事件
 */
public class ExplosionEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    
    public ExplosionEvent(AceVoteMode plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // TODO: 实现爆炸逻辑
        player.sendMessage("§c[幸运方块] §f糟糕！发生了爆炸！");
        plugin.getLogger().info("执行爆炸事件: " + player.getName());
    }
    
    @Override
    public String getName() {
        return "EXPLOSION";
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public int getWeight() {
        return 15; // 权重
    }
}
