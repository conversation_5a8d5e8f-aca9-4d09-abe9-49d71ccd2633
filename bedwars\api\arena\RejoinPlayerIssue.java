package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.message.Message;
import java.util.Locale;
import org.jetbrains.annotations.Nullable;

/**
 * Reasons why a player has failed to rejoin a match.
 */
public enum RejoinPlayerIssue {

  /**
   * Rejoin process has already been started and is currently running for the player.
   */
  PROCESS_ALREADY_RUNNING,

  /**
   * The player is not participating in any game.
   */
  NOT_PARTICIPATING,

  /**
   * The game is not running anymore.
   */
  NOT_ACTIVE,

  /**
   * A new game is running in which the player wasn't present.
   */
  NOT_VALID_PLAYER,

  /**
   * {@link QuitPlayerMemory#isRejoinPermitted()} returns false.
   */
  NOT_PERMITTED,

  /**
   * Rejoin calculation took too long. Player went offline in the meantime.
   */
  WENT_OFFLINE,

  /**
   * The bed of the team has been destroyed whereby the player may not respawn anymore.
   */
  TEAM_DEAD,

  /**
   * <PERSON> didn't reply to rejoin request.
   */
  SERVER_OFFLINE,

  /**
   * He's already particpating in this or another arena.
   */
  ALREADY_INSIDE,

  /**
   * A custom cause.
   */
  PLUGIN;

  /**
   * Get the message that would be sent to the player.
   *
   * @return Gets a message instance that'd be sent with this given issue
   */
  public Message getMessage() {
    switch (this) {
      case PROCESS_ALREADY_RUNNING:
        return Message.buildByKey("Rejoin_AlreadyTrying");
      case NOT_ACTIVE:
      case NOT_VALID_PLAYER:
      case SERVER_OFFLINE:
      case NOT_PERMITTED:
        return Message.buildByKey("Rejoin_Failed_NotActive");
      case TEAM_DEAD:
        return Message.buildByKey("Rejoin_Failed_TeamDead");
      case NOT_PARTICIPATING:
        return Message.buildByKey("Rejoin_Failed_NotParticipating");
      case ALREADY_INSIDE:
        return Message.buildByKey("JoinMessage_alreadyInside");
      default:
        return Message.buildByKey("Rejoin_Failed_Unknown");
    }
  }

  @Nullable
  public static RejoinPlayerIssue byName(String name) {
    try {
      return RejoinPlayerIssue.valueOf(name.toUpperCase(Locale.ENGLISH));
    } catch (Exception e) {
      return null;
    }
  }
}