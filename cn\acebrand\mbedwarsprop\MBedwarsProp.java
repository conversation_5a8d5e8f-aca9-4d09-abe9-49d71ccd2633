package cn.acebrand.mbedwarsprop;

import cn.acebrand.mbedwarsprop.addon.BedwarsPropAddon;
import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import cn.acebrand.mbedwarsprop.items.DefenseTowerHandler;
import cn.acebrand.mbedwarsprop.items.EmeraldOreHandler;
import cn.acebrand.mbedwarsprop.items.EnderBuilderHandler;
import cn.acebrand.mbedwarsprop.items.FragileBedrockHandler;
import cn.acebrand.mbedwarsprop.items.JumpPadHandler;
import cn.acebrand.mbedwarsprop.items.MemoryAnchorHandler;
import cn.acebrand.mbedwarsprop.items.ResourceBoosterHandler;
import cn.acebrand.mbedwarsprop.items.SeaLanternHandler;
import cn.acebrand.mbedwarsprop.items.StringTrapHandler;
import cn.acebrand.mbedwarsprop.items.TeamCakeHandler;
import cn.acebrand.mbedwarsprop.listeners.EnderBuilderListener;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.java.JavaPlugin;

import org.bukkit.configuration.file.YamlConfiguration;
import cn.acebrand.mbedwarsprop.utils.SkullUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("deprecation") // 抑制所有过时警告
public class MBedwarsProp extends JavaPlugin {

    private BedwarsPropAddon addon;
    public ItemConfigManager itemConfigManager;

    public ItemConfigManager getItemConfigManager() {
        return itemConfigManager;
    }

    @Override
    public void onEnable() {
        // 检查MBedwars插件是否已加载
        if (!getServer().getPluginManager().isPluginEnabled("MBedwars")) {
            getLogger().severe("未找到MBedwars插件，禁用插件...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // 创建配置目录
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }

        // 创建items目录
        File itemsFolder = new File(getDataFolder(), "items");
        if (!itemsFolder.exists()) {
            itemsFolder.mkdirs();
        }

        // 保存默认配置文件
        try {
            if (!new File(getDataFolder(), "config.yml").exists()) {
                saveResource("config.yml", false);
            }
        } catch (Exception e) {
            getLogger().warning("无法保存默认配置文件: " + e.getMessage());
            // 创建一个空的配置文件
            try {
                new File(getDataFolder(), "config.yml").createNewFile();
            } catch (Exception ex) {
                getLogger().severe("无法创建配置文件: " + ex.getMessage());
            }
        }

        // 加载配置文件
        reloadConfig();

        // 初始化配置管理器
        this.itemConfigManager = new ItemConfigManager(this);

        // 注册附属
        this.addon = new BedwarsPropAddon(this);
        if (!this.addon.register()) {
            getLogger().severe("无法注册MBedwars附属，禁用插件...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // 注册特殊物品
        registerSpecialItems();

        // 注册事件监听器
        getServer().getPluginManager().registerEvents(new EnderBuilderListener(this), this);

        getLogger().info("MBedwarsProp 插件已成功启用！");
    }

    @Override
    public void onDisable() {
        // 取消注册附属
        if (this.addon != null && this.addon.isRegistered()) {
            this.addon.unregister();
        }

        getLogger().info("MBedwarsProp 插件已禁用！");
    }

    private void registerSpecialItems() {
        // 注册线陷阱
        registerStringTrap();

        // 注册海景灯
        registerSeaLantern();

        // 注册绿宝石矿石
        registerEmeraldOre();

        // 注册神奇的蛋糕
        registerTeamCake();

        // 注册脆弱的基岩
        registerFragileBedrock();

        // 注册末影建筑师
        registerEnderBuilder();

        // 注册跳跃平台
        registerJumpPad();

        // 注册资源加速器
        registerResourceBooster();

        // 注册防御塔
        registerDefenseTower();

        // 注册记忆锚点
        registerMemoryAnchor();
    }

    private void registerMemoryAnchor() {
        // 获取记忆锚点配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("memory_anchor");

        if (config == null) {
            getLogger().warning("无法加载记忆锚点配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack anchorItem = new ItemStack(config.getMaterial());
        ItemMeta meta = anchorItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        anchorItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem memoryAnchorItem = GameAPI.get().registerSpecialItem(
                "memory_anchor",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                anchorItem);

        // 如果注册成功，初始化处理器
        if (memoryAnchorItem != null) {
            // 创建记忆锚点处理器
            memoryAnchorItem.setHandler(new MemoryAnchorHandler(this));

            getLogger().info("记忆锚点特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加记忆锚点到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: memory_anchor");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: gold");
            getLogger().info("      amount: 12");
        } else {
            getLogger().warning("无法注册记忆锚点特殊物品！");
        }
    }

    private void registerDefenseTower() {
        // 获取防御塔配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("defense_tower");

        if (config == null) {
            getLogger().warning("无法加载防御塔配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack towerItem = new ItemStack(config.getMaterial());
        ItemMeta meta = towerItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        towerItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem defenseTowerItem = GameAPI.get().registerSpecialItem(
                "defense_tower",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                towerItem);

        // 如果注册成功，初始化处理器
        if (defenseTowerItem != null) {
            // 创建防御塔处理器
            defenseTowerItem.setHandler(new DefenseTowerHandler(this));

            getLogger().info("防御塔特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加防御塔到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: defense_tower");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: diamond");
            getLogger().info("      amount: 2");
        } else {
            getLogger().warning("无法注册防御塔特殊物品！");
        }
    }

    private void registerResourceBooster() {
        // 获取资源加速器配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("resource_booster");

        if (config == null) {
            getLogger().warning("无法加载资源加速器配置，使用默认配置");
            return;
        }

        // 获取配置的影响范围和持续时间，用于替换lore中的占位符
        int radius = config.getEffect("radius") != null ? config.getEffect("radius").getLevel() : 5;
        int duration = config.getEffect("duration") != null ? config.getEffect("duration").getDuration() : 60;

        // 创建物品图标
        ItemStack boosterItem = new ItemStack(config.getMaterial());
        ItemMeta meta = boosterItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            // 替换占位符 {radius} 和 {duration}
            String processedLine = line
                    .replace("{radius}", String.valueOf(radius))
                    .replace("{duration}", String.valueOf(duration));
            lore.add(ChatColor.translateAlternateColorCodes('&', processedLine));
        }
        meta.setLore(lore);

        boosterItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem resourceBoosterItem = GameAPI.get().registerSpecialItem(
                "resource_booster",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                boosterItem);

        // 如果注册成功，初始化处理器
        if (resourceBoosterItem != null) {
            // 创建资源加速器处理器
            resourceBoosterItem.setHandler(new ResourceBoosterHandler(this));

            getLogger().info("资源加速器特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加资源加速器到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: resource_booster");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: emerald");
            getLogger().info("      amount: 3");
        } else {
            getLogger().warning("无法注册资源加速器特殊物品！");
        }
    }

    private void registerJumpPad() {
        // 获取跳跃平台配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("jump_pad");

        if (config == null) {
            getLogger().warning("无法加载跳跃平台配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack jumpPadItem = new ItemStack(config.getMaterial());
        ItemMeta meta = jumpPadItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        jumpPadItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem jumpPadSpecialItem = GameAPI.get().registerSpecialItem(
                "jump_pad",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                jumpPadItem);

        // 如果注册成功，初始化处理器
        if (jumpPadSpecialItem != null) {
            // 创建跳跃平台处理器
            jumpPadSpecialItem.setHandler(new JumpPadHandler(this));

            getLogger().info("跳跃平台特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加跳跃平台到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: jump_pad");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: iron");
            getLogger().info("      amount: 24");
        } else {
            getLogger().warning("无法注册跳跃平台特殊物品！");
        }
    }

    private void registerStringTrap() {
        // 获取线陷阱配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("string_trap");

        if (config == null) {
            getLogger().warning("无法加载线陷阱配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack trapItem = new ItemStack(config.getMaterial());
        ItemMeta meta = trapItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        trapItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem stringTrapItem = GameAPI.get().registerSpecialItem(
                "string_trap",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                trapItem);

        // 如果注册成功，设置处理器
        if (stringTrapItem != null) {
            stringTrapItem.setHandler(new StringTrapHandler(this, config));
            getLogger().info("线陷阱特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加线陷阱到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: string_trap");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: iron");
            getLogger().info("      amount: 16");
        } else {
            getLogger().warning("无法注册线陷阱特殊物品！");
        }
    }

    private void registerSeaLantern() {
        // 获取海景灯配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("sea_lantern");

        if (config == null) {
            getLogger().warning("无法加载海景灯配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack lanternItem = new ItemStack(config.getMaterial());
        ItemMeta meta = lanternItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        lanternItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem seaLanternItem = GameAPI.get().registerSpecialItem(
                "sea_lantern",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                lanternItem);

        // 如果注册成功，设置处理器
        if (seaLanternItem != null) {
            seaLanternItem.setHandler(new SeaLanternHandler(this, config));
            getLogger().info("海景灯医疗站特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加海景灯医疗站到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: sea_lantern");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: emerald");
            getLogger().info("      amount: 2");
        } else {
            getLogger().warning("无法注册海景灯医疗站特殊物品！");
        }
    }

    private void registerEmeraldOre() {
        // 获取绿宝石矿石配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("emerald_ore");

        if (config == null) {
            getLogger().warning("无法加载绿宝石矿石配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack oreItem = new ItemStack(config.getMaterial());
        ItemMeta meta = oreItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        oreItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem emeraldOreItem = GameAPI.get().registerSpecialItem(
                "emerald_ore",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                oreItem);

        // 如果注册成功，设置处理器
        if (emeraldOreItem != null) {
            emeraldOreItem.setHandler(new EmeraldOreHandler(this, config));
            getLogger().info("绿宝石矿石特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加绿宝石矿石到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: emerald_ore");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: diamond");
            getLogger().info("      amount: 4");
        } else {
            getLogger().warning("无法注册绿宝石矿石特殊物品！");
        }
    }

    private void registerTeamCake() {
        // 获取神奇的蛋糕配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("team_cake");

        if (config == null) {
            getLogger().warning("无法加载神奇的蛋糕配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack cakeItem = new ItemStack(config.getMaterial());
        ItemMeta meta = cakeItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        cakeItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem teamCakeItem = GameAPI.get().registerSpecialItem(
                "team_cake",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                cakeItem);

        // 如果注册成功，设置处理器
        if (teamCakeItem != null) {
            teamCakeItem.setHandler(new TeamCakeHandler(this, config));
            getLogger().info("神奇的蛋糕特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加神奇的蛋糕到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: team_cake");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: gold");
            getLogger().info("      amount: 24");
        } else {
            getLogger().warning("无法注册神奇的蛋糕特殊物品！");
        }
    }

    private void registerFragileBedrock() {
        // 获取脆弱的基岩配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("fragile_bedrock");

        if (config == null) {
            getLogger().warning("无法加载脆弱的基岩配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack bedrockItem = new ItemStack(config.getMaterial());
        ItemMeta meta = bedrockItem.getItemMeta();

        // 设置名称
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        for (String line : config.getLore()) {
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        bedrockItem.setItemMeta(meta);

        // 注册特殊物品
        SpecialItem fragileBedrockItem = GameAPI.get().registerSpecialItem(
                "fragile_bedrock",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                bedrockItem);

        // 如果注册成功，设置处理器
        if (fragileBedrockItem != null) {
            fragileBedrockItem.setHandler(new FragileBedrockHandler(this, config));
            getLogger().info("脆弱的基岩特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加脆弱的基岩到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: fragile_bedrock");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: diamond");
            getLogger().info("      amount: 2");
        } else {
            getLogger().warning("无法注册脆弱的基岩特殊物品！");
        }
    }

    private void registerEnderBuilder() {
        // 获取末影建筑师配置
        ItemConfigManager.ItemConfig config = itemConfigManager.getItemConfig("ender_builder");

        if (config == null) {
            getLogger().warning("无法加载末影建筑师配置，使用默认配置");
            return;
        }

        // 创建物品图标
        ItemStack enderItem;

        // 检查是否使用头颅
        if (config.getMaterial().name().equals("PLAYER_HEAD")) {
            // 创建玩家头颅
            // 创建玩家头颅，使用兼容的方式
            enderItem = new ItemStack(Material.PLAYER_HEAD, 1);

            // 在新版本中，PLAYER_HEAD 不需要设置数据值
            SkullMeta skullMeta = (SkullMeta) enderItem.getItemMeta();

            // 检查是否有自定义皮肤的Base64编码值
            String textureValue = "";
            ItemConfigManager.EffectConfig textureConfig = config.getEffect("texture-value");
            if (textureConfig != null && !textureConfig.toString().isEmpty()) {
                textureValue = textureConfig.toString();
                // 使用工具类设置自定义皮肤
                if (SkullUtils.isValidTextureValue(textureValue)) {
                    SkullUtils.setSkullTexture(skullMeta, textureValue, this);
                } else {
                    getLogger().warning("无效的皮肤值: " + textureValue);
                    // 如果皮肤值无效，使用玩家名设置皮肤
                    String skullOwner = "";
                    ItemConfigManager.EffectConfig skullOwnerConfig = config.getEffect("skull-owner");
                    if (skullOwnerConfig != null && !skullOwnerConfig.toString().isEmpty()) {
                        skullOwner = skullOwnerConfig.toString();
                        // 使用新API设置头颅所有者
                        OfflinePlayer offlinePlayer = getServer().getOfflinePlayer(skullOwner);
                        skullMeta.setOwningPlayer(offlinePlayer);
                    } else {
                        // 如果没有指定皮肤，使用默认皮肤
                        // 使用新API设置头颅所有者
                        OfflinePlayer offlinePlayer = getServer().getOfflinePlayer("MHF_Enderman");
                        skullMeta.setOwningPlayer(offlinePlayer);
                    }
                }
            } else {
                // 如果没有自定义皮肤，检查是否有玩家名
                String skullOwner = "";
                ItemConfigManager.EffectConfig skullOwnerConfig = config.getEffect("skull-owner");
                if (skullOwnerConfig != null && !skullOwnerConfig.toString().isEmpty()) {
                    skullOwner = skullOwnerConfig.toString();
                    // 使用新API设置头颅所有者
                    OfflinePlayer offlinePlayer = getServer().getOfflinePlayer(skullOwner);
                    skullMeta.setOwningPlayer(offlinePlayer);
                } else {
                    // 如果没有指定皮肤，使用默认皮肤
                    // 使用新API设置头颅所有者
                    OfflinePlayer offlinePlayer = getServer().getOfflinePlayer("MHF_Enderman");
                    skullMeta.setOwningPlayer(offlinePlayer);
                }
            }

            // 设置名称
            skullMeta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

            // 设置描述
            List<String> lore = new ArrayList<>();
            for (String line : config.getLore()) {
                lore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            skullMeta.setLore(lore);

            enderItem.setItemMeta(skullMeta);
        } else {
            // 使用普通物品
            enderItem = new ItemStack(config.getMaterial());
            ItemMeta meta = enderItem.getItemMeta();

            // 设置名称
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', config.getName()));

            // 设置描述
            List<String> lore = new ArrayList<>();
            for (String line : config.getLore()) {
                lore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            meta.setLore(lore);

            enderItem.setItemMeta(meta);
        }

        // 注册特殊物品
        SpecialItem enderBuilderItem = GameAPI.get().registerSpecialItem(
                "ender_builder",
                this,
                ChatColor.translateAlternateColorCodes('&', config.getName()),
                enderItem);

        // 如果注册成功，设置处理器
        if (enderBuilderItem != null) {
            enderBuilderItem.setHandler(new EnderBuilderHandler(this, config));
            getLogger().info("末影建筑师特殊物品已注册！");

            // 输出商店配置示例
            getLogger().info("请在MBedwars的shop.yml中添加以下内容来添加末影建筑师到商店：");
            getLogger().info("- name: '" + ChatColor.translateAlternateColorCodes('&', config.getName()) + "'");
            if (config.getMaterial().name().equals("PLAYER_HEAD")) {
                getLogger().info("  icon: 'PLAYER_HEAD'");

                // 检查是否有自定义皮肤
                ItemConfigManager.EffectConfig textureConfig = config.getEffect("texture-value");
                if (textureConfig != null && !textureConfig.toString().isEmpty()) {
                    getLogger().info("  # 要在商店中使用自定义皮肤，请添加以下行");
                    getLogger().info("  texture: '" + textureConfig.toString() + "'");
                } else {
                    // 如果使用了玩家名，提示如何在商店中使用
                    ItemConfigManager.EffectConfig skullOwnerConfig = config.getEffect("skull-owner");
                    if (skullOwnerConfig != null && !skullOwnerConfig.toString().isEmpty()) {
                        getLogger().info("  skull-owner: '" + skullOwnerConfig.toString() + "'");
                    } else {
                        getLogger().info("  skull-owner: '%player%'");
                    }
                }
            } else {
                getLogger().info("  icon: '" + config.getMaterial().name() + "'");
            }
            getLogger().info("  products:");
            getLogger().info("    - type: special-item");
            getLogger().info("      special-id: ender_builder");
            getLogger().info("      amount: 1");
            getLogger().info("  prices:");
            getLogger().info("    - type: spawner-item");
            getLogger().info("      spawner-id: emerald");
            getLogger().info("      amount: 4");
        } else {
            getLogger().warning("无法注册末影建筑师特殊物品！");
        }
    }

}
