package cn.acebrand.acevotemode.config;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.model.GameMode;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

/**
 * 配置管理器
 * 负责加载和管理插件配置
 */
public class ConfigManager {
    
    private final AceVoteMode plugin;
    private FileConfiguration config;
    

    
    // 游戏模式配置
    private Map<String, GameMode> gameModes;
    
    // 投票设置
    private int voteDisableSeconds;
    private boolean showVoteCount;
    private int resultDisplayInterval;

    // 日志设置
    private boolean enableVoteLogs;
    private boolean enablePlayerLogs;
    private boolean enableArenaLogs;
    private boolean enableRandomSelectionLogs;

    // 消息配置
    private Map<String, String> messages;
    
    public ConfigManager(AceVoteMode plugin) {
        this.plugin = plugin;
        this.gameModes = new HashMap<>();
        this.messages = new HashMap<>();
    }
    
    /**
     * 加载所有配置
     */
    public void loadConfigs() {
        // 保存默认配置文件
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        config = plugin.getConfig();
        
        loadGameModesConfig();
        loadVoteSettings();
        loadLogSettings();
        loadMessages();
        
        plugin.getLogger().info("Configuration loaded successfully!");
    }
    

    
    /**
     * 加载游戏模式配置
     */
    private void loadGameModesConfig() {
        gameModes.clear();
        
        ConfigurationSection gameModesSection = config.getConfigurationSection("game-modes");
        if (gameModesSection == null) {
            plugin.getLogger().warning("game-modes section not found in config!");
            return;
        }
        
        for (String key : gameModesSection.getKeys(false)) {
            ConfigurationSection modeSection = gameModesSection.getConfigurationSection(key);
            if (modeSection == null) continue;
            
            boolean enabled = modeSection.getBoolean("enabled", true);
            if (!enabled) continue;
            
            String name = modeSection.getString("name", key);
            List<String> description = modeSection.getStringList("description");
            
            // 创建图标物品
            ItemStack icon = createIconFromConfig(modeSection.getConfigurationSection("icon"));
            
            GameMode gameMode = new GameMode(key, name, description, icon);
            gameModes.put(key, gameMode);
        }
        
        plugin.getLogger().info("Loaded " + gameModes.size() + " game modes");
    }
    
    /**
     * 从配置创建图标物品
     */
    private ItemStack createIconFromConfig(ConfigurationSection iconSection) {
        if (iconSection == null) {
            return new ItemStack(Material.STONE);
        }
        
        String materialName = iconSection.getString("material", "STONE");
        int amount = iconSection.getInt("amount", 1);
        
        Material material;
        try {
            material = Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid icon material: " + materialName + ", using STONE instead");
            material = Material.STONE;
        }
        
        return new ItemStack(material, amount);
    }
    
    /**
     * 加载投票设置
     */
    private void loadVoteSettings() {
        ConfigurationSection voteSettings = config.getConfigurationSection("vote-settings");
        if (voteSettings == null) {
            plugin.getLogger().warning("vote-settings section not found in config!");
            return;
        }

        voteDisableSeconds = voteSettings.getInt("vote-disable-seconds", 20);
        showVoteCount = voteSettings.getBoolean("show-vote-count", true);
        resultDisplayInterval = voteSettings.getInt("result-display-interval", 10);
    }

    /**
     * 加载日志设置
     */
    private void loadLogSettings() {
        ConfigurationSection logSettings = config.getConfigurationSection("log-settings");
        if (logSettings == null) {
            // 如果没有日志设置，使用默认值
            enableVoteLogs = true;
            enablePlayerLogs = false;
            enableArenaLogs = true;
            enableRandomSelectionLogs = true;
            return;
        }

        enableVoteLogs = logSettings.getBoolean("enable-vote-logs", true);
        enablePlayerLogs = logSettings.getBoolean("enable-player-logs", false);
        enableArenaLogs = logSettings.getBoolean("enable-arena-logs", true);
        enableRandomSelectionLogs = logSettings.getBoolean("enable-random-selection-logs", true);
    }
    
    /**
     * 加载消息配置
     */
    private void loadMessages() {
        messages.clear();
        
        ConfigurationSection messagesSection = config.getConfigurationSection("messages");
        if (messagesSection == null) {
            plugin.getLogger().warning("messages section not found in config!");
            return;
        }
        
        for (String key : messagesSection.getKeys(true)) {
            Object value = messagesSection.get(key);
            if (value instanceof String) {
                messages.put(key, ChatColor.translateAlternateColorCodes('&', (String) value));
            } else if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> list = (List<String>) value;
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < list.size(); i++) {
                    if (i > 0) sb.append("\n");
                    sb.append(ChatColor.translateAlternateColorCodes('&', list.get(i)));
                }
                messages.put(key, sb.toString());
            }
        }
    }
    
    // Getters
    public Map<String, GameMode> getGameModes() { return new HashMap<>(gameModes); }
    public int getVoteDisableSeconds() { return voteDisableSeconds; }
    public boolean isShowVoteCount() { return showVoteCount; }
    public int getResultDisplayInterval() { return resultDisplayInterval; }

    // 日志设置 Getters
    public boolean isEnableVoteLogs() { return enableVoteLogs; }
    public boolean isEnablePlayerLogs() { return enablePlayerLogs; }
    public boolean isEnableArenaLogs() { return enableArenaLogs; }
    public boolean isEnableRandomSelectionLogs() { return enableRandomSelectionLogs; }
    
    /**
     * 获取消息
     */
    public String getMessage(String key) {
        return messages.getOrDefault(key, "&c消息未找到: " + key);
    }
    
    /**
     * 获取带参数的消息
     */
    public String getMessage(String key, String... replacements) {
        String message = getMessage(key);
        for (int i = 0; i < replacements.length; i += 2) {
            if (i + 1 < replacements.length) {
                message = message.replace("{" + replacements[i] + "}", replacements[i + 1]);
            }
        }
        return message;
    }
}
