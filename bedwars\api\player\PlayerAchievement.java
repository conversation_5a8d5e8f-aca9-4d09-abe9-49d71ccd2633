package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.message.Message;
import org.bukkit.command.CommandSender;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

public interface PlayerAchievement {

  /**
   * Returns the id of this achievement
   *
   * @return The internal id of this achievement
   */
  String getId();

  /**
   * Returns the plugin that created this achievement
   *
   * @return The plugin that registered this achievement
   */
  Plugin getPlugin();

  /**
   * Returns the name of this achievement that'll get displayed e.g. when a player earns it<br>
   * Tip: Use {@link Message#buildByKey(String, String)} when you're saving the message in the messages file
   *
   * @param sender Shall return the name in the language of him. Can be null
   * @return The name of this achievement in the language of the sender
   */
  String getName(@Nullable CommandSender sender);

  /**
   * Returns the name of this achievement that'll get displayed e.g. when a player earns it
   *
   * @return The name of this achievement in the default language
   */
  default String getName() {
    return getName(null);
  }


  /**
   * Returns the description that contains info about earning it of this achievement that'll get displayed e.g. when a player earns it<br>
   * Tip: Use {@link Message#buildByKey(String, String)} when you're saving the message in the messages file
   *
   * @param sender Shall return the description in the language of him. Can be null
   * @return The description of this achievement in the language of the sender
   */
  String getDescription(@Nullable CommandSender sender);

  /**
   * Returns the description that contains info about earning it of this achievement that'll get displayed e.g. when a player earns it
   *
   * @return The description of this achievement in the default language
   */
  default String getDescription() {
    return getDescription(null);
  }

  /**
   * Get whether it has been enabled by the server administrator.
   * <p>
   *   Player's won't earn it and see it in relevant UIs it's disabled.
   *   The given state also resets itself with reloads as it loads the given
   *   information from the coressponding config file.
   * </p>
   *
   * @return <code>true</code> if it's enabled
   */
  boolean isEnabled();

  /**
   * Set whether it is enabled.
   * <p>
   *   Player's won't earn it and see it in relevant UIs it's disabled.
   *   The given state also resets itself with reloads as it loads the given
   *   information from the coressponding config file.
   * </p>
   *
   * @param enabled <code>true</code> if it's enabled
   */
  void setEnabled(boolean enabled);
}
