package de.marcely.bedwars.api.game.shop.price;

import de.marcely.bedwars.api.game.shop.ShopItem;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

public interface ShopPrice {

  /**
   * Returns the item to which the price has been added to
   *
   * @return The item to which the price was added to
   */
  ShopItem getItem();

  /**
   * Prices can vary from their type.
   * Meaning that one takes an item from the player while something else does something different
   *
   * @return The type of this price
   */
  ShopPriceType getType();

  /**
   * Returns the name of the price (e.g. "Iron", "Diamond") in the language of the sender
   *
   * @param sender The language of him. Uses the default language when passing null
   * @return The display name
   */
  String getDisplayName(@Nullable CommandSender sender);

  /**
   * Returns the name of the price (e.g. "Command", "Iron", "Dirt") in the default language
   *
   * @return The display name
   */
  default String getDisplayName() {
    return getDisplayName(null);
  }

  /**
   * Returns the name of the price (e.g. "Iron", "Diamond") in the language of the sender
   *
   * @param sender The language of him. Uses the default language when passing null
   * @param amount the amount of material required
   * @return The display name
   */
  String getDisplayName(@Nullable CommandSender sender, int amount);

  /**
   * Returns the name of the price (e.g. "Command", "Iron", "Dirt") in the default language
   *
   * @param amount the amount of material required
   * @return The display name
   */
  default String getDisplayName(int amount) {
    return getDisplayName(null, amount);
  }

  /**
   * Returns the item that'll be displayed in (shop) GUIs
   *
   * @param player Item might vary per player
   * @return The ItemStack that can be used for displaying in GUIs
   */
  ItemStack getDisplayItem(Player player);

  /**
   * Returns the amount that will be taken
   *
   * @param player Might vary per player
   * @return The amount
   */
  int getAmount(Player player);

  /**
   * Returns the general amount that will be taken
   *
   * @return The amount
   */
  int getGeneralAmount();

  /**
   * Set the general amount that will be taken from the player.<br>
   * Not any product type does support this operation, those who don't support it will return false
   *
   * @param amount The new amount
   * @return If this operation is being supported by the type
   */
  boolean setGeneralAmount(int amount);

  /**
   * Returns the amount of instances the player is holding (in his inventory)
   * <p>
   *  Example: If the player has 9 iron and the price for him is 2 iron, then this method returns 4.
   * </p>
   *
   * @param player The player we want to check
   * @param inv The theoretical inventory from which it shall be taken. <code>null</code> if it shall use the players inventory
   * @return The amount he's holding
   */
  int getHoldingAmount(Player player, @Nullable ItemStack[] inv);

  /**
   * Returns the amount of instances the player is holding (in his inventory)
   * <p>
   *  Example: If the player has 9 iron and the price for him is 2 iron, then this method returns 4.
   * </p>
   *
   * @param player The player we want to check
   * @return The amount he's holding
   */
  default int getHoldingAmount(Player player) {
    return getHoldingAmount(player, null);
  }

  /**
   * Returns the amount of instances (i.a. item) the player is missing to pay the price
   * <p>
   *   Example: If the player has 2 iron and the price for him is 5 iron, then this method returns 3.
   * </p>
   *
   * @param player The player we want to check
   * @param inv The theoretical inventory from which it shall be taken. <code>null</code> if it shall use the players inventory
   * @return The amount he's missing
   */
  int getMissingAmount(Player player, @Nullable ItemStack[] inv);

  /**
   * Returns the amount of instances (i.a. item) the player is missing to pay the price
   * <p>
   *   Example: If the player has 2 iron and the price for him is 5 iron, then this method returns 3.
   * </p>
   *
   * @param player The player we want to check
   * @return The amount he's missing
   */
  default int getMissingAmount(Player player) {
    return getMissingAmount(player, null);
  }

  /**
   * Takes the price from the player
   * <p>
   *  Might fail (returns <code>false</code>) when the player does not hold enough.
   * </p>
   *
   * @param player The target player
   * @param multiplier This multiplied with {@link #getAmount(Player)} equals the final amount that will be taken
   * @param inv The theoretical inventory from which it shall be taken. <code>null</code> if it shall use the players inventory
   * @return True if everything has been taken from him, otherwise false
   */
  boolean take(Player player, int multiplier, @Nullable ItemStack[] inv);

  /**
   * Takes the price from the player
   * <p>
   *  Might fail (returns <code>false</code>) when the player does not hold enough.
   * </p>
   *
   * @param player The target player
   * @param multiplier This multiplied with {@link #getAmount(Player)} equals the final amount that will be taken
   * @return True if everything has been taken from him, otherwise false
   */
  default boolean take(Player player, int multiplier) {
    return take(player, multiplier, null);
  }
}