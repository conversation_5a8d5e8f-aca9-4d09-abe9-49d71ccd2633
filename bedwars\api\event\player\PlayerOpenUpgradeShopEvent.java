package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.upgrade.UpgradeShopOpenCause;
import de.marcely.bedwars.api.game.upgrade.layout.UpgradeShopLayout;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when the player is opening the upgrade shop
 */
public class PlayerOpenUpgradeShopEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final UpgradeShopOpenCause cause;

  private final Arena arena;
  private final Team team;
  private UpgradeShopLayout layout;
  private Object layoutData;
  @Getter @Setter
  private boolean cancelled = false;

  public PlayerOpenUpgradeShopEvent(
      Player player,
      @Nullable Arena arena,
      Team team,
      UpgradeShopLayout layout,
      UpgradeShopOpenCause cause,
      @Nullable Object layoutData) {

    super(player);

    this.arena = arena;
    this.team = team;
    this.layout = layout;
    this.cause = cause;
    this.layoutData = layoutData;
  }

  /**
   * Returns the arena in which the shop was opened.
   * <p>
   * It's equal to the arena in which the player is playing.
   *
   * @return The arena in which the upgrade shop was opened
   */
  public @Nullable Arena getArena() {
    return arena;
  }

  /**
   * Returns the arena in which the shop was opened.
   * <p>
   * The team that the player is trying to buy upgrades for
   *
   * @return The team for which the upgrade shop was opened
   */
  public Team getTeam() {
    return team;
  }

  /**
   * Returns the layout that will be used for the GUI.
   *
   * @return The layout that shall be shown
   */
  public UpgradeShopLayout getLayout() {
    return this.layout;
  }

  /**
   * Set the layout of the shop GUI that shall be shown.
   *
   * @param layout The layout that shall be shown to the player
   */
  public void setLayout(UpgradeShopLayout layout) {
    Validate.notNull(layout, "layout");

    this.layout = layout;
  }

  /**
   * A layout may hold and pass around data during its session.
   * <p>
   * This might be useful when you're trying to manipulate the layout.
   *
   * @return The layout specific data
   */
  @Nullable
  public Object getLayoutData() {
    return this.layoutData;
  }

  /**
   * A layout may hold and pass around data during its session.
   * <p>
   * This might be useful when you're trying to manipulate the layout.
   * Keep in mind that errors might occur with the layout when inserting unexpected data.
   *
   * @param layoutData The new layout data
   */
  public void setLayoutData(@Nullable Object layoutData) {
    this.layoutData = layoutData;
  }

  /**
   * Returns the way how the player opened the upgrade shop
   *
   * @return The cause of the opening
   */
  public UpgradeShopOpenCause getCause() {
    return this.cause;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
