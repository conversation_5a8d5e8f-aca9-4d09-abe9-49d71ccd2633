package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.Random;

/**
 * 瞬间爆炸事件
 * 在玩家位置产生爆炸
 */
public class InstantExplosionEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    
    public InstantExplosionEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(badDir, "instant_explosion.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/instant_explosion.yml", false);
                plugin.getLogger().info("已生成瞬间爆炸事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载瞬间爆炸事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载瞬间爆炸事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public String getName() {
        return "INSTANT_EXPLOSION";
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 35) : 35;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }
        
        // 获取配置参数
        float power = (float) (config != null ? config.getDouble("explosion.power", 4.0) : 4.0);
        boolean breakBlocks = config != null ? config.getBoolean("explosion.break_blocks", true) : true;
        boolean setFire = config != null ? config.getBoolean("explosion.set_fire", true) : true;
        int delay = config != null ? config.getInt("explosion.delay", 10) : 10;
        double damageMultiplier = config != null ? config.getDouble("explosion.damage_multiplier", 1.0) : 1.0;
        double positionOffset = config != null ? config.getDouble("explosion.position_offset", 0.0) : 0.0;
        
        // 计算爆炸位置
        Location explosionLocation = player.getLocation().clone();
        if (positionOffset > 0) {
            double offsetX = (random.nextDouble() - 0.5) * 2 * positionOffset;
            double offsetZ = (random.nextDouble() - 0.5) * 2 * positionOffset;
            explosionLocation.add(offsetX, 0, offsetZ);
        }
        
        // 延迟爆炸
        if (delay > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    createExplosion(explosionLocation, power, breakBlocks, setFire, damageMultiplier);
                }
            }.runTaskLater(plugin, delay);
        } else {
            createExplosion(explosionLocation, power, breakBlocks, setFire, damageMultiplier);
        }
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了瞬间爆炸事件");
    }
    
    /**
     * 创建爆炸
     */
    private void createExplosion(Location location, float power, boolean breakBlocks, 
                               boolean setFire, double damageMultiplier) {
        try {
            // 创建爆炸
            location.getWorld().createExplosion(location, power, setFire, breakBlocks);
            
            // 如果需要额外伤害倍数，对附近玩家造成额外伤害
            if (damageMultiplier != 1.0) {
                double radius = power * 2; // 爆炸影响半径
                for (Player nearbyPlayer : location.getWorld().getPlayers()) {
                    if (nearbyPlayer.getLocation().distance(location) <= radius) {
                        double distance = nearbyPlayer.getLocation().distance(location);
                        double damage = (power * 2) * (1 - distance / radius) * damageMultiplier;
                        if (damage > 0) {
                            nearbyPlayer.damage(damage);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            plugin.getLogger().warning("创建爆炸失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null) return true;
        return config.getBoolean("messages.send_message", true);
    }
    
    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null) return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }
    
    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null) return "§c轰！！！";
        return config.getString("messages.event_message", "§c轰！！！");
    }
}
