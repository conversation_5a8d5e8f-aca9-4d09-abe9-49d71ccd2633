package de.marcely.bedwars.api.world.block;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.Validate;

/**
 * A sign which makes the player join the arena once he interacts with it
 */
public interface JoinArenaSignBlock extends SpecialBlock {

  /**
   * Returns the name of the arena or arena picker to which the player will be added
   *
   * @return The name of the arena that this block represents
   */
  String getArenaName();

  /**
   * Set the new arena or arena picker to which shall be added once he interacts with the block
   *
   * @param name The new arena name
   */
  void setArenaName(String name);

  /**
   * Set the new arena to which the player shall be added once he interacts with the block
   *
   * @param arena The new arena
   */
  default void setArena(Arena arena) {
    Validate.notNull(arena, "arena");

    setArenaName(arena.getName());
  }
}