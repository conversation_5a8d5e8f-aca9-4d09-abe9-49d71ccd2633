package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.Validate;
import java.time.Duration;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when the lobby countdown is supposed to start
 * <p>
 *   This method might be called frequently whenever the requirements
 *   (more players than min players ({@link Arena#getMinPlayers()})) are reached, but previous attempts
 *   have been cancelled.
 * </p>
 */
public class ArenaLobbyCountdownStartEvent extends Event implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;

  private Duration duration;

  @Getter @Setter
  private boolean cancelled = false;

  public ArenaLobbyCountdownStartEvent(Arena arena, Duration duration) {
    this.arena = arena;
    this.duration = duration;
  }

  /**
   * Get how long countdown will count down until the match starts.
   *
   * @return The duration of the countdown
   */
  public Duration getDuration() {
    return this.duration;
  }

  /**
   * Set how long countdown will count down until the match starts.
   *
   * @param duration The new duration of the countdown.
   */
  public void setDuration(Duration duration) {
    Validate.notNull(duration, "duration");

    this.duration = duration;
  }

  @Override
  public Arena getArena() {
    return this.arena;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
