package de.marcely.bedwars.api.hook;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import java.util.Collection;
import org.bukkit.plugin.Plugin;

/**
 * Aggregates a collection of APIs that are used to maintain a list for features that MBedwars makes use of.
 */
public interface HookAPI {

  /**
   * Gets all hooks that currently are active.
   *
   * @return All active hooks
   */
  Collection<Hook> getHooks();

  /**
   * Get all hooks that hook into the given plugin.
   *
   * @param plugin The plugin into which a hook is hooking into
   * @return A collection of all hooks into that plugin. Empty if there are none
   */
  Collection<Hook> getHooksByHooked(Plugin plugin);

  /**
   * Get all hooks that are being managed by the given plugin.
   *
   * @param plugin The plugin that created the hooks
   * @return A collection of hooks that are being managed. Empty if there are none
   */
  Collection<Hook> getHooksByManaging(Plugin plugin);

  /**
   * Returns the names of all plugin with which we can create a hook.
   * <p>
   *     Includes names of plugins that aren't installed on the server.
   * </p>
   *
   * @return Names of all plugins that we can hook into
   * @see #tryAutoHook(Plugin)
   */
  String[] getAutoPluginNames();

  /**
   * Tries to let MBedwars automatically initiate a hook with a plugin.
   * <p>
   *     Reasons for failure:<br>
   *      - Plugin is already hooked<br>
   *      - {@link de.marcely.bedwars.api.event.PluginHookEvent} got cancelled<br>
   *      - Plugin is not supported (not hookable, version/version not supported)<br>
   *      - An error occured while we tried to hook into it<br>
   *      - It may only be hooked into after all arenas have been loaded<br>
   *      - Plugin is not enabled
   * </p>
   *
   * @param plugin The plugin with a hook shall be created
   * @return A list of the hooks that we created. May be empty if there are none
   * @see #getAutoPluginNames()
   */
  Collection<? extends Hook> tryAutoHook(Plugin plugin);

  /**
   * Removes a hook.
   *
   * @param hook The hook that shall be removed
   * @return <code>true</code> when we succeded
   */
  boolean unregisterHook(Hook hook);

  /**
   * Get all currently registered parties hooks.
   * <p>
   *   This also includes the ones that MBedwars registers (e.g. Parties, Parties and Friends ...)
   * </p>
   *
   * @return All the currently registered parties hooks
   */
  PartiesHook[] getPartiesHooks();

  /**
   * Manually register a new parties hook.
   * <p>
   *   You may use {@link #unregisterHook(Hook)} to remove the hook again.
   * </p>
   *
   * @param hook The hook that shall be registered
   * @return <code>false</code> if it is already registered. Otherwise <code>true</code>
   * @throws IllegalStateException In case the class is incorrectly being handled
   */
  boolean registerPartiesHook(PartiesHook hook);

  /**
   * Get all currently registered nicknaming hooks.
   * <p>
   *   This also includes the ones that MBedwars registers (e.g. Nicknamer, NameTagEdit, CMI ...)
   * </p>
   *
   * @return All the currently registered nicknaming hooks
   */
  NicknamingHook[] getNicknamingHooks();

  /**
   * Manually register a new nicknaming hook.
   * <p>
   *   You may use {@link #unregisterHook(Hook)} to remove the hook again.
   * </p>
   *
   * @param hook The hook that shall be registered
   * @return <code>false</code> if it is already registered. Otherwise <code>true</code>
   * @throws IllegalStateException In case the class is incorrectly being handled
   */
  boolean registerNicknamingHook(NicknamingHook hook);

  /**
   * Get all currently registered cloud system hooks.
   * <p>
   *   This also includes the ones that MBedwars registers (e.g. CloudNet, CaveCloud ...)
   * </p>
   *
   * @return All the currently registered cloud system hooks
   */
  CloudSystemHook[] getCloudSystemHooks();

  /**
   * Manually register a new cloud system hook.
   * <p>
   *   You may use {@link #unregisterHook(Hook)} to remove the hook again.
   * </p>
   *
   * @param hook The hook that shall be registered
   * @return <code>false</code> if it is already registered. Otherwise <code>true</code>
   * @throws IllegalStateException In case the class is incorrectly being handled
   */
  boolean registerCloudSystemHook(CloudSystemHook hook);

  /**
   * Returns the global HookAPI instance.
   *
   * @return The global HookAPI instance
   */
  static HookAPI get() {
    return BedwarsAPILayer.INSTANCE.getHookAPI();
  }
}
