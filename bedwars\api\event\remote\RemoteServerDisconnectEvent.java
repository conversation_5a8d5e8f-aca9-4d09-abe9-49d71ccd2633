package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemoteServer;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called whenever we disconnected with another server.
 * <p>
 *     Keep in mind that this event is async.
 * </p>
 */
public class RemoteServerDisconnectEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemoteServer server;
  private final Cause cause;

  public RemoteServerDisconnectEvent(RemoteServer server, Cause cause) {
    super(true);

    this.server = server;
    this.cause = cause;
  }

  /**
   * Gets the server that has disconnected.
   *
   * @return The involved server
   */
  public RemoteServer getServer() {
    return this.server;
  }

  /**
   * Gets the cause of his disconnection.
   *
   * @return The reason behind him disconnecting
   */
  public Cause getCause() {
    return this.cause;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  /**
   * The reasons why no connection is further kept with this server.
   */
  public enum Cause {

    /**
     * We haven't received any ping responses since a while.
     * <p>
     *     Server possibly lost internet connection or stopped without notifiying about a {@link #SHUTDOWN}.
     * </p>
     */
    TIMEOUT,

    /**
     * The remote server hasn't received any ping responses from us since a while and timed us out.
     * <p>
     *   This is a really rare edge case, but actually occurred in the past. Main reason is likely that the used protocol
     *   server was overloaded for a longer period.
     * </p>
     */
    SELF_TIMEOUT,

    /**
     * Server notified us that the connection loss is wanted.
     */
    SHUTDOWN,

    /**
     * Server relogged while we thought that it was still alive.
     * <p>
     *     The {@link RemoteServerConnectEvent} will get called immediately after this event.
     * </p>
     */
    RECONNECT
  }
}