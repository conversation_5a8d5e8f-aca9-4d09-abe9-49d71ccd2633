package de.marcely.bedwars.api.hook;

import de.marcely.bedwars.tools.Validate;
import java.util.Collection;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a class that hooks into a Parties system.
 * <p>
 *   Parties are basically groups that players may create to easily join together into an arena.
 *   MBedwars makes use of this by automatically teleporting all members once the leader joins an arena.
 * </p>
 */
public interface PartiesHook extends Hook {

  default HookCategory getCategory() {
    return HookCategory.PARTIES;
  }

  /**
   * Asynchronously receive the info about a member and its party in which the player may be a part of.
   * <p>
   *   The callback must not return on the main thread.
   *   This method may be called for both members and the leader.
   *   In case the player is not a part of a party, the callback returns {@link Optional#empty()}.
   * </p>
   * <p>
   *   The method must not return an instance that is being automatically updated!
   *   It may also just be a snapshot of when the method was called.
   * </p>
   * <p>
   *   <b>Warning for PaF Bungee:</b> This plugin requires the player to be online on this server due to how
   *   it and the BungeeCord messaging system works.
   * </p>
   *
   * @param playerUUID The UUID of the player
   * @param callback The callback containing the info about the player
   */
  void getMember(UUID playerUUID, Consumer<Optional<Member>> callback);

  /**
   * Asynchronously receive the info about a member and its party in which the player may be a part of.
   * <p>
   *   The callback must not return on the main thread.
   *   This method may be called for both members and the leader.
   *   In case the player is not a part of a party, the callback returns {@link Optional#empty()}.
   * </p>
   * <p>
   *   The method must not return an instance that is being automatically updated!
   *   It may also just be a snapshot of when the method was called.
   * </p>
   * <p>
   *   <b>Warning for PaF Bungee:</b> This plugin requires the player to be online on this server due to how
   *   it and the BungeeCord messaging system works.
   * </p>
   * <p>
   *   This method basically just calls {@link #getMember(UUID, Consumer)}.
   * </p>
   *
   * @param player The player we want to check
   * @param callback The callback containing the info about the player
   */
  default void getMember(Player player, Consumer<Optional<Member>> callback) {
    Validate.notNull(player, "player");

    getMember(player.getUniqueId(), callback);
  }


  /**
   * Represents a party that holds various members.
   */
  public interface Party {

    /**
     * Get all the members that are a part of this party.
     *
     * @param includeLeaders Whether the leaders shall be counted as well or not
     * @return All members of the party +- the leaders
     */
    Collection<Member> getMembers(boolean includeLeaders);

    /**
     * Get the leaders of the party.
     *
     * @return All the leaders that maintain this party
     */
    Collection<Member> getLeaders();

    /**
     * Get a member (or leader) by its id.
     *
     * @param playerUUID The UUID of the member
     * @return The member that matches the UUID. May be <code>null</code> if there is none
     */
    @Nullable
    default Member getMember(UUID playerUUID) {
      Validate.notNull(playerUUID, "playerUUID");

      for (Member member : getMembers(true)) {
        if (member.getUniqueId().equals(playerUUID))
          return member;
      }

      return null;
    }
  }

  /**
   * Represents a member of a party.
   */
  public interface Member {

    /**
     * Get the party of which the member is a part of.
     *
     * @return The party of which he is a part of
     */
    Party getParty();

    /**
     * The UUID of the player ({@link Player#getUniqueId()}.
     *
     * @return The UUID of the player
     */
    UUID getUniqueId();

    /**
     * The name of the player ({@link Player#getName()}.
     *
     * @return The name of the player
     */
    String getUsername();

    /**
     * Get whether he is a leader of the party or not.
     *
     * @return <code>true</code> in case he is a leader and maintains the party
     */
    boolean isLeader();
  }
}