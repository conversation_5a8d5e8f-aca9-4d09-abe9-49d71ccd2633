package de.marcely.bedwars.api.game.upgrade;

import org.jetbrains.annotations.Nullable;

/**
 * Represents the type of {@link UpgradeTriggerHandler}.
 * <p>
 *  Custom ones use {@link #PLUGIN}
 * </p>
 */
public enum UpgradeTriggerHandlerType {

  /**
   * Adds the Sharpness enchantment to swords and axes
   */
  SHARPNESS("bedwars:sharpness"),

  /**
   * Adds the Protection enchantment to armor
   */
  ARMOR_PROTECTION("bedwars:armor_protection"),

  /**
   * Adds the Feather Fall enchantment to boots
   */
  FEATHER_FALL("bedwars:feather_fall"),

  /**
   * Gives the Haste effect
   */
  HASTE("bedwars:haste"),

  /**
   * Increases the speed of nearby spawners
   */
  SPAWNER_MULTIPLIER("bedwars:spawner_multiplier"),

  /**
   * Gives the regeneration effect near your base
   */
  HEAL_POOL("bedwars:heal_pool"),

  /**
   * Gives an infiltrating player blindness and slowness
   */
  REGULAR_TRAP("bedwars:regular_trap"),

  /**
   * Gives players Speed and Jump Boost when there is an infiltrating player
   */
  COUNTER_OFFENSIVE_TRAP("bedwars:counter_offensive_trap"),

  /**
   * Reveals infiltrating players who are invisible
   */
  ALARM_TRAP("bedwars:alarm_trap"),

  /**
   * Gives mining fatigue to infuriating players
   */
  MINING_FATIGUE_TRAP("bedwars:mining_fatigue_trap"),

  /**
   * A custom type created by something accessing the API.
   */
  PLUGIN(null);

  private final String id;

  UpgradeTriggerHandlerType(@Nullable String id) {
    this.id = id;
  }

  /**
   * Returns the id that's being used for the handler.
   * <code>null</code> is being returned when passing {@link #PLUGIN}.
   *
   * @return The id of this handler type
   */
  public @Nullable String getId() {
    return this.id;
  }
}
