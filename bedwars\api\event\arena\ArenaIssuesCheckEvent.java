package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.RegenerationType;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import de.marcely.bedwars.tools.Validate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

import java.util.Set;

/**
 * Gets called when the plugin is checking whether or not an arena has issues.
 * <p>
 * This can occur for any reason, but usually happens with {@link ArenaEnableEvent}
 */
public class ArenaIssuesCheckEvent extends Event implements ArenaEvent {


  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Set<Issue> issues;
  private final CommandSender sender;
  private final CommandSenderWrapper senderWrapped;

  public ArenaIssuesCheckEvent(Arena arena, Set<Issue> issues, @Nullable CommandSenderWrapper sender) {
    this.arena = arena;
    this.issues = issues;
    this.sender = sender != null ? sender.getCommandSender() : null;
    this.senderWrapped = sender;
  }

  /**
   * Returns if the arena has any issues and by that won't be able to start correctly
   *
   * @return <code>true</code> if it has one or more issues
   */
  public boolean hasIssues() {
    return !this.issues.isEmpty();
  }

  /**
   * Returns every issue that prevents the arena from being enabled
   *
   * @return Every issue that the arena has
   */
  public Set<Issue> getIssues() {
    return this.issues;
  }

  /**
   * Add an issue which prevents the arena from being enabled
   *
   * @param issue The issue that the arena has
   */
  public void addIssue(Issue issue) {
    Validate.notNull(issue, "issue");

    this.issues.add(issue);
  }

  /**
   * Remove a specific issue from this event
   *
   * @param issue The issue that should be removed
   * @return returns true if the issue was successfully removed
   */
  public boolean removeIssue(Issue issue) {
    Validate.notNull(issue, "issue");

    return this.issues.remove(issue);
  }

  /**
   * Removes every issue and causes the arena to be enabled anyways
   */
  public void removeIssues() {
    this.issues.clear();
  }

  /**
   * Returns the person who initiated the check.
   * May return <code>null</code> when a system initiated that (or e.g. someone remotely)
   *
   * @return The person who tried to enable the arena
   */
  @Nullable
  public CommandSender getSender() {
    return this.sender;
  }

  /**
   * Returns the person who initiated the check.
   * May return <code>null</code> when a system initiated that
   *
   * @return The person who tried to enable the arena
   */
  @Nullable
  public CommandSenderWrapper getSenderWrapped() {
    return this.senderWrapped;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }

  /**
   * Things preventing the arena from getting enabled
   */
  @EqualsAndHashCode
  public static class Issue {

    private final String message, detail;
    private final IssueType type;

    public Issue(String message, @Nullable String detail) {
      this(message, detail, null);
    }

    public Issue(String message, @Nullable IssueType type) {
      this(message, null, type);
    }

    public Issue(String message, @Nullable String detail, @Nullable IssueType type) {
      this.message = message;
      this.detail = detail;
      this.type = type != null ? type : IssueType.PLUGIN;
    }

    public String getMessage() {
      return this.message;
    }

    /**
     * A more deeper info about what item or hologram caused it
     *
     * @return The detail of the message
     */
    public @Nullable String getDetail() {
      return this.detail;
    }

    /**
     * Returns the type of the issue to differentiate it from others
     *
     * @return The type
     */
    public IssueType getType() {
      return this.type;
    }
  }

  /**
   * Represents the type of an issue.
   */
  public enum IssueType {

    /**
     * A bed of a team is missing.
     */
    MISSING_TEAM_BED,

    /**
     * The spawnpoint of a team is missing.
     */
    MISSING_TEAM_SPAWN,

    /**
     * No (waiting) lobby is missing.
     */
    MISSING_LOBBY,

    /**
     * The hub-location (or also known as game-done-location) is missing.
     */
    MISSING_GAME_DONE_LOCATION,

    /**
     * The game-world is either missing or not loaded.
     */
    MISSING_GAME_WORLD,

    /**
     * No corners have been defined for region arenas.
     */
    MISSING_CORNERS,

    /**
     * The region's maxY is greater than the world's max height.
     */
    REGION_GREATER_THAN_WORLD_MAX_HEIGHT,

    /**
     * The region's minY is less than the world's min height.
     */
    REGION_LESS_THAN_WORLD_MIN_HEIGHT,

    /**
     * The arena's {@link RegenerationType} is WORLD, and the arena's world is the server's main world.
     */
    GAME_WORLD_IS_MAIN_WORLD,

    /**
     * The game world has been set, but it's not loaded.
     */
    GAME_WORLD_NOT_LOADED,

    /**
     * The match area of the arena collides with other arenas.
     */
    MATCH_AREA_COLLIDES,

    /**
     * The hub-location (or also known as game-done-location) is inside the match area.
     * <p>
     *   The reason why this is a problem is because non-op players may not get teleported back to the hub.
     * </p>
     */
    MATCH_AREA_INTERFERES_GAME_DONE_LOCATION,

    /**
     * A plugin added a custom issue.
     */
    PLUGIN;

    /**
     * Get the error message that would be displayed using this issue-type.
     * <p>
     *   It only returns <code>null</code> for {@link #PLUGIN}.
     * </p>
     *
     * @return The message that'd get displayed. May be <code>null</code>
     */
    @Nullable
    public Message getMessage() {
      switch (this) {
        case MISSING_TEAM_BED:
          return Message.buildByKey("CrashMessage_MissingBed");
        case MISSING_TEAM_SPAWN:
          return Message.buildByKey("CrashMessage_MissingTeamSpawn");
        case MISSING_LOBBY:
          return Message.buildByKey("CrashMessage_MissingLobbyLocation");
        case MISSING_GAME_DONE_LOCATION:
          return Message.buildByKey("CrashMessage_MissingGameDoneLocation");
        case MISSING_GAME_WORLD:
          return Message.buildByKey("CrashMessage_MissingGameWorld");
        case MISSING_CORNERS:
          return Message.buildByKey("CrashMessage_MissingCorners");
        case REGION_GREATER_THAN_WORLD_MAX_HEIGHT:
          return Message.buildByKey("CrashMessage_UpperCornerOutOfBounds");
        case REGION_LESS_THAN_WORLD_MIN_HEIGHT:
          return Message.buildByKey("CrashMessage_LowerCornerOutOfBounds");
        case GAME_WORLD_IS_MAIN_WORLD:
          return Message.buildByKey("CrashMessage_WorldTypeArenaInMainWorld");
        case GAME_WORLD_NOT_LOADED:
          return Message.buildByKey("CrashMessage_NotLoadedGameWorld");
        case MATCH_AREA_COLLIDES:
          return Message.buildByKey("CrashMessage_MatchAreaColliding");
        case MATCH_AREA_INTERFERES_GAME_DONE_LOCATION:
          return Message.buildByKey("CrashMessage_GameDoneLocationInterfering");
      }

      return null;
    }

    /**
     * Construct an issue instance from this type.
     *
     * @param sender The sender to which this shall be displayed to for auto-translation. May be <code>null</code>
     * @return The issue instance
     * @throws IllegalStateException If done for {@link #PLUGIN}
     */
    public Issue construct(@Nullable CommandSenderWrapper sender) {
      return construct(sender, null);
    }

    /**
     * Construct an issue instance from this type.
     *
     * @param sender The sender to which this shall be displayed to for auto-translation. May be <code>null</code>
     * @param detail The optional, additional detail that'd get displayed. May be <code>null</code>
     * @return The issue instance
     * @throws IllegalStateException If done for {@link #PLUGIN}
     */
    public Issue construct(@Nullable CommandSenderWrapper sender, @Nullable String detail) {
      final Message msg = getMessage();

      if (msg == null)
        throw new IllegalStateException("You may not construct an issue for " + this.name() + " using this method");

      return new Issue(
          getMessage().done(sender),
          detail,
          this
      );
    }
  }
}
