package de.marcely.bedwars.api.game.specialitem;

import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import org.bukkit.plugin.Plugin;

public interface SpecialItemUseHandler {

  /**
   * Returns the plugin that's in charge of this handler
   *
   * @return The plugin that created this handler
   */
  Plugin getPlugin();

  /**
   * Use {@link SpecialItemUseSession#SpecialItemUseSession(PlayerUseSpecialItemEvent)} and pass it back to this event<br>
   * Make sure to call {@link SpecialItemUseSession#stop()} when the item did its thing<br>
   * Also optionally call {@link SpecialItemUseSession#takeItem()} when needed
   *
   * @param event The informations needed to handle the event
   * @return The session containing the actual handler of the item
   */
  SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event);
}
