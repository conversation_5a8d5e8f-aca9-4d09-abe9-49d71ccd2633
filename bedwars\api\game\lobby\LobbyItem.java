package de.marcely.bedwars.api.game.lobby;

import org.bukkit.command.CommandSender;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * Represents an actual item that will be added into the hotbar to players during the lobby phase
 */
public interface LobbyItem {

  /**
   * Returns {@link #getConfigName()}, but formats it into the default configured language.
   *
   * @return The name in the default language
   */
  default String getName() {
    return getName(null);
  }

  /**
   * Returns {@link #getConfigName()}, but formats it and translates it to the given <code>sender</code>.
   *
   * @param sender The person from which it should look up the language. Null if it should take the default language
   * @return The name in the language of the sender
   */
  String getName(@Nullable CommandSender sender);

  /**
   * Returns the name of the item exactly the same as it has been configured.
   *
   * @return The raw name
   */
  String getConfigName();

  /**
   * Set the unformatted name of the config.
   *
   * @param name The new raw name
   */
  void setConfigName(String name);

  /**
   * Returns the slot to which this item will be added to.
   *
   * @return The slot in the hotbar at which this item is located at.
   */
  int getSlot();

  /**
   * Returns the item that will be set into the hotbar.
   *
   * @return The item/icon
   */
  ItemStack getItem();

  /**
   * Set the item that will be set into the hotbar.
   *
   * @param is The new item/icon
   */
  void setItem(ItemStack is);

  /**
   * Returns the handler that handles the execution of an item.
   * <p>
   * May be <code>null</code> even when {@link #getHandlerId()} returns a non-null value.
   * This means that no handler has been registered under the given id.
   *
   * @return The handler of this item, <code>null</code> if none under the set id is registered
   */
  @Nullable LobbyItemHandler getHandler();

  /**
   * Returns the id of the current set handler for this item.
   * It's possible that <code>null</code> is being returned, this means that none has been defined yet.
   *
   * @return The set handler id, may be <code>null</code>
   */
  @Nullable String getHandlerId();

  /**
   * Set the handler for this item.
   * Make sure it's registered, otherwise no change is being applied.
   * <p>
   * Passing <code>null</code> causes the item to do nothing when used.
   *
   * @param handler The new handler, may be <code>null</code>
   */
  void setHandler(@Nullable LobbyItemHandler handler);

  /**
   * Set the handler id of this item.
   * It'll automatically update {@link #setHandler(LobbyItemHandler)} if the given id is registered.
   * If the handler under the given id isn't registered then the id will still be anyways.
   *
   * @param id The id of the handler, may be <code>null</code>
   */
  void setHandlerId(@Nullable String id);

  /**
   * Returns all commands that will be executed whenever a player uses the item.
   *
   * @return The commands that will be executed on use
   * @see #addCommand(String)
   * @see #removeCommand(String)
   */
  List<String> getCommands();

  /**
   * Returns whether the commands ({@link #getCommands()}) will be executed as console or as the player who used the item.
   *
   * @return <code>true</code> if the commands will be executed as console, <code>false</code> if as the player
   */
  boolean isCommandsAsConsole();

  /**
   * Set whether the commands ({@link #getCommands()}) will be executed as console or as the player who used the item.
   *
   * @param value <code>true</code> if the commands will be executed as console, <code>false</code> if as the player
   */
  void setCommandsAsConsole(boolean value);

  /**
   * Adds a command that will be executed whenever a player uses the item.
   *
   * @param cmd The command that will be executed on use
   */
  void addCommand(String cmd);

  /**
   * Removes a command from the {@link #getCommands()} List.
   * That command won't be executed any further if the operation was successful (returns <code>true</code>).
   *
   * @param cmd The command that shall be removed
   * @return <code>false</code> if it wouldn't have been executed anyways
   */
  boolean removeCommand(String cmd);
}
