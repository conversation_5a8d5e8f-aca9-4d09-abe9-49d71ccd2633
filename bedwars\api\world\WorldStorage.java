package de.marcely.bedwars.api.world;

import de.marcely.bedwars.api.event.SpecialBlockAddEvent;
import de.marcely.bedwars.api.world.block.BlockType;
import de.marcely.bedwars.api.world.block.SpecialBlock;
import de.marcely.bedwars.api.world.hologram.HologramControllerType;
import de.marcely.bedwars.api.world.hologram.HologramEntity;
import de.marcely.bedwars.api.world.hologram.HologramSkinType;
import de.marcely.bedwars.tools.location.XYZ;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;

/**
 * This plugin contains features which require informations to be bound on blocks, entities, holograms etc.
 * <p>
 * 	Bounds are being separated by worlds. This class represents a world in which they get stored
 * </p>
 */
public interface WorldStorage {

  /**
   * Returns the world which this instance correspondence to
   *
   * @return The world in which the stuff is located at
   */
  World asBukkit();

  /**
   * Tries to fetch and return a special block given by its coordinates
   *
   * @param x The x coordinate of the block
   * @param y The y coordinate of the block
   * @param z The z coordinate of the block
   * @return The special block located at the coordinates. <code>null</code> if there's none at that location
   */
  @Nullable SpecialBlock getBlock(int x, int y, int z);

  /**
   * Tries to fetch and return a special block given by its coordinates
   *
   * @param location The coordinates of the block
   * @return The special block located at the coordinates. <code>null</code> if there's none at that location
   * @throws IllegalArgumentException When the worlds mismatch
   */
  default @Nullable SpecialBlock getBlock(Location location) {
    if (location.getWorld() == null || location.getWorld() != asBukkit())
      throw new IllegalArgumentException("World mismatch");

    return getBlock(location.getBlockX(), location.getBlockY(), location.getBlockZ());
  }

  /**
   * Tries to fetch and return a special block given by its coordinates
   *
   * @param location The coordinates of the block
   * @return The special block located at the coordinates. <code>null</code> if there's none at that location
   */
  default @Nullable SpecialBlock getBlock(XYZ location) {
    return getBlock(location.getBlockX(), location.getBlockY(), location.getBlockZ());
  }

  /**
   * Tries to fetch and return a special block given by its block coordinates
   *
   * @param block Will use the coordinates of the given block
   * @return The special block located at the coordinates. <code>null</code> if there's none at that location
   */
  default @Nullable SpecialBlock getBlock(Block block) {
    return getBlock(block.getX(), block.getY(), block.getZ());
  }

  /**
   * Returns an iterator to iterate through all SpecialBlocks that were added to this world
   * <p>
   * It is not possible to remove or add entries
   *
   * @return A iterator for fetching any block added to this world
   */
  Collection<SpecialBlock> getBlocks();

  /**
   * Returns the amount of special blocks that exist in this world
   *
   * @return The total amount of added SpecialBlocks
   */
  int getBlocksAmount();

  /**
   * Tries to create a special block at the given coordinates.
   * <p>
   * May fail when there's already one or a plugin cancelled it via the {@link SpecialBlockAddEvent}.
   *
   * @param type The type of SpecialBlock you want to create
   * @param x The x coordinate of the block
   * @param y The y coordinate of the block
   * @param z The z coordinate of the block
   * @return The created block. <code>null</code> when it failed as there's already one
   */
  @Nullable SpecialBlock addBlock(BlockType type, int x, int y, int z);

  /**
   * Tries to create a special block at the given coordinates.
   * May fail when there's already one
   *
   * @param type The type of SpecialBlock you want to create
   * @param location The coordinates of the block
   * @return The created block. <code>null</code> when it failed as there's already one
   * @throws IllegalArgumentException When the worlds mismatch
   */
  default @Nullable SpecialBlock addBlock(BlockType type, Location location) {
    if (location.getWorld() == null || location.getWorld() != asBukkit())
      throw new IllegalArgumentException("World mismatch");

    return addBlock(type, location.getBlockX(), location.getBlockY(), location.getBlockZ());
  }

  /**
   * Tries to create a special block at the given coordinates.
   * May fail when there's already one
   *
   * @param type The type of SpecialBlock you want to create
   * @param location The coordinates of the block
   * @return The created block. <code>null</code> when it failed as there's already one
   */
  default @Nullable SpecialBlock addBlock(BlockType type, XYZ location) {
    return addBlock(type, location.getBlockX(), location.getBlockY(), location.getBlockZ());
  }

  /**
   * Tries to create a special block at the given block coordinates.
   * May fail when there's already one
   *
   * @param type The type of SpecialBlock you want to create
   * @param block Will use the coordinates of the given block
   * @return The created block. <code>null</code> when it failed as there's already one
   */
  default @Nullable SpecialBlock addBlock(BlockType type, Block block) {
    return addBlock(type, block.getX(), block.getY(), block.getZ());
  }

  /**
   * Returns all the holograms that have been spawned into this world.
   * Read {@link HologramEntity} if you want to know more about the feature.
   * <p>
   * It is not possible to remove or add entries.
   *
   * @return All existing holograms in this world
   */
  Collection<HologramEntity> getHolograms();

  /**
   * Returns the total amount of holograms that currently are in this world.
   *
   * @return Amount of existing holograms in this world
   */
  int getHologramsAmount();

  /**
   * Returns all the holograms of the {@link HologramEntity#getSkinType()} <code>skin</code> that have been spawned into this world.
   * Read {@link HologramEntity} if you want to know more about the feature.
   *
   * @return All existing holograms in this world
   */
  HologramEntity[] getHolograms(HologramSkinType skin);

  /**
   * Returns all the holograms of the {@link HologramEntity#getControllerType()} <code>controllerType</code> that have been spawned into this world.
   * Read {@link HologramEntity} if you want to know more about the feature.
   *
   * @return All existing holograms in this world
   */
  HologramEntity[] getHolograms(HologramControllerType controllerType);

  /**
   * Returns all the holograms within the given radius.
   *
   * @param location The location to search around
   * @param radius The radius to search in
   * @return All holograms within the radius
   */
  Collection<HologramEntity> getNearbyHolograms(Location location, double radius);

  /**
   * Spawns a new hologram into this world.
   *
   * @param skin The skin of the hologram
   * @param location His location
   * @return The newly initiated instance of the hologram
   */
  HologramEntity spawnHologram(HologramSkinType skin, Location location);

  /**
   * Spawns a new hologram into this world and uses a default skin based on the controller.
   *
   * @param controller The controller aka the brain of the hologram
   * @param location His location
   * @return The newly initiated instance of the hologram
   */
  HologramEntity spawnHologram(HologramControllerType controller, Location location);
}