package de.marcely.bedwars.api.game.shop;

import de.marcely.bedwars.api.arena.picker.condition.ArenaConditionGroup;
import de.marcely.bedwars.api.event.player.PlayerBuyInShopEvent;
import de.marcely.bedwars.api.game.shop.price.*;
import de.marcely.bedwars.api.game.shop.product.*;
import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * Represents a buyable item in the shop
 */
public interface ShopItem extends Cloneable {

  /**
   * Get the id of this item.
   * <p>
   *   Note that specifying the id in the configs file is optional file.
   *   If it's not specified, it'll be automatically generated.
   *   If auto-generated, then {@link #isIdAutogenerated()} will return <code>true</code>.
   * </p>
   * <p>
   *   In case it is auto-generated, a combination of name, icon and page will be used.
   * </p>
   *
   * @return The id of the item. Unique towards other items, yet persistent after restarts
   */
  String getId();

  /**
   * Get whether the id of this item has been auto-generated or not.
   *
   * @return <code>true</code> if the id has been auto-generated, otherwise <code>false</code>
   * @see #getId()
   */
  boolean isIdAutogenerated();

  /**
   * Returns its unformatted name as it is in the config file.
   *
   * @return The name of the item
   */
  String getName();

  /**
   * Changes the name of this ShopItem.
   *
   * @param name the new name of this item
   */
  void setName(String name);

  /**
   * Returns the formatted name of the item in the default language.
   *
   * @return The display name in the default language
   */
  default String getDisplayName() {
    return getDisplayName(null);
  }

  /**
   * Returns the formatted name in the language of the sender.
   *
   * @param sender The person
   * @return The display name in the language of the sender
   */
  String getDisplayName(@Nullable CommandSender sender);

  /**
   * Returns the formatted description of the item in the default language.
   *
   * @return The description in the default language. May be <code>null</code> when it doesn't have one
   */
  @Nullable
  default String getDescription() {
    return getDisplayName(null);
  }

  /**
   * Returns the formatted description in the language of the sender.
   *
   * @param sender The person
   * @return The description in the language of the sender. May be <code>null</code> when it doesn't have one
   */
  @Nullable
  String getDescription(@Nullable CommandSender sender);

  /**
   * Returns the unformatted description as it is written in the configs.
   *
   * @return The unformatted description. May be <code>null</code> when it doesn't have one
   */
  @Nullable
  String getConfigDescription();

  /**
   * Change the description of the item.
   *
   * @param description The new unformatted description. May be <code>null</code>
   */
  void setConfigDescription(@Nullable String description);

  /**
   * Returns the page in which the item is inside.
   *
   * @return The page in which the item has been added
   */
  ShopPage getPage();

  /**
   * Returns the prices that will be taken from the player when he's trying to buy the item.
   *
   * @return The price for this item
   */
  List<? extends ShopPrice> getPrices();

  /**
   * Returns the products that will be given to the buyer.
   *
   * @return The products of this item
   */
  List<? extends ShopProduct> getProducts();

  /**
   * Adds an ItemStack price to this instance.
   * <p>
   *     The type of the returned instance is equal to {@link ShopPriceType#ITEM}.
   * </p>
   *
   * @param price The material required for the purchase
   * @param amount The amount of material required for the purchase
   * @return The price created and applied to the item
   */
  ItemShopPrice addPriceItem(ItemStack price, int amount);

  /**
   * Adds a Spawner price to this instance.
   * <p>
   *     The type of the returned instance is equal to {@link ShopPriceType#SPAWNER_ITEM}.
   * </p>
   *
   * @param price The DropType required for the purchase
   * @param amount The amount of material required for the purchase
   * @return The price created and applied to the item
   */
  SpawnerItemShopPrice addPriceSpawner(DropType price, int amount);

  /**
   * Removes a price instance from this item.
   *
   * @param price The instance that shall be removed
   * @return <code>true</code> if it's a part of this item and if it has been removed. Otherwise <code>false</code>
   */
  boolean removePrice(ShopPrice price);

  /**
   * Adds a product that executes a command when this item is being bought.
   * <p>
   *     The type of the returned instance is equal to {@link ShopProductType#COMMAND}.
   * </p>
   *
   * @param command The command that will be executed on a successful purchase
   * @param executeAsConsole Whether the command shall be executed as the console (true) or as the player (false)
   * @return The product created and applied to the item
   */
  CommandShopProduct addProductCommand(String command, boolean executeAsConsole);

  /**
   * Adds a product that gives the player an ItemStack when this item is being bought.
   *
   * <p>
   *     The type of the returned instance is equal to {@link ShopProductType#ITEM}.
   * </p>
   *
   * @param item The ItemStack the player will receive on a successful purchase
   * @param amount The amount of items the player will receive
   * @return The product created and applied to the item
   */
  ItemShopProduct addProductItem(ItemStack item, int amount);

  /**
   * Adds a product that gives the player a spawner drops when this item is being bought.
   *
   * <p>
   *     The type of the returned instance is equal to {@link ShopProductType#ITEM}.
   * </p>
   *
   * @param dropType The DropType the player will receive on a successful purchase
   * @param amount The amount of items the player will receive
   * @return The product created and applied to the item
   */
  SpawnerItemShopProduct addProductSpawner(DropType dropType, int amount);

  /**
   * Adds a product that gives the player a SpecialItem when this item is being bought.
   *
   * <p>
   *     The type of the returned instance is equal to {@link ShopProductType#ITEM}.
   * </p>
   *
   * @param specialItem The special item that the player will receive when he buys the item
   * @param amount The amount of items the player will receive
   * @return The product created and applied to the item
   */
  SpecialItemShopProduct addProductSpecialItem(SpecialItem specialItem, int amount);

  /**
   * Removes a product instance from this item.
   *
   * @param product The instance that shall be removed
   * @return <code>true</code> if it's a part of this item and if it has been removed. Otherwise <code>false</code>
   */
  boolean removeProduct(ShopProduct product);

  /**
   * Returns the icon that will be shown in the shop GUI.
   *
   * @return The icon
   */
  ItemStack getIcon();

  /**
   * Set the icon that shall be shown in the shop GUI.
   *
   * @param icon The new icon
   */
  void setIcon(ItemStack icon);

  /**
   * Get the max amount that'll be multiplied when shift-clicking the item in the shop.
   *
   * @return The caps-multiply
   */
  int getCapsMultiply();

  /**
   * Set the max amount that'll be multiplied when shift-clicking the item in the shop.
   *
   * @param capsMultiply The new value
   */
  void setCapsMultiply(int capsMultiply);

  /**
   * Returns whether the item will be kept when the player dies.
   *
   * @return If the item will be kept on death
   */
  boolean isKeptOnDeath();

  /**
   * Define whether the item shall be kept on death.
   *
   * @param keepOnDeath The new value
   */
  void setKeptOnDeath(boolean keepOnDeath);

  /**
   * Returns whether the item can be only bought once.
   *
   * @return If the item can only be bought once
   */
  boolean isOneTimePurchase();

  /**
   * Define whether the item can be only bought once.
   *
   * @param oneTimePurchase The new value
   */
  void setOneTimePurchase(boolean oneTimePurchase);

  /**
   * Returns the buy-group of the item.
   * Might be <code>null</code> if it isn't in any.
   *
   * @return The buy-group of this item
   */
  @Nullable BuyGroup getBuyGroup();

  /**
   * Set the new buy-group of this item or remove it from an existing one.
   *
   * @param buyGroup Its new buy-group, or <code>null</code> to remove it from the existing one
   */
  void setBuyGroup(@Nullable BuyGroup buyGroup);

  /**
   * Returns if the item is a member of a buy-group or not.
   *
   * @return <code>true</code> if it has a buy-group, otherwise <code>false</code>
   */
  boolean hasBuyGroup();

  /**
   * Returns the buy-group level of this item.
   * <p>
   * It might be return a non -1 number even tho it's currently not in any buy-group.
   *
   * @return Its buy-group level
   */
  int getBuyGroupLevel();

  /**
   * Set the buy-group level of this item.
   * <p>
   * The change is even being (temporarily) being applied even when it currently is not a member of one.
   *
   * @param level Its new buy-group level
   */
  void setBuyGroupLevel(int level);

  /**
   * Weather or not a player has permission to buy this item.
   *
   * @param player The player we want to check
   */
  boolean hasBuyPermission(Player player);

  /**
   * Set a required buy permission for players.
   * The full permission will look like 'shopitem.id'
   * Can be null if there should be no buy permission
   *
   * @param permissionId The ID the custom buy permission should have. <code>null</code> is there should be none.
   */
  void setBuyPermissionId(@Nullable String permissionId);

  /**
   * Get the required buy permission id for shop item purchase.
   * The full permission will look like 'shopitem.id'
   * Can be null if there is no buy permission set
   *
   * @return The id of the required by permission. <code>null</code> if there is no permission set.
   */
  @Nullable String getBuyPermissionId();

  /**
   * Returns the slot at which it'll be placed at after the rendering of the shop layout GUI
   * Can be null if it shouldn't do that
   *
   * @return The slot at which it shall be forced at
   */
  @Nullable Integer getForceSlot();

  /**
   * Define at which it shall be forced at after the rendering of the shop GUI<br>
   * Can be null if it shouldn't do that
   *
   * @param forceSlot The new value
   */
  void setForceSlot(@Nullable Integer forceSlot);

  /**
   * The ArenaConditionGroup that controls what arenas this ShopItem is visible in.
   *
   * @return The condition that states the arenas this ShopItem will be available in. <code>null</code> if the ShopItem should be displayed in every arena.
   */
  @Nullable ArenaConditionGroup getRestriction();

  /**
   * Lets you restrict this ShopItem, so only be available in certain arenas
   *
   * @param restriction The condition that states the arenas this ShopItem will be available in. <code>null</code> if the ShopItem should be displayed in every arena.
   */
  void setRestriction(@Nullable ArenaConditionGroup restriction);

  /**
   * Returns the amount of how often the player could purchase this item in the invoked moment
   *
   * @param player The player we want to check
   * @param inv The theoretical inventory from which it shall be taken. <code>null</code> if it shall use the players inventory
   * @return The quantity of purchase instances the player could theoretically have in this moment
   */
  int calcPurchasableQuantity(Player player, @Nullable ItemStack[] inv);

  /**
   * Returns the amount of how often the player could purchase this item in the invoked moment
   *
   * @param player The player we want to check
   * @return The quantity of purchase instances the player could theoretically have in this moment
   */
  default int calcPurchasableQuantity(Player player) {
    return calcPurchasableQuantity(player, null);
  }

  /**
   * Make the player buy the item.
   * <p>
   *   Note that it will automatically make use of {@link #getOriginal()}, as it is not possible to buy cloned items.
   *   Procedure is exactly the same as if the player would click by himself on the given item.
   * </p>
   *
   * @param player The player who is supposed to
   * @param isShiftClick Whether he held shift during the purchase (Meaning that it's supposed to bought in stacks)
   * @return The result of the purchase
   */
  PlayerBuyInShopEvent buy(Player player, boolean isShiftClick);

  /**
   * Checks whether this instance is a clone (as if it has been cloned using {@link #clone()}).
   * <p>
   *  ShopItems are cloned in some cases (e.g. when a shop is opened) to allow you to modify the shop without affecting other arenas.
   * </p>
   *
   * @return whether this BuyGroup is a clone
   * @see #clone()
   * @see #getOriginal()
   */
  boolean isClone();

  /**
   * Returns the original "non-cloned" instance.
   * <p>
   *     This will return the original instance from which the clone has been created from.
   *     In case {@link #isClone()} returns false, the same instance is being returned.
   * </p>
   *
   * @return The original non-cloned instance
   * @see #isClone()
   * @see #clone()
   */
  ShopItem getOriginal();

  /**
   * Returns a clone of this instance.
   *
   * @return A clone of this instance
   * @see #isClone()
   * @see #getOriginal()
   */
  ShopItem clone();
}
