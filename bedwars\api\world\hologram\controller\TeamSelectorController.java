package de.marcely.bedwars.api.world.hologram.controller;

import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.world.hologram.HologramController;
import de.marcely.bedwars.api.world.hologram.HologramControllerType;
import de.marcely.bedwars.api.world.hologram.HologramEntity;

/**
 * Extending API if {@link HologramControllerType#TEAM_SELECTOR} is used.
 * <p>
 *   Cast {@link HologramEntity#getController()} to this class to access the methods.
 * </p>
 */
public interface TeamSelectorController extends HologramController {

  /**
   * Get the team that will be selected when clicked on.
   *
   * @return The team that will be selected
   */
  Team getTeam();

  /**
   * Set the team that will be selected when clicked on.
   * <p>
   *   Make sure to call {@link #applyProperties()} after changing the name to update the hologram.
   * </p>
   *
   * @param team The team that will be selected
   */
  void setTeam(Team team);
}
