package de.marcely.bedwars.api.game.spectator;

import de.marcely.bedwars.api.arena.Arena;
import java.time.Instant;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a spectating player and contains the info about him
 */
public interface Spectator {

  /**
   * Returns the player behind this data.
   *
   * @return Bukkit's player
   */
  Player getPlayer();

  /**
   * Returns the arena which the player is spectating.
   *
   * @return The arena he's spectating
   */
  Arena getArena();

  /**
   * Returns the reason or the cause of him being a spectator.
   *
   * @return The reason he's spectating
   */
  SpectateReason getReason();

  /**
   * Returns the time when he got a spectator.
   *
   * @return The time he started to spectate
   */
  Instant getEnterTime();

  /**
   * Returns the time when he is supposed to be released from enforced spectating.
   * <p>
   *   This is used for death spectating ({@link #getReason()} equals {@link SpectateReason#DEATH}).
   * </p>
   *
   * @return The time he's supposed to be released. <code>null</code> if he's not supposed to be released automatically
   */
  @Nullable
  Instant getReleaseTime();

  /**
   * Returns if this Spectator instance is still present.
   * <p>
   * This method will always return false after he stopped being a spectator.
   *
   * @return <code>true</code> if he hasn't stopped being a spectator yet
   */
  boolean isPresent();

  /**
   * Same as {@link Spectator#kick(KickSpectatorReason)}.
   * Only uses {@link KickSpectatorReason#PLUGIN} as the reason.
   *
   * @return <code>true</code> if it was successful
   */
  default boolean kick() {
    return kick(KickSpectatorReason.PLUGIN);
  }

  /**
   * Stops the player from spectating any further.
   * <p>
   * Additional things might happen depending on the reason given.
   *
   * @param reason Reason why he shall get kicked
   * @return <code>true</code> if it was successful
   */
  boolean kick(KickSpectatorReason reason);
}
