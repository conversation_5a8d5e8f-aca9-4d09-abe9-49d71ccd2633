# 恐怖降临模式 - MythicMobs集成说明

## 概述
恐怖降临模式已经适配您现有的MythicMobs配置，使用您的三阶段Boss战斗系统。

## Boss配置
模式将按顺序生成以下Boss：

### 第一阶段：LightHerald1
- **MythicMob ID**: `LightHerald1`
- **显示名称**: 光明先驱 (第一阶段)
- **血量**: 500
- **技能**: LH1, LH2, LH3, LH4, LH5, LH6, LH7
- **死亡后**: 自动生成 LightHerald2

### 第二阶段：LightHerald2  
- **MythicMob ID**: `LightHerald2`
- **显示名称**: 光明先驱 (第二阶段)
- **血量**: 500
- **技能**: LK1, LK2, LK3, LK4, LK5, LK6, LK8, LK9, LK10, LK11, LK12, LK13
- **死亡后**: 自动生成 LightCore

### 第三阶段：LightCore
- **MythicMob ID**: `LightCore`
- **显示名称**: 光明核心 (最终阶段)
- **血量**: 500
- **技能**: LC1, LC2, LC2_2, LC3, LC4, LC5, LC6, LC6_2, LC7, LC8, LC9, LC10, LC11
- **死亡后**: Boss战结束，队伍获得奖励

## 召唤物配置
Boss战斗中会出现以下召唤物：

### LightWarrior (光明战士)
- **血量**: 35
- **技能**: LW1, LW2, LW3
- **作用**: 近战攻击玩家

### Spear/Spear2 (光明长矛)
- **血量**: 100
- **特点**: 无敌，装饰性召唤物
- **作用**: 投射物攻击

### Beam1/Beam2 (光明光束)
- **血量**: 100
- **特点**: 无敌，范围伤害
- **作用**: 区域光束攻击

### LightProjectile (光明投射物)
- **血量**: 100
- **特点**: 无敌，移动投射物
- **作用**: 追踪攻击

## 模式特性

### 生成机制
- **生成间隔**: 每10分钟生成一次Boss
- **生成位置**: 竞技场中心点
- **最大数量**: 同时最多1个Boss
- **防重复**: 如果有Boss存活，不会生成新的

### 阶段切换
- Boss死亡后自动生成下一阶段
- 每个阶段都有独立的血量条
- 阶段切换时会有特殊消息提示

### 奖励系统
- 击杀最终Boss (LightCore) 后，击杀队伍获得属性效果
- 效果包括：力量II、速度II、抗性提升I、再生I
- 效果持续5分钟

### 怪物生成
- 绿宝石和钻石资源点会生成恐怖怪物
- 怪物类型：僵尸、骷髅、蜘蛛、苦力怕、末影人
- Boss死亡后所有怪物自动清除

## 配置文件位置
- **Boss配置**: `mythicmobs-example/IntoTheLightBoss.yml`
- **技能配置**: `mythicmobs-example/IntoTheLightSkillsBoss.yml`
- **模式配置**: `plugins/AceVoteMode/modes/terror-descent.yml`

## 使用说明

### 1. 确保MythicMobs配置已加载
```bash
/mm reload
```

### 2. 检查Boss是否存在
```bash
/mm mobs list
```
应该能看到：LightHerald1, LightHerald2, LightCore, LightWarrior, Spear, Spear2, Beam1, Beam2, LightProjectile

### 3. 测试Boss生成
```bash
/mm spawn LightHerald1
```

### 4. 启动恐怖降临模式
在起床战争游戏开始前投票选择"恐怖降临"模式

## 注意事项

1. **模型文件**: 确保您有对应的模型文件：
   - herald_of_light_phase_1
   - herald_of_light_phase_2  
   - herald_of_light_core
   - light_warrior
   - light_herald_spear
   - light_herald_spear2
   - light_projectile
   - sun_light_beam
   - sun_light_long_beam

2. **权限设置**: 确保服务器有MythicMobs的生成权限

3. **性能考虑**: Boss技能较多，建议在性能较好的服务器上使用

4. **平衡性**: 如需调整伤害或血量，请修改您的MythicMobs配置文件

## 故障排除

### Boss不生成
- 检查MythicMobs是否正确加载
- 确认Boss ID拼写正确
- 检查竞技场是否设置了Boss生成点

### 技能不工作
- 确认所有技能ID存在于配置文件中
- 检查MythicMobs版本兼容性
- 查看控制台错误信息

### 阶段不切换
- 确认Boss配置中的summon命令正确
- 检查下一阶段的Boss ID是否存在

## 自定义修改

如果您想修改Boss行为，请编辑您的MythicMobs配置文件：
- `mythicmobs-example/IntoTheLightBoss.yml` - 修改Boss属性
- `mythicmobs-example/IntoTheLightSkillsBoss.yml` - 修改技能效果

修改后记得执行 `/mm reload` 重新加载配置。
