package de.marcely.bedwars.api.world.hologram.skin;

import de.marcely.bedwars.api.world.hologram.HologramSkin;
import de.marcely.bedwars.tools.VarSound;
import org.bukkit.Sound;
import org.jetbrains.annotations.Nullable;

/**
 * Certain skin types have heatlh and may receive damage.
 */
public interface DamageableSkin extends HologramSkin {

  /**
   * Get the sound that will be played when it is being attacked.
   *
   * @return The sound that will be played when the hologram receives damage. May be <code>null</code> if none shall be played
   */
  @Nullable
  VarSound getDamageSound();

  /**
   * Get the sound that will be played when it died.
   *
   * @return The sound that will be played when the hologram dies. May be <code>null</code> if none shall be played
   */
  @Nullable
  VarSound getDeathSound();

  /**
   * Get the amount of heatlh that the entity currently has.
   *
   * @return The amount of heatlh he has
   */
  float getHealth();

  /**
   * Change the amount of health.
   * <p>
   *   Setting it to 0 or less kills and removes the hologram.
   *   While no sound or animation is played when the health decreases,
   *   one is being played if the change caused the hologram to die.
   * </p>
   *
   * @param health The new health amount. 0 or less to kill it
   * @return <code>true</code> whether the new amount caused the hologram to die. Stays <code>false</code> if the hologram was already dead
   */
  boolean setHealth(float health);

  /**
   * Decreases the amount of health by a given amount and plays animation and sound.
   * <p>
   *   Death animation and sound is being played if the new health is 0 or less.
   * </p>
   *
   * @param damage
   */
  void attack(float damage);
}
