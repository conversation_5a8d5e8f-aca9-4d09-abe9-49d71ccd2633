package cn.acebrand.acevotemode.commands;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import cn.acebrand.acevotemode.gamemode.terror.BossManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 恐怖降临模式Boss位置管理指令
 */
public class TerrorBossCommand implements CommandExecutor, TabCompleter {
    
    private final AceVoteMode plugin;

    public TerrorBossCommand(AceVoteMode plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("acevotemode.admin")) {
            sender.sendMessage(ChatColor.RED + "你没有权限使用此指令！");
            return true;
        }

        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "set":
                return handleSetCommand(sender, args);
            case "list":
                return handleListCommand(sender, args);
            case "remove":
                return handleRemoveCommand(sender, args);
            case "test":
                return handleTestCommand(sender, args);
            case "help":
            default:
                sendHelpMessage(sender);
                return true;
        }
    }

    /**
     * 处理设置位置指令
     */
    private boolean handleSetCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "此指令只能由玩家执行！");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "用法: /terrorboss set <竞技场名称> [位置名称]");
            return true;
        }

        Player player = (Player) sender;
        String arenaName = args[1];
        String locationName = args.length > 2 ? args[2] : "location_" + System.currentTimeMillis();

        // 检查竞技场是否存在
        Arena arena = GameAPI.get().getArenaByName(arenaName);
        if (arena == null) {
            sender.sendMessage(ChatColor.RED + "竞技场 '" + arenaName + "' 不存在！");
            return true;
        }

        // 获取TerrorDescentMode实例
        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
        if (terrorMode == null) {
            sender.sendMessage(ChatColor.RED + "恐怖降临模式未启用！");
            return true;
        }

        // 设置Boss位置
        Location location = player.getLocation();
        BossManager bossManager = terrorMode.getBossManager();
        bossManager.setBossLocation(arenaName, locationName, location);

        sender.sendMessage(ChatColor.GREEN + "已为竞技场 '" + arenaName + "' 设置Boss生成位置: " + locationName);
        sender.sendMessage(ChatColor.GRAY + "位置: " + formatLocation(location));

        return true;
    }

    /**
     * 处理列出位置指令
     */
    private boolean handleListCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "用法: /terrorboss list <竞技场名称>");
            return true;
        }

        String arenaName = args[1];

        // 检查竞技场是否存在
        Arena arena = GameAPI.get().getArenaByName(arenaName);
        if (arena == null) {
            sender.sendMessage(ChatColor.RED + "竞技场 '" + arenaName + "' 不存在！");
            return true;
        }

        // 获取TerrorDescentMode实例
        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
        if (terrorMode == null) {
            sender.sendMessage(ChatColor.RED + "恐怖降临模式未启用！");
            return true;
        }

        // 获取Boss位置列表
        BossManager bossManager = terrorMode.getBossManager();
        List<Location> locations = bossManager.getBossLocations(arenaName);

        if (locations.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "竞技场 '" + arenaName + "' 没有设置Boss生成位置！");
            return true;
        }

        sender.sendMessage(ChatColor.GREEN + "竞技场 '" + arenaName + "' 的Boss生成位置:");
        for (int i = 0; i < locations.size(); i++) {
            Location location = locations.get(i);
            sender.sendMessage(ChatColor.GRAY + "  " + (i + 1) + ". " + formatLocation(location));
        }

        return true;
    }

    /**
     * 处理移除位置指令
     */
    private boolean handleRemoveCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /terrorboss remove <竞技场名称> <位置索引>");
            return true;
        }

        String arenaName = args[1];
        int index;

        try {
            index = Integer.parseInt(args[2]) - 1; // 转换为0基索引
        } catch (NumberFormatException e) {
            sender.sendMessage(ChatColor.RED + "位置索引必须是数字！");
            return true;
        }

        // 检查竞技场是否存在
        Arena arena = GameAPI.get().getArenaByName(arenaName);
        if (arena == null) {
            sender.sendMessage(ChatColor.RED + "竞技场 '" + arenaName + "' 不存在！");
            return true;
        }

        // 获取TerrorDescentMode实例
        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
        if (terrorMode == null) {
            sender.sendMessage(ChatColor.RED + "恐怖降临模式未启用！");
            return true;
        }

        // 移除Boss位置
        BossManager bossManager = terrorMode.getBossManager();
        List<Location> locations = bossManager.getBossLocations(arenaName);

        if (index < 0 || index >= locations.size()) {
            sender.sendMessage(ChatColor.RED + "位置索引超出范围！有效范围: 1-" + locations.size());
            return true;
        }

        bossManager.removeBossLocation(arenaName, index);
        sender.sendMessage(ChatColor.GREEN + "已移除竞技场 '" + arenaName + "' 的Boss生成位置 #" + (index + 1));

        return true;
    }

    /**
     * 处理测试指令
     */
    private boolean handleTestCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "用法: /terrorboss test <竞技场名称>");
            return true;
        }

        String arenaName = args[1];

        // 检查竞技场是否存在
        Arena arena = GameAPI.get().getArenaByName(arenaName);
        if (arena == null) {
            sender.sendMessage(ChatColor.RED + "竞技场 '" + arenaName + "' 不存在！");
            return true;
        }

        // 获取TerrorDescentMode实例
        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
        if (terrorMode == null) {
            sender.sendMessage(ChatColor.RED + "恐怖降临模式未启用！");
            return true;
        }

        // 检查MythicMobs是否可用
        BossManager bossManager = terrorMode.getBossManager();
        if (!bossManager.isMythicMobsEnabled()) {
            sender.sendMessage(ChatColor.RED + "MythicMobs插件未启用，无法测试Boss生成！");
            return true;
        }

        // 测试Boss生成
        sender.sendMessage(ChatColor.YELLOW + "正在测试Boss生成...");
        
        boolean success = bossManager.spawnBoss(arena);
        if (success) {
            sender.sendMessage(ChatColor.GREEN + "Boss生成测试成功！");
        } else {
            sender.sendMessage(ChatColor.RED + "Boss生成测试失败！请检查:");
            sender.sendMessage(ChatColor.RED + "1. 是否设置了Boss生成位置");
            sender.sendMessage(ChatColor.RED + "2. MythicMobs配置是否正确");
            sender.sendMessage(ChatColor.RED + "3. 竞技场是否处于正确状态");
        }

        return true;
    }

    /**
     * 发送帮助消息
     */
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== 恐怖降临Boss管理指令 ===");
        sender.sendMessage(ChatColor.YELLOW + "/terrorboss set <竞技场> [位置名] - 设置Boss生成位置");
        sender.sendMessage(ChatColor.YELLOW + "/terrorboss list <竞技场> - 列出Boss生成位置");
        sender.sendMessage(ChatColor.YELLOW + "/terrorboss remove <竞技场> <索引> - 移除Boss生成位置");
        sender.sendMessage(ChatColor.YELLOW + "/terrorboss test <竞技场> - 测试Boss生成");
        sender.sendMessage(ChatColor.YELLOW + "/terrorboss help - 显示此帮助信息");
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(Location location) {
        return String.format("%s: %.1f, %.1f, %.1f (%.1f, %.1f)",
            location.getWorld().getName(),
            location.getX(),
            location.getY(),
            location.getZ(),
            location.getYaw(),
            location.getPitch());
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("acevotemode.admin")) {
            return new ArrayList<>();
        }

        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：子指令
            List<String> subCommands = Arrays.asList("set", "list", "remove", "test", "help");
            for (String subCommand : subCommands) {
                if (subCommand.toLowerCase().startsWith(args[0].toLowerCase())) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2) {
            // 第二个参数：竞技场名称
            String input = args[1].toLowerCase();
            for (Arena arena : GameAPI.get().getArenas()) {
                if (arena.getName().toLowerCase().startsWith(input)) {
                    completions.add(arena.getName());
                }
            }
        } else if (args.length == 3 && args[0].equalsIgnoreCase("remove")) {
            // remove指令的第三个参数：位置索引
            String arenaName = args[1];
            Arena arena = GameAPI.get().getArenaByName(arenaName);
            
            if (arena != null) {
                TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
                if (terrorMode != null) {
                    BossManager bossManager = terrorMode.getBossManager();
                    List<Location> locations = bossManager.getBossLocations(arenaName);
                    
                    for (int i = 1; i <= locations.size(); i++) {
                        completions.add(String.valueOf(i));
                    }
                }
            }
        }

        return completions;
    }
}
