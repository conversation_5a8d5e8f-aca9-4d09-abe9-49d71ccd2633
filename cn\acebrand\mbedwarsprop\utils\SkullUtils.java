package cn.acebrand.mbedwarsprop.utils;

import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.Plugin;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Base64;
import java.util.UUID;
import java.util.logging.Level;

/**
 * 头颅工具类，用于设置自定义头颅皮肤
 */
public class SkullUtils {

    /**
     * 设置头颅的自定义皮肤
     *
     * @param skullMeta 头颅的Meta数据
     * @param textureValue Base64编码的皮肤值
     * @param plugin 插件实例，用于记录日志
     * @return 是否设置成功
     */
    public static boolean setSkullTexture(SkullMeta skullMeta, String textureValue, Plugin plugin) {
        try {
            // 使用反射获取CraftMetaSkull类
            Class<?> craftMetaSkullClass = skullMeta.getClass();
            
            // 获取profile字段
            Field profileField = null;
            
            try {
                // 尝试直接获取profile字段
                profileField = craftMetaSkullClass.getDeclaredField("profile");
            } catch (NoSuchFieldException e) {
                // 如果找不到profile字段，尝试遍历所有字段
                for (Field field : craftMetaSkullClass.getDeclaredFields()) {
                    if (field.getType().getSimpleName().equals("GameProfile")) {
                        profileField = field;
                        break;
                    }
                }
            }
            
            if (profileField == null) {
                plugin.getLogger().warning("无法找到GameProfile字段");
                return false;
            }
            
            // 设置字段可访问
            profileField.setAccessible(true);
            
            // 创建GameProfile实例
            Object gameProfile = createGameProfile(UUID.randomUUID(), null);
            
            // 获取GameProfile的properties字段
            Object properties = getProperties(gameProfile);
            
            // 创建Property实例
            Object property = createProperty("textures", textureValue);
            
            // 将Property添加到properties中
            putProperty(properties, "textures", property);
            
            // 设置profile字段的值
            profileField.set(skullMeta, gameProfile);
            
            return true;
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "设置头颅皮肤失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 创建GameProfile实例
     */
    private static Object createGameProfile(UUID id, String name) throws Exception {
        // 获取GameProfile类
        Class<?> gameProfileClass = Class.forName("com.mojang.authlib.GameProfile");
        // 获取构造函数
        Constructor<?> constructor = gameProfileClass.getDeclaredConstructor(UUID.class, String.class);
        // 创建实例
        return constructor.newInstance(id, name);
    }
    
    /**
     * 获取GameProfile的properties字段
     */
    private static Object getProperties(Object gameProfile) throws Exception {
        // 获取getProperties方法
        Method getPropertiesMethod = gameProfile.getClass().getDeclaredMethod("getProperties");
        // 调用方法
        return getPropertiesMethod.invoke(gameProfile);
    }
    
    /**
     * 创建Property实例
     */
    private static Object createProperty(String name, String value) throws Exception {
        // 获取Property类
        Class<?> propertyClass = Class.forName("com.mojang.authlib.properties.Property");
        // 获取构造函数
        Constructor<?> constructor = propertyClass.getDeclaredConstructor(String.class, String.class);
        // 创建实例
        return constructor.newInstance(name, value);
    }
    
    /**
     * 将Property添加到properties中
     */
    private static void putProperty(Object properties, String key, Object property) throws Exception {
        // 获取put方法
        Method putMethod = properties.getClass().getDeclaredMethod("put", Object.class, Object.class);
        // 调用方法
        putMethod.invoke(properties, key, property);
    }
    
    /**
     * 检查是否是有效的Base64编码的皮肤值
     */
    public static boolean isValidTextureValue(String textureValue) {
        if (textureValue == null || textureValue.isEmpty()) {
            return false;
        }
        
        try {
            // 尝试解码Base64
            byte[] decoded = Base64.getDecoder().decode(textureValue);
            // 检查解码后的字符串是否包含textures关键字
            String decodedStr = new String(decoded);
            return decodedStr.contains("textures") && decodedStr.contains("SKIN");
        } catch (Exception e) {
            return false;
        }
    }
}
