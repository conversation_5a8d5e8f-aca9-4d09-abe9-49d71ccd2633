package de.marcely.bedwars.api.game.upgrade;

import de.marcely.bedwars.tools.Validate;
import java.util.UUID;
import org.bukkit.OfflinePlayer;

/**
 * Represents a trap that has been bought and is queued to be activated.
 */
public class QueuedTrap {

  private final UpgradeLevel level;
  private final UUID buyerUUID;

  public QueuedTrap(UpgradeLevel level, OfflinePlayer player) {
    this(level, player.getUniqueId());
  }

  public QueuedTrap(UpgradeLevel level, UUID buyerUUID) {
    Validate.notNull(level, "level");
    Validate.notNull(buyerUUID, "buyerUUID");
    Validate.isTrue(level.isTrap(), "upgrade must be a trap");

    this.level = level;
    this.buyerUUID = buyerUUID;
  }

  /**
   * Get the upgrade's level that represents the queued trap.
   *
   * @return The upgrade's level that represents the queued trap
   */
  public UpgradeLevel getLevel() {
    return this.level;
  }

  /**
   * Get the UUID of the player who bought the trap.
   *
   * @return The UUID of the player who bought the trap
   * @see org.bukkit.entity.Player#getUniqueId()
   */
  public UUID getBuyerUUID() {
    return this.buyerUUID;
  }
}
