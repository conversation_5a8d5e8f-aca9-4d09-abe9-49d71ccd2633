package cn.acebrand.acevotemode.util;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.manager.VoteManager;
import cn.acebrand.acevotemode.model.GameMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.List;
import java.util.Map;

/**
 * 消息工具类
 * 负责处理消息发送和结果显示
 */
public class MessageUtil {
    
    /**
     * 向竞技场中的所有玩家发送消息
     */
    public static void sendMessageToArena(Arena arena, String message) {
        for (Player player : arena.getPlayers()) {
            player.sendMessage(message);
        }
    }
    
    /**
     * 向竞技场中的所有玩家发送标题
     */
    public static void sendTitleToArena(Arena arena, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        for (Player player : arena.getPlayers()) {
            player.sendTitle(title, subtitle, fadeIn, stay, fadeOut);
        }
    }
    
    /**
     * 显示获胜模式结果
     */
    public static void displayWinningMode(AceVoteMode plugin, Arena arena, GameMode winningMode) {
        if (winningMode == null) {
            plugin.getLogger().warning("No winning mode found for arena: " + arena.getName());
            return;
        }

        VoteManager.ArenaVoteData voteData = plugin.getVoteManager().getArenaVoteData(arena);
        List<String> topModes = voteData.getTopModes();
        int maxVotes = voteData.getMaxVotes();

        // 检查是否有平票情况
        boolean isTie = topModes.size() > 1;
        boolean noVotes = voteData.getTotalVotes() == 0;

        // 发送聊天消息
        String chatMessage;
        if (noVotes) {
            // 无人投票，从所有模式中随机选择
            chatMessage = plugin.getConfigManager().getMessage("no-votes-random",
                    "mode", winningMode.getPlainName());
        } else if (isTie) {
            // 平票，从平票模式中随机选择
            chatMessage = plugin.getConfigManager().getMessage("tie-random",
                    "mode", winningMode.getPlainName(),
                    "count", String.valueOf(topModes.size()));
        } else {
            // 正常获胜
            chatMessage = plugin.getConfigManager().getMessage("winning-mode",
                    "mode", winningMode.getPlainName());
        }
        sendMessageToArena(arena, chatMessage);

        // 发送标题
        String title = ChatColor.GOLD + "" + ChatColor.BOLD + "游戏模式";
        String subtitle = winningMode.getName();
        sendTitleToArena(arena, title, subtitle, 10, 60, 20);

        // 延迟发送详细信息
        new BukkitRunnable() {
            @Override
            public void run() {
                // 发送模式描述
                sendMessageToArena(arena, ChatColor.YELLOW + "模式介绍:");
                for (String line : winningMode.getDescription()) {
                    sendMessageToArena(arena, ChatColor.translateAlternateColorCodes('&', line));
                }

                // 如果是平票或无投票，显示额外信息
                if (isTie) {
                    sendMessageToArena(arena, ChatColor.GRAY + "平票的模式:");
                    for (String modeId : topModes) {
                        GameMode mode = plugin.getConfigManager().getGameModes().get(modeId);
                        if (mode != null) {
                            sendMessageToArena(arena, ChatColor.GRAY + "  - " + mode.getPlainName() +
                                    " (" + maxVotes + "票)");
                        }
                    }
                } else if (noVotes) {
                    sendMessageToArena(arena, ChatColor.GRAY + "下次记得投票哦！");
                }
            }
        }.runTaskLater(plugin, 40L); // 2秒后发送

        // 记录日志
        if (plugin.getConfigManager().isEnableVoteLogs()) {
            String logMessage = "Displayed winning mode '" + winningMode.getPlainName() + "' for arena: " + arena.getName();
            if (noVotes) {
                logMessage += " (no votes, randomly selected)";
            } else if (isTie) {
                logMessage += " (tie between " + topModes.size() + " modes, randomly selected)";
            }
            plugin.getLogger().info(logMessage);
        }
    }
    
    /**
     * 显示投票统计信息
     */
    public static void displayVoteStatistics(AceVoteMode plugin, Arena arena) {
        VoteManager.ArenaVoteData voteData = plugin.getVoteManager().getArenaVoteData(arena);
        Map<String, Integer> voteCounts = voteData.getAllVoteCounts();

        if (voteCounts.isEmpty()) {
            // 美化无投票的显示
            sendMessageToArena(arena, "");
            sendMessageToArena(arena, ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
            sendMessageToArena(arena, ChatColor.YELLOW + "" + ChatColor.BOLD + "         📊 投票统计 📊");
            sendMessageToArena(arena, ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
            sendMessageToArena(arena, "");
            sendMessageToArena(arena, ChatColor.GRAY + "         💭 本局没有玩家投票");
            sendMessageToArena(arena, ChatColor.GRAY + "         将随机选择游戏模式");
            sendMessageToArena(arena, "");
            sendMessageToArena(arena, ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
            sendMessageToArena(arena, "");
            return;
        }

        // 美化有投票的显示
        sendMessageToArena(arena, "");
        sendMessageToArena(arena, ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sendMessageToArena(arena, ChatColor.YELLOW + "" + ChatColor.BOLD + "         📊 投票统计 📊");
        sendMessageToArena(arena, ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sendMessageToArena(arena, "");

        // 按票数排序显示，添加排名和视觉效果
        int rank = 1;
        for (Map.Entry<String, Integer> entry : voteCounts.entrySet().stream()
                .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                .collect(java.util.stream.Collectors.toList())) {

            String modeId = entry.getKey();
            int votes = entry.getValue();

            GameMode gameMode = plugin.getConfigManager().getGameModes().get(modeId);
            if (gameMode != null) {
                // 创建投票条形图效果
                StringBuilder voteBar = new StringBuilder();
                for (int i = 0; i < votes; i++) {
                    voteBar.append("█");
                }

                // 根据排名显示不同的图标
                String rankIcon;
                ChatColor rankColor;
                switch (rank) {
                    case 1:
                        rankIcon = "🥇";
                        rankColor = ChatColor.GOLD;
                        break;
                    case 2:
                        rankIcon = "🥈";
                        rankColor = ChatColor.GRAY;
                        break;
                    case 3:
                        rankIcon = "🥉";
                        rankColor = ChatColor.YELLOW;
                        break;
                    default:
                        rankIcon = "▸";
                        rankColor = ChatColor.WHITE;
                        break;
                }

                String message = " " + rankIcon + " " + rankColor + gameMode.getPlainName() +
                        ChatColor.GRAY + ": " + ChatColor.GREEN + "" + ChatColor.BOLD + votes + " 票 " +
                        ChatColor.GREEN + voteBar.toString();
                sendMessageToArena(arena, message);
                rank++;
            }
        }

        // 显示总票数和底部装饰
        int totalVotes = voteData.getTotalVotes();
        sendMessageToArena(arena, "");
        sendMessageToArena(arena, ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sendMessageToArena(arena, ChatColor.GRAY + "总投票数: " + ChatColor.WHITE + "" + ChatColor.BOLD + totalVotes +
                ChatColor.GRAY + " | 投票已关闭，正在选择模式...");
        sendMessageToArena(arena, ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sendMessageToArena(arena, "");
    }
    
    /**
     * 发送彩色消息
     */
    public static void sendColoredMessage(Player player, String message) {
        player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
    }
    
    /**
     * 向所有在线玩家发送消息
     */
    public static void broadcastMessage(String message) {
        Bukkit.broadcastMessage(ChatColor.translateAlternateColorCodes('&', message));
    }
    
    /**
     * 向具有特定权限的玩家发送消息
     */
    public static void sendMessageToPermission(String permission, String message) {
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.hasPermission(permission)) {
                player.sendMessage(coloredMessage);
            }
        }
    }
    
    /**
     * 格式化时间显示
     */
    public static String formatTime(int seconds) {
        if (seconds >= 60) {
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;
            return String.format("%d:%02d", minutes, remainingSeconds);
        } else {
            return String.valueOf(seconds);
        }
    }
    
    /**
     * 创建分隔线
     */
    public static String createSeparator(char character, int length) {
        return ChatColor.GRAY + String.valueOf(character).repeat(length);
    }
    
    /**
     * 创建居中文本
     */
    public static String centerText(String text, int lineLength) {
        if (text.length() >= lineLength) {
            return text;
        }
        
        int spaces = (lineLength - ChatColor.stripColor(text).length()) / 2;
        return " ".repeat(Math.max(0, spaces)) + text;
    }
}
