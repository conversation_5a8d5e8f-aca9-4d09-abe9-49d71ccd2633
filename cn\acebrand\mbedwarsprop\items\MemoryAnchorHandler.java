package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.MBedwarsProp;
import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class MemoryAnchorHandler implements SpecialItemUseHandler, Listener {

    private final MBedwarsProp plugin;
    private final Map<UUID, Location> playerAnchors = new HashMap<>();
    private final Map<UUID, ArmorStand> anchorHolograms = new HashMap<>();
    private final Map<UUID, BukkitTask> teleportTasks = new HashMap<>();
    private final Map<UUID, Long> lastCombatTime = new HashMap<>();

    // 配置参数
    private int teleportDelay = 3;
    private boolean playSound = true;
    private boolean showParticles = true;
    private int anchorDuration = 0;
    private boolean consumeOnUse = false;
    private boolean consumeOnTeleport = true;
    private boolean allowCrossTeam = false;
    private boolean allowInCombat = true;
    private int combatTime = 10;

    public MemoryAnchorHandler(MBedwarsProp plugin) {
        this.plugin = plugin;

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        // 加载配置
        loadConfig();
    }

    private void loadConfig() {
        try {
            ItemConfigManager.ItemConfig config = plugin.getItemConfigManager().getItemConfig("memory_anchor");

            if (config != null) {
                if (config.getEffect("teleport-delay") != null) {
                    this.teleportDelay = config.getEffect("teleport-delay").getLevel();
                }

                if (config.getEffect("play-sound") != null) {
                    this.playSound = config.getEffect("play-sound").isEnabled();
                }

                if (config.getEffect("show-particles") != null) {
                    this.showParticles = config.getEffect("show-particles").isEnabled();
                }

                if (config.getEffect("duration") != null) {
                    this.anchorDuration = config.getEffect("duration").getDuration();
                }

                if (config.getEffect("consume-on-use") != null) {
                    this.consumeOnUse = config.getEffect("consume-on-use").isEnabled();
                }

                if (config.getEffect("consume-on-teleport") != null) {
                    this.consumeOnTeleport = config.getEffect("consume-on-teleport").isEnabled();
                }

                if (config.getEffect("cross-team") != null) {
                    this.allowCrossTeam = config.getEffect("cross-team").isEnabled();
                }

                if (config.getEffect("allow-in-combat") != null) {
                    this.allowInCombat = config.getEffect("allow-in-combat").isEnabled();
                }

                if (config.getEffect("combat-time") != null) {
                    this.combatTime = config.getEffect("combat-time").getLevel();
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("加载记忆锚点配置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // 玩家退出时清理资源
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 移除锚点
        removeAnchor(playerId);

        // 取消传送任务
        cancelTeleport(playerId);

        // 移除战斗状态
        lastCombatTime.remove(playerId);
    }

    @EventHandler
    public void onPlayerDamage(EntityDamageByEntityEvent event) {
        // 记录玩家受到伤害的时间，用于战斗状态检测
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            lastCombatTime.put(player.getUniqueId(), System.currentTimeMillis());
        }

        if (event.getDamager() instanceof Player) {
            Player player = (Player) event.getDamager();
            lastCombatTime.put(player.getUniqueId(), System.currentTimeMillis());
        }
    }

    // 检查玩家是否处于战斗状态
    private boolean isInCombat(Player player) {
        if (allowInCombat) {
            return false; // 如果允许在战斗中使用，则始终返回非战斗状态
        }

        Long lastCombat = lastCombatTime.get(player.getUniqueId());
        if (lastCombat == null) {
            return false;
        }

        // 检查是否超过战斗状态时间
        return System.currentTimeMillis() - lastCombat < combatTime * 1000L;
    }

    // 设置锚点
    private void setAnchor(Player player, Location location) {
        UUID playerId = player.getUniqueId();

        // 移除旧的锚点（如果有）
        removeAnchor(playerId);

        // 保存新锚点位置
        playerAnchors.put(playerId, location.clone());

        // 创建全息显示
        createHologram(player, location);

        // 播放音效
        if (playSound) {
            player.playSound(location, Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.0f);
        }

        // 显示粒子效果
        if (showParticles) {
            location.getWorld().spawnParticle(Particle.PORTAL, location.clone().add(0.5, 0.5, 0.5), 50, 0.5, 0.5, 0.5,
                    0.1);
        }

        // 如果设置了持续时间，创建过期任务
        if (anchorDuration > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (playerAnchors.containsKey(playerId)) {
                        removeAnchor(playerId);
                        player.sendMessage(ChatColor.RED + "你的记忆锚点已过期！");
                    }
                }
            }.runTaskLater(plugin, anchorDuration * 20L);
        }

        player.sendMessage(ChatColor.GREEN + "记忆锚点已设置！潜行+右键点击可以传送回此位置。");
    }

    // 创建全息显示
    private void createHologram(Player player, Location location) {
        UUID playerId = player.getUniqueId();

        // 创建全息显示的位置（在锚点上方）
        Location holoLoc = location.clone().add(0.5, 2.0, 0.5);

        // 创建隐形盔甲架
        ArmorStand hologram = (ArmorStand) location.getWorld().spawnEntity(holoLoc, EntityType.ARMOR_STAND);
        hologram.setVisible(false);
        hologram.setGravity(false);
        hologram.setCustomName(ChatColor.AQUA + player.getName() + "的记忆锚点");
        hologram.setCustomNameVisible(true);
        hologram.setMarker(true);
        hologram.setSmall(true);

        // 存储全息显示实体
        anchorHolograms.put(playerId, hologram);
    }

    // 移除锚点
    private void removeAnchor(UUID playerId) {
        // 移除锚点位置
        playerAnchors.remove(playerId);

        // 移除全息显示
        ArmorStand hologram = anchorHolograms.remove(playerId);
        if (hologram != null && !hologram.isDead()) {
            hologram.remove();
        }
    }

    // 开始传送
    private void startTeleport(Player player) {
        UUID playerId = player.getUniqueId();
        Location anchorLocation = playerAnchors.get(playerId);

        if (anchorLocation == null) {
            player.sendMessage(ChatColor.RED + "你没有设置记忆锚点！");
            return;
        }

        // 检查是否在战斗中
        if (isInCombat(player)) {
            player.sendMessage(ChatColor.RED + "你在战斗中无法使用记忆锚点！");
            return;
        }

        // 取消之前的传送任务（如果有）
        cancelTeleport(playerId);

        // 通知玩家
        player.sendMessage(ChatColor.YELLOW + "传送将在 " + teleportDelay + " 秒后开始，请不要移动...");

        // 记录初始位置
        final Location initialLocation = player.getLocation().clone();

        // 创建传送任务
        BukkitTask task = new BukkitRunnable() {
            private int countdown = teleportDelay;

            @Override
            public void run() {
                // 检查玩家是否移动
                if (hasPlayerMoved(player, initialLocation)) {
                    player.sendMessage(ChatColor.RED + "传送已取消，你移动了！");
                    cancelTeleport(playerId);
                    return;
                }

                // 检查玩家是否在战斗中
                if (isInCombat(player)) {
                    player.sendMessage(ChatColor.RED + "传送已取消，你进入了战斗状态！");
                    cancelTeleport(playerId);
                    return;
                }

                // 倒计时
                if (countdown > 0) {
                    // 显示粒子效果
                    if (showParticles) {
                        player.getWorld().spawnParticle(
                                Particle.PORTAL,
                                player.getLocation().add(0, 1, 0),
                                20, 0.5, 0.5, 0.5, 0.1);
                    }

                    // 播放音效
                    if (playSound) {
                        player.playSound(player.getLocation(), Sound.BLOCK_PORTAL_TRIGGER, 0.3f, 1.0f);
                    }

                    countdown--;
                } else {
                    // 执行传送
                    executeTeleport(player, anchorLocation);
                    cancel();
                    teleportTasks.remove(playerId);
                }
            }
        }.runTaskTimer(plugin, 0L, 20L);

        // 保存任务引用
        teleportTasks.put(playerId, task);
    }

    // 检查玩家是否移动
    private boolean hasPlayerMoved(Player player, Location initialLocation) {
        Location currentLocation = player.getLocation();

        // 检查世界是否相同
        if (!currentLocation.getWorld().equals(initialLocation.getWorld())) {
            return true;
        }

        // 检查位置是否变化（允许视角旋转）
        double distance = currentLocation.distance(initialLocation);
        return distance > 0.1; // 允许微小的移动（网络抖动）
    }

    // 取消传送
    private void cancelTeleport(UUID playerId) {
        BukkitTask task = teleportTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }
    }

    // 执行传送
    private void executeTeleport(Player player, Location destination) {
        UUID playerId = player.getUniqueId();

        // 传送玩家
        player.teleport(destination);

        // 播放音效
        if (playSound) {
            player.playSound(destination, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
        }

        // 显示粒子效果
        if (showParticles) {
            destination.getWorld().spawnParticle(
                    Particle.DRAGON_BREATH,
                    destination.clone().add(0.5, 1, 0.5),
                    50, 0.5, 1, 0.5, 0.1);
        }

        // 移除锚点
        removeAnchor(playerId);

        // 如果配置为传送后消耗道具
        if (consumeOnTeleport) {
            // 在下一个tick移除物品，避免并发修改问题
            // 注意：这里不能使用Session的takeItem方法，因为我们不在会话中
            // 所以使用自定义的方法移除物品
            new BukkitRunnable() {
                @Override
                public void run() {
                    removeMemoryAnchorItem(player);
                }
            }.runTask(plugin);
        }

        player.sendMessage(ChatColor.GREEN + "成功传送到记忆锚点！");
    }

    // 移除玩家背包中的记忆锚点物品
    private void removeMemoryAnchorItem(Player player) {
        ItemStack[] contents = player.getInventory().getContents();

        for (int i = 0; i < contents.length; i++) {
            ItemStack item = contents[i];

            if (item != null && item.getType() == Material.BEACON &&
                    item.hasItemMeta() && item.getItemMeta().hasDisplayName() &&
                    ChatColor.stripColor(item.getItemMeta().getDisplayName()).contains("记忆锚点")) {

                if (item.getAmount() > 1) {
                    item.setAmount(item.getAmount() - 1);
                } else {
                    player.getInventory().setItem(i, null);
                }

                player.updateInventory();
                break;
            }
        }
    }

    // 会话类，处理玩家使用道具的逻辑
    private class Session extends SpecialItemUseSession {
        private Block placedBlock;

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            // 如果会话被强制停止，清理资源
            if (placedBlock != null) {
                placedBlock.setType(Material.AIR);
            }
        }

        public void run() {
            Player player = getEvent().getPlayer();
            UUID playerId = player.getUniqueId();

            // 获取玩家所在竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(player);
            if (arena == null || arena.getStatus() != ArenaStatus.RUNNING) {
                player.sendMessage(ChatColor.RED + "你只能在游戏中使用记忆锚点！");
                stop();
                return;
            }

            // 检查玩家是否潜行（用于区分设置锚点和传送）
            boolean isSneaking = player.isSneaking();

            if (isSneaking) {
                // 玩家潜行，尝试传送回锚点
                if (playerAnchors.containsKey(playerId)) {
                    startTeleport(player);
                } else {
                    player.sendMessage(ChatColor.RED + "你没有设置记忆锚点！");
                }
            } else {
                // 玩家未潜行，设置锚点

                // 获取目标方块
                Block targetBlock = player.getTargetBlock(null, 5);

                // 检查目标方块是否有效
                if (targetBlock == null || targetBlock.getType() == Material.AIR) {
                    player.sendMessage(ChatColor.RED + "请对着地面使用此道具！");
                    stop();
                    return;
                }

                // 获取放置位置（目标方块上方）
                Location location = targetBlock.getLocation().add(0, 1, 0);
                Block placeBlock = location.getBlock();

                // 检查放置位置是否为空气
                if (placeBlock.getType() != Material.AIR) {
                    player.sendMessage(ChatColor.RED + "无法在此处设置记忆锚点！");
                    stop();
                    return;
                }

                // 设置锚点
                this.placedBlock = placeBlock;
                setAnchor(player, location);

                // 如果配置为使用后消耗道具
                if (consumeOnUse) {
                    super.takeItem();
                }
            }
        }
    }
}
