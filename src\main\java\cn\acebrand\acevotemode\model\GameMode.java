package cn.acebrand.acevotemode.model;

import org.bukkit.ChatColor;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * 游戏模式数据模型
 */
public class GameMode {
    
    private final String id;
    private final String name;
    private final List<String> description;
    private final ItemStack icon;
    
    public GameMode(String id, String name, List<String> description, ItemStack icon) {
        this.id = id;
        this.name = name;
        this.description = new ArrayList<>(description);
        this.icon = icon.clone();
        
        // 设置图标的名称和描述
        setupIcon();
    }
    
    /**
     * 设置图标的显示名称和描述
     */
    private void setupIcon() {
        ItemMeta meta = icon.getItemMeta();
        if (meta != null) {
            // 设置显示名称
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));
            
            // 设置描述
            List<String> lore = new ArrayList<>();
            for (String line : description) {
                lore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            lore.add(""); // 空行
            lore.add(ChatColor.YELLOW + "点击投票给此模式");
            
            meta.setLore(lore);
            icon.setItemMeta(meta);
        }
    }
    
    /**
     * 获取模式ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * 获取模式名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取格式化的名称（去除颜色代码）
     */
    public String getPlainName() {
        return ChatColor.stripColor(ChatColor.translateAlternateColorCodes('&', name));
    }
    
    /**
     * 获取模式描述
     */
    public List<String> getDescription() {
        return new ArrayList<>(description);
    }
    
    /**
     * 获取模式图标
     */
    public ItemStack getIcon() {
        return icon.clone();
    }
    
    /**
     * 获取带票数的图标
     */
    public ItemStack getIconWithVotes(int votes) {
        ItemStack iconWithVotes = getIcon();
        ItemMeta meta = iconWithVotes.getItemMeta();
        
        if (meta != null) {
            List<String> lore = meta.getLore();
            if (lore == null) {
                lore = new ArrayList<>();
            }
            
            // 移除旧的票数信息
            lore.removeIf(line -> ChatColor.stripColor(line).contains("当前票数"));
            
            // 添加新的票数信息
            lore.add("");
            lore.add(ChatColor.GREEN + "当前票数: " + ChatColor.WHITE + votes);
            lore.add(ChatColor.YELLOW + "点击投票给此模式");
            
            meta.setLore(lore);
            iconWithVotes.setItemMeta(meta);
        }
        
        return iconWithVotes;
    }
    
    @Override
    public String toString() {
        return "GameMode{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        GameMode gameMode = (GameMode) obj;
        return id.equals(gameMode.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
