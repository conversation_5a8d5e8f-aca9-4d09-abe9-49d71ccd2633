# TNT生成事件配置
# 坏事件 - 生成5个点燃的TNT

# 事件基本信息
event:
  name: "TNT_SPAWN"
  type: "BAD"
  weight: 30
  enabled: true

# 消息设置
messages:
  # 是否发送事件消息到聊天栏
  send_message: true
  # 消息前缀
  message_prefix: "§c[幸运方块] §f"
  # 事件消息
  event_message: "§c嘶嘶嘶...TNT来了！快跑！"

# TNT配置
tnt_spawn:
  # TNT数量
  tnt_count: 5
  # TNT生成范围（以玩家为中心的半径）
  spawn_radius: 5.0
  # TNT引爆时间（游戏刻，80刻=4秒）
  fuse_time: 80
  # TNT爆炸威力
  explosion_power: 4.0
  # 是否破坏方块
  break_blocks: true
  # 是否产生火焰
  set_fire: true
  # TNT生成高度偏移（相对于玩家位置）
  height_offset: 2
  # 是否给TNT随机速度
  random_velocity:
    enabled: true
    # 水平速度范围
    horizontal_range: 0.3
    # 垂直速度范围
    vertical_range: 0.2
  
  # 音效配置
  sound:
    # 是否播放TNT点燃音效
    enabled: true
    sound_type: "ENTITY_TNT_PRIMED"
    volume: 1.0
    pitch: 1.0
  
  # 粒子效果
  particles:
    # 是否显示粒子效果
    enabled: true
    particle_type: "SMOKE_NORMAL"
    count: 10
