package cn.acebrand.acevotemode.listener;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.manager.VoteManager;
import cn.acebrand.acevotemode.model.GameMode;
import cn.acebrand.acevotemode.task.VoteCountdownTask;
import cn.acebrand.acevotemode.util.MessageUtil;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.event.arena.ArenaLobbyCountdownStartEvent;
import de.marcely.bedwars.api.event.arena.ArenaStatusChangeEvent;
import de.marcely.bedwars.api.event.arena.RoundStartEvent;
import de.marcely.bedwars.api.event.arena.RoundEndEvent;
import de.marcely.bedwars.api.event.player.PlayerJoinArenaEvent;
import de.marcely.bedwars.api.event.player.PlayerQuitArenaEvent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 竞技场事件监听器
 * 监听竞技场状态变化和玩家事件
 */
public class ArenaEventListener implements Listener {

    private final AceVoteMode plugin;
    private final Map<Arena, BukkitTask> countdownTasks;

    public ArenaEventListener(AceVoteMode plugin) {
        this.plugin = plugin;
        this.countdownTasks = new ConcurrentHashMap<>();
    }

    /**
     * 监听竞技场状态变化
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onArenaStatusChange(ArenaStatusChangeEvent event) {
        Arena arena = event.getArena();
        ArenaStatus newStatus = event.getNewStatus();

        switch (newStatus) {
            case LOBBY:
                // 竞技场进入大厅状态，重置投票数据
                plugin.getVoteManager().getArenaVoteData(arena).clear();
                if (plugin.getConfigManager().isEnableArenaLogs()) {
                }
                break;

            case END_LOBBY:
                // 大厅阶段结束，准备开始游戏
                // 这里可以添加大厅结束时的处理逻辑
                break;

            case RUNNING:
                // 游戏开始，显示获胜模式
                handleGameStart(arena);
                break;

            case RESETTING:
            case STOPPED:
                // 竞技场重置或停止，清理数据
                cleanupArenaData(arena);
                break;
        }
    }

    /**
     * 监听大厅倒计时开始
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onLobbyCountdownStart(ArenaLobbyCountdownStartEvent event) {
        if (event.isCancelled()) {
            return;
        }

        Arena arena = event.getArena();

        // 取消之前的倒计时任务
        BukkitTask oldTask = countdownTasks.remove(arena);
        if (oldTask != null && !oldTask.isCancelled()) {
            oldTask.cancel();
        }

        // 检查当前倒计时时间，判断是否需要立即处理投票
        int countdownSeconds = (int) event.getDuration().getSeconds();
        int voteCloseTime = plugin.getConfigManager().getVoteDisableSeconds();

        if (countdownSeconds <= voteCloseTime) {
            // 倒计时已经很短（管理员强制开始或其他原因），立即关闭投票并选择模式
            handleImmediateVoteClose(arena);
        } else {
            // 正常启动投票倒计时任务
            VoteCountdownTask countdownTask = new VoteCountdownTask(plugin, arena, event.getDuration());
            BukkitTask task = countdownTask.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次
            countdownTasks.put(arena, task);
        }

        if (plugin.getConfigManager().isEnableArenaLogs()) {
        }
    }

    /**
     * 监听游戏开始
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onRoundStart(RoundStartEvent event) {
        Arena arena = event.getArena();
        handleGameStart(arena);
    }

    /**
     * 监听玩家加入竞技场
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoinArena(PlayerJoinArenaEvent event) {
        // 记录玩家加入日志
        if (plugin.getConfigManager().isEnablePlayerLogs()) {
        }
    }

    /**
     * 监听玩家离开竞技场
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuitArena(PlayerQuitArenaEvent event) {
        Arena arena = event.getArena();
        Player player = event.getPlayer();

        // 移除玩家的投票
        boolean hadVote = plugin.getVoteManager().removePlayerVote(player, arena);

        if (hadVote && plugin.getConfigManager().isEnableVoteLogs()) {
        }

        // 检查是否需要重置投票状态
        checkAndResetVotingIfNeeded(arena);

        // 记录玩家离开日志
        if (plugin.getConfigManager().isEnablePlayerLogs()) {
        }
    }

    /**
     * 监听游戏结束
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onRoundEnd(RoundEndEvent event) {
        Arena arena = event.getArena();

        // 游戏结束后清理投票数据
        plugin.getVoteManager().getArenaVoteData(arena).clear();

        // 停用游戏模式
        plugin.getGameModeManager().deactivateGameMode(arena);

        // 清理全局资源生成
        plugin.getGlobalResourceManager().cleanupArena(arena);

        if (plugin.getConfigManager().isEnableArenaLogs()) {
        }
    }

    /**
     * 处理立即关闭投票（管理员强制开始等情况）
     */
    private void handleImmediateVoteClose(Arena arena) {
        // 确保投票已关闭
        plugin.getVoteManager().closeVoting(arena);

        // 通知所有玩家投票已关闭
        String message = plugin.getConfigManager().getMessage("vote-closed");
        for (Player player : arena.getPlayers()) {
            player.sendMessage(message);
        }

        // 显示投票统计
        MessageUtil.displayVoteStatistics(plugin, arena);

        // 延迟显示投票结果GUI并选择模式
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            plugin.getVoteResultManager().showVoteResultAndSelectMode(arena);
        }, 20L); // 1秒延迟

        if (plugin.getConfigManager().isEnableArenaLogs()) {
        }
    }

    /**
     * 处理游戏开始
     */
    private void handleGameStart(Arena arena) {
        // 取消倒计时任务
        BukkitTask task = countdownTasks.remove(arena);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }

        // 检查是否已经处理过这个竞技场的游戏开始
        if (plugin.getGameModeManager().isArenaProcessed(arena)) {
            return; // 避免重复处理
        }

        // 获取已选择的游戏模式
        GameMode selectedMode = plugin.getVoteManager().getWinningMode(arena);

        if (selectedMode != null) {

            // 应用游戏模式
            boolean activated = plugin.getGameModeManager().activateGameMode(arena, selectedMode.getId());

            if (activated) {
                // 只显示title，消息由游戏模式自己处理
                showGameModeTitle(arena, selectedMode);
            } else {
            }
        }

        // 检查是否需要应用全局资源生成（即使有游戏模式也可能需要）
        plugin.getGlobalResourceManager().applyGlobalResources(arena);

        // 游戏开始后清理投票数据（延迟清理，确保模式已经正确应用）
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            plugin.getVoteManager().clearArenaData(arena);
        }, 20L); // 1秒后清理

        if (plugin.getConfigManager().isEnableArenaLogs()) {
        }
    }

    /**
     * 向竞技场所有玩家显示游戏模式title
     */
    private void showGameModeTitle(Arena arena, GameMode gameMode) {
        // 延迟显示title，确保玩家已经准备好
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // 使用ChatColor.translateAlternateColorCodes处理颜色代码
            String title = org.bukkit.ChatColor.translateAlternateColorCodes('&', "&c&l" + gameMode.getPlainName());

            // 安全获取subtitle
            String subtitle = "";
            if (gameMode.getDescription() != null && !gameMode.getDescription().isEmpty()) {
                subtitle = org.bukkit.ChatColor.translateAlternateColorCodes('&',
                        "&7" + gameMode.getDescription().get(0));
            } else {
                subtitle = org.bukkit.ChatColor.translateAlternateColorCodes('&', "&7游戏模式已启动！");
            }

            for (Player player : arena.getPlayers()) {
                if (player.isOnline()) {
                    // 发送title
                    player.sendTitle(title, subtitle, 20, 80, 20); // 淡入1秒，显示4秒，淡出1秒

                    // 发送模式教程信息到聊天栏
                    sendModeStartTutorial(player, gameMode);

                    // 播放音效
                    try {
                        player.playSound(player.getLocation(),
                                org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
                    } catch (Exception e) {
                        // 如果音效播放失败，使用备用音效
                        try {
                            player.playSound(player.getLocation(),
                                    org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.5f);
                        } catch (Exception ex) {
                            // 忽略音效错误
                        }
                    }
                }
            }
        }, 40L); // 2秒延迟，确保游戏已经开始
    }

    /**
     * 检查并在需要时重置投票状态
     */
    private void checkAndResetVotingIfNeeded(Arena arena) {
        // 只在大厅状态下检查
        if (arena.getStatus() != ArenaStatus.LOBBY) {
            return;
        }

        VoteManager.ArenaVoteData voteData = plugin.getVoteManager().getArenaVoteData(arena);

        // 如果投票已关闭，检查是否还有玩家在竞技场
        if (voteData.isVotingClosed()) {
            int playerCount = arena.getPlayers().size();

            // 如果没有玩家了，或者玩家数量不足以开始游戏，重置投票状态
            if (playerCount == 0 || playerCount < arena.getMinPlayers()) {
                // 重置投票状态
                voteData.clear();
                voteData.setVotingClosed(false);

                // 取消倒计时任务
                BukkitTask task = countdownTasks.remove(arena);
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }

            }
        }
    }

    /**
     * 清理竞技场数据
     */
    private void cleanupArenaData(Arena arena) {
        // 取消倒计时任务
        BukkitTask task = countdownTasks.remove(arena);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }

        // 注意：不在这里清理投票数据，投票数据应该在游戏真正结束后清理
        // 这样可以确保游戏开始时还能获取到正确的投票结果

        if (plugin.getConfigManager().isEnableArenaLogs()) {
        }
    }

    /**
     * 发送模式启动教程信息到聊天栏
     */
    private void sendModeStartTutorial(Player player, GameMode gameMode) {

        // 从配置文件获取消息
        String border = plugin.getConfigManager().getMessage("mode-start.border");
        String titleTemplate = plugin.getConfigManager().getMessage("mode-start.title");
        String description = plugin.getConfigManager().getMessage("mode-start.descriptions." + gameMode.getId());

        // 如果没有找到特定模式的描述，使用默认描述或简单消息
        if (description.contains("消息未找到")) {
            // 使用简单的启动消息作为后备
            player.sendMessage(org.bukkit.ChatColor.translateAlternateColorCodes('&',
                    "&c&l" + gameMode.getPlainName() + " &a模式已启动！"));
            return;
        }

        // 替换标题中的模式名称
        String title = titleTemplate.replace("{mode}", gameMode.getPlainName());

        // 发送华丽的教程信息
        player.sendMessage("");
        player.sendMessage(border);
        player.sendMessage(title);

        // 处理多行描述信息
        String[] lines = description.split("\n");
        for (String line : lines) {
            // 转换颜色代码
            player.sendMessage(org.bukkit.ChatColor.translateAlternateColorCodes('&', line));
        }

        player.sendMessage(border);
        player.sendMessage("");
    }
}
