package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a match is about to end and the winning team needs to be determined.
 */
public class ArenaWinningTeamDetermineEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private Team winningTeam;

  public ArenaWinningTeamDetermineEvent(Arena arena, @Nullable Team winningTeam) {
    this.arena = arena;
    this.winningTeam = winningTeam;
  }

  /**
   * Returns if the round has ended as a tie.
   * <p>
   *   A round is considered a tie if no team has won and
   *   {@link #getWinningTeam()} returns <code>null</code>.
   * </p>
   *
   * @return If the round ended as a tie
   */
  public boolean isTie() {
    return this.winningTeam == null;
  }

  /**
   * Sets the round to end as a tie.
   * <p>
   *   This will set the winning team to <code>null</code>.
   * </p>
   */
  public void setTie() {
    this.winningTeam = null;
  }

  /**
   * Sets the winning team of the arena.
   * <p>
   *   If the round is a tie, use {@link #setTie()} instead.
   * </p>
   *
   * @param team The winning team
   */
  public void setWinningTeam(Team team) {
    Validate.notNull(team, "team");

    this.winningTeam = team;
  }

  /**
   * Returns the winning team of the arena.
   *
   * @return The winning team. <code>null</code> if the round ended as a tie
   */
  @Nullable
  public Team getWinningTeam() {
    return this.winningTeam;
  }

  @Override
  public Arena getArena() {
    return this.arena;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
