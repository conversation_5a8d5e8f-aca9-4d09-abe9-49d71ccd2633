package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.entity.Player;
import org.bukkit.Location;

/**
 * 怪物生成事件 - 坏事件
 */
public class MonsterSpawnEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    
    public MonsterSpawnEvent(AceVoteMode plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // TODO: 实现怪物生成逻辑
        player.sendMessage("§c[幸运方块] §f危险！怪物出现了！");
        plugin.getLogger().info("执行怪物生成事件: " + player.getName());
    }
    
    @Override
    public String getName() {
        return "MONSTER_SPAWN";
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public int getWeight() {
        return 10; // 权重
    }
}
