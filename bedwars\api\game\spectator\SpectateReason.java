package de.marcely.bedwars.api.game.spectator;

/**
 * Reasons or causes for someone spectating
 */
public enum SpectateReason {

  /**
   * Player entered manually
   */
  ENTER,

  /**
   * Player lost and is now watching the game
   */
  LOSE,

  /**
   * Player died and is temporarily a spectator
   */
  DEATH,

  /**
   * Player used the "join a random arena" item as a spectator, but failed and reentered as a spectator
   */
  ARENA_CHANGE_FAILED,

  /**
   * Player is a member of a party and followed its leader. However, the match is already running, whereby he's spectating instead.
   */
  FOLLOW_PARTY,

  /**
   * Player entered as a spectator to watch another person play using the 'locate player' command.
   */
  LOCATE_PLAYER,

  /**
   * A plugin made him into a spectator
   */
  PLUGIN;

  /**
   * Get whether a back up of the player's inventory is being created in this reason.
   * <p>
   *   Depending on the server's configuration, the players inventory is being backed up before he spectates an arena.
   *   And when he leaves, it is possibly being applied again.
   * </p>
   *
   * @return If a backup is being created of the player's inventory
   */
  public boolean isBackingUpInventory() {
    switch (this) {
      case ARENA_CHANGE_FAILED:
      case DEATH:
        return false;
      default:
        return true;
    }
  }

  /**
   * Get whether the join message shall be sent to the player with this reason.
   * <p>
   *   It might not be neccessary to display the join message (Spectator_Join) in some cases.
   * </p>
   *
   * @return Whether the join message shall be sent with this reason
   */
  public boolean isDisplayingJoinMessage() {
    switch (this) {
      case ARENA_CHANGE_FAILED:
      case DEATH:
        return false;
      default:
        return true;
    }
  }
}
