package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerEvent;

import java.util.Collection;
import java.util.Set;

/**
 * Gets called when a player dies during a match
 */
public class PlayerIngameDeathEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final PlayerDeathEvent bukkitEvent;
  private final Arena arena;

  private boolean fatalDeath;
  private Message deathMessage;
  private Set<Player> deathMessageTargets;
  private int deathSpectateDuration;

  public PlayerIngameDeathEvent(PlayerDeathEvent bukkitEvent, Arena arena, boolean fatalDeath, Message deathMessage, Set<Player> deathMessageTargets, int deathSpectateDuration) {
    super(bukkitEvent.getEntity());

    this.bukkitEvent = bukkitEvent;
    this.arena = arena;
    this.fatalDeath = fatalDeath;
    this.deathMessage = deathMessage != null ? deathMessage.cloneNonUpcyable() : null;
    this.deathMessageTargets = deathMessageTargets;
    this.deathSpectateDuration = deathSpectateDuration;
  }

  /**
   * Returns the event that got everything rolling and that has been used to identify the players death.
   *
   * @return The event that contains deeper info about the players death
   */
  public PlayerDeathEvent getBukkitEvent() {
    return this.bukkitEvent;
  }

  /**
   * Returns the arena in which the player has died.
   *
   * @return The involved arena
   */
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns whether or not the player is getting excluded due his death.
   *
   * @return If his death was fatal or not
   */
  public boolean isFatalDeath() {
    return this.fatalDeath;
  }

  /**
   * Set whether the player shall get excluded due his death.
   *
   * @param fatalDeath If his death was fatal or not
   */
  public void setFatalDeath(boolean fatalDeath) {
    this.fatalDeath = fatalDeath;
  }

  /**
   * Returns the death message that'll be displayed in the chat to the players instead of the default vanilla one.
   * <p>
   *     This method never returns <code>null</code>. It might return an empty String with the {@link String#length()} of 0.
   *     In this case it won't display any death message to him.
   *     <br>
   *     Make sure to not free the Message instance! Otherwise you might face an unexpected behaviour.
   * </p>
   *
   * @return The death message of his death
   */
  public Message getDeathMessage() {
    return this.deathMessage;
  }

  /**
   * Set the death message that'll be displayed in the chat to the players instead of the default vanilla one.
   * <p>
   *     Pass in an empty string (has a {@link String#length()} of 0). In this case it won't display any death message.
   * </p>
   *
   * @param deathMessage The new death message of his death
   */
  public void setDeathMessage(Message deathMessage) {
    Validate.notNull(deathMessage, "deathMessage. Use an empty String to not display any death message");

    this.deathMessage = deathMessage.cloneNonUpcyable();
  }

  /**
   * Returns all the players to whom the custom death message will be displayed to.
   * <p>
   *     This calculates it for all players on the server and not only for those inside the arena.
   *     It's legal to modify the entries of the returning Collection.
   * </p>
   *
   * @return All players (on the server) to whom the death message will be shown
   */
  public Set<Player> getDeathMessageTargets() {
    return this.deathMessageTargets;
  }

  /**
   * Add a player to whom the custom death message will be displayed to.
   * <p>
   *     You may add any player on the server. This does not only count for the players on the server.
   *     Using {@link #getDeathMessageTargets()}#add(player) is also legal.
   * </p>
   *
   * @param player The player who shall be added
   * @return <code>false</code> if he's already receiving it, otherwise <code>true</code>
   */
  public boolean addDeathMessageTarget(Player player) {
    return this.deathMessageTargets.add(player);
  }

  /**
   * Add multiple players simultaneously to whom the custom death message will be displayed to.
   * <p>
   *     You may add any player on the server. This does not only count for the players on the server.
   *     Using {@link #getDeathMessageTargets()}#add(players) is also legal.
   * </p>
   *
   * @param players The players who shall be added
   * @return <code>true</code> if at least one player has been newly added
   */
  public boolean addDeathMessageTargets(Collection<? extends Player> players) {
    return this.deathMessageTargets.addAll(players);
  }


  /**
   * Remove a player to whom the custom death message shall not be displayed to.
   * <p>
   *     You may remove any player on the server. This does not only count for the players on the server.
   *     Using {@link #getDeathMessageTargets()}#remove(player) is also legal.
   * </p>
   *
   * @param player The player who shall be removed
   * @return <code>false</code> if he hasn't already been receiving it, otherwise <code>true</code>
   */
  public boolean removeDeathMessageTarget(Player player) {
    return this.deathMessageTargets.remove(player);
  }

  /**
   * Remove players to whom the custom death message shall not be displayed to.
   * <p>
   *     You may remove any player on the server. This does not only count for the players on the server.
   *     Using {@link #getDeathMessageTargets()}#remove(players) is also legal.
   * </p>
   *
   * @param players The players who shall be removed
   * @return <code>true</code> if at least one player has been removed
   */
  public boolean removeDeathMessageTargets(Collection<? extends Player> players) {
    return this.deathMessageTargets.removeAll(players);
  }

  /**
   * Returns the duration in seconds in which the player will enter the spectator mode after his respawn.
   * He has to wait this duration until he's able to respawn again.
   * <p>
   *     This returning 0 or less causes him to get respawned immediately.
   * </p>
   *
   * @return The duration in seconds he'll have to wait until he respawns. <code>-1</code> to make him respawn immediately
   */
  public int getDeathSpectateDuration() {
    return this.deathSpectateDuration;
  }

  /**
   * Set the duration in seconds in which the player will enter the spectator mode after his respawn.
   * He has to wait this duration until he's able to respawn again.
   * <p>
   *     Setting this to 0 or less causes him to get respawned immediately.
   * </p>
   *
   * @param duration Set the duration in seconds he'll have to wait until he respawns. <code>-1</code> to make him respawn immediately
   */
  public void setDeathSpectateDuration(int duration) {
    this.deathSpectateDuration = duration;
  }

  /**
   * Returns whether the player will have to wait by spectating.
   * <p>
   *     Use {@link #setDeathSpectateDuration(int)} to modify the behaviour.
   * </p>
   *
   * @return Whether he'll have to wait to respawn or not
   */
  public boolean isDeathSpectating() {
    return this.deathSpectateDuration >= 1;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
