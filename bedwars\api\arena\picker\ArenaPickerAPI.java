package de.marcely.bedwars.api.arena.picker;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.picker.condition.ArenaConditionGroup;
import de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariable;
import de.marcely.bedwars.api.exception.ArenaConditionParseException;
import de.marcely.bedwars.api.exception.ArenaPickerParseException;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Set;

/**
 * This class contains all the API that is relevant to arena pickers.
 * <p>
 *     In case you are looking to obtain an arena given by a condition, you may simply use {@link de.marcely.bedwars.api.GameAPI#getArenaByName(String)}.
 * </p>
 */
public interface ArenaPickerAPI {

  /**
   * Parses the given condition and returns all arenas for which the condition applies to.
   * <p>
   * 	An example of an arena condition is {@literal [}status=2 {@literal &} teams=4{@literal ]}
   * </p>
   *
   * @param string The arena condition string that will be parsed.
   * @return All arenas that meet the pickers conditions
   * @throws ArenaConditionParseException When it failed to parse the String
   */
  Collection<Arena> getArenasByCondition(String string) throws ArenaConditionParseException;

  /**
   * Parses the picker.
   *
   * @param string The String that will be parsed
   * @return The parsed picker
   * @throws ArenaConditionParseException When it failed to parse the condition (because of e.g. malformed syntax)
   * @throws ArenaPickerParseException When it failed to parse the picker (because of e.g. malformed syntax)
   * @see ArenaPicker#serialize()
   */
  ArenaPicker parsePicker(String string) throws ArenaConditionParseException, ArenaPickerParseException;

  /**
   * Parses the condition.
   *
   * @param string The String that will be parsed
   * @return The parsed condition
   * @throws ArenaConditionParseException When it failed (because of e.g. malformed syntax)
   * @see ArenaConditionGroup#serialize(boolean)
   */
  ArenaConditionGroup parseCondition(String string) throws ArenaConditionParseException;

  /**
   * Gets the names of all registered condition variables.
   *
   * @return All registered condition variable names
   */
  Set<String> getConditionVariableNames();

  /**
   * Gets all condition registered condition variables.
   *
   * @return All registered condition variables
   */
  Collection<ArenaConditionVariable<?>> getConditionVariables();

  /**
   * Tries to get a condition variable given by its name.
   *
   * @param name The name of the condition variable
   * @return The matching condition variable. <code>null</code> if none has been found
   */
  @Nullable
  ArenaConditionVariable<?> getConditionVariableByName(String name);

  /**
   * Tries to register the condition variable
   *
   * @param variable The condition variable instance
   * @return <code>true</code> when it succeded. May fail when e.g. the name is already taken
   */
  boolean registerConditionVariable(ArenaConditionVariable<?> variable);

  /**
   * Tries to unregister the condition variable
   *
   * @param variable The condition variable instance
   * @return <code>true</code> when it has been found and removed
   */
  boolean unregisterConditionVariable(ArenaConditionVariable<?> variable);

  /**
   * Gets whether a given condition variable instance is currently registered
   *
   * @param variable The condition variable instance
   * @return <code>true</code> when it's currently registered
   */
  boolean isConditionVariableRegistered(ArenaConditionVariable<?> variable);

  /**
   * Gets the names of all registered selectors.
   *
   * @return The names of all selectors
   */
  Set<String> getSelectorNames();

  /**
   * Gets the instances of all registered selectors.
   *
   * @return All registered selectors
   */
  Collection<ArenaSelector> getSelectors();

  /**
   * Tries to find a selector given by its name.
   *
   * @param name Its name
   * @return The matching instance. <code>null</code> in case none has been found
   */
  @Nullable
  ArenaSelector getSelectorByName(String name);

  /**
   * Tries to register a new selector instance.
   *
   * @param selector The instance that shall be registered
   * @return <code>true</code> when it succeded. May fail when e.g. the name is already taken
   */
  boolean registerSelector(ArenaSelector selector);

  /**
   * Tries to unregister the given selector instance.
   *
   * @param selector The instance that shall be removed
   * @return <code>true</code> when it has been found and unregistered
   */
  boolean unregisterSelector(ArenaSelector selector);

  /**
   * Find out whether a selector is currently registered
   *
   * @param selector The given instance
   * @return <code>true</code> in case it's currently registered
   */
  boolean isSelectorRegistered(ArenaSelector selector);

  /**
   * Returns the global ArenaPickerAPI instance.
   *
   * @return The global ArenaPickerAPI instance
   */
  static ArenaPickerAPI get() {
    return BedwarsAPILayer.INSTANCE.getArenaPickerAPI();
  }
}
