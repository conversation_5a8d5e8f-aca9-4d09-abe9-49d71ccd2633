package de.marcely.bedwars.tools;

import com.google.gson.JsonObject;
import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.tools.location.XYZ;
import java.time.Duration;
import net.md_5.bungee.api.ChatColor;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionType;
import org.jetbrains.annotations.Nullable;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.UUID;
import java.util.function.Consumer;

public interface Helper {

  /**
   * Returns the current java version.
   *
   * @return The current java version used in this JVM
   */
  int getJavaVersion();

  /**
   * Parses the string to an int
   *
   * @param str The string we want to parse
   * @return Parsed string. Null if it's not valid
   */
  @Nullable Integer parseInt(String str);

  /**
   * Parses the string to a long
   *
   * @param str The string we want to parse
   * @return Parsed string. Null if it's not valid
   */
  @Nullable Long parseLong(String str);

  /**
   * Parses the string to a double
   *
   * @param str The string we want to parse
   * @return Parsed string. Null if it's not valid
   */
  @Nullable Double parseDouble(String str);

  /**
   * Tries to format the number in a human-readable way.
   * <p>
   * Uses system or user configuration to understand in which language it shall format in.<br>
   * Example: When user is running an english system and you're passing the number 1989321, then this method returns "1,989,321"
   * </p>
   *
   * @param num The number you want to get formatted
   * @return The formatted number
   */
  String formatNumber(long num);

  /**
   * Tries to format the number in a human-readable way.
   * <p>
   * Uses system or user configuration to understand in which language it shall format in.<br>
   * Example: When user is running an english system and you're passing the number 1989321.5555, then this method returns "1,989,321.555,5"
   * </p>
   *
   * @param num The number you want to get formatted
   * @return The formatted number
   */
  String formatNumber(double num);

  /**
   * Tries to format the number in a human-readable way.
   * <p>
   * Uses system or user configuration to understand in which language it shall format in.<br>
   * Example: When user is running an english system and you're passing the number 1989321.5555, then this method returns "1,989,321.555,5"
   * </p>
   *
   * @param num The number you want to get formatted
   * @return The formatted number
   */
  String formatNumber(BigDecimal num);

  /**
   * Formats the duration in a human-readable string.
   * <p>
   *   Example: 5h 10m 30s. Exact value depends on the configuration of the user.
   * </p>
   *
   * @param duration the duration to format
   * @return The human-readable format
   */
  default String formatDuration(Duration duration) {
    return formatDuration(duration.toMillis());
  }

  /**
   * Formats the milliseconds duration in a human-readable string.
   * <p>
   *   Example: 5h 10m 30s. Exact value depends on the configuration of the user.
   * </p>
   *
   * @param duration the duration to format in milliseconds
   * @return The human-readable format
   */
  String formatDuration(long duration);

  /**
   * Tries to evaluate an equation passed in as a String.
   * <p>
   *   Uses the javaluator library internally.
   *   Example: If you pass in the string <code>(e^3-1)*sin(pi/4)*ln(pi^2)</code>, the result would be 30.8974331526.
   * </p>
   *
   * @param expression The expression you want to evaluate
   * @return The solution to the expression. May be <code>null</code> if the equation is invalid.
   */
  @Nullable Double evaluateString(String expression);

  /**
   * Tries to form an {@link ItemStack} given by a String.
   * The same format as every else in the plugin configurations is being used.
   * <p>
   *     The opposite of that is {@link #composeItemStack(ItemStack)}.
   * </p>
   * @param string The String that shall get parsed
   * @return The resulting ItemStack. May be <code>null</code> if the given String is nonsense
   */
  @Nullable ItemStack parseItemStack(String string);

  /**
   * Forms a human-readable String given by an ItemStack.
   * The same format as every else in the plugin configurations is being used.
   * <p>
   *     The opposite of that is {@link #parseItemStack(String)}.
   * </p>
   *
   * @param is The ItemStack that shall be parsed
   * @return The resulting human-readable String
   */
  String composeItemStack(ItemStack is);

  /**
   * Tries to apply a color to an {@link ItemStack} if possible.
   * This includes e.g. dying a leather armor or changing the wool color.
   * <p>
   *     It's possible that this method doesn't return a new instance and instead applies
   *     the effect to the instance given.
   * </p>
   *
   * @param is The item that shall be dyed
   * @param color The color that needs to be applied to the item
   * @return Possibly a new instance depending on the internal method used. Otherwise the same ItemStack instance
   */
  ItemStack dye(ItemStack is, DyeColor color);

  /**
   * Sets the "unbreakable" tag to the given ItemMeta.
   * <p>
   *     You should use this method instead of {@link ItemMeta#setUnbreakable(boolean)}
   *     as that method has involved in some spigot versions and by that might not work for any user.
   * </p>
   *
   * @param im The ItemMeta of the item whose tag shall be changed
   * @param unbreakable <code>true</code> causes the item to not lose any damage
   */
  void setUnbreakable(ItemMeta im, boolean unbreakable);

  /**
   * Sets the "unbreakable" tag to the given item.
   * <p>
   *     You should use this method instead of {@link ItemMeta#setUnbreakable(boolean)}
   *     as that method has involved in some spigot versions and by that might not work for any user.
   * </p>
   *
   * @param is The item whose tag shall be changed
   * @param unbreakable <code>true</code> causes the item to not lose any damage
   */
  default void setUnbreakable(ItemStack is, boolean unbreakable) {
    final ItemMeta im = is.getItemMeta();

    setUnbreakable(im, unbreakable);
    is.setItemMeta(im);
  }

  /**
   * Will return a new String will all the PAPI (or similar supported plugins) being replaced.
   * <p>
   *     Example: "Hi, my K/D is %mbedwars_stats-kd%"<br>
   *     will be become: "Hi, my K/D is 1.5"
   * </p>
   *
   * @param str The raw string
   * @return The new formatted string
   */
  String replacePAPIPlaceholders(String str, Player player);

  /**
   * Tries to obtain the nicked name of a plugin which possibly has been changed by a third-party nick plugin.
   * <p>
   *     The plugin automatically injects itself into quite a few nick plugins automatically and tries to obtain the players nick name using their API.
   *     In cases where the player is not nicked the method returns <code>null</code>.
   * </p>
   *
   * @param player The player whose nickname we want to find out
   * @return The nickname of the player. <code>null</code> if he doesn't have one
   */
  @Nullable String getPlayerNickName(Player player);

  /**
   * Nicknaming plugins might replace {@link Player#getName()} in favour for the nicked name. This method returns the player's real name.
   * <p>
   *     The plugin automatically injects itself into quite a few nick plugins automatically and tries to obtain the players real name using their API.
   * </p>
   *
   * @param player The player from who want to find out the real name
   * @return His real username
   */
  String getNickedPlayerRealName(Player player);

  /**
   * Returns the display name of a player that shall be shown to the public.
   * <p>
   *     Generally, this method works similar to {@link #getPlayerNickName(Player)}.
   *     Only that it returns the player's name if he isn't nicked. Meaning that this method never returns null.
   * </p>
   *
   * @param player The player whose public display name we want to find out
   * @return His name that can safely be shown to the public
   */
  String getPlayerDisplayName(Player player);

  /**
   * Returns whether the player is playing using Minecraft's Bedrock Edition or not.
   *
   * @param uuid The id of the player
   * @return <code>true</code> when we are able to confirm that
   */
  boolean isBedrockPlayer(UUID uuid);

  /**
   * Returns whether the player is playing using Minecraft's Bedrock Edition or not.
   *
   * @param player The player we want to check
   * @return <code>true</code> when we are able to confirm that
   */
  boolean isBedrockPlayer(OfflinePlayer player);

  /**
   * Tries to identify the type of an entity given by its name.
   *
   * @param name The name of the entity
   * @return The EntityType that might fit. <code>null</code> if none was found
   */
  @Nullable EntityType getEntityTypeByName(String name);

  /**
   * Copies bytes from an InputStream to an OutputStream.
   * <br>
   * This method buffers the input internally, so there is no need to use a BufferedInputStream.
   * <br>
   * Large streams (over 2GB) will return a bytes copied value of -1 after the copy has completed since the correct number of bytes cannot be returned as an int. For large streams use the copyLarge(InputStream, OutputStream) method.
   *
   * @param is The InputStream to read.
   * @param os The OutputStream to write.
   * @return The number of bytes copied, or -1 if greater than Integer.MAX_VALUE.
   * @throws IOException If an I/O error occurs.
   */
  int copy(InputStream is, OutputStream os) throws IOException;

  /**
   * Converts a SNBT (String format of NBT) to a JsonObject.
   *
   * @param nbt The data tag
   * @return The converted JsonObject containing the info of the given SNBT tag.
   * @throws IllegalArgumentException When it failed to parse the NBT
   * @deprecated That method is prone to problems anyway and has no use for the MBedwars pluin
   */
  @Deprecated
  JsonObject parseSNBTAsJson(String nbt) throws IllegalArgumentException;

  /**
   * Tries to convert a JsonObject to a SNBT data tag.
   * <p>
   *     The process is not perfect due to the way of how json stores their numbers etc.
   * </p>
   *
   * @param json The json object
   * @return The converted NBT string containing the info of the JsonObject.
   * @deprecated That method is prone to problems anyway and has no use for the MBedwars pluin
   */
  @Deprecated
  String composeJsonAsSNBT(JsonObject json);

  /**
   * Tries to make a player wear armor (ie automatically puts an items in a players armor slot)
   *
   * @param player the player we are giving the armor to
   * @param armor itemstack of armor that the player should wear
   * @param force weather or not to override an existing piece of armor
   * @return whether or not the armor was given successfully
   */
  boolean setPlayerArmor(Player player, ItemStack armor, boolean force);


  /**
   * Properly gives an item to a player.
   * <p>
   *     If a players Inventory is full, the items will be dropped close to them.
   *     No items will be deleted/lost (Unlike when using Inventory#addItem())
   * </p>
   *
   * @param player the player we are giving the item to
   * @param itemStack the item that is being given to the player
   */
  void givePlayerItem(Player player, ItemStack itemStack);

  /**
   * Tries to identify a material given by its name.
   *
   * @param name The name of the material
   * @return The Material that might fit. <code>null</code> if none was found
   */
  @Nullable Material getMaterialByName(String name);

  /**
   * Tries to identify an effect given by its name.
   *
   * @param name The name of the effect
   * @return The Effect that might fit. <code>null</code> if none was found
   */
  @Nullable Effect getEffectByName(String name);

  /**
   * Tries to identify a potion type given by its name.
   *
   * @param name The name of the potion type
   * @return The type that might fit. <code>null</code> if none was found
   */
  @Nullable PotionType getPotionTypeByName(String name);

  /**
   * Tries to identify a sound given by its name.
   *
   * @param name The name of the sound
   * @return The sound that might fit. <code>null</code> if none was found
   */
  @Nullable Sound getSoundByName(String name);

  /**
   * Tries to identify an enchantment given by its name.
   *
   * @param name The name of the enchantment
   * @return The enchantment that might fit. <code>null</code> if none was found
   */
  @Nullable Enchantment getEnchantmentByName(String name);

  /**
   * Returns the block variant of a material.
   * <p>
   *     Sometimes there are multiple entries within Material for basically the same material, but with the only difference being that one is thought to be added to the inventory and the other one to be placed.
   *     This is especially common in 1.12 and previous versions.
   * </p>
   *
   * @param mat The input material
   * @return The output material, possibly the same one as the input one
   */
  Material getBlockVariant(Material mat);

  /**
   * Returns the inventory variant of a material.
   * <p>
   *     Sometimes there are multiple entries within Material for basically the same material, but with the only difference being that one is thought to be added to the inventory and the other one to be placed.
   *     This is especially common in 1.12 and previous versions.
   * </p>
   *
   * @param mat The input material
   * @return The output material, possibly the same one as the input one
   */
  Material getInventoryVariant(Material mat);

  /**
   * Runs a given task on the main thread.
   * <p>
   *     Due to performance reasons, it's not granted that the task will be executed immediately with the next tick-
   * </p>
   *
   * @param runn The task that shall be run on the main thread
   */
  void synchronize(Runnable runn);

  /**
   * Returns the minimum height of an arena.
   * <p>
   *     Basically returns World#getMinHeight() on 1.17+. Always returns 0 on older version.
   *     Useful when you're trying to add support for newer versions.
   * </p>
   *
   * @param world The world
   * @return The minimum height at which players may place blocks at
   */
  int getMinHeight(World world);

  /**
   * Gets the texture {@literal &} signature of the player's skin.
   * <p>
   *     Note that the method may return <code>null</code> when the skin generally can't be seen,
   *     such as when the server is on cracked mode, when the server hasn't fetched the skin yet or when no custom skin has been defined by the player.
   * </p>
   *
   * @param player The player from whom we want to fetch it
   * @return A pair, with the key being the texture, and the value being the signature. May be <code>null</code> if he doesn't have one
   */
  @Nullable
  Pair<String, String> getPlayerSkinData(Player player);

  /**
   * Gets the ChatColor (from the Bukkit package) that fits the most to the Bungee color.
   * <p>
   *     This method also supports hex colors.
   * </p>
   *
   * @param color The color instance from the Bungee package
   * @return The matching Spigot color
   */
  org.bukkit.ChatColor getBukkitChatColorFromBungee(ChatColor color);

  /**
   * Allows you to retrieve and modify a block asynchronously on 1.14+
   * <p>
   *     If run on unsupported versions, the loading of the block, as well as the execution
   *     callback will occur synchronously
   * </p>
   *
   * @param location the location of the block
   * @param callback called when the block is ready to be used
   */
  default void getBlockAsync(Location location, Consumer<Block> callback){
    getBlockAsync(location.getWorld(), location.getBlockX(), location.getBlockY(), location.getBlockZ(), callback);
  }

  /**
   * Allows you to retrieve and modify a block asynchronously on 1.14+
   * <p>
   *     If run on unsupported versions, the loading of the block, as well as the execution
   *     callback will occur synchronously
   * </p>
   *
   * @param world the world of the block
   * @param xyz the coordinates if the block
   * @param callback called when the block is ready to be used
   */
  default void getBlockAsync(World world, XYZ xyz, Consumer<Block> callback){
    getBlockAsync(world, xyz.getBlockX(), xyz.getBlockY(), xyz.getBlockZ(), callback);
  }

  /**
   * Allows you to retrieve and modify a block asynchronously on 1.14+
   * <p>
   *     If run on unsupported versions, the loading of the block, as well as the execution
   *     callback will occur synchronously
   * </p>
   *
   * @param world the world of the block
   * @param x the x coordinate of the block
   * @param y the y coordinate of the block
   * @param z the z coordinate of the block
   * @param callback called when the block is ready to be used
   */
  void getBlockAsync(World world, int x, int y, int z, Consumer<Block> callback);

  /**
   * Allows you to retrieve and modify a block asynchronously on 1.14+
   * <p>
   *     If run on unsupported versions, the loading of the chunk, as well as the execution
   *     callback will occur synchronously
   * </p>
   *
   * @param world the world of the chunk
   * @param x the x coordinate of the chunk
   * @param z they y coordinate of the chunk
   * @param callback called when the chunk is ready to be used
   */
  void getChunkAsync(World world, int x, int z, Consumer<Chunk> callback);

  /**
   * Allows you to teleport entities asynchronously on 1.14+
   * <p>
   *     If run on unsupported versions, the teleport and callback will be called synchronously.
   *     The callback can be null if nothing needs to be done after the entity has teleported.
   * </p>
   *
   * @param entity The entity being teleported
   * @param loc The location the entity should be teleported to
   * @param callback Called after the entity has been teleported
   */
  void teleportAsync(Entity entity, Location loc, @Nullable Runnable callback);

  /**
   * Plays a sound - even on a non-main thread.
   * <p>
   *   It is not possible anymore to play a sound on a non-main thread since 1.19.
   *   This method fixes it, but you may surely use this method on the main thread and on older versions as well.
   * </p>
   *
   * @param player The player who shall hear the sound
   * @param location The location where the sound shall be played
   * @param sound The sound that shall be played
   * @param volume The volume of the sound
   * @param pitch The pitch of the sound
   */
  void playSound(Player player, Location location, Sound sound, float volume, float pitch);

  /**
   * Plays a sound - even on a non-main thread.
   * <p>
   *   It is not possible anymore to play a sound on a non-main thread since 1.19.
   *   This method fixes it, but you may surely use this method on the main thread and on older versions as well.
   * </p>
   *
   * @param location The location where the sound shall be played
   * @param sound The sound that shall be played
   * @param volume The volume of the sound
   * @param pitch The pitch of the sound
   */
  void playSound(Location location, Sound sound, float volume, float pitch);

  /**
   * Plays a custom sound - even on a non-main thread.
   * <p>
   *   It is not possible anymore to play a sound on a non-main thread since 1.19.
   *   This method fixes it, but you may surely use this method on the main thread and on older versions as well.
   * </p>
   * <p>
   *   This method does not play vanilla sounds and only works on 1.9+
   * </p>
   *
   * @param player The player who shall hear the sound
   * @param location The location where the sound shall be played
   * @param sound The sound that shall be played
   * @param volume The volume of the sound
   * @param pitch The pitch of the sound
   */
  void playCustomSound(Player player, Location location, String sound, float volume, float pitch);

  /**
   * Plays a custom sound - even on a non-main thread.
   * <p>
   *   It is not possible anymore to play a sound on a non-main thread since 1.19.
   *   This method fixes it, but you may surely use this method on the main thread and on older versions as well.
   * </p>
   * <p>
   *   This method does not play vanilla sounds and only works on 1.9+
   * </p>
   *
   * @param location The location where the sound shall be played
   * @param sound The sound that shall be played
   * @param volume The volume of the sound
   * @param pitch The pitch of the sound
   */
  void playCustomSound(Location location, String sound, float volume, float pitch);

  /**
   * Get whether special behavior is expected to occur if interacted with the block.
   * <p>
   *   This includes e.g. droppers, furnaces, etc. or also flower pots,
   *   as you are able to insert flowers into them.
   * </p>
   * <p>
   *   Important: This only includes configs relevant for the "interacting" config.
   *   Meaning, this config won't check chests as there is a separate config for that.
   * </p>
   *
   * @param mat The material the block represents
   * @return <code>true</code> if vanilla would do something special if interacted with
   * @deprecated Might break in the future when Spigot releases their BlockType API
   */
  @Deprecated
  boolean isInteractableBlock(Material mat);

  /**
   * A fast implementation for removing items from a player's inventory.
   * <p>
   *   This was designed to be more efficient than Bukkit's {@link org.bukkit.inventory.Inventory#removeItem(org.bukkit.inventory.ItemStack...)} implementation.
   *   Further, this method is able to remove items from other slots as well, such as the off-hand, cursor and armor slots.
   * </p>
   *
   * @param player The player whose inventory we want to remove items from
   * @param is The item we want to remove. The amount is being ignored
   * @param amount The amount of items we want to remove
   * @return The amount of items that the player doesn't have and therefore couldn't be removed
   */
  int takeItems(Player player, ItemStack is, int amount);

  /**
   * A fast implementation for adding items to a player's inventory.
   * <p>
   *   This was designed to be more efficient than Bukkit's {@link org.bukkit.inventory.Inventory#addItem(org.bukkit.inventory.ItemStack...)} implementation.
   *   Further, this method also allows you to automatically drop the overflowing items.
   * </p>
   *
   * @param player The player whose inventory we want to add items to
   * @param is The item we want to add. The amount is being considered
   * @param dropOverflow Whether to drop the overflowing items
   * @return The amount of items that couldn't be added. Always 0 if dropOverflow is <code>true</code>
   */
  int giveItems(Player player, ItemStack is, boolean dropOverflow);

  /**
   * Contains utility stuff.
   *
   * @return The global Helper instance
   */
  static Helper get() {
    return BedwarsAPI.getHelper();
  }
}
