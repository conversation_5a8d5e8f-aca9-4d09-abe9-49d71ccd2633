package de.marcely.bedwars.api.player;

/**
 * Add a certain cause when setting a player attribute.
 */
public enum AttributeChangeCause {

  /**
   * Simply set the value.
   * <p>
   *   - Calls the related change event<br>
   *   - Synchronizes the new value with remote servers<br>
   *   - Marks that the attributes container must be saved again
   * </p>
   */
  GENERIC,

  /**
   * Used when the attributes container is being loaded.
   * <p>
   *   - Does <b>not</b> call the related change event<br>
   *   - Does <b>not</b> synchronize the new value with remote servers<br>
   *   - Does <b>not</b> mark that the attributes container must be saved again
   * </p>
   */
  INITIALIZATION,

  /**
   * Used by the ProxySync addon.
   * <p>
   *   - Call the related change event<br>
   *   - Does <b>not</b> synchronize the new value with remote servers<br>
   *   - Does <b>not</b> mark that the attributes container must be saved again
   * </p>
   */
  REMOTE
}