package de.marcely.bedwars.api;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.util.Collection;

/**
 * API for managing addons that were extended to this plugin
 */
@SuppressWarnings("deprecation")
public interface AddonAPI {

  /**
   * Returns all addons that are currently registered.
   *
   * @return All registered addons
   */
  Collection<BedwarsAddon> getAll();

  /**
   * Returns all registered addons whose {@link BedwarsAddon#getPlugin()} is equal to the one given as a parameter.
   *
   * @param plugin The plugin who created the addons
   * @return All the addons that were created by it
   */
  BedwarsAddon[] getByPlugin(Plugin plugin);

  /**
   * Returns the registered addon given by its <code>name</code>.
   * Can return <code>null</code> if there's none with that name.
   *
   * @param name The name of the addon
   * @return The addon that has the given name. <code>null</code> if there's none
   */
  @Nullable BedwarsAddon getByName(String name);

  /**
   * Returns the folder in which all the addon data is located in.
   * <p>
   * By default it's /MBedwars/add-ons/
   *
   * @return The add-ons folder
   */
  File getFolder();

  /**
   * Returns the global AddonAPI instance.
   *
   * @return The global AddonAPI instance
   */
  static AddonAPI get() {
    return BedwarsAPILayer.INSTANCE.getAddonAPI();
  }
}