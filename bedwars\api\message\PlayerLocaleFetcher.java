package de.marcely.bedwars.api.message;

import de.marcely.bedwars.api.BedwarsAPI;
import java.util.Locale;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * Used to fetch the locale of a player.
 *
 * @see MessageAPI#setLocaleFetcher(PlayerLocaleFetcher)
 */
public abstract class PlayerLocaleFetcher {

  /**
   * Returns the plugin that's owning this instance
   *
   * @return The plugin that created this instance
   */
  public abstract Plugin getPlugin();

  /**
   * Returns if it's the PlayerLocaleFetcher provided by MBedwars
   *
   * @return <code>true</code> if it's the PlayerLocaleFetcher provided by MBedwars
   */
  public final boolean isDefault() {
    return BedwarsAPI.getMessageAPI().getDefaultLocaleFetcher() == this;
  }

  /**
   * Fetches the locale of a player.
   * <p>
   *   The default configured locale will be used if null is returned.
   *   It might also fallback to the default configured if the returned locale is not configured
   *   or if multi-language support has been disabled by the server-admin.
   * </p>
   *
   * @param player The player to fetch the locale from
   * @return The locale of the player or <code>null</code> if the player has no locale
   */
  @Nullable
  public abstract Locale fetch(Player player);

  /**
   * Fetches the locale in Java's Locale String format (e.g. "en_US").
   * <p>
   *   It is recommended to implement this as well for performance reasons,
   *   if the locale is already stored in this format. The reason why it
   *   exists separately is that Minecraft's clients send the locale in
   *   this format.
   * </p>
   *
   * @param player The player to fetch the locale from
   * @return The locale of the player in {@link Locale#toString()} format or <code>null</code> if the player has no locale
   */
  @Nullable
  public String fetchAsString(Player player) {
    final Locale locale = fetch(player);

    return locale != null ? locale.toString() : null;
  }
}
