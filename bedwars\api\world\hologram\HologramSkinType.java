package de.marcely.bedwars.api.world.hologram;

import org.bukkit.entity.EntityType;
import de.marcely.bedwars.api.world.hologram.skin.*;

/**
 * The skin of the hologram which specifies the way the hologram is going look like.
 */
public enum HologramSkinType {

  /**
   * Equally to {@link EntityType#ARMOR_STAND}.
   * <p>
   *   Implemented by {@link ArmorStandHologramSkin}.
   * </p>
   */
  ARMOR_STAND,

  /**
   * Spawns a bunch of invisible armor stands to create something-like a hologram.
   * <p>
   *   Main purpose is to display larger text.<br>
   *   Implemented by {@link HolographicHologramSkin}.
   * </p>
   */
  HOLOGRAM,

  /**
   * Equally to {@link EntityType#PLAYER}.
   * <p>
   *   Implemented by {@link NPCHologramSkin}.
   * </p>
   */
  NPC,

  /**
   * Equally to {@link EntityType#VILLAGER}.
   *
   * @deprecated Will be replaced by {@link #LIVING}.
   */
  @Deprecated
  VILLAGER,

  /**
   * Represents a LivingEntity with a dynamic EntityType.
   * <p>
   *   Implemented by {@link LivingEntityHologramSkin}.
   * </p>
   */
  LIVING
}
