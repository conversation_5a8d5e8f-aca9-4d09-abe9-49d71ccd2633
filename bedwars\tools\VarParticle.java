package de.marcely.bedwars.tools;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Locale;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Consumer;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Effect;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.block.BlockFace;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.MemoryConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.material.MaterialData;
import org.jetbrains.annotations.Nullable;

/**
 * Easy solution for using VarParticles that are being supported on any version.
 */
public abstract class VarParticle implements Cloneable {

  // Names changed with updates, want to keep cross-version support
  // Must be at top as fields below depend on it
  private static final String[][] LEGACY_NAMES = {
      // Changes with 1.9 (prior on left)
      {"COLOURED_DUST", "REDSTONE"},
      {"EXPLOSION", "EXPLOSION_NORMAL"},
      {"FLYING_GLYPH", "ENCHANTMENT_TABLE"},
      {"HAPPY_VILLAGER", "VILLAGER_HAPPY"},
      {"INSTANT_SPELL", "SPELL_INSTANT"},
      {"ITEM_BREAK", "ITEM_CRACK"},
      {"LARGE_SMOKE", "SMOKE_LARGE"},
      {"SMALL_SMOKE", "TOWN_AURA"},
      {"LAVA_POP", "LAVA"},
      {"LAVADRIP", "DRIP_LAVA"},
      {"MAGIC_CRIT", "CRIT_MAGIC"},
      {"PARTICLE_SMOKE", "SMOKE_NORMAL"},
      {"POTION_SWIRL", "SPELL_MOB"},
      {"POTION_SWIRL_TRANSPARENT", "SPELL_MOB_AMBIENT"},
      {"SNOWBALL_BREAK", "SNOWBALL"},
      {"SPLASH", "WATER_SPLASH"},
      {"TILE_BREAK", "BLOCK_CRACK"},
      {"TILE_DUST", "BLOCK_DUST"},
      {"VILLAGER_THUNDERCLOUD", "VILLAGER_ANGRY"},
      {"VOID_FOG", "SUSPENDED_DEPTH"},
      {"WATERDRIP", "DRIP_WATER" /* New ones (similar, keep cross-support): */, "WATER_DROP", "WATER_BUBBLE"},
      {"WITCH_MAGIC", "SPELL_WITCH"},

      // Changes with 1.20.5 (prior on left)
      {"BLOCK_DUST", "BLOCK_CRACK", "BLOCK"}, // Prior had duplicates
      {"CRIT_MAGIC", "ENCHANTED_HIT"},
      {"DRIP_LAVA", "DRIPPING_LAVA"},
      {"DRIP_WATER", "DRIPPING_WATER"},
      {"ENCHANTMENT_TABLE", "ENCHANT"},
      {"EXPLOSION_HUGE", "EXPLOSION_EMITTER"},
      {"EXPLOSION_LARGE", "EXPLOSION"},
      {"EXPLOSION_NORMAL", "POOF"},
      {"FIREWORKS_SPARK", "FIREWORK"},
      {"ITEM_CRACK", "ITEM"},
      {"MOB_APPEARANCE", "ELDER_GUARDIAN"},
      {"REDSTONE", "DUST"},
      {"SLIME", "ITEM_SLIME"},
      {"SMOKE_LARGE", "LARGE_SMOKE"},
      {"SMOKE_NORMAL", "SMOKE"},
      {"SNOWBALL", "SNOW_SHOVEL", "ITEM_SNOWBALL"}, // Prior had duplicates
      {"SPELL", "EFFECT"},
      {"SPELL_INSTANT", "INSTANT_EFFECT"},
      {"SPELL_MOB", "ENTITY_EFFECT"},
      {"SPELL_WITCH", "WITCH"},
      {"SUSPENDED", "SUSPENDED_DEPTH", "UNDERWATER"}, // Prior had duplicates
      {"TOTEM", "TOTEM_OF_UNDYING"},
      {"TOWN_AURA", "MYCELIUM"},
      {"VILLAGER_ANGRY", "ANGRY_VILLAGER"},
      {"VILLAGER_HAPPY", "HAPPY_VILLAGER"},
      {"WATER_BUBBLE", "BUBBLE"},
      {"WATER_DROP", "RAIN"},
      {"WATER_SPLASH", "SPLASH"},
      {"WATER_WAKE", "FISHING"}
  };

  /**
   * Doesn't actually play any particle. Used as a placeholder.
   */
  public static final VarParticle NONE = new NoneParticle();

  /**
   * Plays the "SMOKE" particle effect.
   */
  public static final VarParticle PARTICLE_SMOKE = newInstanceByName("SMOKE");

  /**
   * Plays the "EXPLOSION_HUGE" ("EXPLOSION_EMITTER" with 1.20.5+) particle effect.
   */
  public static final VarParticle PARTICLE_EXPLOSION_HUGE = newInstanceByName("EXPLOSION_HUGE");

  /**
   * Plays the "CLOUD" particle effect.
   */
  public static final VarParticle PARTICLE_CLOUD = newInstanceByName("CLOUD");

  /**
   * Plays the "MOB_SPELL" particle effect with the colors R129 G133 B149.
   */
  public static final VarParticle PARTICLE_POTION_INVISIBILITY = newDyedMobSpell(
      NMSHelper.get().getVersion() >= 21 ?
          Color.fromRGB(246, 246, 246) :
          Color.fromRGB(129, 133, 149));

  private VarParticle() {
  }

  /**
   * Plays the particle globally at the given location.
   *
   * @param loc The loaction where it shall be played.
   */
  public abstract void play(Location loc);

  /**
   * Plays the particle for at the given location.
   *
   * @param loc    The loaction where it shall be played.
   * @param player The player who shall see it
   */
  public abstract void play(Location loc, Player player);

  /**
   * Returns the name of the name/particle.
   *
   * @return The name of the particle
   */
  public abstract String getName();

  /**
   * Returns whether {@link Effect} is being used.
   *
   * @return <code>true</code> when Bukkit's Effect API is being used
   */
  public abstract boolean isInternallyEffect();

  /**
   * Returns whether {@link Particle} is being used.
   *
   * @return <code>true</code> when Bukkit's Particle API is being used
   */
  public abstract boolean isInternallyParticle();

  /**
   * Defines the random offset at the x-coordinate at which a particle may be spawned.
   *
   * @return The defined x-offset
   */
  public abstract float getOffsetX();

  /**
   * Defines the random offset at the y-coordinate at which a particle may be spawned.
   *
   * @return The defined y-offset
   */
  public abstract float getOffsetY();

  /**
   * Defines the random offset at the z-coordinate at which a particle may be spawned.
   *
   * @return The defined z-offset
   */
  public abstract float getOffsetZ();

  /**
   * Set the random offset at which a particle may be spawned.
   *
   * @param x Max radius for the x-coordinate
   * @param y Max radius for the y-coordinate
   * @param z Max radius for the z-coordinate
   */
  public abstract void setOffset(float x, float y, float z);

  /**
   * Get the amount of particles that will be shown.
   *
   * @return The amount of particles
   */
  public abstract int getCount();

  /**
   * Set the amount of particles that will be shown.
   *
   * @param count The new amount of particles
   */
  public abstract void setCount(int count);

  protected boolean testPlayAsync(Location loc) {
    if (Bukkit.isPrimaryThread())
      return false;

    // global spawnParticle not thread-safe
    for (Player player : Bukkit.getOnlinePlayers()) {
      if (player.getWorld() != loc.getWorld())
        continue;

      final Location pLoc = player.getLocation();

      if (pLoc.getWorld() != loc.getWorld() || pLoc.distanceSquared(loc) > 32*32)
        continue;

      play(loc, player);
    }

    return true;
  }

  @Override
  public VarParticle clone() {
    try {
      return (VarParticle) super.clone();
    } catch (CloneNotSupportedException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Parses a given config.
   * <p>
   * Goes through all existing particles and effects within Bukkit. It's possible that the data vary between the versions.
   * </p>
   * <p>
   * It'll read the following fields:
   *     <ul>
   *         <li>type (string, the name of the particle/effect)</li>
   *         <li>count (int)</li>
   *         <li>offset-x (double)</li>
   *         <li>offset-y (double)</li>
   *         <li>offset-z (double)</li>
   *         <li>extra (double)</li>
   *         <li>data (depends on the type)</li>
   *     </ul>
   * <p>
   *     When parsing a particle (1.9+) then there are the following possible types for the data:
   *     <ul>
   *         <li><b>DustOptions</b>: {@literal <}red{@literal >},{@literal <}blue{@literal >},{@literal <}green{@literal >}:{@literal <}size{@literal >}</li>
   *         <li><b>ItemStack</b>: {@literal <}itemstack in mbedwars format{@literal >}</li>
   *         <li><b>BlockData</b>: {@literal <}material{@literal >}</li>
   *         <li><b>MaterialData</b>: {@literal <}material{@literal >}{@literal [:}data{@literal ]}</li>
   *     </ul>
   *
   * @param config The config that shall be parsed
   * @return The parsed VarParticle instance that's ready to be played
   * @throws IllegalArgumentException If an error occured while parsing
   */
  public static VarParticle parse(ConfigurationSection config) throws IllegalArgumentException {
    Validate.notNull(config, "config");

    try {
      if (!config.contains("type"))
        throw new IllegalArgumentException("Missing \"type\" config");

      final int version = NMSHelper.get().getVersion();
      String type = config.getString("type").toUpperCase(Locale.ENGLISH);

      if (type.equals("NONE") || type.equals("TYPE"))
        return NONE;

      final int count = getInt(config, "count", 0);
      final float offsetX = Math.max((float) (double) getDouble(config, "offset-x", 0D), 0);
      final float offsetY = Math.max((float) (double) getDouble(config, "offset-y", 0D), 0);
      final float offsetZ = Math.max((float) (double) getDouble(config, "offset-z", 0D), 0);
      final boolean hasExtra = config.contains("extra");
      final double extra = getDouble(config, "extra", 0D);
      Object data = config.get("data");
      final float speed = (float) (double) getDouble(config, "speed", 0D);
      final int radius = getInt(config, "radius", 64);

      // check if it's a particle
      if (version >= 9) {
        final int ver = NMSHelper.get().getVersion();
        final int patch = NMSHelper.get().getPatchVersion();

        // special cases where ones got removed
        {
          switch (type) {
            case "SPELL_MOB_AMBIENT": {
              if (ver >= 21 || ver == 20 && patch >= 5) { // just transparent MOB_AMBIENT
                type = "SPELL_MOB";

                if (data != null)
                  data = data + ",120";
              }
            }
          }
        }

        final Particle pat = findParticle(type, name -> TypeV9Particle.getByName(name));

        if (pat != null) {
          // fallback. modern versions now prefer data instead of extra
          if (data == null && pat.getDataType() != null && config.contains("extra")) {
            if (extra % 1 == 0D)
              data = (int) extra;
            else
              data = extra;
          }

          return new TypeV9Particle(
              pat,
              count,
              offsetX,
              offsetY,
              offsetZ,
              hasExtra ? extra : speed,
              TypeV9Particle.parse(pat, data != null ? data.toString() : null)
          );
        }
      }

      // check if it's an effect
      final Effect effect = findParticle(type, name -> Helper.get().getEffectByName(name));

      if (effect != null) {
        if (version <= 12) {
          final TypeV8Effect tEffect = new TypeV8Effect(
              effect,
              extra,
              TypeV8Effect.parse(effect, data, true),
              (float) offsetX,
              (float) offsetY,
              (float) offsetZ,
              speed,
              count,
              radius
          );

          // otherwise the client crashes due to a Spigot bug
          if (NMSHelper.get().getVersion() == 8 && effect == Effect.ITEM_BREAK) {
            tEffect.effect = Effect.TILE_BREAK;

            if (tEffect.data instanceof Material) {
              Material mat = (Material) tEffect.data;

              if (!mat.isBlock())
                mat = Helper.get().getBlockVariant(mat);

              if (!mat.isBlock())
                mat = Material.SOUL_SAND;

              tEffect.data = new MaterialData(mat);

            } else if (tEffect.data instanceof Number) {
              tEffect.data = new MaterialData(((Number) tEffect.data).intValue());

            } else
              tEffect.data = new MaterialData(1);
          }

          // better support for materialdata (allows the usage of the remaining properties)
          if (tEffect.data instanceof MaterialData) {
            final MaterialData matData = (MaterialData) tEffect.data;

            tEffect.extra = matData.getItemTypeId();
            tEffect.data = (int) matData.getData();
          }

          return tEffect;
        } else {
          return new TypeV13Effect(
              effect,
              TypeV8Effect.parse(effect, data, false),
              radius,
              offsetX,
              offsetY,
              offsetZ,
              count);
        }
      }

      throw new IllegalArgumentException("Unknown particle/effect type \"" + type + "\"");
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      throw new IllegalArgumentException(e);
    }
  }

  @Nullable
  private static <T> T findParticle(String input, Function<String, T> lookup) {
    return findParticle(input, lookup, null);
  }

  @Nullable
  private static <T> T findParticle(String input, Function<String, T> lookup, @Nullable Collection<String> pastInputs) {
    // lucky shot
    {
      final T result = lookup.apply(input);

      if (result != null)
        return result;
    }

    // deep search
    for (String[] variantNames : LEGACY_NAMES) {
      // find the matching variant
      String found = null;

      for (String name : variantNames) {
        if (name.equals(input))
          found = name;
      }

      if (found == null)
        continue;

      // find the name that is supported by this version
      for (String name : variantNames) {
        if (found == name) // == is intended. same reference
          continue;

        final T result = lookup.apply(name);

        if (result != null)
          return result;
      }

      // try to use that one to find the next one
      if (pastInputs == null || !pastInputs.contains(input)) {
        if (pastInputs == null)
          pastInputs = new ArrayList<>(2);

        pastInputs.add(input);

        for (String name : variantNames) {
          if (found == name) // == is intended. same reference
            continue;

          final T result = findParticle(name, lookup, pastInputs);

          if (result != null)
            return result;
        }
      }
    }

    return null;
  }

  /**
   * Constructs a new VarParticle that displays a redstone dust at the given color.
   *
   * @param color The color of the redstone dust
   * @return A new instance with that color
   */
  public static VarParticle newDyedParticle(Color color) {
    return newDyedParticle(color, "COLOURED_DUST", "REDSTONE");
  }

  /**
   * Constructs a new VarParticle that displays swirls as seen with potion effects.
   *
   * @param color The color of the swirl particle
   * @return A new instance with that color
   */
  public static VarParticle newDyedMobSpell(Color color) {
    return newDyedParticle(color, "POTION_SWIRL", "SPELL_MOB");
  }

  private static VarParticle newDyedParticle(Color color, String legacyName, String particleName) {
    final int version = NMSHelper.get().getVersion();

    if (version <= 12) {
      float red = color.getRed() / 255F;

      if (red == 0)
        red = Float.MIN_NORMAL;

      return new TypeV8Effect(
          Effect.valueOf(legacyName),
          0,
          1,
          red,
          color.getGreen() / 255F,
          color.getBlue() / 255F,
          1F,
          0,
          32
      );

    } else {
      try {
        final Particle particle = findParticle(particleName, name -> TypeV9Particle.getByName(name));

        if (particle.getDataType() == Color.class) {
          return new TypeV9Particle(
              particle,
              1,
              0,
              0,
              0,
              0,
              color);

        } else if (particle.getDataType() == getDustOptionsClass()) {
          final Object data = constructDustOptions(color, 1F);

          return new TypeV9Particle(
              particle,
              1,
              0,
              0,
              0,
              0,
              data);

        } else {
          return new TypeV9Particle(
              particle,
              0,
              color.getRed() / 255F,
              color.getGreen() / 255F,
              color.getBlue() / 255F,
              1,
              null);
        }
      } catch (Exception e) {
        e.printStackTrace();
      }
    }

    return null;
  }

  /**
   * Returns a new instance of VarParticle that persists of a random color.
   * <p>
   * Uses the particle <code>POTION_SWIRL</code> on 1.12 or older. Uses <code>REDSTONE</code> on 1.13+
   * </p>
   *
   * @param count  The amount of particles
   * @param offset The offset of particles
   * @return A newly constructed instance with a random color
   * @see #newDyedMobSpell(Color)
   */
  public static VarParticle newRandomizedDyedSwirl(int count, double offset) {
    final ConfigurationSection section = new MemoryConfiguration();

    section.set("offset-x", offset);
    section.set("offset-y", offset);
    section.set("offset-z", offset);
    section.set("count", count);

    if (NMSHelper.get().getVersion() >= 13) {
      section.set("type", "REDSTONE");
      section.set("data", ThreadLocalRandom.current().nextInt(256) + "," + ThreadLocalRandom.current().nextInt(256) + "," + ThreadLocalRandom.current().nextInt(256));
    } else {
      section.set("type", "POTION_SWIRL");
      section.set("data", 1);
    }

    return parse(section);
  }

  /**
   * Looks up for a particle/effect by the given name and returns a new instance.
   *
   * @param type The name
   * @return A new VarParticle instance with the given type
   * @throws IllegalArgumentException When the given name is invalid
   */
  public static VarParticle newInstanceByName(String type) throws IllegalArgumentException {
    final ConfigurationSection section = new MemoryConfiguration();

    section.set("type", type);

    return parse(section);
  }

  @Nullable
  private static Color getColor(String str) {
    final String[] parts = str.split(",");
    boolean hasAlpha = parts.length >= 4;

    if (hasAlpha) { // is he allowed to use alpha?
      final int ver = NMSHelper.get().getVersion();
      final int patchVer = NMSHelper.get().getPatchVersion();

      if (!(ver >= 21 || ver == 20 && patchVer >= 5))
        hasAlpha = false;
    }

    if (!hasAlpha && parts.length >= 3)
      return Color.fromRGB(Integer.parseInt(parts[0]), Integer.parseInt(parts[1]), Integer.parseInt(parts[2]));
    else if (hasAlpha) {
      try {
        return (Color) Color.class.getMethod("fromARGB", int.class, int.class, int.class, int.class)
            .invoke(null, Integer.parseInt(parts[3]), Integer.parseInt(parts[0]), Integer.parseInt(parts[1]), Integer.parseInt(parts[2]));
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    } else
      return null;
  }

  @Nullable
  private static MaterialData getMaterialData(String str) {
    final String[] parts = str.split(":");
    final Material mat = Helper.get().getMaterialByName(str);

    if (mat == null)
      throw new IllegalArgumentException("Unknown material \"" + parts[0] + "\"");
    if (mat == Material.AIR)
      throw new IllegalArgumentException("Invalid material \"" + parts[0] + "\"");

    return new MaterialData(
        mat,
        parts.length >= 2 ? (byte) Integer.parseInt(parts[1]) : 0);
  }

  private static Integer getInt(ConfigurationSection config, String name, Integer def) throws Exception {
    if (config.contains(name)) {
      if (!(config.get(name) instanceof Number))
        throw new UnsupportedOperationException("\"" + name + "\" is not a number");

      return ((Number) config.get(name)).intValue();
    }

    return def;
  }

  private static Double getDouble(ConfigurationSection config, String name, Double def) {
    if (config.contains(name)) {
      if (!(config.get(name) instanceof Number))
        throw new UnsupportedOperationException("\"" + name + "\" is not a number");

      return ((Number) config.get(name)).doubleValue();
    }

    return def;
  }

  private static Class<?> getDustOptionsClass() throws Exception {
    return Class.forName("org.bukkit.Particle$DustOptions");
  }

  private static Object constructDustOptions(Color color, float size) throws Exception {
    return getDustOptionsClass()
        .getConstructor(Color.class, float.class)
        .newInstance(color, size);
  }


  @Getter
  @Setter
  @AllArgsConstructor
  private static class TypeV8Effect extends VarParticle {

    private static final Color RAINBOW_COLOR = Color.fromRGB(69, 42, 0); // never do .equals, only ==

    private Effect effect;
    private double extra;
    private Object data;
    private float offsetX, offsetY, offsetZ;
    private float speed;
    private int count;
    private int radius;

    @Override
    public void play(Location loc) {
      if (NMSHelper.get().getVersion() == 8 && this.effect == Effect.ITEM_BREAK)
        throw new SecurityException("ITEM_BREAK causes the client to crash on 1.8 (Spigot bug)");
      if (testPlayAsync(loc))
        return;

      // legacy color
      if (this.data != null && this.data instanceof Color) {
        final Color color = (Color) this.data;
        final float red = color.getRed() <= 0D ? Float.MIN_VALUE : color.getRed() / 255F;
        final float green = color.getGreen() / 255F;
        final float blue = color.getBlue() / 255F;
        final int count = Math.max(1, this.count);

        if (this.offsetX != 0D || this.offsetY != 0D || this.offsetZ != 0D) {
          for (int i = 0; i < count; i++) {
            loc.getWorld().spigot().playEffect(
                new Location(
                    loc.getWorld(),
                    loc.getX() + (ThreadLocalRandom.current().nextFloat() * 2F - 1F) * this.offsetX,
                    loc.getY() + (ThreadLocalRandom.current().nextFloat() * 2F - 1F) * this.offsetY,
                    loc.getZ() + (ThreadLocalRandom.current().nextFloat() * 2F - 1F) * this.offsetZ),
                this.effect,
                0,
                1,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : red,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : green,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : blue,
                1F,
                0,
                32
            );
          }

        } else {
          for (int i = 0; i < count; i++) {
            loc.getWorld().spigot().playEffect(
                loc,
                this.effect,
                0,
                1,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : red,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : green,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : blue,
                1F,
                0,
                32
            );
          }
        }

        // spawn normally
      } else {
        if (this.data == null || this.data instanceof Integer) {
          loc.getWorld().spigot().playEffect(
              loc,
              this.effect,
              (int) this.extra,
              (int) (this.data == null ? 0 : this.data),
              this.offsetX,
              this.offsetY,
              this.offsetZ,
              this.speed,
              this.count,
              this.radius
          );

        } else {
          loc.getWorld().playEffect(
              loc,
              this.effect,
              this.data,
              this.radius
          );
        }
      }
    }

    @Override
    public void play(Location loc, Player player) {
      if (NMSHelper.get().getVersion() == 8 && this.effect == Effect.ITEM_BREAK)
        throw new SecurityException("ITEM_BREAK causes the client to crash on 1.8 (Spigot bug)");

      // legacy color
      if (this.data != null && this.data instanceof Color) {
        final Color color = (Color) this.data;
        final float red = color.getRed() <= 0D ? Float.MIN_VALUE : color.getRed() / 255F;
        final float green = color.getGreen() / 255F;
        final float blue = color.getBlue() / 255F;
        final int count = Math.max(1, this.count);

        if (this.offsetX != 0D || this.offsetY != 0D || this.offsetZ != 0D) {
          for (int i = 0; i < count; i++) {
            player.spigot().playEffect(
                new Location(
                    loc.getWorld(),
                    loc.getX() + (ThreadLocalRandom.current().nextFloat() * 2F - 1F) * this.offsetX,
                    loc.getY() + (ThreadLocalRandom.current().nextFloat() * 2F - 1F) * this.offsetY,
                    loc.getZ() + (ThreadLocalRandom.current().nextFloat() * 2F - 1F) * this.offsetZ),
                this.effect,
                0,
                1,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : red,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : green,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : blue,
                1F,
                0,
                32
            );
          }

        } else {
          for (int i = 0; i < count; i++) {
            player.spigot().playEffect(
                loc,
                this.effect,
                0,
                1,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : red,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : green,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextFloat() : blue,
                1F,
                0,
                32
            );
          }
        }

        // spawn normally
      } else {
        if (this.data == null || this.data instanceof Integer) {
          player.spigot().playEffect(
              loc,
              this.effect,
              (int) this.extra,
              (int) (this.data == null ? 0 : this.data),
              this.offsetX,
              this.offsetY,
              this.offsetZ,
              this.speed,
              this.count,
              this.radius
          );

        } else {
          player.playEffect(
              loc,
              this.effect,
              this.data
          );
        }
      }
    }

    @Override
    public String getName() {
      return this.effect.name();
    }

    @Override
    public boolean isInternallyEffect() {
      return true;
    }

    @Override
    public boolean isInternallyParticle() {
      return false;
    }

    @Override
    public void setOffset(float x, float y, float z) {
      this.offsetX = Math.max(0, x);
      this.offsetY = Math.max(0, y);
      this.offsetZ = Math.max(0, z);
    }

    static Object parse(Effect effect, Object rawData, boolean legacyColorHack) throws Exception {
      if (rawData instanceof Number)
        return ((Number) rawData).intValue();

      if (!(rawData instanceof String))
        return 0;

      final String strData = (String) rawData;

      // parse color
      if (legacyColorHack) {
        switch (effect) {
          case COLOURED_DUST:
          case POTION_SWIRL:
          case POTION_SWIRL_TRANSPARENT: {
            final Color color = getColor(strData);

            return color != null ? color : RAINBOW_COLOR;
          }
        }
      }

      // parse various data
      if (effect.getData() != null) {
        switch (effect.getData().getSimpleName()) {
          case "Material": {
            final Material mat = Helper.get().getMaterialByName(strData);

            if (mat == null)
              throw new IllegalArgumentException("Unknown material \"" + strData + "\"");
            if (mat == Material.AIR)
              throw new IllegalArgumentException("Invalid material \"" + strData + "\"");

            return mat;
          }

          case "BlockFace": {
            try {
              return BlockFace.valueOf(strData);
            } catch (IllegalArgumentException e) {
              throw new IllegalArgumentException("Unknown BlockFace \"" + strData + "\"");
            }
          }

          case "MaterialData":
            return getMaterialData(strData);
        }
      }

      return 0; // fallback to 0
    }
  }

  @Getter
  @Setter
  @AllArgsConstructor
  private static class TypeV13Effect extends VarParticle {

    private Effect effect;
    private Object data;
    private int radius;
    private float offsetX, offsetY, offsetZ;
    private int count;

    @Override
    public void play(Location centerLoc) {
      if (testPlayAsync(centerLoc))
        return;

      obtainLocations(centerLoc, loc -> {
        if (this.data == null || this.data instanceof Integer) {
          loc.getWorld().playEffect(
              loc,
              this.effect,
              this.data != null ? (int) this.data : 0,
              this.radius
          );

        } else {
          loc.getWorld().playEffect(
              loc,
              this.effect,
              this.data
          );
        }
      });
    }

    @Override
    public void play(Location centerLoc, Player player) {
      obtainLocations(centerLoc, loc -> {
        if (this.data == null || this.data instanceof Integer) {
          player.playEffect(
              loc,
              this.effect,
              this.data != null ? (int) this.data : 0
          );

        } else {
          player.playEffect(
              loc,
              this.effect,
              this.data
          );
        }
      });
    }

    @Override
    public String getName() {
      return this.effect.name();
    }

    @Override
    public boolean isInternallyEffect() {
      return true;
    }

    @Override
    public boolean isInternallyParticle() {
      return false;
    }

    @Override
    public void setOffset(float x, float y, float z) {
      this.offsetX = Math.max(0, x);
      this.offsetY = Math.max(0, y);
      this.offsetZ = Math.max(0, z);
    }

    private void obtainLocations(Location loc, Consumer<Location> callback) {
      if (this.offsetX == 0D && this.offsetY == 0D && this.offsetZ == 0D && this.count == 1)
        callback.accept(loc);
      else {
        final Random rand = ThreadLocalRandom.current();

        for (int i = 0; i < this.count; i++) {
          final double x = loc.getX() + rand.nextDouble() * this.offsetX * 2 - this.offsetX;
          final double y = loc.getY() + rand.nextDouble() * this.offsetY * 2 - this.offsetY;
          final double z = loc.getZ() + rand.nextDouble() * this.offsetZ * 2 - this.offsetZ;

          callback.accept(new Location(loc.getWorld(), x, y, z));
        }
      }
    }
  }

  @Getter
  @Setter
  private static class TypeV9Particle extends VarParticle {

    private static final Color RAINBOW_COLOR = Color.fromRGB(69, 42, 0); // never do .equals, only ==
    private static final Particle[] PARTICLES = Particle.values();

    private Particle particle;
    private int count;
    private float offsetX, offsetY, offsetZ;
    private double extra;
    private Object data;

    public TypeV9Particle(Particle particle, int count, float offsetX, float offsetY, float offsetZ, double extra, Object data) {
      this.particle = particle;
      this.count = count;
      this.offsetX = offsetX;
      this.offsetY = offsetY;
      this.offsetZ = offsetZ;
      this.extra = extra;
      this.data = data;
    }

    @Override
    public void play(Location loc) {
      if (testPlayAsync(loc))
        return;

      play(loc, (particle, x, y, z, count, offsetX, offsetY, offsetZ, extra, data) -> loc.getWorld().spawnParticle(
          particle,
          x, y, z,
          count,
          offsetX, offsetY, offsetZ,
          extra,
          data));
    }

    @Override
    public void play(Location loc, Player player) {
      play(loc, (particle, x, y, z, count, offsetX, offsetY, offsetZ, extra, data) -> player.spawnParticle(
          particle,
          x, y, z,
          count,
          offsetX, offsetY, offsetZ,
          extra,
          data));
    }

    private void play(Location loc, Spawner spawner) {
      final int ver = NMSHelper.get().getVersion();
      final boolean is20_5 = ver > 20 || ver == 20 && NMSHelper.get().getPatchVersion() >= 5;

      // legacy color
      if (!is20_5 && this.data instanceof Color && (NMSHelper.get().getVersion() <= 12 || this.particle != Particle.REDSTONE)) {
        final Color color = (Color) this.data;
        final double red = color.getRed() <= 0D ? Float.MIN_VALUE : color.getRed() / 255D;
        final double green = color.getGreen() / 255D;
        final double blue = color.getBlue() / 255D;
        final int count = Math.max(1, this.count);

        if (this.offsetX != 0D || this.offsetY != 0D || this.offsetZ != 0D) {
          for (int i = 0; i < count; i++) {
            spawner.spawn(
                this.particle,
                loc.getX() + (ThreadLocalRandom.current().nextDouble(2) - 1D) * this.offsetX,
                loc.getY() + (ThreadLocalRandom.current().nextDouble(2) - 1D) * this.offsetY,
                loc.getZ() + (ThreadLocalRandom.current().nextDouble(2) - 1D) * this.offsetZ,
                0,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextDouble() : red,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextDouble() : green,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextDouble() : blue,
                1,
                null
            );
          }
        } else {
          for (int i = 0; i < count; i++) {
            spawner.spawn(
                this.particle,
                loc,
                0,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextDouble() : red,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextDouble() : green,
                color == RAINBOW_COLOR ? ThreadLocalRandom.current().nextDouble() : blue,
                1,
                null
            );
          }
        }

        // rainbow colors
      } else if (this.data == RAINBOW_COLOR) {
        try {
          for (int i = 0; i < Math.max(1, this.count); i++) {
            final Color color = Color.fromRGB(
                ThreadLocalRandom.current().nextInt(256),
                ThreadLocalRandom.current().nextInt(256),
                ThreadLocalRandom.current().nextInt(256));

            if (this.particle.getDataType() == Color.class) {
              spawner.spawn(
                  this.particle,
                  loc,
                  this.count,
                  this.offsetX,
                  this.offsetY,
                  this.offsetZ,
                  this.extra,
                  color);

            } else {
              spawner.spawn(
                  this.particle,
                  loc,
                  this.count,
                  this.offsetX,
                  this.offsetY,
                  this.offsetZ,
                  this.extra,
                  VarParticle.constructDustOptions(color, 1F));
            }
          }
        } catch (Exception e) {
          e.printStackTrace();
        }

        // spawn normally
      } else {
        spawner.spawn(
            this.particle,
            loc,
            this.count,
            this.offsetX,
            this.offsetY,
            this.offsetZ,
            this.extra,
            this.data
        );
      }
    }

    private static interface Spawner {

      void spawn(Particle particle, double x, double y, double z, int count, double offsetX, double offsetY, double offsetZ, double extra, Object data);

      default void spawn(Particle particle, Location loc, int count, double offsetX, double offsetY, double offsetZ, double extra, Object data) {
        spawn(particle, loc.getX(), loc.getY(), loc.getZ(), count, offsetX, offsetY, offsetZ, extra, data);
      }
    }

    @Override
    public String getName() {
      return this.particle.name();
    }

    @Override
    public boolean isInternallyEffect() {
      return false;
    }

    @Override
    public boolean isInternallyParticle() {
      return true;
    }

    @Override
    public void setOffset(float x, float y, float z) {
      this.offsetX = Math.max(0, x);
      this.offsetY = Math.max(0, y);
      this.offsetZ = Math.max(0, z);
    }

    static Object parse(Particle particle, String rawData) throws Exception {
      if (rawData == null) {
        if (particle.getDataType() == Void.class)
          return null;
        else
          rawData = "";
      }

      final int version = NMSHelper.get().getVersion();
      Object parsedData = null;

      // color implementation for REDSTONE between 1.9 and 1.12
      if (version <= 12 && particle == Particle.REDSTONE) {
        final Color color = getColor(rawData);

        return color != null ? color : RAINBOW_COLOR;
      }

      // color implementation for misc ones
      if (particle == Particle.SPELL_MOB || particle.name().equals("SPELL_MOB_AMBIENT")) {
        final Color color = getColor(rawData);

        return color != null ? color : RAINBOW_COLOR;
      }

      // misc
      switch (particle.getDataType().getSimpleName()) {
        case "DustOptions": {
          final String[] parts = rawData.split(":");
          final Color color = getColor(parts[0]);

          if (color == null)
            return RAINBOW_COLOR; // ignore the check at the bottom
          else
            parsedData = constructDustOptions(color, parts.length >= 2 ? Float.parseFloat(parts[1]) : 1F);
        }
        break;

        case "ItemStack": {
          final ItemStack is = Helper.get().parseItemStack(rawData);

          if (is == null)
            throw new IllegalArgumentException("Unknown item \"" + rawData + "\"");

          parsedData = is;
        }
        break;

        case "BlockData": {
          Material mat = Helper.get().getMaterialByName(rawData);

          if (mat == null)
            throw new IllegalArgumentException("Unknown material \"" + rawData + "\"");
          if (mat == Material.AIR)
            throw new IllegalArgumentException("Invalid material \"" + rawData + "\"");

          mat = Helper.get().getBlockVariant(mat);

          //if(!mat.isBlock())
          //    throw new IllegalArgumentException("Material \""+rawData+"\" is not a valid block");

          parsedData = Material.class.getMethod("createBlockData").invoke(mat);
        }
        break;

        case "MaterialData":
          return VarParticle.getMaterialData(rawData);

        case "Float":
          return Float.parseFloat(rawData);

        case "Integer":
          return Integer.parseInt(rawData);

        default:
          parsedData = null;
      }

      if ((parsedData != null && !particle.getDataType().isAssignableFrom(parsedData.getClass()))
          || (parsedData == null && particle.getDataType() != Void.class)) {
        throw new IllegalArgumentException("Failed to spawn particle \"" + particle.name()
            + "\": Expects data of type " + particle.getDataType().getSimpleName() + " (got " + (parsedData == null ? "nothing" : parsedData.getClass().getSimpleName() + ")"));
      }

      return parsedData;
    }

    @Nullable
    static Particle getByName(String name) {
      for (Particle p : PARTICLES) {
        if (p.name().equals(name))
          return p;
      }

      return null;
    }
  }

  private static class NoneParticle extends VarParticle {

    @Override
    public void play(Location loc) {
    }

    @Override
    public void play(Location loc, Player player) {
    }

    @Override
    public String getName() {
      return "none";
    }

    @Override
    public boolean isInternallyEffect() {
      return false;
    }

    @Override
    public boolean isInternallyParticle() {
      return false;
    }

    @Override
    public float getOffsetX() {
      return 0;
    }

    @Override
    public float getOffsetY() {
      return 0;
    }

    @Override
    public float getOffsetZ() {
      return 0;
    }

    @Override
    public void setOffset(float x, float y, float z) {
    }

    @Override
    public int getCount() {
      return 0;
    }

    @Override
    public void setCount(int count) {
    }
  }
}