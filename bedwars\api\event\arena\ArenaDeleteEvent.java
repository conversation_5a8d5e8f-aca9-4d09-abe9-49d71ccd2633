package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import lombok.Getter;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when an arena gets deleted.
 * <p>
 *   This event might not be called when i.a. the server shuts down.
 *   If you want to catch them all to e.g. update a Map, use {@link ArenaUnloadEvent}.
 * </p>
 */
public class ArenaDeleteEvent extends Event implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final CommandSenderWrapper sender;

  private boolean cancel = false;

  public ArenaDeleteEvent(Arena arena, @Nullable CommandSenderWrapper sender) {
    this.arena = arena;
    this.sender = sender;
  }

  /**
   * Returns the person who deleted the arena.
   * <p>
   * 	Can be <code>null</code> when something else deleted it (such as a plugin or a player remotely).
   * </p>
   *
   * @return The person behind this
   */
  @Nullable
  public CommandSender getSender() {
    return this.sender != null ? this.sender.getCommandSender() : null;
  }

  /**
   * Returns the person who deleted the arena.
   * <p>
   *     Can be <code>null</code> when someone else deleted it (such as a plugin).
   * </p>
   *
   * @return The person behind this
   */
  @Nullable
  public CommandSenderWrapper getSenderWrapped() {
    return this.sender;
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public void setCancelled(boolean cancel) {
    this.cancel = cancel;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}