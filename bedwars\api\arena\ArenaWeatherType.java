package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.tools.Helper;
import org.bukkit.WeatherType;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * A per-arena weather that'll be used during a match.
 */
public enum ArenaWeatherType {

  /**
   * Weather will be untouched by MBedwars
   */
  UNTOUCHED,

  /**
   * Weather will be fixed to clear
   */
  CLEAR,

  /**
   * Weather will be fixed to precipitation
   */
  RAINING;


  /**
   * Get the equivalent of Bukkit's API
   *
   * @return The equivalent of Bukkit's API. <code>null</code> if untouched
   */
  @Nullable
  public WeatherType asBukkit() {
    switch (this) {
      case UNTOUCHED:
        return null;
      case CLEAR:
        return WeatherType.CLEAR;
      case RAINING:
        return WeatherType.DOWNFALL;
      default:
        throw new IllegalStateException("Missing implementation");
    }
  }

  /**
   * Get the icon that is being displayed in the properties menu
   *
   * @return The icon used in GUIs
   */
  public ItemStack getIcon() {
    switch (this) {
      case UNTOUCHED:
        return Helper.get().parseItemStack("PAINTING");
      case RAINING:
        return Helper.get().parseItemStack("PLAYER_HEAD:eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZGYwZDk2MzM2ZmZjYzdmMTJhOGE4ZjVlM2JkMjU4ZWMyZDgwMzdiYTI0YTQzNTkxN2MzZDUzMWIyMTZkZTZkNyJ9fX0=");
      case CLEAR:
        return Helper.get().parseItemStack("PLAYER_HEAD:eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvY2Q2ZDEzM2UxZGZmMTQyMGY1ZWM4MGNkMWYzZTdiYTIyMmZjOGMzM2ZhMDQ0OTMxZjY1ZTNiYzY5NzNhNDgifX19");
      default:
        throw new IllegalStateException("Missing implementation");
    }
  }
}