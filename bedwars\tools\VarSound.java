package de.marcely.bedwars.tools;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.MemoryConfiguration;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

/**
 * Easy solution for playing vanilla and custom sounds while retaining async support.
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class VarSound implements Cloneable {

  private float volume, pitch;
  private boolean isActive;

  /**
   * Returns whether this sound is a vanilla sound or whether it's a custom sound that requires a resource pack.
   *
   * @return <code>true</code> if it's a vanilla sound, <code>false</code> otherwise
   */
  public abstract boolean isVanilla();

  /**
   * Returns the name of the sound.
   * <p>
   * If it's a vanilla sound, it returns {@link Sound#name()}.
   * </p>
   *
   * @return The name of the sound
   */
  public abstract String getName();

  /**
   * Returns the bukkit enum of the sound.
   *
   * @return The bukkit enum of the sound. May be <code>null</code> if it's not present
   */
  public abstract Sound getBukkitEnum();

  /**
   * Returns the volume of the sound.
   * <p>
   * Default is <code>1</code>.
   * </p>
   *
   * @return The volume of the sound
   */
  public float getVolume() {
    return this.volume;
  }

  /**
   * Sets the volume of the sound.
   *
   * @param volume The volume of the sound
   * @return Returns this exact instance
   */
  public VarSound setVolume(float volume) {
    this.volume = volume;

    return this;
  }

  /**
   * Returns the pitch of the sound.
   * <p>
   * Default is <code>1</code>.
   * </p>
   *
   * @return The pitch of the sound
   */
  public float getPitch() {
    return this.pitch;
  }

  /**
   * Sets the pitch of the sound.
   *
   * @param pitch The pitch of the sound
   * @return Returns this exact instance
   */
  public VarSound setPitch(float pitch) {
    this.pitch = pitch;

    return this;
  }

  /**
   * Returns whether this sound is active.
   * <p>
   * If it's not active, it won't play.
   * </p>
   *
   * @return <code>true</code> if it's active, <code>false</code> otherwise
   */
  public boolean isActive() {
    return this.isActive;
  }

  /**
   * Sets whether this sound is active.
   * <p>
   * If it's not active, it won't play.
   * </p>
   *
   * @param active <code>true</code> if it's active, <code>false</code> otherwis
   * @return Returns this exact instancee
   */
  public VarSound setActive(boolean active) {
    this.isActive = active;

    return this;
  }

  /**
   * Plays the sound to the player at the given location.
   *
   * @param player         The player to play the sound to
   * @param loc            The location to play the sound at
   * @param overrideVolume The volume to play the sound at. If <code>null</code>, it uses {@link #getVolume()}
   * @param overridePitch  The pitch to play the sound at. If <code>null</code>, it uses {@link #getPitch()}
   */
  public abstract void play(Player player, Location loc, @Nullable Float overrideVolume, @Nullable Float overridePitch);

  /**
   * Plays the sound at the given location.
   *
   * @param loc            The location to play the sound at
   * @param overrideVolume The volume to play the sound at. If <code>null</code>, it uses {@link #getVolume()}
   * @param overridePitch  The pitch to play the sound at. If <code>null</code>, it uses {@link #getPitch()}
   */
  public abstract void play(Location loc, @Nullable Float overrideVolume, @Nullable Float overridePitch);

  /**
   * Plays the sound to the player at the given location.
   *
   * @param player The player to play the sound to
   * @param loc    The location to play the sound at
   */
  public void play(Player player, Location loc) {
    play(player, loc, null, null);
  }


  /**
   * Plays the sound to the player at the location of the player.
   *
   * @param player The player to play the sound to
   */
  public void play(Player player) {
    play(player, player.getLocation(), null, null);
  }


  /**
   * Plays the sound at the given location.
   *
   * @param loc The location to play the sound at
   */
  public void play(Location loc) {
    play(loc, null, null);
  }

  @Override
  public VarParticle clone() {
    try {
      return (VarParticle) super.clone();
    } catch (CloneNotSupportedException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Serializes this sound into a {@link ConfigurationSection}.
   *
   * It includes the following keys:
   *   <ul>
   *     <li>name: The name of the sound (required)</li>
   *     <li>is-active: Whether the sound is active (default: true)</li>
   *     <li>is-custom-resourcepack: Whether the sound is a custom sound that requires a resource pack (default: false)</li>
   *     <li>volume: The volume of the sound (default: 1.0)</li>
   *     <li>pitch: The pitch of the sound (default: 1.0)</li>
   *   </ul>
   *
   *
   * @return The serialized sound
   */
  public ConfigurationSection serializeAsConfig() {
    final ConfigurationSection sec = new MemoryConfiguration();

    sec.set("name", getName());
    sec.set("is-active", isActive());
    sec.set("is-custom-resourcepack", !isVanilla());
    sec.set("volume", getVolume());
    sec.set("pitch", getPitch());

    return sec;
  }

  /**
   * Parses a sound from a {@link ConfigurationSection}.
   *
   * It includes the following keys:
   *   <ul>
   *     <li>name: The name of the sound (required)</li>
   *     <li>is-active: Whether the sound is active (default: true)</li>
   *     <li>is-custom-resourcepack: Whether the sound is a custom sound that requires a resource pack (default: false)</li>
   *     <li>volume: The volume of the sound (default: 1.0)</li>
   *     <li>pitch: The pitch of the sound (default: 1.0)</li>
   *   </ul>
   *
   * @param config The config to parse
   * @return The parsed sound
   * @throws IllegalArgumentException If the config is invalid
   */
  public static VarSound parse(ConfigurationSection config) throws IllegalArgumentException {
    Validate.notNull(config, "config");

    if (!config.contains("name"))
      throw new IllegalArgumentException("Missing \"name\" config");

    final String name = config.getString("name");
    final boolean isCustomResourcepack = config.getBoolean("is-custom-resourcepack", false);
    final boolean isActive = config.getBoolean("is-active", true);
    final float volume = (float) config.getDouble("volume", 1);
    final float pitch = (float) config.getDouble("pitch", 1);

    if (isCustomResourcepack)
      return new Custom(name, volume, pitch, isActive);
    else {
      final Sound sound = Helper.get().getSoundByName(name);

      if (sound == null)
        throw new IllegalArgumentException("Invalid sound name: " + name);

      return new Vanilla(sound, volume, pitch, isActive);
    }
  }

  /**
   * Creates a VarSound from a vanilla sound.
   *
   * @param sound The sound
   * @return The created wrapper
   */
  public static VarSound from(Sound sound) {
    Validate.notNull(sound, "sound");

    return new Vanilla(sound, 1, 1, true);
  }

  /**
   * Creates a VarSound from a custom sound.
   *
   * @param name The name of the sound
   * @return The created wrapper
   */
  public static VarSound fromCustom(String name) {
    Validate.notNull(name, "name");

    return new Custom(name, 1, 1, true);
  }


  private static class Vanilla extends VarSound {

    private final Sound sound;

    private Vanilla(Sound sound, float volume, float pitch, boolean isActive) {
      super(volume, pitch, isActive);

      this.sound = sound;
    }

    @Override
    public boolean isVanilla() {
      return true;
    }

    @Override
    public String getName() {
      return this.sound.name();
    }

    @Override
    public Sound getBukkitEnum() {
      return this.sound;
    }

    @Override
    public void play(Player player, Location loc, @Nullable Float overrideVolume, @Nullable Float overridePitch) {
      Validate.notNull(player, "player");
      Validate.notNull(loc, "loc");

      if (!isActive())
        return;

      Helper.get().playSound(
          player,
          loc,
          this.sound,
          overrideVolume != null ? overrideVolume : getVolume(),
          overridePitch != null ? overridePitch : getPitch()
      );
    }

    @Override
    public void play(Location loc, @Nullable Float overrideVolume, @Nullable Float overridePitch) {
      Validate.notNull(loc, "loc");

      if (!isActive())
        return;

      Helper.get().playSound(
          loc,
          this.sound,
          overrideVolume != null ? overrideVolume : getVolume(),
          overridePitch != null ? overridePitch : getPitch()
      );
    }
  }

  private static class Custom extends VarSound {

    private String name;

    private Custom(String name, float volume, float pitch, boolean isActive) {
      super(volume, pitch, isActive);

      this.name = name;
    }

    @Override
    public boolean isVanilla() {
      return false;
    }

    @Override
    public String getName() {
      return this.name;
    }

    @Override
    public Sound getBukkitEnum() {
      return null;
    }

    @Override
    public void play(Player player, Location loc, @Nullable Float overrideVolume, @Nullable Float overridePitch) {
      Validate.notNull(player, "player");
      Validate.notNull(loc, "loc");

      if (!isActive())
        return;

      Helper.get().playCustomSound(
          player,
          loc,
          this.name,
          overrideVolume != null ? overrideVolume : getVolume(),
          overridePitch != null ? overridePitch : getPitch()
      );
    }

    @Override
    public void play(Location loc, @Nullable Float overrideVolume, @Nullable Float overridePitch) {
      Validate.notNull(loc, "loc");

      if (!isActive())
        return;

      Helper.get().playCustomSound(
          loc,
          this.name,
          overrideVolume != null ? overrideVolume : getVolume(),
          overridePitch != null ? overridePitch : getPitch()
      );
    }
  }
}