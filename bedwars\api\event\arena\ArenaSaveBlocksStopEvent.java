package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import lombok.Getter;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

import java.io.File;
import java.time.Duration;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called after the blocks of an arena have been saved/changed
 * <p>
 *   This event gets called for both failures and successes.
 * </p>
 */
public class ArenaSaveBlocksStopEvent extends Event implements ArenaEvent {

    private static final HandlerList HANDLERS = new HandlerList();

    @Getter
    private final Arena arena;
    private final File file;
    private final Duration duration;
    private final CommandSenderWrapper executor;
    private final boolean success;

    public ArenaSaveBlocksStopEvent(Arena arena, File file, Duration duration, @Nullable CommandSenderWrapper executor, boolean success) {
        this.arena = arena;
        this.file = file;
        this.duration = duration;
        this.executor = executor;
        this.success = success;
    }

    /**
     * Returns the file where the blocks were saved
     * <p>
     *     The type of the file depends on the {@link de.marcely.bedwars.api.arena.RegenerationType}.
     * </p>
     *
     * @return The file where the blocks were saved
     */
    public File getFile() {
        return this.file;
    }

    /**
     * Returns the time it took to save the blocks
     *
     * @return The duration of the save operation
     */
    public Duration getDuration() {
        return this.duration;
    }

    /**
     * Returns the command sender that executed the save operation
     *
     * @return The command sender that executed the save operation. <code>null</code> if nobody directly executed it
     */
    @Nullable
    public CommandSenderWrapper getExecutor() {
        return this.executor;
    }

    /**
     * Returns whether the save operation was successful
     *
     * @return <code>true</code> if the save operation was successful, <code>false</code> otherwise
     */
    public boolean isSuccess() {
        return this.success;
    }

    @Override
    public HandlerList getHandlers() {
        return HANDLERS;
    }

    public static HandlerList getHandlerList() {
        return HANDLERS;
    }
}
