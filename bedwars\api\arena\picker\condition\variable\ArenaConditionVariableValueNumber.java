package de.marcely.bedwars.api.arena.picker.condition.variable;

/**
 * Represents a numberic condition value.
 */
public class ArenaConditionVariableValueNumber extends ArenaConditionVariableValue<Float> {

  public ArenaConditionVariableValueNumber(float entry) {
    super(entry);
  }

  @Override
  public byte getType() {
    return TYPE_NUMBER;
  }

  @Override
  public boolean equal(Float entry) {
    return this.entry == (float) entry;
  }

  @Override
  public boolean similar(Float entry) {
    return ((int) (float) entry) == ((int) (float) this.entry);
  }

  @Override
  public boolean notEqual(Float entry) {
    return this.entry != (float) entry;
  }

  @Override
  public boolean greaterThan(Float entry) {
    return this.entry > (float) entry;
  }

  @Override
  public boolean lessThan(Float entry) {
    return this.entry < (float) entry;
  }

  @Override
  public boolean greaterThanOrEqual(Float entry) {
    return this.entry >= (float) entry;
  }

  @Override
  public boolean lessThanOrEqual(Float entry) {
    return this.entry <= (float) entry;
  }

  @Override
  public String toString() {
    if (this.entry%1 == 0D)
      return Integer.toString((int) (float) this.entry);
    else
      return Float.toString(this.entry);
  }
}
