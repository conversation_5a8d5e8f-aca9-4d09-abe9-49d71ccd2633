package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.tools.Validate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a queued entry within {@link ArenaCloningManager}.
 */
public class QueuedCloningArena {

  private final Arena arena;
  private final Integer playersPerTeam;
  private final boolean ignoreLimits;
  private final List<Consumer<Optional<Arena>>> callbacks;

  /**
   * @param arena The arena that shall be cloned
   * @throws IllegalArgumentException when playersPerTeam is not null and &gt; 1
   */
  public QueuedCloningArena(Arena arena) {
    this(arena, null, false, null);
  }

  /**
   * @param arena The arena that shall be cloned
   * @param playersPerTeam The players per team amount that shall be applied for the cloned arena. May be <code>null</code>
   * @param ignoreLimits Whether certain limitations shall be ignored (see {@link ArenaCloningManager#addToQueue(QueuedCloningArena)})
   * @param callback An optional callback that returns the result of the cloning. In case it succeded, the callback will include the cloned arena. It is always being called on the main thread
   * @throws IllegalArgumentException when playersPerTeam is not null and &gt; 1
   */
  public QueuedCloningArena(Arena arena, @Nullable Integer playersPerTeam, boolean ignoreLimits, @Nullable Consumer<Optional<Arena>> callback) {
    Validate.notNull(arena, "arena");
    Validate.isTrue(playersPerTeam == null || playersPerTeam >= 1, "playersPerTeam must be either null or >= 1");

    this.arena = arena;
    this.playersPerTeam = playersPerTeam;
    this.ignoreLimits = ignoreLimits;
    this.callbacks = new ArrayList<>(5);

    if (callback != null)
      this.callbacks.add(callback);
  }

  /**
   * Gets the arena that will be cloned in the future.
   *
   * @return The clone that this queue entry hold
   */
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Gets the players per team amount that will be applied for the cloned arena.
   * <p>
   *   Basically calls {@link Arena#setPlayersPerTeam(int)} once the arena has been cloned.
   *   <code>null</code> means that the original players per team amount stays as it was.
   * </p>
   *
   * @return The players per team amount of the new arena, or <code>null</code> if it stays the same
   */
  @Nullable
  public Integer getApplyingPlayersPerTeam() {
    return this.playersPerTeam;
  }

  /**
   * Whether certain limitations shall be ignored (see {@link ArenaCloningManager#addToQueue(QueuedCloningArena)}).
   *
   * @return <code>true</code> if this entry is ignroing the existing limitations
   */
  public boolean isIgnoringLimits() {
    return this.ignoreLimits;
  }

  /**
   * Gets all callbacks that are being called once the cloning has been finished.
   *
   * @return The list of callbacks
   */
  public List<Consumer<Optional<Arena>>> getCallbacks() {
    return Collections.unmodifiableList(this.callbacks);
  }

  /**
   * Adds a callback that will be called once the cloning has been finished.
   *
   * @param callback The callback to add
   */
  public void addCallback(Consumer<Optional<Arena>> callback) {
    Validate.notNull(callback, "callback");

    this.callbacks.add(callback);
  }
}
