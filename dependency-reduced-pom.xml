<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.acebrand</groupId>
  <artifactId>AceVoteMode</artifactId>
  <name>AceVoteMode</name>
  <version>1.0.0</version>
  <description>A voting system addon for Bedwars with custom game modes</description>
  <url>https://acebrand.cn</url>
  <developers>
    <developer>
      <id>acebrand</id>
      <name>AceBrand</name>
      <email><EMAIL></email>
      <url>https://acebrand.cn</url>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <build>
    <sourceDirectory>src/main/java</sourceDirectory>
    <defaultGoal>clean package</defaultGoal>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*.yml</include>
          <include>**/*.yaml</include>
          <include>**/*.txt</include>
          <include>**/*.md</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>21</source>
          <target>21</target>
          <encoding>UTF-8</encoding>
          <executable>${java.home}\bin\javac</executable>
          <fork>true</fork>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <configuration>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <finalName>AceVoteMode-${project.version}</finalName>
              <minimizeJar>false</minimizeJar>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
              </filters>
              <relocations />
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.3.0</version>
        <configuration>
          <archive>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
            <manifestEntries>
              <Built-By>AceBrand</Built-By>
              <Implementation-Title>${project.name}</Implementation-Title>
              <Implementation-Version>${project.version}</Implementation-Version>
              <Implementation-Vendor>AceBrand</Implementation-Vendor>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.3.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-install-plugin</artifactId>
        <version>3.1.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>3.1.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.1.2</version>
        <configuration>
          <skipTests>true</skipTests>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>local-libs</id>
      <url>file://${project.basedir}/libs</url>
    </repository>
    <repository>
      <id>placeholderapi</id>
      <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains</groupId>
      <artifactId>annotations</artifactId>
      <version>24.0.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>me.clip</groupId>
      <artifactId>placeholderapi</artifactId>
      <version>2.11.6</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>de.marcely.bedwars</groupId>
      <artifactId>mbedwars-api</artifactId>
      <version>5.5.3</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/libs/MBedwars-5.5.3.jar</systemPath>
    </dependency>
    <dependency>
      <groupId>io.lumine</groupId>
      <artifactId>MythicMobs</artifactId>
      <version>5.7.2</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/libs/MythicMobs-5.7.2.jar</systemPath>
    </dependency>
  </dependencies>
  <properties>
    <maven.compiler.source>21</maven.compiler.source>
    <java.home>C:\Program Files\Java\jdk-21</java.home>
    <spigot.version>1.20.1-R0.1-SNAPSHOT</spigot.version>
    <maven.compiler.target>21</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <minecraft.version>1.20.1</minecraft.version>
  </properties>
</project>
