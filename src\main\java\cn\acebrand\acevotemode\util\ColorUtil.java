package cn.acebrand.acevotemode.util;

import org.bukkit.ChatColor;

/**
 * 颜色工具类
 * 用于处理颜色代码转换
 */
public class ColorUtil {
    
    /**
     * 转换颜色代码
     * @param text 包含&颜色代码的文本
     * @return 转换后的文本
     */
    public static String color(String text) {
        if (text == null) {
            return "";
        }
        return ChatColor.translateAlternateColorCodes('&', text);
    }
    
    /**
     * 移除颜色代码
     * @param text 包含颜色代码的文本
     * @return 移除颜色代码后的纯文本
     */
    public static String stripColor(String text) {
        if (text == null) {
            return "";
        }
        return ChatColor.stripColor(text);
    }
    
    /**
     * 获取彩色文本（快捷方法）
     */
    public static String red(String text) {
        return ChatColor.RED + text;
    }
    
    public static String green(String text) {
        return ChatColor.GREEN + text;
    }
    
    public static String yellow(String text) {
        return ChatColor.YELLOW + text;
    }
    
    public static String blue(String text) {
        return ChatColor.BLUE + text;
    }
    
    public static String gold(String text) {
        return ChatColor.GOLD + text;
    }
    
    public static String gray(String text) {
        return ChatColor.GRAY + text;
    }
    
    public static String bold(String text) {
        return ChatColor.BOLD + text;
    }
    
    /**
     * 组合颜色和格式
     */
    public static String colorBold(String text) {
        return ChatColor.YELLOW + "" + ChatColor.BOLD + text;
    }
    
    public static String greenBold(String text) {
        return ChatColor.GREEN + "" + ChatColor.BOLD + text;
    }
    
    public static String redBold(String text) {
        return ChatColor.RED + "" + ChatColor.BOLD + text;
    }
}
