package de.marcely.bedwars.api.event.remote;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called whenever a server wants to connect to the network.
 * <p>
 *     Cancelling this event causes the server to fail connecting to the network.
 *     Keep in mind that this event is async.
 * </p>
 * <p>
 *   This event may be called multiple times for the same join process,
 *   in case the remote server wants to be sure that there is no other server
 *   with the same channel name connecting at the same time.
 * </p>
 */
public class RemoteServerConnectRequestEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final String bungeeChannelName;
  private final boolean isHub;
  private final int playersCount, maxPlayersCount;
  private final int bedwarsAPIVersion;
  private final String bedwarsPluginVersion, addonPluginVersion;

  @Getter @Setter
  private boolean cancelled = false;

  public RemoteServerConnectRequestEvent(
      String bungeeChannelName,
      boolean isHub,
      int playersCount,
      int maxPlayersCount,
      int bedwarsAPIVersion,
      String bedwarsPluginVersion,
      String addonPluginVersion) {
    super(true);

    this.bungeeChannelName = bungeeChannelName;
    this.isHub = isHub;
    this.playersCount = playersCount;
    this.maxPlayersCount = maxPlayersCount;
    this.bedwarsAPIVersion = bedwarsAPIVersion;
    this.bedwarsPluginVersion = bedwarsPluginVersion;
    this.addonPluginVersion = addonPluginVersion;
  }

  public String getBungeeChannelName() {
    return this.bungeeChannelName;
  }

  public boolean isHub() {
    return this.isHub;
  }

  public int getPlayersCount() {
    return this.playersCount;
  }

  public int getMaxPlayersCount() {
    return this.maxPlayersCount;
  }

  public int getBedwarsAPIVersion() {
    return this.bedwarsAPIVersion;
  }

  public String getBedwarsPluginVersion() {
    return this.bedwarsPluginVersion;
  }

  public String getAddonPluginVersion() {
    return this.addonPluginVersion;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
