package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.QuitPlayerMemory;
import de.marcely.bedwars.api.arena.Team;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Gets called when a game has ended and is about to move into the EndLobby / Resetting state.
 */
public class RoundEndEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Team winnerTeam;
  private final Collection<Player> onlineWinners, onlineLosers;
  private final Collection<QuitPlayerMemory> offlineWinners, offlineLosers;

  public RoundEndEvent(
      Arena arena,
      @Nullable Team winnerTeam,
      Collection<Player> onlineWinners,
      Collection<QuitPlayerMemory> offlineWinners,
      Collection<Player> onlineLosers,
      Collection<QuitPlayerMemory> offlineLosers) {

    this.arena = arena;
    this.winnerTeam = winnerTeam;
    this.onlineWinners = Collections.unmodifiableCollection(onlineWinners);
    this.offlineWinners = Collections.unmodifiableCollection(offlineWinners);
    this.onlineLosers = Collections.unmodifiableCollection(onlineLosers);
    this.offlineLosers = Collections.unmodifiableCollection(offlineLosers);
  }

  public RoundEndEvent(
      Arena arena,
      @Nullable Team winnerTeam,
      Map<Player, Team> onlinePlayers,
      Collection<QuitPlayerMemory> offlinePlayers) {

    this(
        arena,
        winnerTeam,

        winnerTeam != null ?
            onlinePlayers.entrySet().stream()
                .filter(e -> e.getValue() != null && e.getValue() == winnerTeam)
                .map(e -> e.getKey())
                .collect(Collectors.toList())
            : Collections.emptyList(),

        winnerTeam != null ?
            offlinePlayers.stream()
                .filter(e -> e.getTeam() == winnerTeam)
                .collect(Collectors.toList())
            : Collections.emptyList(),

        winnerTeam != null ?
            onlinePlayers.entrySet().stream()
                .filter(e -> e.getValue() != null && e.getValue() != winnerTeam)
                .map(e -> e.getKey())
                .collect(Collectors.toList())
            : Collections.emptyList(),

        winnerTeam != null ?
            offlinePlayers.stream()
                .filter(e -> e.getTeam() != winnerTeam)
                .collect(Collectors.toList())
            : Collections.emptyList());
  }

  /**
   * Returns the team that won the round.
   * <p>
   * 	It's <code>null</code> if no team has won because it ended as a tie.
   * </p>
   *
   * @return The team that won
   */
  @Nullable
  public Team getWinnerTeam() {
    return this.winnerTeam;
  }

  /**
   * Returns if the round has ended as a tie.
   *
   * @return If the round ended as a tie
   */
  public boolean isTie() {
    return this.winnerTeam == null;
  }

  /**
   * Returns the online player that won the round.
   * <p>
   * 	The collection is empty when nobody who is online on the server has won or when there was a tie.
   * </p>
   *
   * @return The players that are online and who have won the round
   */
  public Collection<Player> getWinners() {
    return this.onlineWinners;
  }

  /**
   * Returns the offline players that won the round.
   * <p>
   *     The collection is empty when nobody who is offline has won or when there was a tie.
   * </p>
   *
   * @return All players that are offline and have won the round
   */
  public Collection<QuitPlayerMemory> getQuitWinners() {
    return this.offlineWinners;
  }

  /**
   * Returns *all* the online player that lost the round.
   * <p>
   * 	The collection is empty when nobody who is online on the server has lost or when there was a tie.
   * </p>
   *
   * @return The players that are online and who have lost the round
   */
  public Collection<Player> getLosers() {
    return this.onlineLosers;
  }

  /**
   * Returns the offline players that lost the round.
   * <p>
   *     The collection is empty when nobody who is offline has lost or when there was a tie.
   * </p>
   *
   * @return All players that are offline and have lost the round
   */
  public Collection<QuitPlayerMemory> getQuitLosers() {
    return this.offlineLosers;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
