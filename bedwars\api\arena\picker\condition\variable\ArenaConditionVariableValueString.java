package de.marcely.bedwars.api.arena.picker.condition.variable;

/**
 * Represents a condition value of the type String.
 */
public class ArenaConditionVariableValueString extends ArenaConditionVariableValue<String> {

  public ArenaConditionVariableValueString(String entry) {
    super(entry);
  }

  @Override
  public byte getType() {
    return TYPE_STRING;
  }

  @Override
  public boolean equal(String entry) {
    return this.entry.equals(entry);
  }

  @Override
  public boolean similar(String entry) {
    return this.entry.equalsIgnoreCase(entry);
  }

  @Override
  public boolean notEqual(String entry) {
    return !this.entry.equals(entry);
  }

  @Override
  public boolean greaterThan(String entry) {
    return this.entry.toLowerCase().startsWith(entry.toLowerCase());
  }

  @Override
  public boolean lessThan(String entry) {
    return this.entry.toLowerCase().endsWith(entry.toLowerCase());
  }

  @Override
  public boolean greaterThanOrEqual(String entry) {
    return this.entry.startsWith(entry);
  }

  @Override
  public boolean lessThanOrEqual(String entry) {
    return this.entry.endsWith(entry);
  }

  @Override
  public String toString() {
    return "\"" + this.entry + "\"";
  }
}
