package de.marcely.bedwars.api.arena.picker.condition;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.remote.RemoteArena;

import java.util.Iterator;

/**
 * A generic condition that checks an arena.
 */
public abstract class ArenaCondition {

  /**
   * Checks whether the given arena fullfills the conditions or not.
   *
   * @param arena The local arena that is being checked
   * @return <code>true</code> in case it may pass
   */
  public abstract boolean check(Arena arena);

  /**
   * Checks whether the given arena fullfills the conditions or not.
   *
   * @param arena The remote arena that is being checked
   * @return <code>true</code> in case it may pass
   */
  public abstract boolean check(RemoteArena arena);

  /**
   * Filters out all arenas that do not match the given condition.
   *
   * @param arenas A mutable iterable collection of arenas
   */
  public final void filterLocalArenas(Iterable<Arena> arenas) {
    final Iterator<Arena> it = arenas.iterator();

    while (it.hasNext()) {
      if (!check(it.next()))
        it.remove();
    }
  }

  /**
   * Filters out all arenas that do not match the given condition.
   *
   * @param arenas A mutable iterable collection of arenas
   */
  public final void filterRemoteArenas(Iterable<RemoteArena> arenas) {
    final Iterator<RemoteArena> it = arenas.iterator();

    while (it.hasNext()) {
      if (!check(it.next()))
        it.remove();
    }
  }
}
