package de.marcely.bedwars.api.remote;

import de.marcely.bedwars.api.arena.AddPlayerCause;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.tools.Validate;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Contains all the info needed to add a remote player to an arena.
 */
public class AddRemotePlayerInfo implements Cloneable {

  private List<RemotePlayer> players = new ArrayList<>(2);
  private Team targetTeam = null;
  private boolean autoAssignTeam = false;
  private Collection<RemotePlayer> autoAssignTeamIncludePlayers = Collections.emptyList();
  private boolean fallbackAsSpectator = false;
  private boolean forcefully = false;
  private AddPlayerCause cause = AddPlayerCause.PLUGIN;

  /**
   * Creates a new instance.
   */
  public AddRemotePlayerInfo() {
  }

  /**
   * Add a player that shall be added to the arena.
   *
   * @param player The player who shall be added
   * @return This instance
   */
  public AddRemotePlayerInfo addPlayer(RemotePlayer player) {
    Validate.notNull(player, "player");

    this.players.add(player);

    return this;
  }

  /**
   * Add multiple players that shall be added to the arena.
   *
   * @param players The player who shall be added
   * @return This instance
   */
  public AddRemotePlayerInfo addPlayers(RemotePlayer... players) {
    Validate.notNullDeep(players, "players");

    for (RemotePlayer player : players)
      addPlayer(player);

    return this;
  }

  /**
   * Add multiple players that shall be added to the arena.
   *
   * @param players The player who shall be added
   * @return This instance
   */
  public AddRemotePlayerInfo addPlayers(Collection<RemotePlayer> players) {
    Validate.notNull(players, "players");

    this.players.addAll(players);

    return this;
  }

  /**
   * Returns all players that have been added so far.
   *
   * @return A (unmodifiable) list of all players that have been added
   */
  public Collection<RemotePlayer> getPlayers() {
    return Collections.unmodifiableCollection(this.players);
  }

  /**
   * Set the team which the player shall automatically enter when he joins the arena.
   * <code>null</code> by default.
   * <p>
   *     Setting it as <code>null</code> causes him to not join any arena automatically.
   * </p>
   *
   * @param targetTeam His target team. May be <code>null</code>
   * @return This instance
   */
  public AddRemotePlayerInfo setTargetTeam(@Nullable Team targetTeam) {
    this.targetTeam = targetTeam;

    return this;
  }

  /**
   * Returns the target team that has been specified using {@link #getTargetTeam()}.
   *
   * @return The target team. May be <code>null</code>
   */
  @Nullable
  public Team getTargetTeam() {
    return this.targetTeam;
  }

  /**
   * Whether a team shall be automatically assigned. <code>false</code> by default.
   * <p>
   *     The difference to {@link #setTargetTeam(Team)} with <code>null</code> as a parameter,
   *      is that this method tries to add all given players into the same arena.
   *      This may not succed every time when a team is already fully occupied.
   *      In this case, the remaining players won't enter any team.
   *      The team is still going to be filled.
   * </p>
   *
   * @param autoAssign Whether all players shall be assigned to the same team
   * @return This instance
   */
  public AddRemotePlayerInfo setAutoAssignTeam(boolean autoAssign) {
    this.autoAssignTeam = autoAssign;

    return this;
  }

  /**
   * Whether auto-assign has been enabled using {@link #setAutoAssignTeam(boolean)}.
   *
   * @return Either true or false
   */
  public boolean isAutoAssignTeam() {
    return this.autoAssignTeam;
  }

  /**
   * Set all players that shall also be concidered for the auto-team assignation.
   * <p>
   *     The collection should only contain players that are already playing within the arena.
   * </p>
   *
   * @param players All players that shall also have their team changed to the same one
   * @return This instance
   */
  public AddRemotePlayerInfo setAutoAssignTeamIncludePlayers(Collection<RemotePlayer> players) {
    this.autoAssignTeamIncludePlayers = players;

    return this;
  }

  /**
   * All players that also are being concidered for the auto-team assignation.
   * <p>
   *     The collection should only contain players that are already playing within the arena.
   * </p>
   *
   * @return All players that are also being concidered for the auto-team assignation
   */
  public Collection<RemotePlayer> getAutoAssignTeamIncludePlayers() {
    return this.autoAssignTeamIncludePlayers;
  }

  /**
   * Define whether players shall fall back as spectators when the arena is running.
   * <code>false</code> by default.
   *
   * @param enableFallback Whether they shall fall back as a spectator
   * @return This instance
   */
  public AddRemotePlayerInfo setSpectatorFallback(boolean enableFallback) {
    this.fallbackAsSpectator = enableFallback;

    return this;
  }

  /**
   * Whether they shall fallback as spectators. Defined using {@link #setSpectatorFallback(boolean)}.
   *
   * @return Either true or false
   */
  public boolean isSpectatorFallbackActive() {
    return this.fallbackAsSpectator;
  }

  /**
   * Define the cause that made the player join the arena.
   * <p>
   *     {@link AddPlayerCause#PLUGIN} by default.
   * </p>
   *
   * @param cause The cause that made him join the arena
   * @return This instance
   */
  public AddRemotePlayerInfo setCause(AddPlayerCause cause) {
    Validate.notNull(cause, "cause");

    this.cause = cause;

    return this;
  }

  /**
   * Returns the cause that made him join the arena-
   * Defined using {@link #setCause(AddPlayerCause)}.
   *
   * @return The cause that made him join the arena
   */
  public AddPlayerCause getCause() {
    return this.cause;
  }

  /**
   * Whether it should ignore that he's already playing within an arena.
   * <p>
   *     They won't rejoin an arena in case they are already playing inside it.
   * </p>
   *
   * @param force Force him to leave his existing arena
   * @return This instance
   */
  public AddRemotePlayerInfo setForcefully(boolean force) {
    this.forcefully = force;

    return this;
  }

  /**
   * Returns whether all players are being forcefully being removed from their existing arenas.
   * <p>
   *     They won't rejoin an arena in case they are already playing inside it.
   * </p>
   *
   * @return Whether they are being forced to join the given arena
   */
  public boolean isForcefully() {
    return this.forcefully;
  }

  @Override
  public AddRemotePlayerInfo clone() {
    try {
      final AddRemotePlayerInfo info = (AddRemotePlayerInfo) super.clone();

      info.players = new ArrayList<>(this.players);

      return info;
    } catch (CloneNotSupportedException e) {
      e.printStackTrace();
    }

    return null;
  }
}
