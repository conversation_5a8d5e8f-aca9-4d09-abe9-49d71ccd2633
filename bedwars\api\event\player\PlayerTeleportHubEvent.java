package de.marcely.bedwars.api.event.player;

import java.util.function.Consumer;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

/**
 * This event gets called whenever the player gets teleported to the hub.
 */
public class PlayerTeleportHubEvent extends PlayerEvent implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final boolean declineServerMovement;

  private Consumer<Runnable> customImplementationCallback;
  @Getter @Setter
  private boolean cancelled = false;

  public PlayerTeleportHubEvent(Player player, boolean declineServerMovement) {
    super(player);

    this.declineServerMovement = declineServerMovement;
  }

  /**
   * In somce cases the player is only getting teleported to the hub position on the local server.
   * <p>
   *     This method defines whether the player should never be teleported to an other server or get kicked.
   *     Reason being, that the player might get teleported when he e.g. joins the server.
   * </p>
   *
   * @return <code>true</code> when the plugin forbids server movements
   */
  public boolean isDecliningServerMovement() {
    return this.declineServerMovement;
  }

  /**
   * This method is used to define a custom implementation for the movement.
   * <p>
   *   Used e.g. by ProxySync. MBedwars internally puts the player into a state
   *   where he isn't able to i.a. receive damage or interact while he is being moved.
   *   Movement is delayed due to e.g. async teleportations or async bungee communication.
   * </p>
   * <p>
   *   There is an internal timeout to avoid a bug in your implementation
   *   causing the callback to never be called, which would otherwise result
   *   in a memory leak.
   * </p>
   * <p>
   *   MBedwars will pass you a runnable, in case it accepts your
   *   requests of a custom implementation (which it commonly does).
   *   Run the given runnable when the movement is done.
   * </p>
   *
   * @param callback The callback to be executed when the movement is done
   */
  public void setCustomImplementationCallback(@Nullable Consumer<Runnable> callback) {
    this.customImplementationCallback = callback;
  }

  /**
   * This method is used to get the custom implementation callback.
   *
   * @return The callback to be executed when the movement is done
   * @see #setCustomImplementationCallback(Consumer)
   */
  @Nullable
  public Consumer<Runnable> getCustomImplementationCallback() {
    return this.customImplementationCallback;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
