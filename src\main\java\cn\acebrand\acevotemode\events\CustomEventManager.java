package cn.acebrand.acevotemode.events;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义事件管理器
 * 负责注册和执行所有自定义事件
 */
public class CustomEventManager {
    
    private final AceVoteMode plugin;
    private final Map<String, CustomEvent> registeredEvents;
    // 跟踪每个竞技场的活跃事件任务
    private final Map<Arena, Set<BukkitTask>> arenaActiveTasks;
    
    public CustomEventManager(AceVoteMode plugin) {
        this.plugin = plugin;
        this.registeredEvents = new HashMap<>();
        this.arenaActiveTasks = new ConcurrentHashMap<>();

        // 注册所有内置事件
        registerBuiltinEvents();
    }
    
    /**
     * 注册内置事件
     */
    private void registerBuiltinEvents() {
        registerEvent(new DragonEvent());
        registerEvent(new BedDestroyEvent());
        registerEvent(new GameEndEvent());
        
        plugin.getLogger().info("Registered " + registeredEvents.size() + " custom events");
    }
    
    /**
     * 注册自定义事件
     */
    public void registerEvent(CustomEvent event) {
        registeredEvents.put(event.getEventType(), event);
        plugin.getLogger().info("Registered custom event: " + event.getEventName() + " (" + event.getEventType() + ")");
    }
    
    /**
     * 执行自定义事件
     */
    public boolean executeEvent(String eventType, Arena arena, EventConfig eventConfig) {
        CustomEvent event = registeredEvents.get(eventType);
        if (event == null) {
            plugin.getLogger().warning("Unknown custom event type: " + eventType);
            return false;
        }
        
        try {
            plugin.getLogger().info("Executing custom event: " + event.getEventName() + " in arena: " + arena.getName());
            event.execute(plugin, arena, eventConfig);
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to execute custom event " + eventType + " in arena " + arena.getName() + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 检查事件类型是否存在
     */
    public boolean hasEvent(String eventType) {
        return registeredEvents.containsKey(eventType);
    }
    
    /**
     * 获取所有注册的事件类型
     */
    public String[] getRegisteredEventTypes() {
        return registeredEvents.keySet().toArray(new String[0]);
    }
    
    /**
     * 获取事件信息
     */
    public String getEventInfo(String eventType) {
        CustomEvent event = registeredEvents.get(eventType);
        if (event == null) {
            return "Unknown event type: " + eventType;
        }
        return event.getEventName() + " (" + event.getEventType() + ")";
    }

    /**
     * 添加任务到竞技场的活跃任务列表
     */
    public void addActiveTask(Arena arena, BukkitTask task) {
        arenaActiveTasks.computeIfAbsent(arena, k -> ConcurrentHashMap.newKeySet()).add(task);
    }

    /**
     * 清理竞技场的所有活跃任务
     */
    public void cleanupArenaTasks(Arena arena) {
        Set<BukkitTask> tasks = arenaActiveTasks.remove(arena);
        if (tasks != null) {
            for (BukkitTask task : tasks) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            plugin.getLogger().info("Cleaned up " + tasks.size() + " active custom event tasks for arena: " + arena.getName());
        }
    }

    /**
     * 清理所有竞技场的任务
     */
    public void cleanupAllTasks() {
        for (Arena arena : arenaActiveTasks.keySet()) {
            cleanupArenaTasks(arena);
        }
    }
}
