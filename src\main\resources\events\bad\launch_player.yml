# 玩家起飞事件配置
# 坏事件 - 起飞50格

# 事件基本信息
event:
  name: "LAUNCH_PLAYER"
  type: "BAD"
  weight: 35
  enabled: true

# 消息设置
messages:
  # 是否发送事件消息到聊天栏
  send_message: true
  # 消息前缀
  message_prefix: "§c[幸运方块] §f"
  # 事件消息
  event_message: "§c准备起飞！3...2...1...发射！"

# 起飞配置
launch:
  # 起飞高度（格数）
  height: 50
  # 起飞速度（向上的速度）
  velocity_y: 3.0
  # 水平随机速度（X和Z轴的随机偏移）
  velocity_random_x: 0.5
  velocity_random_z: 0.5
  # 是否给予缓降效果防止摔死
  feather_falling:
    enabled: true
    level: 4
    duration: 200  # 10秒
  # 是否给予抗性提升防止摔伤
  resistance:
    enabled: true
    level: 2
    duration: 200  # 10秒
  # 起飞音效
  sound:
    enabled: true
    sound_type: "ENTITY_FIREWORK_ROCKET_LAUNCH"
    volume: 1.0
    pitch: 1.0
  # 起飞粒子效果
  particles:
    enabled: true
    particle_type: "EXPLOSION_LARGE"
    count: 10
