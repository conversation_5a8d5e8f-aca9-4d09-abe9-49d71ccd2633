package de.marcely.bedwars.api;

import de.marcely.bedwars.api.arena.*;
import de.marcely.bedwars.api.game.lobby.LobbyItem;
import de.marcely.bedwars.api.game.lobby.LobbyItemHandler;
import de.marcely.bedwars.api.game.scoreboard.ScoreboardHandler;
import de.marcely.bedwars.api.game.shop.BuyGroup;
import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.api.game.shop.ShopOpenCause;
import de.marcely.bedwars.api.game.shop.ShopPage;
import de.marcely.bedwars.api.game.shop.layout.ShopLayout;
import de.marcely.bedwars.api.game.spawner.CustomDropTypeHandler;
import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import de.marcely.bedwars.api.game.spectator.Spectator;
import de.marcely.bedwars.api.game.spectator.SpectatorItem;
import de.marcely.bedwars.api.game.spectator.SpectatorItemHandler;
import de.marcely.bedwars.api.game.upgrade.Upgrade;
import de.marcely.bedwars.api.game.upgrade.UpgradeShopOpenCause;
import de.marcely.bedwars.api.game.upgrade.UpgradeTriggerHandler;
import de.marcely.bedwars.api.game.upgrade.layout.UpgradeShopLayout;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.tools.location.XYZ;
import java.util.UUID;
import org.bukkit.Location;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * Contains API for managing game-related stuff, such as arenas, lobbies, spawners...
 */
@SuppressWarnings("deprecation")
public interface GameAPI {

  /**
   * Returns a builder to create and add an arena.
   * <p>
   * 	Invoke {@link ArenaBuilder#finish()} to add the arena.
   * </p>
   *
   * @return The ArenaBuilder you can use to pass needed information to the arena
   */
  ArenaBuilder createArena();

  /**
   * Returns all existing arenas.
   *
   * @return Returns all added arenas
   */
  Collection<Arena> getArenas();

  /**
   * Looks for an arena with that name and optionally parses it as an arena picker.
   *
   * @param name The name of the arena
   * @return The arena instance. Returns <code>null</code> if it hasn't found it
   */
  @Nullable Arena getArenaByName(String name);

  /**
   * Looks for an arena with exactly that name.
   * <p>
   * 	Ignores display name, arena pickers etc.
   * </p>
   *
   * @param name The name of the arena
   * @return The arena instance. Returns <code>null</code> if it hasn't found it
   */
  @Nullable Arena getArenaByExactName(String name);

  /**
   * Looks for an arena with that player inside the arena.
   * <p>
   * Ignores spectators and the state of the arena,
   * meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param player The player who joined the arena
   * @return The arena instance. Returns <code>null</code> when the player isn't inside the arena
   */
  @Nullable Arena getArenaByPlayer(Player player);

  /**
   * Looks for an arena with that player spectating the arena.
   * <p>
   * 	Ignores normal players and the state of the arena,
   * 	meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param player The player who's spectating the arena
   * @return The arena instance. Returns null when the player isn't spectating any arena
   */
  @Nullable Arena getArenaBySpectator(Player player);

  /**
   * Looks for all arenas where the location is inside the arena.
   * <p>
   * There's most of the time and when the user has created the arenas correctly,
   * only one entry in the collection.<br>
   * Ignores lobby locations etc. and ignores non-normal types ({@link RegenerationType#isNormal()})
   * </p>
   *
   * @param location The location inside the arena
   * @return Returns the arena in which the location is inside. Returns null if there isn't any
   */
  @Nullable Collection<Arena> getArenaByLocation(Location location);

  /**
   * Returns weather or not the given String can be used as the name for an arena
   * <p>
   * Does not check whether or not the arena already exists.
   * It's only checking if the characters are valid.
   * </p>
   *
   * @param name The name we want to check
   * @return If the given name is valid
   */
  boolean isArenaNameValid(String name);

  /**
   * Returns all existing {@link LobbyItem}s.
   *
   * @return All existing LobbyItems
   * @see #getLobbyItems(int)
   * @see #getVisibleLobbyItem(int, Player, Arena)
   */
  Collection<? extends LobbyItem> getLobbyItems();

  /**
   * Returns all {@link LobbyItem}s that were added to a specific slot.
   * Might be <code>null</code> if there's no item at the given slot.
   * <p>
   * While it does not make any sense at the first glance
   * keep in mind that LobbyItems are only visible at specific conditions.
   * If you want to know which one is currently visible you might want to use {@link #getVisibleLobbyItem(int, Player, Arena)}.
   *
   * @param slot The slot at which it's located at (0-8)
   * @return All items that were added to that slot, <code>null</code> if there's none
   * @throws IndexOutOfBoundsException When slot is less than 0 or greater than 8
   * @see #getVisibleLobbyItem(int, Player, Arena)
   */
  @Nullable Collection<? extends LobbyItem> getLobbyItems(int slot);

  /**
   * Returns the currently visible item at a specific slot.
   * Might be <code>null</code> if there's none.
   *
   * @param slot The slot at which it's located at (0-8)
   * @param player The player who'd see the item
   * @param arena The arena in which the player is located in
   * @return The item that's visible for the player, <code>null</code> if there's none
   * @see #getLobbyItems(int)
   */
  @Nullable LobbyItem getVisibleLobbyItem(int slot, Player player, Arena arena);

  /**
   * Returns all registered handlers.
   * Also includes the default ones.
   * <p>
   * They are being used for {@link LobbyItem}s (items that are being placed in the hotbar).
   *
   * @return All registered item handlers
   */
  Collection<LobbyItemHandler> getLobbyItemHandlers();

  /**
   * Returns a registered item handler whos id is equal to the passed one.
   * When there's none the method return <code>null</code>.
   *
   * @param id The id that shall match
   * @return The handler whos id is equal. <code>null</code> when there's none
   */
  @Nullable LobbyItemHandler getLobbyItemHandler(String id);

  /**
   * Registers a handler for {@link LobbyItem}s.
   * <p>
   * The items are will automatically use it if their set handler id is equal to the given one,
   * so it's not needed to use {@link LobbyItem#setHandler(LobbyItemHandler)}.
   * <p>
   * Might fail (returns <code>false</code>) if the id is already taken
   *
   * @param handler The handler that shall be registered
   * @return <code>true</code> if it was successful
   * @throws IllegalStateException when there's something wrong with the properties of the handler
   */
  boolean registerLobbyItemHandler(LobbyItemHandler handler);

  /**
   * Unregisters a previously registered handler for {@link LobbyItem}s.
   * <p>
   * Might fail when it's not registered.
   *
   * @param handler The handler that shall be unregistered
   * @return <code>true</code> when it was successful
   */
  boolean unregisterLobbyItemHandler(LobbyItemHandler handler);

  /**
   * Forcefully refresh the hotbar of a player during lobby phase.
   * <p>
   *   This may be useful if e.g. the icon changes.
   *   Note that there is no need to call this to update visibilities, as these get checked frequently.
   *   Refreshes only occur periodically and not immediately.
   *   May fail if the player isn't in a lobby.
   * </p>
   *
   * @param player The player for whom we want to do the refresh
   * @return <code>true</code> if the player has been enqueued for a refresh
   */
  boolean forceLobbyHotbarRefresh(Player player);

  /**
   * Returns all currently existing drop type.
   *
   * @return All currently existing drop types
   */
  Collection<DropType> getDropTypes();

  /**
   * Tries to find a DropType who could've dropped the item <code>is</code>
   *
   * @param is The item that could've been dropped by a spawner
   * @return The DropType that fits to the <code>is</code>. <code>null</code> when there's none
   */
  @Nullable DropType getDropTypeByDrop(ItemStack is);

  /**
   * Tries to find a DropType given by its id.
   *
   * @param id The id of the drop type
   * @return The DropType that fits to the <code>id</code>. <code>null</code> when there's none
   */
  @Nullable DropType getDropTypeById(String id);

  /**
   * Registers the custom spawner handler.
   * Spawners will automatically adapt to it.
   *
   * @param handler The spawner handler
   * @return <code>false</code> if it failed because there's already a handler with the same id. Otherwise <code>true</code>
   * @throws IllegalStateException When there's something wrong with the properties of the handler
   */
  boolean registerCustomSpawnerHandler(CustomDropTypeHandler handler);

  /**
   * Unregisters the custom spawner handler.
   * Spawners will automatically detach from it.
   *
   * @param handler The spawner handler
   * @return <code>true</code> if its existence has been proved
   */
  boolean unregisterCustomSpawnerHandler(CustomDropTypeHandler handler);

  /**
   * Returns all registered custom spawner handlers.
   *
   * @return All currently registered custom spawner handlers
   */
  Collection<CustomDropTypeHandler> getCustomSpawnerHandlers();

  /**
   * Returns all players who are currently spectating.
   *
   * @return Every spectating player
   */
  Collection<Player> getSpectatingPlayers();

  /**
   * Returns the spectator data of every player who is currently spectating.
   *
   * @return Spectator instances of any player who's currently spectating
   */
  Collection<Spectator> getSpectators();

  /**
   * Get whether the player is currently spectating.
   *
   * @param player The player who might be spectating
   * @return <code>true</code> if the player is currently spectating
   */
  boolean isSpectator(Player player);

  /**
   * Tries to find the corresponding Spectator instance given by its player.
   * May return <code>null</code> when the player isn't actually spectating anything.
   *
   * @param player The player who might be spectating
   * @return His Spectator instance
   */
  @Nullable Spectator getSpectatorByPlayer(Player player);

  /**
   * Returns a hotbar slot given by its slot.
   * Might be <code>null</code> if there's no item at the given slot.
   *
   * @param slot The slot at which it's located at (0-8)
   * @return The item located at the slot, <code>null</code> if there's none
   * @throws IndexOutOfBoundsException When slot is less than 0 or greater than 8
   */
  @Nullable SpectatorItem getSpectatorItem(int slot);

  /**
   * Returns all registered handlers.
   * Also includes the default ones.
   * <p>
   * They are being used for {@link SpectatorItem}s (items that are being placed in the hotbar).
   *
   * @return All registered item handlers
   */
  Collection<SpectatorItemHandler> getSpectatorItemHandlers();

  /**
   * Returns a registered item handler whos id is equal to the passed one.
   * When there's none the method return <code>null</code>.
   *
   * @param id The id that shall match
   * @return The handler whos id is equal. <code>null</code> when there's none
   */
  @Nullable SpectatorItemHandler getSpectatorItemHandler(String id);

  /**
   * Registers a handler for {@link SpectatorItem}s.
   * <p>
   * The items are will automatically use it if their set handler id is equal to the given one,
   * so it's not needed to use {@link SpectatorItem#setHandler(SpectatorItemHandler)}.
   * <p>
   * Might fail (returns <code>false</code>) if the id is already taken
   *
   * @param handler The handler that shall be registered
   * @return <code>true</code> if it was successful
   * @throws IllegalStateException when there's something wrong with the properties of the handler
   */
  boolean registerSpectatorItemHandler(SpectatorItemHandler handler);

  /**
   * Unregisters a previously registered handler for {@link SpectatorItem}s.
   * <p>
   * Might fail when it's not registered.
   *
   * @param handler The handler that shall be unregistered
   * @return <code>true</code> when it was successful
   */
  boolean unregisterSpectatorItemHandler(SpectatorItemHandler handler);

  /**
   * Returns all existing pages inside the shop
   *
   * @return All ShopPages
   */
  Collection<ShopPage> getShopPages();

  /**
   * Returns all existing items inside the shop
   *
   * @return All ShopItems
   * @see #getShopPages()
   */
  Collection<ShopItem> getShopItems();

  /**
   * Tries to locate and return a {@link ShopItem} by its id.
   *
   * @param id The id of the item that matches with {@link ShopItem#getId()},
   * @return The item whose id is equal. <code>null</code> if there's none
   */
  @Nullable
  ShopItem getShopItemById(String id);

  /**
   * Returns all existing buy groups
   *
   * @return All BuyGroups
   */
  Collection<BuyGroup> getBuyGroups();

  /**
   * Tries to locate and return a {@link BuyGroup} by its name
   *
   * @param name The name of the BuyGroup
   * @return The BuyGroup whose name is equal. <code>null</code> if there's none
   */
  @Nullable BuyGroup getBuyGroup(String name);

  /**
   * Returns all existing layouts for the shop
   *
   * @return All existing ShopLayouts
   */
  Collection<ShopLayout> getShopLayouts();

  /**
   * Tries to look up for a registered layout that has the given name.
   *
   * @param layoutName The name of the layout
   * @return <code>null</code> if none has been found or the layout that has been found
   */
  @Nullable ShopLayout getShopLayout(String layoutName);

  /**
   * Create and register your own custom ShopLayout using this method.
   * Have a class that inherits from {@link ShopLayout} and pass it to this method.
   * Users are able to use it by setting the name of your custom layout in their shop config.
   *
   * @param layout The new shop config
   * @return Returns <code>false</code> if there's already a layout under the given name. Otherwise <code>true</code>
   */
  boolean registerShopLayout(ShopLayout layout);

  /**
   * Unregister an already existing ShopLayout using this method.
   * It's not possible to unregister default types.
   *
   * @param layout The layout that shall be removed
   * @return Returns <code>ture</code> if it got successfully removed
   */
  boolean unregisterShopLayout(ShopLayout layout);

  /**
   * Returns the default {@link ShopLayout} that's being used whenever the player clicks on the dealer
   *
   * @return The default ShopLayout
   */
  ShopLayout getDefaultShopLayout();

  /**
   * Returns all existing layouts for the upgrade shop
   *
   * @return All existing UpgradeShopLayouts
   */
  Collection<UpgradeShopLayout> getUpgradeShopLayouts();

  /**
   * Tries to look up for a registered layout that has the given name.
   *
   * @param layoutName The name of the layout
   * @return <code>null</code> if none has been found or the layout that has been found
   */
  @Nullable UpgradeShopLayout getUpgradeShopLayout(String layoutName);

  /**
   * Create and register your own custom UpgradeShopLayout using this method.
   * Have a class that inherits from {@link UpgradeShopLayout} and pass it to this method.
   * Users are able to use it by setting the name of your custom layout in their upgrade shop config.
   *
   * @param layout The new upgrade shop config
   * @return Returns <code>false</code> if there's already a layout under the given name. Otherwise <code>true</code>
   */
  boolean registerUpgradeShopLayout(UpgradeShopLayout layout);

  /**
   * Unregister an already existing UpgradeShopLayout using this method.
   * It's not possible to unregister default types.
   *
   * @param layout The layout that shall be removed
   * @return Returns <code>ture</code> if it got successfully removed
   */
  boolean unregisterUpgradeShopLayout(UpgradeShopLayout layout);

  /**
   * Returns the default {@link UpgradeShopLayout} that's being used whenever the player clicks on the upgrade dealer
   *
   * @return The default ShopLayout
   */
  UpgradeShopLayout getDefaultUpgradeShopLayout();

  /**
   * Opens the AchievementsGUI for a player
   * <p>
   * The player doesn't have to be in an arena
   *
   * @param player The player for whom the AchievementsGUI shall open
   */
  void openAchievementsGUI(Player player);

  /**
   * Opens the shop for a player.
   * <p>
   * The player doesn't have to be in an arena for debug purposes.
   * It's not recommended to misuse this as it's likely that errors will occur.
   *
   * @param player The player for whom the shop shall open
   * @param layout The layout that shall be shown to the player
   * @param cause The cause for this event. Recommended to use {@link ShopOpenCause#PLUGIN}
   * @param shopPage The page the shop will open on (If null, the default first page will be used)
   */

  void openShop(Player player, ShopLayout layout, ShopOpenCause cause, @Nullable ShopPage shopPage);

  /**
   * Opens the shop for a player.
   * <p>
   * The player doesn't have to be in an arena for debug purposes.
   * It's not recommended to misuse this as it's likely that errors will occur.
   *
   * @param player The player for whom the shop shall open
   * @param layout The layout that shall be shown to the player
   * @param cause The cause for this event. Recommended to use {@link ShopOpenCause#PLUGIN}
   */
  default void openShop(Player player, ShopLayout layout, ShopOpenCause cause) {
    openShop(player, layout, cause, null);
  }

  /**
   * Opens the shop for a player.
   * <p>
   * The player doesn't have to be in an arena for debug purposes.
   * It's not recommended to misuse this as it's likely that errors will occur.
   *
   * @param player The player for whom the shop shall open
   * @param layout The layout that shall be shown to the player
   */
  default void openShop(Player player, ShopLayout layout) {
    openShop(player, layout, ShopOpenCause.PLUGIN, null);
  }

  /**
   * Opens the shop for a player.
   * <p>
   * The player doesn't have to be in an arena for debug purposes.
   * It's not recommended to misuse this as it's likely that errors will occur.
   *
   * @param player The player for whom the shop shall open
   * @param cause The cause for this event. Recommended to use {@link ShopOpenCause#PLUGIN}
   */
  default void openShop(Player player, ShopOpenCause cause) {
    openShop(player, getDefaultShopLayout(), cause, null);
  }

  /**
   * Opens the shop for a player.
   * <p>
   * The player doesn't have to be in an arena for debug purposes.
   * It's not recommended to misuse this as it's likely that errors will occur.
   *
   * @param player The player for whom the shop shall open
   */
  default void openShop(Player player) {
    openShop(player, getDefaultShopLayout(), ShopOpenCause.PLUGIN, null);
  }

  /**
   * Opens the upgrade shop for a player.
   * <p>
   * The upgrade shop will fail to open if the player is not inside an arena,
   * or the arena is not running, or if the player does not have a team
   *
   * @param player The player for whom the upgrade shop shall open
   * @return whether or not the upgrade shop could be opened successfully
   */
  boolean openUpgradeShop(Player player);

  /**
   * Opens the upgrade shop for a player.
   * <p>
   * The upgrade shop will fail to open if the arena is not running
   *
   * @param player The player for whom the upgrade shop shall open
   * @param arena What arena the upgrade shop is being open for
   * @param team The team the player is trying to buy an upgrade for
   * @param cause Why the upgrade shop is being opened
   * @return whether or not the upgrade shop could be opened successfully
   */
  boolean openUpgradeShop(Player player, Arena arena, Team team, UpgradeShopOpenCause cause);

  /**
   * Returns all registered (including the default ones) SpecialItems.
   * <p>
   * It's not safe to modify the resulting Collection.
   * You may want to use {@link #registerSpecialItem(String, Plugin, String, ItemStack)} or
   * {@link #unregisterSpecialItem(SpecialItem)} instead
   *
   * @return All existing {@link SpecialItem}s
   */
  Collection<SpecialItem> getSpecialItems();

  /**
   * Tries to locate and return a {@link SpecialItem} given by its id.
   *
   * @param id The id that shall match to the one of the item
   * @return The item whose id is equal. <code>null</code> when there's none
   */
  @Nullable SpecialItem getSpecialItem(String id);

  /**
   * Tries to locate and return a {@link SpecialItem} given by its usable item.
   *
   * @param itemStack The item that we want to check
   * @return The {@link SpecialItem} that'll be executed on use
   */
  @Nullable SpecialItem getSpecialItem(ItemStack itemStack);

  /**
   * Registers a custom {@link SpecialItem} for the later ingame use.
   * <p>
   * Might fail when the id is already taken.
   *
   * @param id The id that the item shall receive
   * @param plugin The plugin that's creating the SpecialItem
   * @param name The name of the SpecialItem (that also will be displayed ingame, supports message codes)
   * @param item The ItemStack which players will use to execute the item
   * @return The newly initiated {@link SpecialItem} instance. <code>null</code> when it failed because the id is already taken
   * @throws IllegalStateException when there's something wrong with the properties of the handler
   */
  @Nullable SpecialItem registerSpecialItem(String id, Plugin plugin, String name, ItemStack item);

  /**
   * Reregisters a SpecialItem that previously has been unregistered.
   * <p>
   * Might fail when it's already registered or the id is already taken.
   *
   * @param specialItem The item that shall be reregistered
   * @return <code>true</code> if it was successful
   * @throws IllegalStateException when there's something wrong with the properties of the handler
   */
  boolean registerSpecialItem(SpecialItem specialItem);

  /**
   * Tries to unregister an existing SpecialItem.
   * This causes the item not to be useable and configurable anymore.
   *
   * @param specialItem The item that shall get removed
   * @return <code>false</code> when the SpecialItem already has been unregistered
   */
  boolean unregisterSpecialItem(SpecialItem specialItem);

  /**
   * Simulates the player using the special item.
   * <p>
   *   May fail if it has been cancelled using {@link de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent#setCancelled(boolean)}.
   * </p>
   *
   * @param item The item that's being used
   * @param player The player that uses the item
   * @param arena The arena in which the player is using the item
   * @param is The item that's being used (the one that he is holding in his hand)
   * @throws IllegalStateException If the item isn't registered
   */
  void useSpecialItem(SpecialItem item, Player player, Arena arena, ItemStack is);

  /**
   * Returns all registered (including the default ones) UpgradeTriggerHandlers.
   * <p>
   * It's not safe to modify the resulting Collection.
   * You may want to use {@link #registerUpgradeTriggerHandler(UpgradeTriggerHandler)}
   *
   * @return All existing {@link UpgradeTriggerHandler}s
   */
  Collection<UpgradeTriggerHandler> getUpgradeTriggerHandlers();

  /**
   * Tries to locate and return a {@link UpgradeTriggerHandler} given by its id.
   *
   * @param id The id that shall match to the one of the handler
   * @return The handler whose id is equal. <code>null</code> when there's none
   */
  @Nullable UpgradeTriggerHandler getUpgradeTriggerHandler(String id);

  /**
   * Returns all the upgrades that are currently loaded.
   *
   * @return all loaded upgrades
   */
  Collection<Upgrade> getUpgrades();

  /**
   * Registers a custom {@link UpgradeTriggerHandler} for use in the upgradeshop.
   * <p>
   * Might fail when the handlers id is already taken.
   *
   * @param handler the handler that is being registered
   * @return The newly initiated {@link SpecialItem} instance. <code>null</code> when it failed because the id is already taken
   */
  boolean registerUpgradeTriggerHandler(UpgradeTriggerHandler handler);

  /**
   * Tries to unregister an existing Upgrade.
   * This causes the item not to be useable and configurable anymore.
   *
   * @param handler item that shall get removed
   * @return <code>false</code> when the Upgrade already has been unregistered
   */
  boolean unregisterUpgradeTriggerHandler(UpgradeTriggerHandler handler);

  /**
   * Plugin might mark blocks as player placed depending on their configuration.
   * This allows to e.g. make players only to be able to break blocks they've placed during the match.
   * <p>
   * This method {@link Arena#setBlockPlayerPlaced(org.bukkit.block.Block, boolean)}
   * and {@link Arena#isBlockPlayerPlaced(org.bukkit.block.Block)} to check whether or not this is even possible.
   *
   * @return <code>true</code> if the plugin supports player marking. <code>false</code> if it has been disabled by the user
   */
  boolean isPlayerBlockMarkingSupported();

  /**
   * Returns the default handler for scoreboards that's being provided by the plugin.
   * <br>
   * It's not a must that this handler is actually being used.
   * It's possible that an other plugin might overwrite it using {@link #setScoreboardHandler(ScoreboardHandler)}.
   * Use {@link #getScoreboardHandler()} to obtain the currently used handler.
   *
   * @return The default handler for scoreboards
   */
  ScoreboardHandler getDefaultScoreboardHandler();

  /**
   * Returns the current handler for scoreboards.
   * <p>
   * It's possible for plugins to set a new handler using {@link #setScoreboardHandler(ScoreboardHandler)}.
   *
   * @return The currently used handler for scoreboards
   */
  ScoreboardHandler getScoreboardHandler();

  /**
   * Set the new handler for scoreboards.
   * <p>
   * The plugin will automatically replace it with the default one when the plugin unloads.
   * Additionally it'll automatically add the existing players from the previous one to the new one.
   * <p>
   * Use {@link #getDefaultScoreboardHandler()} to change it back to default.
   *
   * @param handler The new handler for scoreboards
   */
  void setScoreboardHandler(ScoreboardHandler handler);

  /**
   * Tries to make the player rejoin the arena he previously was playing in.
   * <p>
   *     The task is being processed async, hence the callback.
   * </p>
   *
   * @param player The player who shall rejoin a match
   * @param callback The result of all of this
   */
  void rejoinPlayer(Player player, @Nullable Consumer<Optional<RejoinPlayerIssue>> callback);

  /**
   * Sends the player to the hub.
   * <p>
   *     Where exactly the player is being teleported to depends on the configuration of the server. It could include one of these options:<br>
   *     - To the hub-position that has been specified using /bw sethubpos<br>
   *     - Being kicked out of the server<br>
   *     - Being sent to the hub server<br>
   * </p>
   *
   * @param player The player that shall be moved
   * @see #sendToHub(Player, boolean)
   */
  default void sendToHub(Player player) {
    sendToHub(player, true);
  }

  /**
   * Sends the player to the hub.
   * <p>
   *     Where exactly the player is being teleported to depends on the configuration of the server. It could include one of these options:<br>
   *     - To the hub-position that has been specified using /bw sethubpos<br>
   *     - Being kicked out of the server (only when <code>permitKicking</code> is set to true)<br>
   *     - Being sent to the hub server (only when <code>permitKicking</code> is set to true)<br>
   * </p>
   *
   * @param player The player that shall be moved
   * @param permitKicking Whether the player may only get teleported on the same server. When set to <code>false</code>, the player may never leave the server
   * @see #sendToHub(Player)
   */
  void sendToHub(Player player, boolean permitKicking);

  /**
   * Get the UUID of the player who owns the given companion.
   * <p>
   *   A companion may e.g. be an iron golem, a guard dog, a fireball etc.
   * </p>
   *
   * @param entity The companion
   * @return The UUID of the player {@link Player#getUniqueId()} who spawned the companion. May be <code>null</code> for none
   * @see #setCompanionOwner(Entity, UUID)
   * @see #setCompanionOwner(Entity, OfflinePlayer)
   * @throws IllegalArgumentException If <code>entity</code> is an invalid entity type (e.g. if it's a player)
   */
  @Nullable
  UUID getCompanionOwner(Entity entity);

  /**
   * Set the owner of a companion.
   * <p>
   *   A companion may e.g. be an iron golem, a guard dog, a fireball etc.
   * </p>
   *
   * @param entity The companion
   * @param owner The UUID of the player {@link Player#getUniqueId()} who spawned the companion. May be <code>null</code> for none
   * @see #getCompanionOwner(Entity)
   * @see #setCompanionOwner(Entity, OfflinePlayer)
   * @throws IllegalArgumentException If <code>entity</code> is an invalid entity type (e.g. if it's a player)
   */
  void setCompanionOwner(Entity entity, @Nullable UUID owner);

  /**
   * Set the owner of a companion.
   * <p>
   *   A companion may e.g. be an iron golem, a guard dog, a fireball etc.
   * </p>
   *
   * @param entity The companion
   * @param owner The player who spawned the companion. May be <code>null</code> for none
   * @see #getCompanionOwner(Entity)
   * @throws IllegalArgumentException If <code>entity</code> is an invalid entity type (e.g. if it's a player)
   */
  default void setCompanionOwner(Entity entity, @Nullable OfflinePlayer owner) {
    setCompanionOwner(entity, owner != null ? owner.getUniqueId() : null);
  }

  /**
   * Returns the {@link ArenaCloningManager} whose purpose it is to automatically clone new arenas.
   *
   * @return The global ArenaCloningManager instance
   */
  ArenaCloningManager getCloningManager();

  /**
   * Draw a cube border once.
   * <p>
   *   This method uses the exact same logic as for the region arena border.
   *   It may be expensive to process, thus it's recommended to call it async.
   * </p>
   *
   * @param min Coordinates that are less than the <code>max</code> parameter
   * @param max Coordinates that are greater than the <code>min</code> parameter
   * @param player For whom the border shall be drawn for
   */
  void drawBorder(XYZ min, XYZ max, Player player);

  /**
   * Get the info of the last directly/indirectly player-caused damage during a match.
   * <p>
   *   This info is used to obtain the killer when the damaged player dies.
   * </p>
   *
   * @param damaged The player who got damaged
   * @return The related info object. May be <code>null</code> if nobody damaged him yet
   */
  @Nullable
  PlayerDamageInfo getLastPlayerCausedDamage(Player damaged);

  /**
   * Returns the global GameAPI instance.
   *
   * @return The global GameAPI instance
   */
  static GameAPI get() {
    return BedwarsAPILayer.INSTANCE.getGameAPI();
  }
}