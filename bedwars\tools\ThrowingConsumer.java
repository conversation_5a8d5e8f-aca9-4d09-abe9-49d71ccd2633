package de.marcely.bedwars.tools;

import java.util.Objects;

/**
 * Based on Java's Consumer with the difference that this class may permit the throwing of an Exception.
 *
 * @param <T> The type of the input to the operation
 * @see java.util.function.Consumer
 */
@FunctionalInterface
public interface ThrowingConsumer<T> {

  /**
   * Performs this operation on the given argument.
   *
   * @param t the input argument
   * @throws Exception in case an exception occurs while it is being run
   */
  void accept(T t) throws Exception;

  /**
   * Returns a composed {@code ThrowingConsumer} that performs, in sequence, this
   * operation followed by the {@code after} operation. If performing either
   * operation throws an exception, it is relayed to the caller of the
   * composed operation.  If performing this operation throws an exception,
   * the {@code after} operation will not be performed.
   *
   * @param after the operation to perform after this operation
   * @return a composed {@code Consumer} that performs in sequence this
   * operation followed by the {@code after} operation
   * @throws NullPointerException if {@code after} is null
   */
  default ThrowingConsumer<T> andThen(ThrowingConsumer<? super T> after) {
    Objects.requireNonNull(after);

    return (T t) -> {
      accept(t);
      after.accept(t);
    };
  }
}
