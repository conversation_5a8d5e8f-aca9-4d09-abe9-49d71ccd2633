package de.marcely.bedwars.api.game.scoreboard;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.player.PlayerStats;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.util.Collection;

/**
 * Manages the creation and termination of a scoreboard and what's getting displayed on the scoreboard
 */
public abstract class ScoreboardHandler {

  /**
   * Returns the plugin that initiated this instance
   *
   * @return The plugin backed by this handler
   */
  public abstract Plugin getPlugin();

  /**
   * Displays the scoreboard to the player.
   * <p>
   * Use {@link Arena#updateScoreboard()} to automatically do that for all players in an arena.
   *
   * @param player The target to whom the scoreboard shall be shown to
   * @param arena The arena in which the player is probably in. Will display the infos from that arena
   * @param type For what purpose the scorebord is getting displayed
   * @return <code>false</code> if it failed or the scoreboard is disabled for the arena's current state
   * @throws Exception MBedwars will handle occurring errors
   */
  protected abstract boolean display0(Player player, Arena arena, ScoreboardType type) throws Exception;

  /**
   * Hides the scoreboard from a player who's seeing it
   *
   * @param player The player from whom it shall be hidden
   * @return <code>false</code> if the player is already not seeing it. Otherwise <code>true</code>
   * @throws Exception MBedwars will handle occurring errors
   */
  protected abstract boolean hide0(Player player) throws Exception;

  /**
   * Updates a specific part of the scoreboard
   *
   * @param cause What part shall be updated
   * @param arena The arena involved in the update
   * @param data Data that comes with it
   * @throws Exception MBedwars will handle occurring errors
   */
  protected abstract void update0(ScoreboardUpdateCause cause, Arena arena, Object data) throws Exception;

  /**
   * Returns whether or not the scoreboard is active for a given player.
   * This does not mean that it's actually visible for him.
   * It only means that the handler is processing the given player.
   * Possibly an other plugin is overwritting the scoreboard or the handler just hasn't staerted yet.
   *
   * @param player The player for who the scoreboard is possibly active
   * @return <code>true</code> when this handler is processing the given player
   */
  public abstract boolean isActive(Player player);

  /**
   * Similar to {@link #isActive(Player)}, but returns all players to whom this applies to.
   *
   * @return All players that get processed by this handler
   */
  public abstract Collection<Player> getActivePlayers();

  /**
   * Similar to {@link #hide(Player)}, but does that for all players (including spectators) inside an arena
   *
   * @param arena The arena in which the players aren in
   */
  public void hideAll(Arena arena) {
    arena.getPlayers().forEach(this::hide);
    arena.getSpectators().forEach(this::hide);
  }

  /**
   * Similar to {@link #hide(Player)}, but does that for all active players
   */
  public void hideAll() {
    getActivePlayers().forEach(this::hide);
  }

  /**
   * Displays the scoreboard to the player.
   * <p>
   * Use {@link Arena#updateScoreboard()} to automatically do that for all players in an arena.
   *
   * @param player The target to whom the scoreboard shall be shown to
   * @param arena The arena in which the player is probably in. Will display the infos from that arena
   * @param type For what purpose the scorebord is getting displayed
   * @return <code>false</code> if it failed or the scoreboard is disabled for the arena's current state
   */
  public final boolean display(Player player, Arena arena, ScoreboardType type) {
    Validate.notNull(player, "player");
    Validate.notNull(arena, "arena");
    Validate.notNull(type, "type");

    try {
      return display0(player, arena, type);
    } catch (Throwable e) {
      e.printStackTrace();
    }

    return false;
  }

  /**
   * Hides the scoreboard from a player who's seeing it
   *
   * @param player The player from whom it shall be hidden
   * @return <code>false</code> if the player is already not seeing it. Otherwise <code>true</code>
   */
  public final boolean hide(Player player) {
    Validate.notNull(player, "player");

    try {
      return hide0(player);
    } catch (Throwable e) {
      e.printStackTrace();
    }

    return false;
  }

  /**
   * Updates a specific part of the scoreboard
   *
   * @param cause What part shall be updated
   * @param arena The arena involved in the update
   * @param data Data that comes with it
   */
  public final void update(ScoreboardUpdateCause cause, Arena arena, Object data) {
    Validate.notNull(cause, "cause");
    Validate.notNull(arena, "arena");

    // check whether or not it's valid
    switch (cause) {
      case COMPLETE_REFRESH:
      case TICK:
      case PAPI_PLACEHOLDERS:
        Validate.isTrue(data == null, "data must be null");
        break;

      case PLAYER_ADD:
      case PLAYER_REMOVE:
      case PLAYER_TEAM_CHANGE:
        Validate.isTrue(data != null && data instanceof Player, "data must be of type Player");
        break;

      case PLAYER_STATS_UPDATE:
        Validate.isTrue(data != null && data instanceof PlayerStats, "data must be of type PlayerStats");
        Validate.isTrue(!((PlayerStats) data).isGameStats(), "data may not be the game stats");
        break;

      case BED_DESTROY:
      case BED_REVIVE:
        Validate.isTrue(data != null && data instanceof Team, "data must be of type Team");
        break;
    }

    // run update
    try {
      update0(cause, arena, data);
    } catch (Throwable e) {
      e.printStackTrace();
    }
  }

  /**
   * Returns if it's the ScoreboardHandler provided by MBedwars
   *
   * @return <code>true</code> if it's the ScoreboardHandler provided by MBedwars
   */
  public final boolean isDefault() {
    return BedwarsAPI.getGameAPI().getDefaultScoreboardHandler() == this;
  }
}