package de.marcely.bedwars.tools.location;

import org.bukkit.Location;
import org.bukkit.util.Vector;

/**
 * Extends {@link XYZYP} and forbids any modifications done to it
 */
public class ImmutableXYZYP extends XYZYP {

  public ImmutableXYZYP(Location loc) {
    super(loc);
  }

  public ImmutableXYZYP(Vector vec) {
    super(vec);
  }

  public ImmutableXYZYP(XYZ xyz) {
    super(xyz);
  }

  public ImmutableXYZYP(XYZYP xyz) {
    super(xyz);
  }

  public ImmutableXYZYP() {
    super();
  }

  public ImmutableXYZYP(double x, double y, double z) {
    super(x, y, z);
  }

  public ImmutableXYZYP(double x, double y, double z, float yaw, float pitch) {
    super(x, y, z, yaw, pitch);
  }

  @Override
  public XYZYP setX(double x) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP setY(double y) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP setZ(double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP set(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP set(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP set(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP add(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP add(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP add(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP add(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP subtract(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP subtract(XYZ xyz) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP subtract(Location loc) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP subtract(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP multiply(double amount) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP multiply(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP zero() {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP setYaw(float yaw) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public XYZYP setPitch(float pitch) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }
}
