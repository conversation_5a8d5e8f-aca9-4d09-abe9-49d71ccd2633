package de.marcely.bedwars.tools;

/**
 * Based on Java's Supplier with the difference that this class may permit the throwing of an Exception.
 *
 * @param <T> The type of the input to the operation
 * @see java.util.function.Supplier
 */
@FunctionalInterface
public interface ThrowingSupplier<T> {

  /**
   * Gets a result.
   *
   * @return a result
   * @throws Exception in case an exception occurs while it is being run
   */
  T get() throws Exception;
}
