package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.upgrade.QueuedTrap;
import de.marcely.bedwars.api.game.upgrade.UpgradeState;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a player attempts to remove a trap.
 */
public class PlayerRemoveTrapEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final UpgradeState upgradeState;
  private final QueuedTrap trap;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerRemoveTrapEvent(Player player, Arena arena, UpgradeState upgradeState, QueuedTrap trap) {
    super(player);

    this.arena = arena;
    this.upgradeState = upgradeState;
    this.trap = trap;
  }

  /**
   * Returns the upgrade state of the player's team.
   *
   * @return The upgrade state
   */
  public UpgradeState getUpgradeState() {
    return this.upgradeState;
  }

  /**
   * Returns the trap that the player wants to remove.
   *
   * @return The trap
   */
  public QueuedTrap getTrap() {
    return this.trap;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
