package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.game.shop.ShopPage;
import de.marcely.bedwars.api.game.shop.layout.ShopLayout;
import de.marcely.bedwars.tools.gui.GUI;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called during the post processing of a shop GUI
 */
public class ShopGUIPostProcessEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Player player;
  private final ShopLayout design;
  private final ShopPage page;

  private GUI gui;

  public ShopGUIPostProcessEvent(Player player, ShopLayout design, ShopPage page, @Nullable GUI gui) {
    this.player = player;
    this.design = design;
    this.page = page;
    this.gui = gui;
  }

  /**
   * Returns the player that opened the shop
   *
   * @return The player involved in this
   */
  public Player getPlayer() {
    return this.player;
  }

  /**
   * Returns the layout that was used for the shop GUI
   *
   * @return The used layout
   */
  public ShopLayout getLayout() {
    return this.design;
  }

  /**
   * Returns the page that was used to build the shop GUI
   *
   * @return The page opened
   */
  @Nullable
  public ShopPage getPage() {
    return this.page;
  }

  /**
   * Returns the GUI that'll be shown to the player.
   * <p>
   * Can be <code>null</code> whereby the GUI won't be shown
   *
   * @return The gui that will be displayed
   */
  public @Nullable GUI getGUI() {
    return this.gui;
  }

  /**
   * Set the GUI that shall be shown to the player.
   * <p>
   * Won't open any gui when passing <code>null</code>
   *
   * @param gui The new gui
   */
  public void setGUI(@Nullable GUI gui) {
    this.gui = gui;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}