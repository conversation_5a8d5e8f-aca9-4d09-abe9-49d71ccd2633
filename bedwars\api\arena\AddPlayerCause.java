package de.marcely.bedwars.api.arena;

/**
 * The reason why a player joined an arena.
 */
public enum AddPlayerCause {

  /**
   * He used a command (/mbedwars join).
   */
  COMMAND,

  /**
   * He clicked on a sign (/mbedwars spawn joinarenasign).
   */
  SIGN,

  /**
   * He used the setup gui (opened with /mbedwars arena setupgui)
   */
  SETUP_GUI,

  /**
   * He used an arenas gui (opened with /mbedwars arenasgui)
   */
  ARENAS_GUI,

  /**
   * auto-join feature caused him to join the arena as he entered the server.
   */
  AUTO_JOIN,

  /**
   * Used the {@link de.marcely.bedwars.api.game.spectator.SpectatorItemHandlerType#NEXT_ROUND} item.
   */
  SPECTATOR_FIND_NEXT_ROUND,

  /**
   * Voting has ended, and he now joined the arena that won.
   */
  VOTING_SWITCH_ARENA,

  /**
   * He got pulled into the arena by his party as his leader wants to join the arena.
   */
  PARTY_SWITCH_ARENA,

  /**
   * The match ended and players that died during the match - thus become spectators - get re-added to the end lobby.
   * <p>
   *     The status of the arena must match {@link ArenaStatus#END_LOBBY}.
   * </p>
   *
   * @deprecated Broken. Using rejoin system instead, see {@link de.marcely.bedwars.api.arena.RejoinPlayerCause#END_LOBBY}
   */
  @Deprecated
  END_LOBBY,

  /**
   * A plugin made him enter it using the API.
   */
  PLUGIN
}
