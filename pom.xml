<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 项目信息 -->
    <groupId>cn.acebrand</groupId>
    <artifactId>AceVoteMode</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>AceVoteMode</name>
    <description>A voting system addon for Bedwars with custom game modes</description>
    <url>https://acebrand.cn</url>

    <!-- 项目属性 -->
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <minecraft.version>1.20.1</minecraft.version>
        <spigot.version>1.20.1-R0.1-SNAPSHOT</spigot.version>
        <java.home>C:\Program Files\Java\jdk-21</java.home>
    </properties>

    <!-- 仓库配置 -->
    <repositories>
        <!-- Spigot 仓库 -->
        <repository>
            <id>spigot-repo</id>
            <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
        </repository>
        
        <!-- 本地依赖仓库 -->
        <repository>
            <id>local-libs</id>
            <url>file://${project.basedir}/libs</url>
        </repository>

        <!-- PlaceholderAPI 仓库 -->
        <repository>
            <id>placeholderapi</id>
            <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
        </repository>


    </repositories>

    <!-- 依赖配置 -->
    <dependencies>
        <!-- Spigot API -->
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>${spigot.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- MBedwars API (本地依赖) -->
        <dependency>
            <groupId>de.marcely.bedwars</groupId>
            <artifactId>mbedwars-api</artifactId>
            <version>5.5.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/libs/MBedwars-5.5.3.jar</systemPath>
        </dependency>

        <!-- JetBrains Annotations -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>24.0.1</version>
            <scope>provided</scope>
        </dependency>

        <!-- PlaceholderAPI -->
        <dependency>
            <groupId>me.clip</groupId>
            <artifactId>placeholderapi</artifactId>
            <version>2.11.6</version>
            <scope>provided</scope>
        </dependency>

        <!-- MythicMobs API - 本地依赖 -->
        <dependency>
            <groupId>io.lumine</groupId>
            <artifactId>MythicMobs</artifactId>
            <version>5.7.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/libs/MythicMobs-5.7.2.jar</systemPath>
        </dependency>
    </dependencies>

    <!-- 构建配置 -->
    <build>
        <!-- 默认目标 -->
        <defaultGoal>clean package</defaultGoal>
        
        <!-- 源码目录 -->
        <sourceDirectory>src/main/java</sourceDirectory>
        
        <!-- 资源目录 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.txt</include>
                    <include>**/*.md</include>
                </includes>
            </resource>
        </resources>

        <!-- 插件配置 -->
        <plugins>
            <!-- Maven 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <encoding>UTF-8</encoding>
                    <executable>${java.home}\bin\javac</executable>
                    <fork>true</fork>
                </configuration>
            </plugin>

            <!-- Maven 资源插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- Maven Shade 插件 (用于打包依赖) -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <!-- 输出文件名 -->
                            <finalName>AceVoteMode-${project.version}</finalName>
                            
                            <!-- 最小化JAR -->
                            <minimizeJar>false</minimizeJar>
                            
                            <!-- 过滤器 -->
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>META-INF/MANIFEST.MF</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            
                            <!-- 重定位包 (避免冲突) -->
                            <relocations>
                                <!-- 如果有需要重定位的包，在这里添加 -->
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven JAR 插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                        <manifestEntries>
                            <Built-By>AceBrand</Built-By>
                            <Implementation-Title>${project.name}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Implementation-Vendor>AceBrand</Implementation-Vendor>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <!-- Maven Clean 插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.3.1</version>
            </plugin>

            <!-- Maven Install 插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>

            <!-- Maven Deploy 插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>

            <!-- Maven Surefire 插件 (测试) -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 开发者信息 -->
    <developers>
        <developer>
            <id>acebrand</id>
            <name>AceBrand</name>
            <email><EMAIL></email>
            <url>https://acebrand.cn</url>
            <roles>
                <role>developer</role>
            </roles>
        </developer>
    </developers>

    <!-- 许可证信息 -->
    <licenses>
        <license>
            <name>MIT License</name>
            <url>https://opensource.org/licenses/MIT</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
</project>
