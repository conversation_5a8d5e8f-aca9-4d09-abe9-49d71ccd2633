package de.marcely.bedwars.api.game.lobby;

import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

/**
 * Handles the execution of a {@link LobbyItem}.
 * <P>
 * Make sure to register it with {@link GameAPI#registerLobbyItemHandler(LobbyItemHandler)}.
 */
public abstract class LobbyItemHandler {

  private final String id;
  private final Plugin plugin;

  /**
   * @param id The id of this handler, should be unique to others. Default ones start with bedwars:
   * @param plugin The plugin that constructs this handler
   */
  public LobbyItemHandler(String id, Plugin plugin) {
    this.id = id;
    this.plugin = plugin;
  }

  /**
   * Handles the execution of the item.
   *
   * @param player The player who used the arena
   * @param arena The arena in which the item was executed in
   * @param item The item that has been used
   */
  public abstract void handleUse(Player player, Arena arena, LobbyItem item);

  /**
   * Whether the item shall be shown and usable at the given circumstances.
   * <p>
   * Important: This method is getting called very frequently (every second when the timer is running).
   * Make sure to write efficient code.
   *
   * @param player The player who might use it
   * @param arena The arena in which it might get used
   * @param item The item to which this handler was added to
   * @return <code>true</code> if it shall be visible in this session
   */
  public abstract boolean isVisible(Player player, Arena arena, LobbyItem item);

  /**
   * Returns the id of this handler. This one should be unique to others.
   * <p>
   * It's being used by {@link LobbyItem} to allow users to configure which exact handler they want to use for their item.
   *
   * @return The id of this handler
   */
  public final String getId() {
    return this.id;
  }

  /**
   * Returns the plugin that constructed this handler.
   *
   * @return The plugin behind this handler
   */
  public final Plugin getPlugin() {
    return this.plugin;
  }

  /**
   * Returns the type of this handler.
   * Custom ones should return {@link LobbyItemHandlerType#PLUGIN}.
   *
   * @return The type of this handler
   */
  public LobbyItemHandlerType getType() {
    return LobbyItemHandlerType.PLUGIN;
  }

  /**
   * Returns whether or not this handler is registered.
   * <p>
   * Use {@link GameAPI#registerLobbyItemHandler(LobbyItemHandler)} to register it,
   * or {@link GameAPI#unregisterLobbyItemHandler(LobbyItemHandler)} to unregister it.
   *
   * @return <code>true</code> if it's registered
   */
  public final boolean isRegistered() {
    final LobbyItemHandler handler = GameAPI.get().getLobbyItemHandler(getId());

    return handler == this;
  }
}