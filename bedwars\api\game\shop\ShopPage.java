package de.marcely.bedwars.api.game.shop;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.picker.condition.ArenaConditionGroup;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * Represents a page in the shop that contains a collection of items
 */
public interface ShopPage extends Cloneable {

  /**
   * Returns the unformatted name.
   *
   * @return The name of the page
   */
  String getName();

  /**
   * Returns the formatted name of the page in the default language.
   *
   * @return The display name in the default language
   */
  default String getDisplayName() {
    return getDisplayName(null);
  }

  /**
   * Returns the formatted name in the language of the sender.
   *
   * @param sender The person
   * @return The display name in the language of the sender
   */
  String getDisplayName(@Nullable CommandSender sender);

  /**
   * Returns the icon that will be shown in the shop GUI.
   *
   * @return The icon
   */
  ItemStack getIcon();

  /**
   * Set the icon that shall be shown in the shop GUI.
   *
   * @param icon The new icon
   */
  void setIcon(ItemStack icon);

  /**
   * Returns all the possible items that are inside the page.
   * <p>
   *     All will be cloned as well if this page has been cloned beforehand ({@link #isClone()}).
   * </p>
   *
   * @return All items that the page has
   */
  List<? extends ShopItem> getItems();

  /**
   * Returns the items that are inside the page for a specific player.
   *
   * @param arena The arena the player is in
   * @param player The player
   * @return All items visible to the player
   */
  List<? extends ShopItem> getVisiblePageItems(@Nullable Arena arena, @Nullable Player player);

  /**
   * Removes an item from this shop page.
   * <p>
   *     Note that both instances must have the same reference.
   *     If you try to remove a cloned shopItem using a non-cloned or vice versa,
   *     then you might want to use {@link #removeConnectedItems(ShopItem)} instead.
   * </p>
   *
   * @param shopItem the item that you are trying to remove
   * @return <code>true</code> if it was successfully removed
   */
  boolean removeItem(ShopItem shopItem);

  /**
   * Removes all ShopItems that share the original instance.
   * <p>
   *     As it is possible to insert multiple ShopItems into a page that share the original instance
   *     (e.g. you inserted a non-cloned ShopItem and a ShopItem that has been cloned using that other ShopItem),
   *     it might be annoying to find all similar instance. This methods helps you to get easily rid of them.
   * </p>
   *
   * @param shopItem The ShopItem or whose original instance whose clones we want to remove
   * @return The amount of items that got found and removed
   */
  int removeConnectedItems(ShopItem shopItem);

  /**
   * Adds an item to a shop page.
   *
   * @param shopItem the item that you are trying to remove
   * @return <code>true</code> if it was successfully removed
   * @throws IllegalStateException If you pass a non-cloned item if this instance has been cloned ({@link #isClone()} returns true)
   */
  boolean addItem(ShopItem shopItem);


  /**
   * Constructs a new ShopItem, and adds it to the ShopPage.
   *
   * @param name The name of the item you are attempting to create
   * @param icon The ItemStack that will be displayed as the icon in the shop
   * @return The new item created, and added to the shop
   */
  ShopItem addItem(String name, ItemStack icon);

  /**
   * The ArenaConditionGroup that controls what arenas this ShopPage is visible in.
   *
   * @return The condition that states the arenas this ShopPage will be available in. <code>null</code> if the ShopPage should be displayed in every arena.
   */
  @Nullable
  ArenaConditionGroup getRestriction();

  /**
   * Lets you restrict this ShopPage, so only be available in certain arenas
   *
   * @param restriction The condition that states the arenas this ShopPage will be available in. <code>null</code> if the ShopPage should be displayed in every arena.
   */
  void setRestriction(@Nullable ArenaConditionGroup restriction);

  /**
   * Returns the slot at which it'll be placed at after the rendering of the shop layout GUI.
   * <p>
   *  Can be <code>null</code> if it is not supposed to have this behaviour.
   * </p>
   *
   * @return The slot at which it shall be forced at
   */
  @Nullable Integer getForceSlot();

  /**
   * Define at which slot the item shall be forced at after the rendering of the shop GUI.
   * <p>
   *  Can be <code>null</code> if it is not supposed to have this behaviour.
   * </p>
   *
   * @param forceSlot The new value
   */
  void setForceSlot(@Nullable Integer forceSlot);

  /**
   * Checks whether this instance is a clone (as if it has been cloned using {@link #clone()}).
   * <p>
   *  ShopPages are cloned in some cases (e.g. when a shop is opened) to allow you to modify the shop without affecting other arenas.
   * </p>
   *
   * @return whether this BuyGroup is a clone
   * @see #clone()
   * @see #getOriginal()
   */
  boolean isClone();

  /**
   * Returns the original "non-cloned" instance.
   * <p>
   *     This will return the original instance from which the clone has been created from.
   *     In case {@link #isClone()} returns false, the same instance is being returned.
   * </p>
   *
   * @return The original non-cloned instance
   * @see #isClone()
   * @see #clone()
   */
  ShopPage getOriginal();

  /**
   * Returns a clone of this instance.
   *
   * @return A clone of this instance
   * @see #isClone()
   * @see #getOriginal()
   */
  ShopPage clone();
}
