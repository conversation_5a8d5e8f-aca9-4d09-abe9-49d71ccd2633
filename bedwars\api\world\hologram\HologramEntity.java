package de.marcely.bedwars.api.world.hologram;

import de.marcely.bedwars.api.world.WorldStorage;
import de.marcely.bedwars.tools.PersistentStorage;
import java.util.function.Predicate;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.metadata.Metadatable;
import org.jetbrains.annotations.Nullable;

/**
 * The plugin has a special feature included called "Holohraphic Entities".
 * Those are entities that aren't actually being spawned in the world, instead they're being send directly to the player via packets.
 * <p>
 *     They're permanently being stored and due to the fact that they don't actually exist they can't be accessed via Bukkit's API.
 *     The extensibility of them is very limited, but you're able to do things that you'd otherwise not be able to do, such as having different NPC skins per player.
 * </p>
 */
public interface HologramEntity extends Metadatable, PersistentStorage.Holder {

  /**
   * Returns the skin type of the hologram.
   *
   * @return The skin type
   */
  HologramSkinType getSkinType();

  /**
   * Returns the skin object of the hologram.
   *
   * @return The skin object
   */
  HologramSkin getSkin();

  /**
   * Returns the radius in which the hologram gets hidden for the player.
   * People getting inside the given radius won't see the hologram anymore.
   * <p>
   *     This is for instance being used for the holograms above the bed that disappear in a specific range.
   * </p>
   *
   * @return The minimum visibility range
   */
  int getMinVisibilityRadius();

  /**
   * Make him only visible at a specific distance.
   * People getting inside the given radius won't see the hologram anymore.
   * <p>
   *     This is for instance being used for the holograms above the bed that disappear in a specific range.
   * </p>
   *
   * @param distance The minimum visibility range
   */
  void setMinVisibilityRadius(int distance);

  /**
   * Returns the current location of the hologram.
   *
   * @return It's location
   * @see #getSpawnLocation()
   * @see #getSavingLocation()
   */
  Location getLocation();

  /**
   * Returns the location at which the hologram was initially spawned.
   *
   * @return His never changing original spawn location
   * @see #getLocation()
   * @see #getSavingLocation()
   */
  Location getSpawnLocation();

  /**
   * Returns the location that will be saved and reused after the plugin restarts.
   * <p>
   *    Generally, the given location is equal to {@link #getLocation()}. It may differ when passing false to
   * </p>
   *
   * @return The location that will be saved in a file
   * @see #getLocation()
   * @see #getSpawnLocation()
   */
  Location getSavingLocation();

  /**
   * Returns the world to which the hologram was added to
   *
   * @return The world of the hologram
   */
  WorldStorage getWorld();

  /**
   * Teleport him to another location.
   * <p>
   *     The new location is also permenantly being saved and reused after a restart.
   * </p>
   *
   * @param loc Its new location
   */
  default void teleport(Location loc) {
    teleport(loc, true);
  }

  /**
   * Teleport him to another location.
   *
   * @param loc Its new location
   * @param save <code>true</code> to save it permanently. <code>false</code> if it shall automatically revert to {@link #getSavingLocation()} after a restart
   */
  void teleport(Location loc, boolean save);

  /**
   * Returns the name that will be displayed above his head.
   *
   * @return His display name. <code>null</code> when none has been set
   */
  String getDisplayName();

  /**
   * Returns whether or not a custom display name is given.
   *
   * @return If a display name has been set or not
   */
  boolean hasDisplayName();

  /**
   * Set the name that will be displayed above his head.
   *
   * @param name His new display name
   */
  void setDisplayName(String name);

  /**
   * Returns all the player who are theoretically able to see him.
   * The player does not have to directly look at him, all the hologram has to be is to be inside the servers/clients view distance and got send.
   *
   * @return All players who are nearby this hologram
   */
  Player[] getSeeingPlayers();

  /**
   * Returns the amount of {@link #getSeeingPlayersAmount()}
   *
   * @return The amount of players who are theoretically seeing this hologram
   */
  int getSeeingPlayersAmount();

  /**
   * Returns whether or not it'll stay even after a reload.
   * <p>
   *     Gets stored locally if set to true.
   * </p>
   *
   * @return If the hologram is persistent or not
   */
  boolean isPersistent();

  /**
   * Returns whether or not it'll disappear after a reload.
   * <p>
   *     Gets stored locally if set to true.
   * </p>
   *
   * @param persistent If the hologram is persistent or not
   */
  void setPersistent(boolean persistent);

  /**
   * Set the controller (aka the brain) of the hologram.
   * <p>
   *  It'll automatically and <i>always</i> initiate a new {@link HologramController} instance that can be accessed using {@link #getController()}.
   * </p>
   *
   * @param controller The new type of the controller
   */
  void setControllerType(HologramControllerType controller);

  /**
   * Returns the current type of the controller (aka the brain) of the given hologram.
   *
   * @return The type of the current set controller
   */
  HologramControllerType getControllerType();

  /**
   * Returns the instance of the controller (aka the brain) of the hologram.
   * <p>
   *     There's no global instance per type. Each hologram as his own instance that may be deconstructed and reconstructed at any time using {@link #setControllerType(HologramControllerType)}.
   * </p>
   *
   * @return The current instance of the controller
   */
  HologramController getController();

  /**
   * Returns if the hologram still exists and hasn't been removed yet.
   *
   * @return If it exists
   */
  boolean exists();

  /**
   * Set a Predicate that returns whether it should be visible to a player.
   * <p>
   *   The Predicate will be called on an async thread and if it returns <code>true</code>,
   *   the player will be able to see the hologram. If it returns <code>false</code>, the hologram will be hidden.
   *   It will be called either when a player comes nearby or if {@link #updatePerPlayerVisibility()} is called.
   * </p>
   *
   * @param predicate The Predicate that returns whether the hologram should be visible. <code>null</code> to remove the current one
   */
  void setPerPlayerVisibility(@Nullable Predicate<Player> predicate);

  /**
   * Returns the Predicate that returns whether it should be visible to a player.
   * <p>
   *   The Predicate will be called on an async thread and if it returns <code>true</code>,
   *   the player will be able to see the hologram. If it returns <code>false</code>, the hologram will be hidden.
   *   It will be called either when a player comes nearby or if {@link #updatePerPlayerVisibility()} is called.
   * </p>
   *
   * @return The Predicate that returns whether the hologram should be visible. <code>null</code> if none has been set
   */
  @Nullable Predicate<Player> getPerPlayerVisibility();

  /**
   * Dynamically updates the visibility of the hologram for each player.
   * <p>
   *   This will call the Predicate that has been set using {@link #setPerPlayerVisibility(Predicate)} for all players
   *   within the next tick in which the hologram is being updated (which could be a few seconds)
   * </p>
   */
  void updatePerPlayerVisibility();

  /**
   * Completely removes the hologram.
   */
  void remove();
}