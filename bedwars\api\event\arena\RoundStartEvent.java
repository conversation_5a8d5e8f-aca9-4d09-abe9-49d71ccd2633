package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import lombok.Getter;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when a round is about to start.
 * <p>
 *   Gets called after every player has been teleported to their spawn position.
 *   Only a few things are left to do, which can be managed within this event.
 * </p>
 */
public class RoundStartEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private boolean givingItems;

  public RoundStartEvent(Arena arena, boolean givingItems) {
    this.arena = arena;

    this.givingItems = givingItems;
  }

  /**
   * Returns whether spawn items will be given to the player respawning.
   *
   * @return whether spawn items will be given to the player
   */
  public boolean isGivingItems() {
    return givingItems;
  }

  /**
   * Set whether spawn items will be given to the player when respawning.
   *
   * @param givingItems Whether spawn items will be given to the player
   */
  public void setGivingItems(boolean givingItems) {
    this.givingItems = givingItems;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
