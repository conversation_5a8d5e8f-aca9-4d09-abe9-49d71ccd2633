package de.marcely.bedwars.api.world.hologram;

import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the skin of the hologram.
 * <p>
 *     It contains stuff that only relate to the skin itself and other skin types may have their own class which inherit from this one.
 * </p>
 */
public interface HologramSkin {

  /**
   * Returns the type of the skin.
   *
   * @return The skin type
   */
  HologramSkinType getType();

  /**
   * Returns the hologram to which this skin is bound to.
   *
   * @return The hologram
   */
  HologramEntity getHologram();

  /**
   * Returns the {@link EntityType} that is being shown with this skin.
   * <p>
   *     Keep in mind that some types may have EntityTypes that change during their lifetime, such as {@link HologramSkinType#LIVING}.
   * </p>
   *
   * @return The EntityType that is being used for this skin
   */
  EntityType getEntityType();

  /**
   * Returns the id of the entity that is being shown.
   * <p>
   *     Note that some variants, such as {@link HologramSkinType#HOLOGRAM} will always return 0 as they persist of multiple entities.
   * </p>
   *
   * @return The {@link Entity#getEntityId()} of the shown entity. May be <code>0</code> for some skin types
   */
  int getEntityId();

  /**
   * Returns the NMS object that is being used internally for this skin.
   * <p>
   *     Note that some variants, such as {@link HologramSkinType#HOLOGRAM} will always return <code>null</code> as they persist of multiple entities.
   * </p>
   *
   * @return The internal NMS instance that is being used. May be <code>null</code> for some skin types
   */
  @Nullable
  Object getNMSEntity();
}
