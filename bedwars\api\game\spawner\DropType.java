package de.marcely.bedwars.api.game.spawner;

import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.VarParticle;
import de.marcely.bedwars.tools.VarSound;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

public interface DropType {

  /**
   * Returns the identifier for this type.
   *
   * @return The identifier
   */
  String getId();

  /**
   * Returns {@link #getConfigName()}, but formats it into the default configured language.
   *
   * @return The name in the default language
   */
  default String getName() {
    return getName(null);
  }

  /**
   * Returns {@link #getConfigName()}, but formats it and translates it to the given <code>sender</code>.
   *
   * @param sender The person from which it should look up the language. Null if it should take the default language
   * @return The name in the language of the sender
   */
  String getName(@Nullable CommandSender sender);

  /**
   * Returns {@link #getConfigName()} or {@link #getConfigPluralName()}, depending on the amount
   * but formats it and translates it to the given <code>sender</code>.
   *
   * @param sender The person from which it should look up the language. Null if it should take the default language
   * @param amount used to desied if the plural or non-plural name should be sent
   * @return The name in the language of the sender
   */
  String getName(@Nullable CommandSender sender, int amount);

  /**
   * Returns {@link #getConfigPluralName()}, but formats it into the default configured language.
   *
   * @return The plural name in the default language
   */
  default String getPluralName() {
    return getPluralName(null);
  }

  /**
   * Returns {@link #getConfigPluralName()}, but formats it and translates it to the given <code>sender</code>.
   *
   * @param sender The person from which it should look up the language. Null if it should take the default language
   * @return The plural name in the language of the sender
   */
  String getPluralName(@Nullable CommandSender sender);

  /**
   * Returns the name of the spawner exactly the same as it has been configured.
   *
   * @return The raw name
   */
  String getConfigName();

  /**
   * Returns the plural name of the spawner exactly the same as it has been configured.
   *
   * @return The raw plural name
   */
  String getConfigPluralName();

  /**
   * Set the unformatted name of the config.
   *
   * @param name The new raw name
   */
  void setConfigName(String name);

  /**
   * Set the unformatted plural name of the config.
   *
   * @param name The new raw plural name
   */
  void setConfigPluralName(String name);

  /**
   * Returns the materials that will be dropped.
   *
   * @return The dropping materials
   */
  ItemStack[] getDroppingMaterials();

  /**
   * Returns the original materials as they were configured.
   * Doesn't contain any included name and etc.
   *
   * @return The original dropping materials
   */
  ItemStack[] getConfigDroppingMaterials();

  /**
   * Set the items that will be dropped.
   *
   * @param droppingMaterials The new materials
   */
  void setConfigDroppingMaterials(ItemStack[] droppingMaterials);

  /**
   * Set the icon that will be shown inside GUIs.
   *
   * @param icon The new icon
   */
  void setIcon(ItemStack icon);

  /**
   * Returns the item that shall be shown in GUIs.
   *
   * @return The icon
   */
  ItemStack getIcon();

  /**
   * Returns the interval between the spawns in seconds for a given arena.
   * <p>
   *   Evaluates the formula with the given arena.
   * </p>
   * <p>
   *  Keep in mind that this might differ with an actual spawner as their duration is modifiable during a match.
   *  Use {@link Spawner#getCurrentDropDuration()} to return the actual value of a match.
   * </p>
   *
   * @return The general spawn duration
   */
  double getDropDuration(Arena arena);

  /**
   * Returns the mathematical expression that calculates the interval between the spawns in seconds.
   * <p>
   *  Keep in mind that this might differ with an actual spawner as their duration is modifiable during a match.
   *  Use {@link Spawner#getCurrentDropDuration()} to return the actual value of a match.
   * </p>
   *
   * @return The general spawn duration math expression
   */
  String getDropDurationExpression();

  /**
   * Set the mathematical expression that calculates the interval between the spawns in seconds.
   * <p>
   *  Keep in mind that this might differ with an actual spawner as their duration is modifiable during a match.
   *  Use {@link Spawner#getCurrentDropDuration()} to return the actual value of a match.
   * </p>
   *
   * @param expression The new general spawn duration math expression
   * @see #setDropDuration(double)
   */
  void setDropDurationExpression(String expression);

  /**
   * Set the interval between the spawns in seconds.
   * <p>
   *  Keep in mind that this might differ with an actual spawner as their duration is modifiable during a match.
   *  Use {@link Spawner#getCurrentDropDuration()} to return the actual value of a match.
   * </p>
   *
   * @param dropDuration The new general spawn duration
   */
  void setDropDuration(double dropDuration);

  /**
   * The sound that should be played whenever it drops something.
   * Returns <code>null</code> if it doesn't play any sound.
   *
   * @return The sound it plays
   */
  @Nullable VarSound getSpawnSound();

  /**
   * Set the sound that it should play whenever it drops something.
   * Pass <code>null</code> when you don't want it to play anything.
   *
   * @param sound The new sound it shall play whenever it drops something
   */
  void setSpawnSound(@Nullable VarSound sound);

  /**
   * The effect that should be played whenever it drops something.
   * Returns <code>null</code> if it doesn't play anything.
   *
   * @return The effect it plays
   */
  @Nullable VarParticle getSpawnEffect();

  /**
   * Set the effect that it should play whenever it drops something.
   * Pass <code>null</code> when you don't want it to play anything.
   *
   * @param particle The new effect it shall play whenever it drops something
   */
  void setSpawnEffect(@Nullable VarParticle particle);

  /**
   * Get the radius in which the item may randomly spawns.
   * The center is the set Location at {@link Spawner#getLocation()}.
   *
   * @return The spawn radius
   */
  int getSpawnRadius();

  /**
   * Set the radius in which the item may randomly spawns.
   * The center is the set Location at {@link Spawner#getLocation()}.
   *
   * @param radius The new spawn raadius
   */
  void setSpawnRadius(int radius);

  /**
   * Returns whether or not the item is tranquil.
   * <code>true</code> means that it will stay at it's location and won't fly away in the x and z directions.
   *
   * @return If it's tranquil or not
   */
  boolean isTranquil();

  /**
   * Set whether the item is tranquil or not.
   * <code>true</code> means that it will stay at it's location and won't fly away in the x and z directions.
   *
   * @param tranquil The new tranquil value
   */
  void setTranquil(boolean tranquil);

  /**
   * Returns whether or not it merges with other items.
   * <p>
   * It does that by giving every item an unique lore, which prevents it from merging with other items.
   * MBedwars will nevertheless merge them when there are too many items at the spawn location to prevent lag.
   *
   * @return Whether or not it merges with other items
   */
  boolean isMerging();

  /**
   * Set whether or not it should merge with other items.
   * <p>
   * It does that by giving every item an unique lore, which prevents it from merging with other items.
   * MBedwars will nevertheless merge them when there are too many items at the spawn location to prevent lag.
   *
   * @param merge The new merge value
   */
  void setMerging(boolean merge);

  /**
   * The material of the hologram that will be shown above the spawner ingame.
   * <code>null</code> means that none will be spawned.
   *
   * @return The material of the hologram
   */
  @Nullable ItemStack getHologramMaterial();

  /**
   * Set the material of the hologram that will be shown above the spawner ingame.
   * <code> null</code> means that none will be spawned.
   *
   * @param material The new material of the hologram
   */
  void setHologramMaterial(@Nullable ItemStack material);

  /**
   * Returns the max amount of items that can be near the spawner until it's stops spawning any further.
   * <code>0</code> or less if there's no limit.
   *
   * @return Max amount of nearby laying items till it stops spawning any further
   */
  int getMaxNearbyItems();

  /**
   * Set the amount of items that can lay near the spawner until it stops spawning more items.
   * <code>0</code> or less to set no limit.
   *
   * @param maxNearby Max amount of nearby laying items till it stops spawning any further
   */
  void setMaxNearbyItems(int maxNearby);

  /**
   * Returns the amount of items that should be spawned at the start of the game.
   *
   * @return The amount of items that should be spawned at the start of the game
   */
  int getSpawnAtStart();

  /**
   * Set the amount of items that should be spawned at the start of the game.
   *
   * @param amount The amount of items that should be spawned at the start of the game
   */
  void setSpawnAtStart(int amount);

  /**
   * Returns the id of the custom handler.
   * <p>
   * This method can return a <code>non-null</code> value even if {@link #getCustomHandler()} returns null as
   * the custom handler might hasn't been registered yet.
   *
   * @return The id of the custom handler
   */
  @Nullable String getCustomHandlerId();

  /**
   * Changes the id of the custom handler.
   * Will also automatically update the {@link #setCustomHandler(CustomDropTypeHandler)} when the custom handler has been found.
   *
   * @param id The new id
   */
  void setCustomHandlerId(String id);

  /**
   * Returns a custom handler that'll overtake the spawning process.
   * Materials will get dropped as usual when it returns null
   *
   * @return The custom spawning handler
   * @throws IllegalStateException If the handler hasn't been registered yet with {@link GameAPI#registerCustomSpawnerHandler(CustomDropTypeHandler)}
   */
  @Nullable CustomDropTypeHandler getCustomHandler();

  /**
   * Changes the custom handler that'll overtake the spawning process.
   * Make sure that the given handler is registered, otherwise no change will be applied.
   * <p>
   * Materials will continue to drop as usual when setting null.
   * It'll also automatically update {@link #setCustomHandlerId(String)}.
   *
   * @param handler The new custom handler
   */
  void setCustomHandler(@Nullable CustomDropTypeHandler handler);

  /**
   * Returns the configuration of the custom handler.
   * <p>
   *   Admins are optionally able to pass a config section to the spawner.
   *   This if for {@link #getCustomHandler()} to use.
   * </p>
   *
   * @return A given config. <code>null</code> if none has been set
   */
  @Nullable ConfigurationSection getCustomHandlerConfig();

  /**
   * Returns the amount of dropped spawners that the player is currently holding in his inventory.
   * <p>
   *   Also supports custom handlers.
   * </p>
   *
   * @param player The player we want to check
   * @return The amount the player is holding
   */
  int getHoldingAmount(Player player);

  /**
   * Returns whether or not it has been added to the registry.
   *
   * @return If it has been added to the mbedwars system
   */
  boolean isRegistered();
}
