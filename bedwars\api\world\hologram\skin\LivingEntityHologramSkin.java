package de.marcely.bedwars.api.world.hologram.skin;

import de.marcely.bedwars.api.world.hologram.HologramSkin;
import org.bukkit.entity.EntityType;

/**
 * Represents an Entity that implements LivingEntity.
 * <p>
 *     The actual EntityType is dynamic with this skin.
 * </p>
 */
public interface LivingEntityHologramSkin extends HologramSkin, DamageableSkin, EquippableSkin, BukkitEntitySkin {

  /**
   * Change the EntityType to something else
   *
   * @param type The new EntityType
   * @throws IllegalArgumentException When the class of the type does not implement LivingEntity
   */
  void setEntityType(EntityType type);

  /**
   * Get the current EntityType that is being used for this skin
   *
   * @return The current EntityType
   */
  EntityType getEntityType();
}
