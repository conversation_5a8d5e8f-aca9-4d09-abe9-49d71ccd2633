package de.marcely.bedwars.api.message;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;

import java.util.Collection;
import java.util.Set;

/**
 * Contains API for the process of messages and languages.
 */
public interface MessageAPI {

  /**
   * A MessageProcessor overtakes the post processing of a message.
   * <p>
   *  This includes: replacing placeholders, message placeholders and replacing chat color codes.<br>
   *  The method {@link Message#done()} passes a message to the current set processor
   * </p>
   *
   * @return The current set processor
   */
  MessageProcessor getProcessor();

  /**
   * A MessageProcessor overtakes the post processing of a message.
   * <p>
   *  This includes: replacing placeholders, message placeholders and replacing chat color codes.<br>
   *  The method {@link Message#done()} passes a message to the current set processor
   * </p>
   *
   * @param processor The new processor
   */
  void setProcessor(MessageProcessor processor);

  /**
   * Returns the default MessageProcessor initiated by the MBedwars plugin
   *
   * @return The default processor
   */
  MessageProcessor getDefaultProcessor();

  /**
   * Get the active instance that fetches the locale of a player.
   * <p>
   *  The locale is used to translate messages into the language of the player,
   *  if activated by the server-admin.
   * </p>
   *
   * @return The current set fetcher
   */
  PlayerLocaleFetcher getLocaleFetcher();

  /**
   * Set the active instance that fetches the locale of a player.
   * <p>
   *  The locale is used to translate messages into the language of the player,
   *  if activated by the server-admin.
   * </p>
   *
   * @param fetcher The new fetcher
   */
  void setLocaleFetcher(PlayerLocaleFetcher fetcher);

  /**
   * Returns the default PlayerLocaleFetcher initiated by the MBedwars plugin
   *
   * @return The default fetcher
   */
  PlayerLocaleFetcher getDefaultLocaleFetcher();

  /**
   * Returns all locales that are currently in memory.<br>
   * Keep in mind that this may vary depending on the configuration of the user.<br>
   * Does not include the default one
   *
   * @return The locales stored in memory
   */
  Set<String> getStoredLocales();

  /**
   * A mapping contains a collection of messages that'll be used when the user tries to obtain a message from his messages file that is however not present.
   * <p>
   *  This class is helpful for plugins that want to retain the users ability to customize the message.
   *	MBedwars will automatically unregister the mappings when the plugin is not loaded anymore.
   * </p>
   *
   * @param mappings The mappings that shall be registered
   * @return <code>true</code> if wasn't already present
   */
  boolean registerDefaultMappings(DefaultMessageMappings mappings);

  /**
   * Unregisteres an already registered mapping.
   *
   * @param mappings The mappings that shall be registered
   * @return <code>true</code> if it has been found and unregistered
   */
  boolean unregisterDefaultMappings(DefaultMessageMappings mappings);

  /**
   * Returns all registered default mappings (This won't include the MBedwars' one).
   *
   * @return All registered mappings
   */
  Collection<DefaultMessageMappings> getDefaultMappings();

  /**
   * Returns the global MessageAPI instance.
   *
   * @return The global MessageAPI instance
   */
  static MessageAPI get() {
    return BedwarsAPILayer.INSTANCE.getMessageAPI();
  }
}