package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.world.hologram.HologramEntity;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a player either clicks or punches an {@link HologramEntity}
 */
public class PlayerInteractHologramEntityEvent extends PlayerEvent implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final HologramEntity entity;
  private final boolean punch;

  @Getter @Setter
  private boolean cancelled = false;

  public PlayerInteractHologramEntityEvent(Player player, HologramEntity entity, boolean punch) {
    super(player);

    this.entity = entity;
    this.punch = punch;
  }

  /**
   * Get the hologram entity that the player tries to interact with.
   *
   * @return The involved hologram entity
   */
  public HologramEntity getEntity() {
    return this.entity;
  }

  /**
   * Get whether the player tried to punch the hologram entity with this interaction.
   *
   * @return <code>true</code> in case the player tried to cause damage (by left clicking on the entity)
   */
  public boolean isPunch() {
    return this.punch;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
