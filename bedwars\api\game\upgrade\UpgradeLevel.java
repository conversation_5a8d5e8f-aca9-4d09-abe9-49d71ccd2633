package de.marcely.bedwars.api.game.upgrade;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.game.spawner.DropType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a specific tier of upgrade to which one may have upgraded to.
 */
public interface UpgradeLevel {

  /**
   * Returns upgrade that this upgrade level belongs to
   *
   * @return The upgrade that owns this level
   */
  Upgrade getUpgrade();

  /**
   * Returns level number of this upgrade level
   *
   * @return The level of this item
   */
  int getLevel();

  /**
   * Returns the default icon that's being used upgrade shop
   *
   * @return The id of this item
   */
  ItemStack getIcon();

  /**
   * Returns the id that's being used in e.g. the shop config
   *
   * @param icon  id of this item
   */
  void setIcon(ItemStack icon);

  /**
   * Returns the slot at which it'll be placed at after the rendering of the upgrade shop layout GUI
   * Can be null if it shouldn't do that
   *
   * @return The slot at which it shall be forced at
   */
  @Nullable Integer getForceSlot();

  /**
   * Define at which it shall be forced at after the rendering of the upgrade shop GUI<br>
   * Can be null if it shouldn't do that
   *
   * @param forceSlot The new value
   */
  void setForceSlot(@Nullable Integer forceSlot);

  /**
   * Sets the DropType required to purchase the item
   *
   * @param dropType the price of this upgrade
   */
  void setPriceDropType(DropType dropType);

  /**
   * Returns the DropType required to purchase the item
   *
   * @return the price of this upgrade
   */
  DropType getPriceDropType();

  /**
   * Sets the duration (in seconds) of an UpgradeLevel. Not all upgrade
   * handlers require a duration
   *
   * @param duration the duration (in seconds) of this UpgradeLevel
   */
  void setDuration(int duration);

  /**
   * Gets the duration (in seconds) of an UpgradeLevel. Not all upgrade
   * handlers require a duration. Returns 0 if this is the case
   *
   * @return the duration (in seconds) of this UpgradeLevel
   */
  int getDuration();

  /**
   * Sets the amount of a specific DropType required to purchase this UpgradeLevel.
   * <p>
   *     The actual price may vary by factors such as e.g. {@link Upgrade#isPriceAutoScaling()}.<br>
   *     Use {@link #getPriceAmount(Player)} or {@link #getPriceAmount(Arena, Team)} for a more practical price amount.
   * </p>
   *
   * @param amount the new price amount of this UpgradeLevel
   */
  void setRegularPriceAmount(int amount);

  /**
   * Returns the actual price that a player has to pay for this upgrade.
   * <p>
   *     The actual price may vary by factors such as e.g. {@link Upgrade#isPriceAutoScaling()}.
   * </p>
   *
   * @return the current price amount of this UpgradeLevel
   */
  int getPriceAmount(Player player);

  /**
   * Returns the actual price that a team has to pay for this upgrade.
   * <p>
   *     The actual price may vary by factors such as e.g. {@link Upgrade#isPriceAutoScaling()}.
   * </p>
   *
   * @return The current practical price amount of this UpgradeLevel
   */
  int getPriceAmount(Arena arena, Team team);

  /**
   * Returns amount of a specific DropType required to purchase this UpgradeLevel
   *
   * @return the current price amount of this UpgradeLevel
   */
  int getRegularPriceAmount();

  /**
   * Returns the amplifier applied to this upgrade level. Used to determine how powerful an upgrade should be
   *
   * @return the amplifier applied to this UpgradeLevel
   */
  double getAmplifier();

  /**
   * Sets the amplifier applied to this upgrade level. Used to determine how powerful an upgrade should be
   *
   * @param amplifier the new amplifier
   */
  void setAmplifier(double amplifier);

  /**
   * Returns whether this UpgradeLevel is a trap. Will also return <code>false</code>
   * if the handler is null
   *
   * @return If this UpgradeLevel is a trap
   */
  boolean isTrap();

  /**
   * Returns UpgradeTriggerHandler used by this UpgradeLevel
   *
   * @return The UpgradeTriggerHandler used by this UpgradeLevel.<code>null</code> when it hasn't been registered yet
   */
  @Nullable
  UpgradeTriggerHandler getTriggerHandler();

  /**
   * Returns UpgradeTriggerHandler used by this UpgradeLevel
   *
   * @return The UpgradeTriggerHandler used by this UpgradeLevel.
   */
  String getTriggerHandlerId();

  /**
   * Set the UpgradeTriggerHandler used by this UpgradeLevel
   *
   * @param handler The new trigger handler
   */
  void setTriggerHandler(UpgradeTriggerHandler handler);

  /**
   * Set the id of the new UpgradeTriggerHandler used by this UpgradeLevel
   *
   * @param handlerId The new trigger handler id
   */
  void setTriggerHandlerId(String handlerId);

  /**
   * Returns if a player could afford to purchase an UpgradeLevel.
   *
   * @return if a player has enough materials to purchase this level
   */
  boolean canAfford(Player player);
}
