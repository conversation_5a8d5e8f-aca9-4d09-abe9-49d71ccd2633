package cn.acebrand.acevotemode.events;

/**
 * 幸运方块事件类型
 */
public enum EventType {
    /**
     * 好事件 - 35%概率
     */
    GOOD(35),
    
    /**
     * 坏事件 - 60%概率
     */
    BAD(60),
    
    /**
     * 中立事件 - 5%概率
     */
    NEUTRAL(5);
    
    private final int chance;
    
    EventType(int chance) {
        this.chance = chance;
    }
    
    /**
     * 获取事件类型的触发概率
     * @return 概率百分比
     */
    public int getChance() {
        return chance;
    }
}
