package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.BedwarsAddon;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when an addon is being unregistered.
 * <p>
 * This may happen when {@link BedwarsAddon#unregister()} is being called or when the plugin that created it is being unloaded.
 */
public class AddonUnregisterEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final BedwarsAddon addon;

  public AddonUnregisterEvent(BedwarsAddon addon) {
    this.addon = addon;
  }

  /**
   * Returns the addon that has been unregistered.
   *
   * @return The addon involved in this event
   */
  public BedwarsAddon getAddon() {
    return this.addon;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
