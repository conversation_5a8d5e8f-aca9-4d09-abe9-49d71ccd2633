package de.marcely.bedwars.tools;

import de.marcely.bedwars.api.BedwarsAPI;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockState;
import org.bukkit.block.Skull;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.Nullable;

/**
 * This class contains some helpers which may help you in solving NMS stuff
 */
public interface NMSHelper {

  /**
   * Returns the minor version of the running server software.
   * <p>
   *   Example: If it's running 1.8.6, then this method returns 8
   * </p>
   *
   * @return The minor version of the running server software
   */
  int getVersion();

  /**
   * Returns the patch version of the running software.
   * <p>
   *  Example: If it's running 1.8.6, then this method returns 6
   * </p>
   *
   * @return The patch version of the running server software
   */
  int getPatchVersion();

  /**
   * Returns the revision number that you can find in NMS packages.<br>
   * Example: If it's running "v1_8_R3" then this method returns 3
   *
   * @return The revision number of the running server software
   */
  int getRevision();

  /**
   * Returns whether or not this server is running paper or a fork of paper
   *
   * @return <code>true</code> when the plugin is running PaperMC or a fork of it
   */
  boolean isRunningPaper();

  /**
   * Returns the locale of the player that he has configured within his client.
   *
   * @param player The given player
   * @return The locale of the player
   */
  String getLocale(Player player);

  /**
   * Adds or removes a "glow" effect to an item.
   * <p>
   *  Glow effects basically are simply enchantments added to an item.
   *  This method also does some extra little things to improve the glow effect furthermore.
   * </p>
   * <p>
   *  It's possible that the glow effect gets added to the item that's being passed with this method.
   *  If it's eventually a new instance that's getting returned depends on the minecraft version that's running and the material of the item.
   * </p>
   * @param is The item to which the glow effect shall be added to / removed from
   * @param glowing <code>true</code>: Add the effect; <code>false</code>: Remove the effect
   * @return Same or cloned ItemStack instance with or without the glow effect
   */
  ItemStack setGlowEffect(ItemStack is, boolean glowing);

  /**
   * Shows the "title" to a player.
   * <p>
   * A title is basically a huge text displayed on the players screen.
   *
   * @param player The target to whom the text shall be displayed to
   * @param title The larger text which is located above <code>subtitle</code>
   * @param subtitle The smaller text which is located below <code>title</code>
   * @param fadeIn The time in ticks for the "appear" or "fade in" effect
   * @param stayTime The time in ticks in which the title will be shown
   * @param fadeOut The time in ticks for the "disappear" or "fade out" effect
   */
  void showTitle(Player player, String title, String subtitle, int fadeIn, int stayTime, int fadeOut);

  /**
   * Shows the "title" to a player.
   * <p>
   * A title is basically a huge text displayed on the players screen.
   *
   * @param player The target to whom the text shall be displayed to
   * @param title The larger text which is located above <code>subtitle</code>
   * @param subtitle The smaller text which is located below <code>title</code>
   */
  void showTitle(Player player, String title, String subtitle);

  /**
   * Will show a small message above the players hotbar
   *
   * @param player The target to whom the text shall be displayed to
   * @param text The string that shall be shown
   */
  void showActionbar(Player player, String text);

  /**
   * Applies a custom texture in a base64 format to a skull block.
   * <p>
   *   Make sure to call {@link BlockState#update()} at some point to actually apply it to the block.
   *   This method only applies it to the cloned Skull instance and not to the actual block.
   * </p>
   *
   * @param skull The skull block to which the texture shall be applied to
   * @param texture The texture in base64 format
   */
  void setSkullTexture(Skull skull, String texture);

  /**
   * Returns the texture of the skull in a base64 format.
   * Might be <code>null</code> when the texture hasn't been loaded yet or when simply none has been applied.
   *
   * @param skull The skull block from which we want to fetch the texture
   * @return The texture of the skull. Might be <code>null</code>
   */
  @Nullable String getSkullTexture(Skull skull);

  /**
   * Applies a custom texture in a base64 format to a skull item
   *
   * @param skullMeta The ItemMeta of the skull item to which the texture shall be applied to
   * @param texture The texture in base64 format
   */
  void setSkullTexture(SkullMeta skullMeta, String texture);

  /**
   * Returns the texture of the skull in a base64 format.
   * Might be <code>null</code> when the texture hasn't been loaded yet or when simply none has been applied.
   *
   * @param skullMeta The ItemMeta of the skull item from which we want to fetch the texture
   * @return The texture of the skull. Might be <code>null</code>
   */
  @Nullable String getSkullTexture(SkullMeta skullMeta);

  /**
   * Returns the death message that Minecraft would use by default if the player would die at this moment
   *
   * @param player The related player
   * @return The vanilla death message of the player
   */
  String getVanillaDeathMessage(Player player);

  /**
   * Causes a walkable hologram to walk to a given location.
   * Pass <code>null</code> to make the hologram stop walking.
   * <p>
   * Keep in mind that there's maximum radius to which the hologram will walk to.
   *
   * @param entity The hologram that shall walk to somewhere
   * @param target The location to which he shall walk to. <code>null</code> to make him stop
   */
  void setEntityTarget(Entity entity, @Nullable Location target);

  /**
   * Will send a message to the player.
   * The message must be an instance of net.minecraft.v&lt;version&gt;.Packet
   *
   * @param player The player to who we want to send a message to
   * @param packet The message that shall be send
   */
  void sendPacket(Player player, Object packet);

  /**
   * Returns the amount of damage a player would cause at the exact moment with a generic entity.
   * <p>
   *     Does not consider e.g. the monster damage enchantments as we don't care of the entity type that gets damaged here.
   * </p>
   *
   * @param player The player who would deal the damage
   * @return The amount of damage he'd deal with a generic entity
   */
  float getAttackDamage(Player player);

  /**
   * Returns the entity whose {@link Entity#getEntityId()} is equal to the given one.
   * <p>
   *     Utilizes NBT for best performance
   * </p>
   *
   * @param world The world in which the entity is located in
   * @param entityId The id of the entity
   * @return The entity with that id. <code>null</code> when there's none
   */
  @Nullable
  Entity getEntityById(World world, int entityId);

  /**
   * Simulates as if someone would open a container, such as an ender chest, normal chest or a shulker box.
   * <p>
   *     It'll play an animation and updates the redstone signal. Possibly even more is happening.
   * </p>
   * <p>
   *     There's an interal numerical counter that checks how many players have opened the chest.
   *     It's being used to determinate when the close animation needs to be played.
   *     Make sure to call {@link #simulateChestClosing(World, int, int, int)} equally as often as you called this method and not more or less, as otherwise it'll possibly break.
   *     The counter may even reach minus values.
   * </p>
   *
   * @param world The world in which the container is located at
   * @param x The x position of the container block
   * @param y The y position of the container block
   * @param z The z position of the container block
   */
  void simulateChestOpening(World world, int x, int y, int z);

  /**
   * Simulates as if someone would open a container, such as an ender chest, normal chest or a shulker box.
   * <p>
   *     It'll play an animation and updates the redstone signal. Possibly even more is happening.
   * </p>
   * <p>
   *     There's an interal numerical counter that checks how many players have opened the chest.
   *     It's being used to determinate when the close animation needs to be played.
   *     Make sure to call {@link #simulateChestClosing(World, int, int, int)} equally as often as you called this method and not more or less, as otherwise it'll possibly break.
   *     The counter may even reach minus values.
   * </p>
   *
   * @param block The block that represents the container
   */
  default void simulateChestOpening(Block block) {
    Validate.notNull(block, "block");

    simulateChestOpening(block.getWorld(), block.getX(), block.getY(), block.getZ());
  }

  /**
   * Simulates as if someone would close a container, such as an ender chest, normal chest or a shulker box.
   * <p>
   *     It'll play an animation and updates the redstone signal. Possibly even more is happening.
   * </p>
   * <p>
   *     There's an interal numerical counter that checks how many players have opened the chest.
   *     It's being used to determinate when the close animation needs to be played.
   *     Make sure to call {@link #simulateChestOpening(World, int, int, int)} equally as often as you called this method and not more or less, as otherwise it'll possibly break.
   *     The counter may even reach minus values.
   * </p>
   *
   * @param world The world in which the container is located at
   * @param x The x position of the container block
   * @param y The y position of the container block
   * @param z The z position of the container block
   */
  void simulateChestClosing(World world, int x, int y, int z);

  /**
   * Simulates as if someone would close a container, such as an ender chest, normal chest or a shulker box.
   * <p>
   *     It'll play an animation and updates the redstone signal. Possibly even more is happening.
   * </p>
   * <p>
   *     There's an interal numerical counter that checks how many players have opened the chest.
   *     It's being used to determinate when the close animation needs to be played.
   *     Make sure to call {@link #simulateChestOpening(World, int, int, int)} equally as often as you called this method and not more or less, as otherwise it'll possibly break.
   *     The counter may even reach minus values.
   * </p>
   *
   * @param block The block that represents the container
   */
  default void simulateChestClosing(Block block) {
    Validate.notNull(block, "block");

    simulateChestClosing(block.getWorld(), block.getX(), block.getY(), block.getZ());
  }

  /**
   * This may sound crazy, but Bukkit's API does not allow the direct change of the server's motd.
   *
   * @param motd The new motd
   */
  void setServerMotd(String motd);

  /**
   * Simulates an item, arrow, exp or whatever getting picked up.
   * <p>
   *     This method does NOT remove the entity from the memory, it only plays the pickup animation.
   * </p>
   *
   * @param entity The entity that picked it up
   * @param item The entity that got picked up
   * @param amount The amount that got picked up. For an item it's for instance its amount, for exp it's the amount it represents
   */
  void playItemPickupAnimation(Entity entity, Entity item, int amount);

  /**
   * Simulates as if the player would click on the "respawn" button while he's dead.
   *
   * @param player The player for whom we want it to simulate
   */
  void simulatePlayerClickingRespawn(Player player);

  /**
   * Changes the movement speed of the given entity.
   * <p>
   *     Basically only changes the "MOVEMENT_SPEED" attribute.
   *     This, however, wasn't possible for all versions in the past and required NMS.
   * </p>
   *
   * @param entity The entity for whom the speed shall be changed
   * @param speed The new movement speed
   */
  void setEntityMovementSpeed(LivingEntity entity, double speed);

  /**
   * Changes the range in which the entity can follow its target/enemy.
   * <p>
   *     Basically only changes the "FOLLOW_RANGE" attribute.
   *     This, however, wasn't possible for all versions in the past and required NMS.
   * </p>
   *
   * @param entity The entity for whom the speed shall be changed
   * @param range The new target range
   */
  void setEntityFollowRange(LivingEntity entity, double range);

  /**
   * Changes if an entity is silent, or is allowed to make noise.
   *
   * @param entity The entity that will be made silent
   * @param silent Whether the entity shall be silent or not
   */
  void setEntitySilent(Entity entity, boolean silent);

  /**
   * Teleports a given entity to a location without calling {@link org.bukkit.event.entity.EntityTeleportEvent}.
   * <p>
   *     Furthermore, it also doesn't check whether the entity is currently being ridden on (unlike Bukkit's API).
   * </p>
   *
   * @param entity The entity that shall be teleported
   * @param location The target location
   */
  void teleportWithoutEvent(Entity entity, Location location);

  /**
   * Calculates the amount of attack damage this item would theoretically generate.
   *
   * @param is The item
   * @return The approx amount of damage it'd generate
   */
  double getItemDamage(ItemStack is);

  /**
   * Hides attributes from the item.
   * <p>
   *     This includes e.g. the damage for swords.
   *     It's not possible to tell beforehand whether a new instance will be constructed as this greatly depends on the version the user is using.
   * </p>
   *
   * @param is The item whose attributes shall be hidden
   * @return Possibly the equal or a new instance
   */
  ItemStack hideAttributes(ItemStack is);

  /**
   * Sends a world time packet to the given player.
   *
   * @param player Target player
   * @param time The new world time
   */
  void sendWorldTimePacket(Player player, long time);

  /**
   * Returns the sound that the entity would play if it'd receive damage.
   *
   * @param entity The involved entity
   * @return The sound that it'd play. <code>null</code> if it doesn't play any sound in that case
   */
  @Nullable
  Sound getEntityDamageSound(Entity entity);

  /**
   * Returns the sound that the entity would play if it'd die.
   *
   * @param entity The involved entity
   * @return The sound that it'd play. <code>null</code> if it doesn't play any sound in that case
   */
  @Nullable
  Sound getEntityDeathSound(Entity entity);

  /**
   * Tries to parse a Minecraft sound given by it's vanilla id (e.g. minecraft:beacon.activate)
   *
   * @param vanillaName The vanilla id of the sound
   * @return The sound that matches to it. <code>null</code> if none has been found
   */
  @Nullable
  Sound getSoundByMinecraftName(String vanillaName);

  /**
   * Get the width of the entity
   *
   * @param entity The involved entity
   * @return The width of the entity
   */
  float getEntityWidth(Entity entity);

  /**
   * Get the height of the entity
   *
   * @param entity The involved entity
   * @return The height of the entity
   */
  float getEntityHeight(Entity entity);

  /**
   * Get the duration that you have to use for infinite potion effects.
   * <p>
   *   While in the past you could just use {@link Integer#MAX_VALUE}, newer
   *   versions instead expect -1. This method automatically handles the differentiation
   *   for you.
   * </p>
   *
   * @return The duration that you have to use for infinite potion effects
   * @see org.bukkit.potion.PotionEffect
   */
  int getEffectInfiniteDuration();

  /**
   * Sets the fireball direction and speed.
   * <p>
   *   This method aims to be consistent across all versions,
   *   even though they were significant changes in the fireball
   *   in how it behaves.
   * </p>
   *
   * @param fireball The fireball entity
   * @param direction The direction in which the fireball shall fly
   * @param speed The speed at which the fireball shall fly
   * @throws java.lang.RuntimeException If the entity is not a valid fireball
   * @throws IllegalArgumentException If the direction's length is exactly 0.0
   */
  void setFireballDirection(Entity fireball, Vector direction, double speed);

  /**
   * Contains API/Helper to access NMS stuff
   *
   * @return The global NMSHelper instance
   */
  static NMSHelper get() {
    return BedwarsAPI.getNMSHelper();
  }
}
