package de.marcely.bedwars.api.game.spawner;

import org.bukkit.plugin.Plugin;

/**
 * {@link Spawner}s have a specific period/duration until they drop something.
 * Use this class to modify the duration that'll be used.
 * Initiate it using {@link Spawner#addDropDurationModifier(String, Plugin, Operation, double)}
 */
public interface SpawnerDurationModifier {

  /**
   * The "base" modifier that used {@link Operation#SET} and the value {@link DropType#getDropDuration(de.marcely.bedwars.api.arena.Arena)} by default
   */
  String ID_DEFAULT_BASE = "bedwars:base";
  /**
   * Gets added when a player purchases an upgrade
   */
  String ID_DEFAULT_UPGRADEDEALER = "bedwars:upgrade_dealer";

  /**
   * Returns the id of this modifier that can be used to identify this modifier
   *
   * @return The id of this modifier
   */
  String getId();

  /**
   * Returns the plugin that created this modifier
   *
   * @return The plugin that initiated this modifier
   */
  Plugin getPlugin();

  /**
   * Returns the spawner to which this modifier has been added to
   *
   * @return The spawner to which this applies to
   */
  Spawner getSpawner();

  /**
   * Get the operation this modifier will apply to the mathematical equation.
   *
   * @return The operation used
   */
  Operation getOperation();

  /**
   * Get the amount in seconds by which this modifier will apply its {@link Operation}.
   *
   * @return The value that will be used in the mathematical equation
   */
  double getValue();

  /**
   * Set the amount in seconds by which this modifier will apply its {@link Operation}.
   *
   * @param value The new value that will be used in the mathematical equation
   */
  void setValue(double value);

  /**
   * Returns whether or not this modifier still exists in {@link #getSpawner()}
   *
   * @return If this modifier is still present
   */
  boolean isPresent();


  /**
   * Enumerable operation to be applied to the mathematical equation
   */
  enum Operation {

    /**
     * Will throw away previous values and set it to a new one (new = value)
     */
    SET,

    /**
     * Adds the value to the previous one (new = value + previous)
     */
    ADD,

    /**
     * Multiplies the value with the previous one (new = value * previous)
     */
    MULTIPLY
  }
}
