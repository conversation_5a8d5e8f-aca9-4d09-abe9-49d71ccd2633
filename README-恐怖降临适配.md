# 恐怖降临模式适配完成

## 概述
您的恐怖降临MythicMobs配置已成功适配到起床战争模式中！现在可以在起床战争游戏中使用您的三阶段Boss战斗系统。

## 已完成的适配工作

### 1. 模式配置更新
- ✅ 更新Boss ID为 `LightHerald1` (第一阶段)
- ✅ 配置三阶段Boss系统：LightHerald1 → LightHerald2 → LightCore
- ✅ 适配召唤物：LightWarrior, Spear, Beam1, Beam2, LightProjectile
- ✅ 更新模式描述和消息

### 2. 技能系统适配
- ✅ 第一阶段技能：LH1-LH8 (7个主要技能)
- ✅ 第二阶段技能：LK1-LK13 (12个强化技能)
- ✅ 第三阶段技能：LC1-LC11 (11个终极技能)
- ✅ 召唤物技能：LW1-LW3 (光明战士技能)

### 3. 消息系统更新
- ✅ Boss生成消息：光明先驱已经苏醒
- ✅ 阶段切换消息：三个阶段的专属提示
- ✅ 召唤物消息：光明战士、光明长矛、光明光束

### 4. 测试工具
- ✅ 添加 `/terrortest` 命令用于测试
- ✅ 检查Boss配置完整性
- ✅ 手动生成Boss进行测试

## 使用方法

### 1. 确保MythicMobs配置已加载
将您的配置文件放在MythicMobs插件目录：
```
plugins/MythicMobs/Mobs/IntoTheLightBoss.yml
plugins/MythicMobs/Skills/IntoTheLightSkillsBoss.yml
```

然后执行：
```
/mm reload
```

### 2. 验证配置
使用测试命令检查所有Boss是否正确加载：
```
/terrortest check
```

### 3. 测试Boss生成
手动生成Boss进行测试：
```
/terrortest spawn LightHerald1
/terrortest spawn LightHerald2  
/terrortest spawn LightCore
```

### 4. 启动恐怖降临模式
在起床战争游戏开始前，玩家投票选择"恐怖降临"模式即可。

## Boss战斗流程

### 阶段1：光明先驱 (LightHerald1)
- **血量**: 500
- **技能**: 基础攻击技能，包括范围攻击、眩晕、传送等
- **死亡后**: 自动生成第二阶段Boss

### 阶段2：光明先驱强化 (LightHerald2)
- **血量**: 500  
- **技能**: 增强版技能，包括冲刺攻击、投射物、光束攻击等
- **死亡后**: 自动生成最终阶段Boss

### 阶段3：光明核心 (LightCore)
- **血量**: 500
- **技能**: 终极技能，包括召唤光明战士、光束轰炸、传送攻击等
- **死亡后**: Boss战结束，击杀队伍获得属性效果奖励

## 召唤物系统

### 光明战士 (LightWarrior)
- 近战攻击单位，会主动攻击玩家
- 血量较低但攻击频繁

### 光明长矛 (Spear/Spear2)
- 投射物攻击，用于远程骚扰
- 无敌状态，主要起装饰作用

### 光明光束 (Beam1/Beam2)
- 区域伤害技能，造成持续伤害
- 有粒子效果和音效提示

### 光明投射物 (LightProjectile)
- 追踪型投射物，会跟随玩家移动
- 用于远程精确打击

## 奖励系统
击败最终Boss后，击杀队伍将获得：
- **力量 II** - 5分钟
- **速度 II** - 5分钟  
- **抗性提升 I** - 5分钟
- **再生 I** - 3分钟

## 注意事项

### 1. 模型文件要求
确保您有以下模型文件：
- herald_of_light_phase_1
- herald_of_light_phase_2
- herald_of_light_core
- light_warrior
- light_herald_spear
- light_herald_spear2
- light_projectile
- sun_light_beam
- sun_light_long_beam

### 2. 性能考虑
- Boss技能较多，建议在性能较好的服务器使用
- 同时最多只会有1个Boss存在
- Boss死亡后会自动清理所有召唤物

### 3. 平衡性调整
如需调整Boss难度，请修改您的MythicMobs配置文件：
- 血量调整：修改各Boss的Health值
- 伤害调整：修改技能中的damage值
- 技能频率：修改技能的Cooldown值

## 故障排除

### Boss不生成
1. 检查MythicMobs是否正确加载：`/mm reload`
2. 验证Boss配置：`/terrortest check`
3. 查看控制台错误信息

### 技能不工作
1. 确认技能ID存在于配置文件中
2. 检查MythicMobs版本兼容性
3. 查看MythicMobs调试信息

### 阶段不切换
1. 确认Boss死亡时的summon命令正确
2. 检查下一阶段Boss ID是否存在
3. 查看服务器日志

## 自定义修改
如需修改Boss行为，请编辑您的MythicMobs配置文件：
- `IntoTheLightBoss.yml` - Boss属性和基础技能
- `IntoTheLightSkillsBoss.yml` - 详细技能配置

修改后记得执行 `/mm reload` 重新加载配置。

## 支持
如有问题，请检查：
1. MythicMobs插件版本
2. 服务器Minecraft版本兼容性
3. 控制台错误日志
4. 使用 `/terrortest` 命令进行诊断

恐怖降临模式现已完全适配您的MythicMobs配置，享受史诗级的Boss战斗体验吧！
