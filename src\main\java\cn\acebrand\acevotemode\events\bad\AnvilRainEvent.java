package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.entity.FallingBlock;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 铁砧雨事件
 * 以玩家为中心召唤3*3的铁砧两次
 */
public class AnvilRainEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    private final List<Location> placedAnvils = new ArrayList<>();

    public AnvilRainEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(badDir, "anvil_rain.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/anvil_rain.yml", false);
                plugin.getLogger().info("已生成铁砧雨事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载铁砧雨事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载铁砧雨事件配置失败: " + e.getMessage());
        }
    }

    @Override
    public EventType getType() {
        return EventType.BAD;
    }

    @Override
    public String getName() {
        return "ANVIL_RAIN";
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 25) : 25;
    }

    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }

    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }

        // 获取配置参数
        int rainCount = config != null ? config.getInt("anvil_rain.rain_count", 2) : 2;
        int rainInterval = config != null ? config.getInt("anvil_rain.rain_interval", 100) : 100;
        int gridSize = config != null ? config.getInt("anvil_rain.grid_size", 3) : 3;
        int heightOffset = config != null ? config.getInt("anvil_rain.height_offset", 10) : 10;
        double fallDamage = config != null ? config.getDouble("anvil_rain.fall_damage", 6.0) : 6.0;
        int lifetime = config != null ? config.getInt("anvil_rain.lifetime", 60) : 60;

        // 获取竞技场
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        if (arena == null) {
            plugin.getLogger().warning("玩家不在竞技场中，无法执行铁砧雨事件");
            return;
        }

        // 开始铁砧雨序列
        new AnvilRainTask(player, location, arena, rainCount, rainInterval,
                gridSize, heightOffset, fallDamage, lifetime).runTaskTimer(plugin, 0L, rainInterval);

        plugin.getLogger().info("玩家 " + player.getName() + " 触发了铁砧雨事件");
    }

    /**
     * 获取随机铁砧类型
     */
    private Material getRandomAnvilType() {
        if (config == null)
            return Material.ANVIL;

        List<String> anvilTypes = config.getStringList("anvil_rain.anvil_types");
        boolean randomType = config.getBoolean("anvil_rain.random_type", true);

        if (!randomType || anvilTypes.isEmpty()) {
            return Material.ANVIL;
        }

        String selectedType = anvilTypes.get(random.nextInt(anvilTypes.size()));
        try {
            return Material.valueOf(selectedType);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的铁砧类型: " + selectedType);
            return Material.ANVIL;
        }
    }

    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null)
            return true;
        return config.getBoolean("messages.send_message", true);
    }

    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null)
            return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }

    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null)
            return "§c天空中传来金属碰撞声...";
        return config.getString("messages.event_message", "§c天空中传来金属碰撞声...");
    }

    /**
     * 铁砧雨任务
     */
    private class AnvilRainTask extends BukkitRunnable {
        private final Player player;
        private final Location baseLocation;
        private final Arena arena;
        private final int gridSize;
        private final int heightOffset;
        private final double fallDamage;
        private final int lifetime;
        private int remainingRains;

        public AnvilRainTask(Player player, Location baseLocation, Arena arena, int rainCount,
                int interval, int gridSize, int heightOffset, double fallDamage, int lifetime) {
            this.player = player;
            this.baseLocation = baseLocation.clone();
            this.arena = arena;
            this.remainingRains = rainCount;
            this.gridSize = gridSize;
            this.heightOffset = heightOffset;
            this.fallDamage = fallDamage;
            this.lifetime = lifetime;
        }

        @Override
        public void run() {
            if (remainingRains <= 0 || !player.isOnline()) {
                cancel();
                return;
            }

            // 放置铁砧网格
            placeAnvilGrid();

            // 对玩家造成掉落伤害
            if (fallDamage > 0) {
                player.damage(fallDamage);
            }

            remainingRains--;

            if (remainingRains <= 0) {
                cancel();

                // 设置铁砧清理时间
                if (lifetime > 0) {
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            cleanupAnvils();
                        }
                    }.runTaskLater(plugin, lifetime * 20L);
                }
            }
        }

        /**
         * 放置铁砧网格（从天空掉落）
         */
        private void placeAnvilGrid() {
            Location playerLoc = player.getLocation();
            int halfGrid = gridSize / 2;

            for (int x = -halfGrid; x <= halfGrid; x++) {
                for (int z = -halfGrid; z <= halfGrid; z++) {
                    // 在玩家上方生成掉落的铁砧
                    Location dropLoc = playerLoc.clone().add(x, heightOffset, z);
                    spawnFallingAnvil(dropLoc);
                }
            }
        }

        /**
         * 生成掉落的铁砧
         */
        private void spawnFallingAnvil(Location location) {
            Material anvilType = getRandomAnvilType();

            // 创建掉落方块实体
            FallingBlock fallingAnvil = location.getWorld().spawnFallingBlock(location, anvilType.createBlockData());

            // 设置掉落方块属性
            fallingAnvil.setDropItem(false); // 不掉落物品
            fallingAnvil.setHurtEntities(true); // 可以伤害实体
            fallingAnvil.setFallDistance(0.0f);

            // 监听铁砧落地并标记为玩家放置
            new AnvilLandingTracker(fallingAnvil, arena, plugin).runTaskTimer(plugin, 1L, 1L);
        }

        /**
         * 清理铁砧
         */
        private void cleanupAnvils() {
            for (Location anvilLoc : placedAnvils) {
                Block block = anvilLoc.getBlock();
                if (block.getType() == Material.ANVIL ||
                        block.getType() == Material.CHIPPED_ANVIL ||
                        block.getType() == Material.DAMAGED_ANVIL) {
                    block.setType(Material.AIR);
                }
            }
            placedAnvils.clear();
        }
    }

    /**
     * 铁砧落地追踪器
     */
    private class AnvilLandingTracker extends BukkitRunnable {
        private final FallingBlock fallingAnvil;
        private final Arena arena;
        private final AceVoteMode plugin;
        private int ticksWaited = 0;
        private final int maxWaitTicks = 200; // 最多等待10秒

        public AnvilLandingTracker(FallingBlock fallingAnvil, Arena arena, AceVoteMode plugin) {
            this.fallingAnvil = fallingAnvil;
            this.arena = arena;
            this.plugin = plugin;
        }

        @Override
        public void run() {
            ticksWaited++;

            // 超时保护
            if (ticksWaited > maxWaitTicks) {
                plugin.getLogger().warning("铁砧落地追踪超时，停止追踪");
                cancel();
                return;
            }

            // 检查掉落方块是否还存在
            if (!fallingAnvil.isValid()) {
                // 掉落方块已经消失，说明已经落地
                // 在掉落方块最后位置附近搜索铁砧方块
                Location lastLocation = fallingAnvil.getLocation();
                findAndMarkLandedAnvils(lastLocation);
                cancel();
            }
        }

        /**
         * 在指定位置附近搜索并标记落地的铁砧
         */
        private void findAndMarkLandedAnvils(Location searchCenter) {
            // 在3x3x3区域内搜索铁砧方块
            for (int x = -1; x <= 1; x++) {
                for (int y = -2; y <= 1; y++) {
                    for (int z = -1; z <= 1; z++) {
                        Location checkLoc = searchCenter.clone().add(x, y, z);
                        Block block = checkLoc.getBlock();

                        // 检查是否是铁砧方块
                        if (isAnvilBlock(block)) {
                            // 检查这个位置是否已经被标记过
                            if (!placedAnvils.contains(checkLoc)) {
                                markAnvilAsPlayerPlaced(block, checkLoc);
                            }
                        }
                    }
                }
            }
        }

        /**
         * 检查方块是否是铁砧
         */
        private boolean isAnvilBlock(Block block) {
            Material type = block.getType();
            return type == Material.ANVIL ||
                    type == Material.CHIPPED_ANVIL ||
                    type == Material.DAMAGED_ANVIL;
        }

        /**
         * 标记铁砧为玩家放置（按照BedWars API推荐的方式）
         */
        private void markAnvilAsPlayerPlaced(Block block, Location location) {
            try {
                // 按照BedWars API推荐的方式标记方块
                arena.setBlockPlayerPlaced(block, true);

                // 验证标记是否成功
                boolean isMarked = arena.isBlockPlayerPlaced(block);
                plugin.getLogger().info("铁砧标记状态: " + isMarked + " 位置: " + location + " 类型: " + block.getType());

                if (!isMarked) {
                    plugin.getLogger().warning("铁砧标记失败，尝试重新标记: " + location);
                    arena.setBlockPlayerPlaced(block, true);
                }

            } catch (Exception e) {
                plugin.getLogger().severe("标记铁砧失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 记录铁砧位置用于清理
            placedAnvils.add(location);
        }
    }
}
