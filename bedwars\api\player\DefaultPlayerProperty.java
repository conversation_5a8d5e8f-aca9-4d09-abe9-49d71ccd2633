package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.game.shop.ShopItem;

/**
 *
 * Contains all default properties of the bedwars plugin
 */
public class DefaultPlayerProperty {

  /**
   * The username of the player
   */
  public static final String BASE_USERNAME = "bedwars:username";

  /**
   * The last time the player joined the server in milliseconds
   */
  public static final String BASE_LAST_LOGIN = "bedwars:last_login";

  /**
   * A JsonObject containing the value and the signature of a cached instance of the skin of a player.
   * <p>
   * 	It's possible that there's an entry of {@link #BASE_SKIN_TEXTURE_LAST_UPDATE}, but not one of {@link #BASE_SKIN_TEXTURE}.
   * 	This can occur when spigot didn't fetch or cache the texture at the moment the plugin tried to obtain it from the memory.
   * </p>
   */
  public static final String BASE_SKIN_TEXTURE = "bedwars:skin_texture";

  /**
   * The time in milliseconds when the plugin tried to obtain the players texture from the memory.
   * <p>
   * 	It's possible that there's an entry of {@link #BASE_SKIN_TEXTURE_LAST_UPDATE}, but not one of {@link #BASE_SKIN_TEXTURE}.
   * 	This can occur when spigot didn't fetch or cache the texture at the moment the plugin tried to obtain it from the memory.
   * </p>
   */
  public static final String BASE_SKIN_TEXTURE_LAST_UPDATE = "bedwars:skin_texture_last_update";

  /**
   * The name of the arena in which he lastly played inside.
   */
  public static final String REJOIN_ARENA_NAME = "bedwars:rejoin_arena_name";

  /**
   * The bungeecord name of the server in which he lastly played inside.
   */
  public static final String REJOIN_SERVER_NAME = "bedwars:rejoin_server_name";

  /**
   * The variant that the player has chosen in the rewinside shop
   */
  public static final String SHOPDESIGN_REWINSIDE_VARIANT = "bedwars:shopdesign_rewinside_variant";

  /**
   * A JsonObject with the user specific quick buy configuration
   *
   * @see PlayerProperties#getShopHypixelV2QuickBuyItems()
   * @see PlayerProperties#setShopHypixelV2QuickBuyItems(ShopItem[])
   */
  public static final String SHOPDESIGN_HYPIXELV2_QUICKBUY = "bedwars:shopdesign_hypixelv2_quickbuy";

  /**
   * The date the user has modified the shop the last time
   * <p>
   *   Formatted using {@link java.time.format.DateTimeFormatter#ISO_OFFSET_DATE_TIME}.
   *   Only counts actual interactions done by the player, such as when he adds or removes an item.
   * </p>
   *
   * @see PlayerProperties#getShopHypixelV2QuickBuyItems()
   * @see PlayerProperties#setShopHypixelV2QuickBuyItems(ShopItem[])
   */
  public static final String SHOPDESIGN_HYPIXELV2_QUICKBUY_MODIFICATION_DATE = "bedwars:shopdesign_hypixelv2_quickbuy_moddate";

  /**
   * The time in milliseconds of when the last time the player has been hinted that he's using the team chat.
   */
  public static final String TEAMCHAT_USE_LAST_HINT_TIME = "bedwars:teamchat_use_hint_time";

  /**
   * The time in milliseconds of when the last time the player has been hinted that the team chat is currently unavailable as he's in solo.
   */
  public static final String TEAMCHAT_UNAVAILABLE_SOLO_LAST_HINT_TIME = "bedwars:teamchat_solo_hint_time";

  /**
   * The amount of past wins that were obtained within a certain round time.
   * <p>
   *  Related to the "stats-antiabuse-enabled" config.
   * </p>
   */
  public static final String ANTI_STATS_ABUSE_QUICK_WINS_COUNT = "bedwars:anti_stats_abuse_wins_count";

  private DefaultPlayerProperty() {
  }
}