package de.marcely.bedwars.api.game.shop;

import de.marcely.bedwars.api.game.specialitem.SpecialItemType;
import de.marcely.bedwars.api.event.player.PlayerBuyInShopEvent;
import org.bukkit.entity.Player;

public enum ShopOpenCause {

  /**
   * Player clicked on a dealer that has been spawned with /bw summon dealer
   */
  DEALER,

  /**
   * Player clicked on a dealer that has been spawned with {@link SpecialItemType#MINI_SHOP}
   */
  MINI_SHOP,

  /**
   * Player clicked on a normal villager hologram
   */
  VILLAGER,

  /**
   * Player changed the page
   */
  CHANGE_PAGE,

  /**
   * Page has been refreshed (After a purchase)
   */
  REFRESH,

  /**
   * The shop was opened using a command
   */
  COMMAND,

  /**
   * Player potentially didn't actually open the shop, he just bought a ShopItem via the API.
   * <p>
   *   Exists due to {@link PlayerBuyInShopEvent#getOpenCause()} and {@link ShopItem#buy(Player, boolean)}.
   * </p>
   */
  API_ITEM_BUY,

  /**
   * A plugin opened the GUI
   */
  PLUGIN;

  /**
   * Get whether the player opened the shop for the first time
   * and not by e.g. changing the page.
   *
   * @return <code>true</code> if the player opened the shop for the first time
   */
  public boolean isInitial() {
    switch (this) {
      case DEALER:
      case MINI_SHOP:
      case VILLAGER:
      case COMMAND:
      case PLUGIN:
        return true;
      default:
        return false;
    }
  }
}
