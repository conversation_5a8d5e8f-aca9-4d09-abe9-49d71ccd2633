package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.exception.ArenaBuildException;
import de.marcely.bedwars.tools.location.XYZ;
import org.bukkit.World;

/**
 *
 * Can be obtained with {@link GameAPI#createArena()}
 */
public interface ArenaBuilder {

  /**
   * The regeneration type of an arena represents its form or its general behavior
   *
   * @param regenType The regeneration type for the arena
   * @return This ArenaBuilder instance
   */
  ArenaBuilder setRegenerationType(RegenerationType regenType);

  /**
   * Can be used for:<br>
   * - {@link RegenerationType#WORLD},<br>
   * - {@link RegenerationType#REGION},<br>
   * - {@link RegenerationType#VOTING}
   *
   * @param name The name for the new arena
   * @return This ArenaBuilder instance
   */
  ArenaBuilder setName(String name);

  /**
   * Can be used for:<br>
   * - {@link RegenerationType#WORLD},<br>
   * - {@link RegenerationType#REGION}
   *
   * @param author An author of the arena
   * @return This ArenaBuilder instance
   */
  ArenaBuilder addAuthor(String author);

  /**
   * Can be used for:<br>
   * - {@link RegenerationType#WORLD},<br>
   * - {@link RegenerationType#REGION}
   *
   * @param world The world for the new arena
   * @return This ArenaBuilder instance
   */
  ArenaBuilder setWorld(World world);

  /**
   * Can be used for:<br>
   * - {@link RegenerationType#REGION}
   *
   * @param loc The location of the first corner
   * @return This ArenaBuilder instance
   */
  ArenaBuilder setLocation1(XYZ loc);

  /**
   * Can be used for:<br>
   * - {@link RegenerationType#REGION}
   *
   * @param loc The location of the second corner
   * @return This ArenaBuilder instance
   */
  ArenaBuilder setLocation2(XYZ loc);

  /**
   *
   * Takes all given parameters and constructs the arena
   *
   * @return The built arena
   * @throws ArenaBuildException Can occur when e.g. the name is already taken or some parameters aren't correct
   */
  Arena finish() throws ArenaBuildException;
}
