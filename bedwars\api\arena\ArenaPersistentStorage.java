package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.tools.PersistentStorage;
import java.io.Reader;
import java.io.Writer;
import java.util.Optional;
import java.util.Set;

/**
 * Represents the persistent storage of an arena.
 * <p>
 *   It also may be synchronized between arenas. Access it using {@link RemoteArena#getPersistentStorage()}.
 *   For local arenas, you may use {@link Arena#getPersistentStorage()}.
 * </p>
 */
public interface ArenaPersistentStorage extends PersistentStorage {

  /**
   * Gets the RemoteArena to which this instance matches.
   * <p>
   *   You may use {@link RemoteArena#getLocal()} to access the local instance, if this instance matches a local arena.
   * </p>
   *
   * @return The matching arena
   */
  RemoteArena getArena();

  /**
   * Gets all the keys that exist within this storage.
   * <p>
   *   Note that this may not include all of them if the {@link RemoteArena#isLocal()} of {@link #getArena()} returns false.
   *   Reason being, that it is possible to disable the synchronization using {@link #setSynchronizedFlag(String, boolean)}.
   * </p>
   *
   * @return All known keys
   */
  Set<String> getKeys();

  /**
   * Gets the value of the key.
   * <p>
   *   Note that this may return an empty result if the {@link RemoteArena#isLocal()} of {@link #getArena()} returns false,
   *   even if the key exists on the server that handles the arena.
   *   Reason being, that it is possible to disable the synchronization using {@link #setSynchronizedFlag(String, boolean)}.
   * </p>
   *
   * @return The value that matches the key. May be empty if there's no value
   */
  Optional<String> get(String key);

  /**
   * Sets the value for the key.
   * <p>
   *   Note that you should set {@link #setSynchronizedFlag(String, boolean)} before you set the value.
   *   Without specified, the key is being synchronized between servers by default.
   * </p>
   * <p>
   *   Following limitations exist:<br>
   *   - Key may not be longer than 255 characters<br>
   *   - Key must only persist of the following characters: <code>a-Z 0-9 ?!_-'#:@</code><br>
   *   - Value may not be longer than 65534 characters
   * </p>
   *
   * @param key The key of the value with which you may identify the value later on
   * @param value The value that you want to store
   * @throws IllegalArgumentException If the limitations aren't met
   */
  void set(String key, String value);

  /**
   * Removes everything we know about a given key-value pair.
   *
   * @param key The key that we want to remove
   * @return <code>true</code> if it has been found and removed
   */
  boolean remove(String key);

  /**
   * Define whether a certain key-value pair shall be synchronized between servers.
   * <p>
   *   This is only interesting for servers that actually make use of the Enhanced ProxySync addon.
   * </p>
   * <p>
   *   By default, it is enabled for all arenas. However, you may disable it for certain key-value pairs to reduce traffic.
   *   You should, however, set this before you actually set the value.
   * </p>
   *
   * @param key The key of the key-value pair
   * @param synchronize Whether synchronization accross servers shall be disabled or enabled for the key
   * @throws UnsupportedOperationException If you are trying to access this for arenas that are remote
   */
  void setSynchronizedFlag(String key, boolean synchronize);

  /**
   * Get whether the key-value pair shall be synchronized between servers.
   * <p>
   *   This is only interesting for servers that actually make use of the Enhanced ProxySync addon.
   * </p>
   * <p>
   *   By default, it is enabled for all arenas. However, you may disable it for certain key-value pairs to reduce traffic.
   *   You should, however, set this before you actually set the value.
   * </p>
   *
   * @param key The key of the key-value pair
   * @return <code>true</code> if they are being synchronized between servers
   */
  boolean getSynchronizedFlag(String key);

  /**
   * Serializes this every entry of this instance into a JsonObject String.
   * <p>
   *   Mainly used for internal purposes.
   * </p>
   *
   * @param writer The writer to which the JSON string will be written into
   * @throws UnsupportedOperationException If you are trying to access this for arenas that are remote
   */
  void serialize(Writer writer);

  /**
   * Deserializes the JSON object string from {@link #serialize(Writer)}.
   * <p>
   *   Mainly used for internal purposes.
   * </p>
   *
   * @param reader The reader that holds the JsonObject that we want to deserialize
   * @throws Exception JsonObject has an invalid format
   * @throws UnsupportedOperationException If you are trying to access this for arenas that are remote
   */
  void deserialize(Reader reader) throws Exception;
}
