package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.event.player.PlayerQuitArenaEvent;
import de.marcely.bedwars.api.game.spectator.KickSpectatorReason;
import org.bukkit.entity.Player;

/**
 * The reason why a player has left an arena.
 * <p>
 *  Used by inter alia {@link Arena#kickPlayer(org.bukkit.entity.Player, KickReason)} and
 *  {@link PlayerQuitArenaEvent#getReason()}
 * </p>
 */
public enum KickReason {

  /**
   * Player left the arena
   */
  LEAVE,

  /**
   * Player disconnected from the server
   */
  SERVER_DISCONNECT,

  /**
   * Player teleported out of the arena (e.g. by using a command, or by a plugin)
   */
  TELEPORT,

  /**
   * Player got kicked by e.g. /bw kick
   */
  KICK,

  /**
   * Arena is being stopped
   */
  ARENA_STOP,

  /**
   * Server/plugin is about to stop or to be reloaded
   */
  PLUGIN_STOP,

  /**
   * Player died ingame while his bed was broken
   */
  GAME_LOSE,

  /**
   * Player got kicked as the game ended (e.g. post {@link ArenaStatus#END_LOBBY} phase)
   */
  GAME_END,

  /**
   * Player got kicked during lobby phase as someone with a higher role wants to join the full arena
   */
  LOBBY_NEED_SLOT,

  /**
   * Voting has ended and player got moved to the new arena
   */
  VOTING_SWITCH_ARENA,

  /**
   * A party plugin caused the player to switch from one arena to another
   */
  PARTY_SWITCH_ARENA,

  /**
   * {@link de.marcely.bedwars.api.remote.AddRemotePlayerInfo#setForcefully(boolean)} forced him to switch to another arena
   */
  FORCE_SWITCH_ARENA,

  /**
   * Player wants to spectate, even though he is still a part of match, whereby he gets kicked from the running match
   */
  SPECTATE,

  /**
   * Player used the {@link de.marcely.bedwars.api.game.spectator.SpectatorItemHandlerType#NEXT_ROUND} item
   */
  SPECTATE_ITEM_NEXT_ROUND,

  /**
   * Doesn't eliminate the team and doesn't stop the game. Only for debugging purposes.
   */
  @Deprecated
  DEBUG,

  /**
   * A plugin has kicked the player
   */
  PLUGIN;


  /**
   * The used reason for when a playing spectator gets kicked.
   * <p>
   *   It is possible for players to be spectators and regular players at the same time (e.g. during death spectate).
   *   In case the player gets kicked (using e.g. {@link Arena#kickPlayer(Player)}), the spectating instance gets
   *   kicked as well. The matching {@link KickSpectatorReason} gets obtained using this method.
   * </p>
   *
   * @return Used spectator kick reason when the player gets kicked
   */
  public KickSpectatorReason getMatchingSpectatorKickReason() {
    switch (this) {
      case PLUGIN:
        return KickSpectatorReason.PLUGIN;
      case SPECTATE_ITEM_NEXT_ROUND:
        return KickSpectatorReason.CHANGE_ARENA;
      case FORCE_SWITCH_ARENA:
        return KickSpectatorReason.FORCE_SWITCH_ARENA;
      case PARTY_SWITCH_ARENA:
        return KickSpectatorReason.FOLLOW_PARTY;
      case KICK:
        return KickSpectatorReason.KICK;
      case SERVER_DISCONNECT:
        return KickSpectatorReason.SERVER_DISCONNECT;
      case LEAVE:
      default:
        return KickSpectatorReason.LEAVE;
    }
  }

  /**
   * Get whether the player's stored inventory does get applied with this reason.
   * <p>
   *   Depending on the server's configuration, the players inventory is being backed up before he spectates an arena.
   *   And when he leaves, it is possibly being applied again. This might not happen every time, as this could possibly cause bugs.
   * </p>
   *
   * @return Whether the player's backed up inventory would get applied again
   */
  public boolean isApplyingStoredInventory() {
    switch (this) {
      case VOTING_SWITCH_ARENA:
      case SPECTATE:
      case SPECTATE_ITEM_NEXT_ROUND:
        return false;
      default:
        return true;
    }
  }

  /**
   * Get whether the player is being teleported to the hub with this reason.
   * <p>
   *   It may not neccessary, or might even cause bugs, if the player would get teleported in these scenarios.
   * </p>
   * <p>
   *   Note that other factors
   * </p>
   *
   * @return Whether the player would get teleported with this reason
   */
  public boolean isTeleportingToHub() {
    switch (this) {
      case VOTING_SWITCH_ARENA:
      case SPECTATE:
      case SPECTATE_ITEM_NEXT_ROUND:
      case PARTY_SWITCH_ARENA:
      case FORCE_SWITCH_ARENA:
        return false;
      default:
        return true;
    }
  }

  /**
   * Get whether a player will be allowed to rejoin the arena with this reason.
   *
   * <p>
   *   If players are only allowed to rejoin if they get disconnected from the server
   * </p>
   *
   * @return Whether the player will be allowed to rejoin the arena
   */
  public boolean isRejoinPermitted() {
    switch (this) {
      case SERVER_DISCONNECT:
      case TELEPORT:
      case SPECTATE:
      case PLUGIN:
      case PARTY_SWITCH_ARENA:
      case FORCE_SWITCH_ARENA:
      case DEBUG:
        return true;
      default:
        return false;
    }
  }

  /**
   * Get whether leaving a running match with this reason is considered a rage quit.
   * <p>
   *   Rage quit (configurable by the server admin) refers to leaving right before dying,
   *   whereby stats are still being counted.
   * </p>
   *
   * @return Whether leaving with this reason is considered as a rage quit
   */
  public boolean isRageQuit() {
    switch (this) {
      case SERVER_DISCONNECT:
      case TELEPORT:
      case LEAVE:
      case SPECTATE:
      case PLUGIN:
        return true;
      default:
        return false;
    }
  }
}
