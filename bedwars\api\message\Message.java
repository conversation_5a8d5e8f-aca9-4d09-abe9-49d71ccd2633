package de.marcely.bedwars.api.message;

import de.marcely.bedwars.api.remote.RemotePlayer;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import java.util.Arrays;
import java.util.List;
import org.bukkit.command.CommandSender;
import org.jetbrains.annotations.Nullable;

/**
 * Easily hook into the messages system of MBedwars using this class.
 *
 * <p>
 *     By this, you're e.g. able to work with messages from the messages file or able to easily replace placeholders / chatcolors etc.
 *     Start with {@link #build(String)}, {@link #buildByKey(String)} or {@link #buildByKey(String, String)}
 * </p>
 */
public interface Message extends Cloneable {

  /**
   * This method is essential for the message processor.
   * It returns the String that shall be processed
   *
   * @param locale Tries to find the message in the given locale. If it doesn't find any or if locale=null then it'll pick the default one
   * @return The raw/unformatted message
   */
  String getRawMessage(@Nullable String locale);

  /**
   * Get the string that has been inserted.
   * <p>
   *   The string can later be parsed to the same message using {@link #build(String)}.
   *   It may does not match *exactly* when this instance has been constructed i.a. {@link #buildByKey(String)}.
   *   In that case, it is being surrounded with % signs, to make it parseable by {@link #build(String)}.
   * </p>
   *
   * @return The input string that has been inserted during the construction of this instance
   */
  String getInputString();

  /**
   * Returns the default string in case no matching message has been found by the key.
   * <p>
   *   This only applies when {@link #buildByKey(String, String)} has been used.
   * </p>
   *
   * @return The default string. <code>null</code> if none is being used
   */
  @Nullable
  String getDefaultString();

  /**
   * Adds a placeholder to the message.
   * <p>
   *  It'll later reformat it from {key} to the value.
   * </p>
   *
   *
   * @param key The name of the placeholder
   * @param value What the placeholder represents
   * @return This instance
   */
  Message placeholder(String key, String value);

  /**
   * Adds a placeholder to the message.
   * <p>
   *  It'll later reformat it from {key} to the value.
   * </p>
   *
   * @param key The name of the placeholder
   * @param value What the placeholder represents
   * @return This instance
   */
  default Message placeholder(String key, Object value) {
    return placeholder(key, value.toString());
  }

  /**
   * Obtains the value of a placeholder that has been previously added.
   *
   * @param key The key of the placeholder
   * @return Its value. <code>null</code> if it hasn't been added yet
   */
  @Nullable
  public String getPlaceholder(String key);

  /**
   * Removes all previously added placeholders to this instance.
   */
  void clearPlaceholders();

  /**
   * Will put the message into the MessageProccessor and returns its result.
   * <p>
   * 	Message instances are stored in a pool to decrease memory usage.<br>
   * 	By that they must be putten back when not used anymore.
   * 	Or in other words: set "freeInstance" to false when you want to store this instance inside the memory
   * </p>
   * <p>
   *     This method also automatically replaces PAPI placeholders, in case this feature is enabled the user.
   * </p>
   *
   * @param sender Will take his language, otherwise the default language when null
   * @param freeInstance If it should put this instance back into the pool or not
   * @return The final String
   */
  String done(@Nullable CommandSender sender, boolean freeInstance);

  /**
   * Same as {@link #done(CommandSender, boolean)} and puts this instance automatically back into the pool
   *
   * @param sender Will take his language, otherwise the default language when null
   * @return The final String
   */
  default String done(@Nullable CommandSender sender) {
    return done(sender, true);
  }

  /**
   * Will put the message into the MessageProccessor and returns its result.
   * <p>
   * 	Message instances are stored in a pool to decrease memory usage.<br>
   * 	By that they must be putten back when not used anymore.
   * 	Or in other words: set "freeInstance" to false when you want to store this instance inside the memory
   * </p>
   * <p>
   *     This method also automatically replaces PAPI placeholders, in case this feature is enabled the user.
   * </p>
   *
   * @param sender Will take his language, otherwise the default language when null
   * @param freeInstance If it should put this instance back into the pool or not
   * @return The final String
   */
  String done(@Nullable RemotePlayer sender, boolean freeInstance);

  /**
   * Same as {@link #done(RemotePlayer, boolean)} and puts this instance automatically back into the pool
   *
   * @param sender Will take his language, otherwise the default language when null
   * @return The final String
   */
  default String done(@Nullable RemotePlayer sender) {
    return done(sender, true);
  }

  /**
   * Will put the message into the MessageProccessor and returns its result.
   * <p>
   * 	Message instances are stored in a pool to decrease memory usage.<br>
   * 	By that they must be putten back when not used anymore.
   * 	Or in other words: set "freeInstance" to false when you want to store this instance inside the memory
   * </p>
   * <p>
   *     This method also automatically replaces PAPI placeholders, in case this feature is enabled the user.
   * </p>
   *
   * @param sender Will take his language, otherwise the default language when null
   * @param freeInstance If it should put this instance back into the pool or not
   * @return The final String
   */
  String done(@Nullable CommandSenderWrapper sender, boolean freeInstance);

  /**
   * Same as {@link #done(CommandSenderWrapper, boolean)} and puts this instance automatically back into the pool
   *
   * @param sender Will take his language, otherwise the default language when null
   * @return The final String
   */
  default String done(@Nullable CommandSenderWrapper sender) {
    return done(sender, true);
  }

  /**
   * Same as {@link #done(CommandSender, boolean)}.
   * <p>
   *   We will fallback to the default language if the locale is not found.
   *   See {@link MessageAPI#getStoredLocales()} to see a list of all available locales.
   * </p>
   *
   * @param locale The name of the locale that shall be used
   * @param freeInstance If it should put this instance back into the pool or not
   * @return The final String
   */
  String done(String locale, boolean freeInstance);

  /**
   * Same as {@link #done(String, boolean)} and puts this instance automatically back into the pool
   *
   * @param locale The name of the locale that shall be used
   * @return The final String
   */
  default String done(String locale) {
    return done(locale, true);
  }

  /**
   * Will put the message into the MessageProccessor and returns its result.
   * <p>
   * 	Message instances are stored in a pool to decrease memory usage.<br>
   * 	By that they must be putten back when not used anymore.
   * 	Or in other words: set "freeInstance" to false when you want to store this instance inside the memory
   * </p>
   * <p>
   *     This method also automatically replaces PAPI placeholders, in case this feature is enabled the user.
   * </p>
   *
   * @param freeInstance If it should put this instance back into the pool or not
   * @return The final String
   */
  default String done(boolean freeInstance) {
    return done((CommandSender) null, freeInstance);
  }

  /**
   * Will put the message into the MessageProccessor and returns the result.
   * <p>
   * Does the same as {@link Message#done(CommandSender)}, but passes null as sender.<br>
   * Also puts this instance automatically back into the pool
   *
   * @return The final String
   */
  default String done() {
    return done((CommandSender) null, true);
  }

  /**
   * Computes it and sends it to the person
   *
   * @param sender The target
   */
  default void send(CommandSender sender) {
    send(sender, true);
  }

  /**
   * Computes it and sends it to the person
   *
   * @param sender The target
   */
  default void send(CommandSenderWrapper sender) {
    send(sender, true);
  }

  /**
   * Computes it and sends it to the person
   *
   * @param sender The target
   */
  default void send(RemotePlayer sender) {
    send(sender, true);
  }

  /**
   * Computes it and sends it to the person
   *
   * @param sender The target
   * @param freeInstance If it should put this instance back into the pool or not
   */
  void send(CommandSender sender, boolean freeInstance);

  /**
   * Computes it and sends it to the person
   *
   * @param sender The target
   * @param freeInstance If it should put this instance back into the pool or not
   */
  void send(CommandSenderWrapper sender, boolean freeInstance);

  /**
   * Computes it and sends it to the person
   *
   * @param sender The target
   * @param freeInstance If it should put this instance back into the pool or not
   */
  void send(RemotePlayer sender, boolean freeInstance);

  /**
   * Messages are stored in a pool to reduce memory usage.
   * Invoke this method when you're done using it and want to put it back manually.<br>
   * Keep in mind that the methods {@link #done()} and {@link #done(CommandSender)} automatically are putting
   * this instance back into memory unless told otherwise using the parameter "freeInstance".
   *
   * @return <code>false</code> if it's already in the pool
   */
  boolean free();

  /**
   * Returns whether the Message instance has been put back to the pool or not.
   *
   * @return If it has been freed by now
   */
  boolean isFreed();

  /**
   * Clones the current Message and creates a non-freeable variant.
   * <p>
   *     You may use it in sensible parts where you want to make sure that the instance 100% doesn't get freed at any moment.
   * </p>
   *
   * @return A new instance that can't be put back into the pool
   */
  Message cloneNonUpcyable();

  /**
   * Clones an exact replica.
   *
   * @return A cloned instance
   */
  Message clone();

  /**
   * Creates an instance of a message.
   * <p>
   *  It's being taken from a pool for greater performance.<br>
   *  Make sure that you won't use the instance after you ran {@link Message#done()} or
   *  {@link Message#done(CommandSender)}. Otherwise the system will break.
   * </p>
   *
   * @param rawMessage The raw/unformatted message containing the placeholders, color codes etc.
   * @return An instance of Message to pass parameters
   */
  static Message build(String rawMessage) {
    return BedwarsAPILayer.INSTANCE.newMessageInstance(rawMessage);
  }

  /**
   * Creates an instance of a message that persists of multiple lines.
   * <p>
   *  It's being taken from a pool for greater performance.<br>
   *  Make sure that you won't use the instance after you ran {@link Message#done()} or
   *  {@link Message#done(CommandSender)}. Otherwise the system will break.
   * </p>
   *
   * @param lines The lines in a raw/unformatted format containing the placeholders, color codes etc.
   * @return An instance of Message to pass parameters
   */
  static Message build(String... lines) {
    return build(Arrays.asList(lines));
  }

  /**
   * Creates an instance of a message that persists of multiple lines.
   * <p>
   *  It's being taken from a pool for greater performance.<br>
   *  Make sure that you won't use the instance after you ran {@link Message#done()} or
   *  {@link Message#done(CommandSender)}. Otherwise the system will break.
   * </p>
   *
   * @param lines The lines in a raw/unformatted format containing the placeholders, color codes etc.
   * @return An instance of Message to pass parameters
   */
  static Message build(List<String> lines) {
    return BedwarsAPILayer.INSTANCE.newMessageInstance(lines);
  }

  /**
   * Creates an instance of a message.
   * <p>
   *  It's being taken from a pool for greater performance.
   *  Make sure that you won't reuse the instance after you ran {@link Message#done()} or
   *  {@link Message#done(CommandSender)}. Otherwise the system will break.<br><br>
   *  The difference to the {@link Message#build(String)} method is that this one will first
   *  try to get the key from the messages file. It'll use the given def String if it hasn't been found.
   * </p>
   *
   * @param key Will look for this key in the messages file
   * @param def Uses this if it hasn't found it in the messages file
   * @return An instance of Message to pass parameters
   */
  static Message buildByKey(String key, String def) {
    return BedwarsAPILayer.INSTANCE.newMessageInstance(key, def);
  }

  /**
   * Creates an instance of a message.
   * <p>
   *  It's being taken from a pool for greater performance.
   *  Make sure that you won't reuse the instance after you ran {@link Message#done()} or
   *  {@link Message#done(CommandSender)}. Otherwise the system will break.<br><br>
   *  The difference to the {@link Message#build(String)} method is that this one will first
   *  try to get the key from the messages file. It'll use the given def String if it hasn't been found
   * <br>
   *  Uses they key itself if it does not exist.
   * </p>
   *
   * @param key Will look for this key in the messages file
   * @return An instance of Message to pass parameters
   */
  static Message buildByKey(String key) {
    return buildByKey(key, key);
  }
}