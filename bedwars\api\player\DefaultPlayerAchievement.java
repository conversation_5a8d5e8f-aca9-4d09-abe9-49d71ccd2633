package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.unsafe.InitBedwarsAPILayer;

/**
 * Contains all default achievements of the bedwars plugin
 */
@SuppressWarnings("deprecation")
public class DefaultPlayerAchievement {

  public static final PlayerAchievement RAGE_QUIT = get("rage_quit");
  public static final PlayerAchievement WIN_ROUND = get("win_round");
  public static final PlayerAchievement LOSE_ROUND = get("lose_round");
  public static final PlayerAchievement WIN_WITHIN_TIME = get("win_within_time");
  public static final PlayerAchievement WIN_100_ROUNDS = get("win_100_rounds");
  public static final PlayerAchievement WIN_WITHOUT_BED = get("win_without_bed");

  public static final PlayerAchievement OWN_BED_DESTROYED = get("own_bed_destroyed");
  public static final PlayerAchievement OP_BOW = get("op_bow");
  public static final PlayerAchievement KILL_WITH_BOW = get("kill_with_bow");
  public static final PlayerAchievement KILL_WITH_HALF_HEART = get("kill_with_half_heart");
  public static final PlayerAchievement DIE_10_SECONDS_AFTER_BED_DESTRUCTION = get("die_10secs_bed_destructed");

  public static final PlayerAchievement USE_RESCUE_PLATFORM = get("use_rescue_platform");
  public static final PlayerAchievement USE_ENDERPEARL = get("use_enderpearl");
  public static final PlayerAchievement USE_MINISHOP = get("use_minishop");
  public static final PlayerAchievement USE_BRIDGE = get("use_bridge");
  public static final PlayerAchievement USE_GUARD_DOG = get("use_guarddog");
  public static final PlayerAchievement USE_MAGNET_SHOES = get("use_magnet_shoes");
  public static final PlayerAchievement USE_TELEPORTER = get("use_teleporter");
  public static final PlayerAchievement USE_TNT_SHEEP = get("use_tntsheep");
  public static final PlayerAchievement USE_TRACKER = get("use_tracker");
  public static final PlayerAchievement PLACE_TRAP = get("place_trap");
  public static final PlayerAchievement WALK_OVER_TRAP = get("walk_over_trap");
  public static final PlayerAchievement USE_FIREBALL = get("use_fireball");
  public static final PlayerAchievement USE_INVIS_POTION = get("use_invis_potion");

  public static final PlayerAchievement SPEND_200_RESOURCES = get("spend_200_resources");
  public static final PlayerAchievement RANKING_TOP_3 = get("ranking_top_3");
  public static final PlayerAchievement GOOD_KD = get("good_kd");
  public static final PlayerAchievement HIGH_PLAY_TIME = get("high_play_time");

  public static final PlayerAchievement WRITE_GG_IN_ENDLOBBY = get("write_gg_endlobby");
  public static final PlayerAchievement OBTAIN_EVERY_ACHIEVEMENT = get("obtain_every_achievement");

  private DefaultPlayerAchievement() {
  }

  private static PlayerAchievement get(String id) {
    return InitBedwarsAPILayer.INSTANCE.getDefaultAchievement(id);
  }
}
