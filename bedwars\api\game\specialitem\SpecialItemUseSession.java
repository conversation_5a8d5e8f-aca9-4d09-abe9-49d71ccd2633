package de.marcely.bedwars.api.game.specialitem;

import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a session of a special item use<br>
 * A new session is created every time someone uses a special item.<br>
 * Make sure to call {@link #stop()}, even when you're handler, after you're done as
 * the plugin will invoke {@link #handleStop()} for EVERY still active session after the round ended.<br>
 * Also make sure to invoke {@link #takeItem()} if you wish the item to be taken away
 */
@SuppressWarnings("deprecation")
public abstract class SpecialItemUseSession {

  private PlayerUseSpecialItemEvent event;

  public SpecialItemUseSession(PlayerUseSpecialItemEvent event) {
    this.event = event;
  }

  /**
   * Forcefully stops and cleans the session
   */
  protected abstract void handleStop();

  /**
   * Returns true when {@link #stop()} hasn't been called yet
   *
   * @return Whether or not the session is still active
   */
  public boolean isActive() {
    return this.event != null;
  }

  /**
   * Returns the event/session details
   *
   * @return The event included in this session.<code>null</code> when {@link #isActive()} returns false
   */
  @Nullable
  public PlayerUseSpecialItemEvent getEvent() {
    return this.event;
  }

  /**
   * Safely stops and cleans the session<br>
   * It's safe to call this even if it hasn't been passed yet to {@link SpecialItemUseHandler#openSession(PlayerUseSpecialItemEvent)}
   *
   * @return <code>false</code> if it already has been stopped
   */
  public boolean stop() {
    if (!this.isActive())
      return false;

    try {
      handleStop();
    } catch (Throwable t) {
      t.printStackTrace();
    }

    BedwarsAPILayer.INSTANCE.removeSpecialItemUseSession(this);
    this.event = null;

    return true;
  }

  /**
   * Takes a special item from the players inventory
   *
   * @return <code>false</code> if it failed as he doesn't have any special items of that type anymore
   * @throws IllegalStateException When the session already has been stopped
   */
  public boolean takeItem() {
    if (!this.isActive())
      throw new IllegalStateException("The session isn't active anymore");

    return BedwarsAPILayer.INSTANCE.takeItemSpecialItemUseSession(this);
  }
}
