package de.marcely.bedwars.api.player;

import org.jetbrains.annotations.Nullable;

import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;

/**
 * An instance represents a mutable collection of stats of a player.
 * <p>
 *     Generally there are 2 cached instances per player: One that holds the stats of a game, and the other of the total ones.
 *     Use {@link #isGameStats()} and {@link #getGameStats()} to differentiate them.
 * </p>
 */
public interface PlayerStats {

  /**
   * The UUID of the involved player
   *
   * @return The UUID of the player who owns the stats.
   */
  UUID getPlayerUUID();

  /**
   * Get whether this stats instance holds the stats of a running match, and not the total ones.
   *
   * @return Whether these stats are about the round that the player currently plays.
   */
  boolean isGameStats();

  /**
   * Will return the stats of the round that the player currently plays.
   *
   * @return Can return <code>null</code> when {@link PlayerStats#isGameStats()} returns true or the player isn't playing right now
   */
  @Nullable PlayerStats getGameStats();

  /**
   * Will reset the game stats.
   * <p>
   * 	It does not matter if you're calling this with the game stats or the normal stats
   * </p>
   */
  void resetGameStats();

  /**
   * Looks for the stats entry with that key.
   * <p>
   * 	Will use 0 as default.
   * </p>
   * @param key The id/key of the stats entry
   * @return The value that this players. 0 if there are no stats with that key
   */
  Number get(String key);

  /**
   * Sets the value of a stats entry.
   * <p>
   *     Keep in mind that the {@link de.marcely.bedwars.api.event.player.PlayerStatChangeEvent} is likely being dispatched with this.
   * </p>
   *
   * @param key The id/key of the stats entry
   * @param value The value for that entry
   * @return Returns the previous value
   * @throws IllegalArgumentException When key doesn't fit the format
   */
  default Number set(String key, Number value) {
    return set(key, value, AttributeChangeCause.GENERIC);
  }

  /**
   * Sets the value of a stats entry,
   *
   * @param key The id/key of the stats entry
   * @param value The value for that entry
   * @param cause The technical cause for setting this
   * @return Returns the previous value
   * @throws IllegalArgumentException When key doesn't fit the format
   */
  Number set(String key, Number value, AttributeChangeCause cause);

  /**
   * Adds an amount to a stats entry.
   * <p>
   *     Keep in mind that the {@link de.marcely.bedwars.api.event.player.PlayerStatChangeEvent} is likely being dispatched with this.
   * </p>
   *
   * @param key The id/key of the stats entry
   * @param addAmount The amount that shall be added
   * @return Returns the previous value
   * @throws IllegalArgumentException When key doesn't fit the format
   */
  default Number add(String key, Number addAmount) {
    return add(key, addAmount, AttributeChangeCause.GENERIC);
  }

  /**
   * Adds an amount to a stats entry.
   *
   * @param key The id/key of the stats entry
   * @param addAmount The amount that shall be added
   * @param cause The technical cause for setting this
   * @return Returns the previous value
   * @throws IllegalArgumentException When key doesn't fit the format
   */
  Number add(String key, Number addAmount, AttributeChangeCause cause);

  /**
   * Returns all stored entries of this object
   *
   * @return Returns a set of all stored stats
   */
  Set<Entry<String, Number>> entrySet();

  /**
   * Any changes made to this instance will not be saved, even if tried.
   * <p>
   *   While it is possible to change the values, MBedwars' auto-saving
   *   and {@link #save()} will not actually do anything and any changes
   *   will be lost with the next loading.
   * </p>
   * <p>
   *   Reasons for read-only are:
   *   <ul>
   *     <li>We failed to load it fully (due to an error).
   *     Reasons for that may include e.g. a disconnection to the storage server.</li>
   *     <li>The player might be online on another server and we
   *     don't want to intercept with whatever the other server is doing.
   *     Although cross-server support can be obtained using the ProxySync addon and
   *     making sure that player-data-syncing is enabled within it, in which case this
   *     instance won't be set as read-only.</li>
   *   </ul>
   *
   * @return Whether this instance is read-only and changes won't be saved
   */
  boolean isReadOnly();

  /**
   * Sets whether this instance is read-only.
   * <p>
   *   This mechanism exists to prevent desyncs between multiple servers.
   *   Use it with caution! Read {@link #isReadOnly()} for more info.
   * </p>
   *
   * @param readOnly Whether this instance should be read-only
   */
  void setReadOnly(boolean readOnly);

  /**
   * Asynchronously saves these stats.
   * <p>
   * 	It's usually not needed to call this method when the player is currently on the server as the plugin will already handle it
   * </p>
   */
  default void save() {
    save(null);
  }

  /**
   * Asynchronously saves these stats.
   * <p>
   * 	It's usually not needed to call this method when the player is currently on the server as the plugin will already handle it.
   * </p>
   * <p>
   * 	<b>Important:</b> The callback doesn't get synced to the main thread.
   * </p>
   * @param callback Gets called when the operation was finished
   */
  void save(@Nullable Runnable callback);
}