package de.marcely.bedwars.api.world.hologram.controller;

import de.marcely.bedwars.api.arena.picker.condition.ArenaConditionGroup;
import de.marcely.bedwars.api.world.hologram.HologramController;
import de.marcely.bedwars.api.world.hologram.HologramEntity;
import de.marcely.bedwars.api.world.hologram.HologramControllerType;
import de.marcely.bedwars.tools.LazyReference;
import org.jetbrains.annotations.Nullable;

/**
 * Extending API if {@link HologramControllerType#ARENASGUI_OPENER} is used.
 * <p>
 *   Cast {@link HologramEntity#getController()} to this class to access the methods.
 * </p>
 */
public interface ArenasGUIController extends HologramController {

  /**
   * Get the id of the arenasgui layout that will be opened when clicked on.
   *
   * @return The layout id opened when clicked
   */
  String getLayoutId();

  /**
   * Set the id of the arenasgui layout that will be opened when clicked on.
   *
   * @param layoutId The layout id opened when clicked
   */
  void setLayoutId(String layoutId);

  /**
   * Get the name of the hologram that will be displayed above his head.
   *
   * @return The name of the hologram
   */
  String getName();

  /**
   * Set the name of the hologram that will be displayed above his head.
   * <p>
   *   Make sure to call {@link #applyProperties()} after changing the name to update the hologram.
   * </p>
   *
   * @param name The name of the hologram
   */
  void setName(String name);

  /**
   * Get the condition that may optionally be used to limit the arenas shown in the GUI.
   * <p>
   *   It returns a LazyReference as the condition may only be loaded when needed,
   *   to give addons time to register their custom variables.
   * </p>
   *
   * @return The condition used to limit the arenas shown in the GUI. May be <code>null</code> if there is none
   */
  @Nullable
  LazyReference<ArenaConditionGroup> getArenasCondition();

  /**
   * Set your own condition that may optionally be used to limit the arenas shown in the GUI.
   *
   * @param condition The condition used to limit the arenas shown in the GUI. May be <code>null</code> if there is none
   */
  void setArenasCondition(@Nullable ArenaConditionGroup condition);
}
