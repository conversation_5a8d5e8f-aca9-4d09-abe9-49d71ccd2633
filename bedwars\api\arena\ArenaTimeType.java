package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.tools.Helper;
import org.bukkit.inventory.ItemStack;

/**
 * A per-arena time that'll be used during a match.
 */
public enum ArenaTimeType {

  /**
   * Player client time will be untouched by MBedwars
   */
  UNTOUCHED,

  /**
   * Player client time will be fixed at noon
   */
  NOON,

  /**
   * Player client time will be fixed at sunset
   */
  SUNSET,

  /**
   * Player client time will be fixed at night
   */
  NIGHT;


  /**
   * Get the ticks that the time should be set to
   *
   * @return The ticks that the time should be set to. <code>-1</code> if untouched
   */
  public int getTicks() {
    switch (this) {
      case UNTOUCHED:
        return -1;
      case NOON:
        return 5000;
      case SUNSET:
        return 12200;
      case NIGHT:
        return 18000;
      default:
        throw new IllegalStateException("Missing implementation");
    }
  }

  /**
   * Get the icon that is being displayed in the properties menu
   *
   * @return The icon used in GUIs
   */
  public ItemStack getIcon() {
    switch (this) {
      case UNTOUCHED:
        return Helper.get().parseItemStack("PAINTING");
      case SUNSET:
        return Helper.get().parseItemStack("PLAYER_HEAD:eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZDY2MzM5ZWQxYmEzOGY0Mzk5MWQzMDM3OTAyYzBhNWUzMjA0MzE1OGFkZDBjOTQ2MTZlYjMyZmVhYmZlNTc5YyJ9fX0=");
      case NOON:
        return Helper.get().parseItemStack("PLAYER_HEAD:eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvM2VmZjQ5ZjQ0ZjNmZWM4OTg4Yzg3OWJkNzlkOTIxNjAwYzBmMmQxNjBiYzE2ZGNlMGVlZDNjZGFlNzc2ZjliZCJ9fX0=");
      case NIGHT:
        return Helper.get().parseItemStack("PLAYER_HEAD:eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZDQwMWM1MWEyODI0ODZiYTBiNWZiYzZjZWU4ZDlkNThiMzk1MjBjNWM0MzkzNTc1Mjk5OTUzYzI3M2JhMGY3MCJ9fX0=");
      default:
        throw new IllegalStateException("Missing implementation");
    }
  }
}
