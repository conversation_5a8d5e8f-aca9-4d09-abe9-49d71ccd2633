package cn.acebrand.acevotemode.events;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * 游戏结束事件
 * 强制结束游戏，设置倒计时
 */
public class GameEndEvent implements CustomEvent {
    
    @Override
    public void execute(AceVoteMode plugin, Arena arena, EventConfig eventConfig) {
        plugin.getLogger().info("Executing game end event in arena: " + arena.getName());
        
        // 广播消息
        if (eventConfig.isShowMessage()) {
            broadcastMessage(arena, eventConfig.getMessage());
        }
        
        // 播放音效
        playSound(arena, org.bukkit.Sound.BLOCK_END_PORTAL_SPAWN, 1.0f, 0.8f);
        
        // 设置游戏剩余时间
        int countdown = eventConfig.getGameEndCountdown();
        arena.setIngameTimeRemaining(countdown);
        
        // 启动倒计时提醒
        startCountdownReminder(plugin, arena, countdown);
        
        plugin.getLogger().info("Set game end countdown to " + countdown + " seconds in arena: " + arena.getName());
    }
    
    @Override
    public String getEventType() {
        return "game-end";
    }
    
    @Override
    public String getEventName() {
        return "游戏结束事件";
    }
    
    /**
     * 启动倒计时提醒
     */
    private void startCountdownReminder(AceVoteMode plugin, Arena arena, int totalSeconds) {
        new BukkitRunnable() {
            private int remainingSeconds = totalSeconds;
            
            @Override
            public void run() {
                // 检查游戏是否还在进行
                if (arena.getStatus() != ArenaStatus.RUNNING) {
                    this.cancel();
                    return;
                }
                
                remainingSeconds--;
                
                // 在特定时间点发送提醒
                if (shouldSendReminder(remainingSeconds)) {
                    sendCountdownMessage(arena, remainingSeconds);
                    playCountdownSound(arena, remainingSeconds);
                }
                
                // 倒计时结束
                if (remainingSeconds <= 0) {
                    sendFinalMessage(arena);
                    playFinalSound(arena);
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒执行一次
    }
    
    /**
     * 判断是否应该发送提醒
     */
    private boolean shouldSendReminder(int seconds) {
        // 在60, 30, 15, 10, 5, 4, 3, 2, 1秒时发送提醒
        return seconds == 60 || seconds == 30 || seconds == 15 || seconds == 10 || 
               (seconds <= 5 && seconds > 0);
    }
    
    /**
     * 发送倒计时消息
     */
    private void sendCountdownMessage(Arena arena, int seconds) {
        String message;
        if (seconds >= 60) {
            int minutes = seconds / 60;
            message = "&c&l游戏将在 &f" + minutes + " &c&l分钟后结束！";
        } else if (seconds >= 10) {
            message = "&c&l游戏将在 &f" + seconds + " &c&l秒后结束！";
        } else {
            message = "&4&l游戏将在 &f&l" + seconds + " &4&l秒后结束！";
        }
        
        broadcastMessage(arena, message);
        
        // 发送Title
        for (Player player : arena.getPlayers()) {
            if (player.isOnline()) {
                String title = "&c&l游戏即将结束";
                String subtitle = "&f" + seconds + " &c秒";
                player.sendTitle(
                    org.bukkit.ChatColor.translateAlternateColorCodes('&', title),
                    org.bukkit.ChatColor.translateAlternateColorCodes('&', subtitle),
                    10, 20, 10
                );
            }
        }
    }
    
    /**
     * 播放倒计时音效
     */
    private void playCountdownSound(Arena arena, int seconds) {
        if (seconds <= 5) {
            // 最后5秒播放紧急音效
            playSound(arena, org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 2.0f);
        } else if (seconds <= 10) {
            // 最后10秒播放警告音效
            playSound(arena, org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.5f);
        } else {
            // 其他时间播放普通提醒音效
            playSound(arena, org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 0.8f, 1.0f);
        }
    }
    
    /**
     * 发送最终消息
     */
    private void sendFinalMessage(Arena arena) {
        broadcastMessage(arena, "&4&l时间到！游戏结束！");
        
        // 发送最终Title
        for (Player player : arena.getPlayers()) {
            if (player.isOnline()) {
                player.sendTitle(
                    org.bukkit.ChatColor.translateAlternateColorCodes('&', "&4&l时间到！"),
                    org.bukkit.ChatColor.translateAlternateColorCodes('&', "&c游戏结束"),
                    20, 60, 20
                );
            }
        }
    }
    
    /**
     * 播放最终音效
     */
    private void playFinalSound(Arena arena) {
        playSound(arena, org.bukkit.Sound.ENTITY_WITHER_DEATH, 1.0f, 1.0f);
        
        // 延迟播放额外音效
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("AceVoteMode"), 
            () -> playSound(arena, org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.8f, 0.8f),
            10L
        );
    }
}
