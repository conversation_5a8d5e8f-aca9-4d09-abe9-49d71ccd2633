package de.marcely.bedwars.tools.gui.type;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.tools.Validate;
import de.marcely.bedwars.tools.gui.GUI;
import de.marcely.bedwars.tools.gui.WriteListener;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;

import java.util.*;
import java.util.function.Consumer;

/**
 * Represents a GUI that allows the player to rename an item
 */
@SuppressWarnings("deprecation")
public class AnvilGUI implements GUI {

  @Getter
  private final List<Consumer<Player>> closeListeners = new ArrayList<>();
  private Map<Player, Inventory> openPlayers = new IdentityHashMap<>();
  private String title = "";
  private String defaultMessage;
  private Set<WriteListener> listeners = new HashSet<>();

  /**
   * Creates a new instance with an empty message
   */
  public AnvilGUI() {
    this("");
  }

  /**
   * Creates a new instance with a default message
   *
   * @param defaultMessage The initial text inside the editor
   */
  public AnvilGUI(String defaultMessage) {
    Validate.notNull(defaultMessage, "defaultMessage");

    this.defaultMessage = defaultMessage;
  }

  @Override
  public void open(Player player) {
    this.openPlayers.put(player, BedwarsAPILayer.INSTANCE.open(this, player));
  }

  @Override
  public void closeAll() {
    for (Player player : this.openPlayers.keySet())
      player.closeInventory();

    this.openPlayers.clear();
  }

  /**
   * Set the title of the GUI.
   * <p>
   * Only supported for 1.14 or higher
   *
   * @param title The new title
   */
  @Override
  public void setTitle(String title) {
    Validate.notNull(title, "title");

    this.title = title;
  }

  @Override
  public String getTitle() {
    return this.title;
  }

  @Override
  public boolean areItemsMoveable() {
    return false;
  }

  @Override
  public Collection<Player> getPlayers() {
    return Collections.unmodifiableCollection(this.openPlayers.keySet());
  }

  @Override
  public boolean hasOpen(Player player) {
    Validate.notNull(player, "player");

    return this.openPlayers.containsKey(player);
  }

  @Override
  public boolean addCloseListener(Consumer<Player> callback) {
    Validate.notNull(callback, "callback");

    if (closeListeners.contains(callback))
      return false;

    return closeListeners.add(callback);
  }

  @Override
  public boolean removeCloseListener(Consumer<Player> callback) {
    Validate.notNull(callback, "callback");

    return closeListeners.remove(callback);
  }

  @Override
  public void clear() {
  }

  @Override
  public void onClose(Player player) {
    this.openPlayers.remove(player);
  }

  /**
   * Returns the default/initial message that will be shown inside the text field
   *
   * @return The text the player has to rename
   */
  public String getDefaultMessage() {
    return this.defaultMessage;
  }

  /**
   * Returns all listeners that were added to this GUI
   *
   * @return All listeners listeners whenever someone writes something in this GUI
   */
  public Set<WriteListener> getListeners() {
    return Collections.unmodifiableSet(this.listeners);
  }

  /**
   * Adds a listener to this GUI
   *
   * @param listener The listener that shall be added
   * @return <code>false</code> if it was already added
   */
  public boolean addListener(WriteListener listener) {
    Validate.notNull(listener, "listener");

    return this.listeners.add(listener);
  }

  /**
   * Removes an already added listener from this GUI
   *
   * @param listener The listener that shall be removed
   * @return <code>true</code> if it has been removed
   */
  public boolean removeListener(WriteListener listener) {
    return this.listeners.remove(listener);
  }
}
