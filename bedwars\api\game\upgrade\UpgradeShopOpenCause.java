package de.marcely.bedwars.api.game.upgrade;

/**
 * Represents the causes of the opening of a upgrade shop.
 */
public enum UpgradeShopOpenCause {

  /**
   * Player clicked on an upgrade dealer that has been spawned with /bw summon upgradedealer
   */
  UPGRADE_DEALER,

  /**
   * The upgrade shop GUI has been refreshed.
   * <p>
   * Refreshes may happen if a team member buys an upgrade, or trap was triggered.
   * </p>
   */
  REFRESH,

  /**
   * A command opened the GUI
   */
  COMMAND,

  /**
   * A plugin opened the GUI
   */
  PLUGIN;


  /**
   * Get whether the player opened the shop for the first time and not by e.g. refreshing the GUI.
   *
   * @return <code>true</code> if the player opened the shop for the first time
   */
  public boolean isInitial() {
    switch (this) {
      case UPGRADE_DEALER:
      case COMMAND:
      case PLUGIN:
        return true;
      default:
        return false;
    }
  }
}
