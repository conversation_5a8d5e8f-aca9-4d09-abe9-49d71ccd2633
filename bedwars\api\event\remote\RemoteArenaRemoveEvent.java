package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemoteArena;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when an arena from another server has been removed from our local collection.
 * <p>
 *     This event also gets called for every arena of a server whenever a server disconnects.
 *     Keep in mind that this event is async.
 * </p>
 */
public class RemoteArenaRemoveEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemoteArena arena;

  public RemoteArenaRemoveEvent(RemoteArena arena) {
    super(true);

    this.arena = arena;
  }

  /**
   * Gets the arena that has been removed.
   *
   * @return The involved arena
   */
  public RemoteArena getArena() {
    return this.arena;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
