package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import java.util.function.Supplier;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.Inventory;

/**
 * Gets called when a player is opening a chest in an arena.
 * <p>
 *   Special chests include private and team chests.
 *   This event also gets called for vanilla chests (without special behavior), e.g. trapped chests
 *   or shulker boxes (furnaces etc. are excluded and do not count as chests).
 *   The exact behavior depends on the configurations of the server.
 *   Cancelling this event will prevent the chest from being opened.
 * </p>
 */
public class PlayerOpenArenaChestEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private final Team team;
  private final Block chestBlock;
  private final ChestType chestType;
  private final Supplier<Inventory> inventory;
  private final OpenPurpose purpose;

  @Getter @Setter
  private boolean cancelled = false;

  // ! Used by PunchToDeposit addon
  public PlayerOpenArenaChestEvent(Player player, Arena arena, Team team, Inventory inventory, Block chestBlock, ChestType chestType, OpenPurpose purpose) {
    this(player, arena, team, () -> inventory, chestBlock, chestType, purpose);
  }

  public PlayerOpenArenaChestEvent(Player player, Arena arena, Team team, Supplier<Inventory> inventory, Block chestBlock, ChestType chestType, OpenPurpose purpose) {
    super(player);

    this.player = player;
    this.arena = arena;
    this.team = team;
    this.inventory = inventory;
    this.chestBlock = chestBlock;
    this.chestType = chestType;
    this.purpose = purpose;
  }

  /**
   * Returns the Arena in which the chest is located
   *
   * @return the arena in which the chest is located
   */
  @Override
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the player's Team
   *
   * @return the Team of the player opening the chest
   */
  public Team getTeam() {
    return this.team;
  }

  /**
   * Return the block the player clicked on to open the chest
   *
   * @return the chest block
   */
  public Block getChestBlock() {
    return this.chestBlock;
  }

  /**
   * Returns the type of chest that has been interacted with.
   *
   * @return the type of chest that has been interacted with
   */
  public ChestType getChestType() {
    return this.chestType;
  }

  /**
   * Returns the purpose of the chest opening.
   *
   * @return The purpose of the chest opening
   */
  public OpenPurpose getPurpose() {
    return this.purpose;
  }

  /**
   * The inventory that holds the contents of the chest.
   * <p>
   *   This inventory is safe to modify. This is the inventory
   *   that is being shown - do not use the inventory of the block itself,
   *   as it may not be the same (if {@link #getChestType()} is not
   *   {@link ChestType#VANILLA}).
   * </p>
   *
   * @return The inventory related to the speciality type of the chest
   */
  public Inventory getInventory() {
    return this.inventory.get();
  }

  /**
   * Returns whether the chest is a team chest
   * <p>
   *   Note that users have team chests disabled in the config, this will always return false.
   * </p>
   *
   * @return If the chest belongs to the team
   * @deprecated Use {@link #getChestType()} instead
   */
  @Deprecated
  public boolean isTeamChest() {
    return this.chestType == ChestType.TEAM;
  }

  /**
   * Returns whether the chest is a private chest
   * <p>
   *   This is usually the case for ender chests, although
   *   admins may turn them into (depending on their configuration) team chests as well.
   * </p>
   *
   * @return If the chest is a private chest
   * @deprecated Use {@link #getChestType()} instead
   */
  @Deprecated
  public boolean isPrivateChest() {
    return this.chestType == ChestType.PRIVATE;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  /**
   * The type of chest that has been interacted with.
   */
  public static enum ChestType {

    /**
     * A regular chest that is not special in any way.
     * <p>
     *   The inventory of the block is being shown when interacted.
     * </p>
     */
    VANILLA,

    /**
     * An inventory only visible to the player himself.
     */
    PRIVATE,

    /**
     * An inventory only visible to members of the same team.
     */
    TEAM;
  }


  /**
   * The purpose of the chest opening.
   */
  public static enum OpenPurpose {

    /**
     * Regularly opening it by interacting with it.
     */
    REGULAR,

    /**
     * Punching the chest to deposit items.
     * <p>
     *   Does not actually open the inventory.
     * </p>
     */
    PUNCH_TO_DEPOSIT;
  }
}
