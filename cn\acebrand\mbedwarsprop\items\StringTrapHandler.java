package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class StringTrapHandler implements SpecialItemUseHandler, Listener {

    private final Plugin plugin;
    private final Map<Location, UUID> trapLocations = new HashMap<>();
    private final ItemConfigManager.ItemConfig config;

    public StringTrapHandler(Plugin plugin, ItemConfigManager.ItemConfig config) {
        this.plugin = plugin;
        this.config = config;
        
        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);
        
        // 运行会话
        session.run();
        
        return session;
    }

    private class Session extends SpecialItemUseSession {
        
        private Block placedBlock;
        
        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }
        
        @Override
        protected void handleStop() {
            if (this.placedBlock == null)
                return;
            
            // 清理陷阱
            trapLocations.remove(this.placedBlock.getLocation());
            this.placedBlock.setType(Material.AIR);
        }
        
        protected void run() {
            final Block clickedBlock = getEvent().getClickedBlock();
            final BlockFace clickedBlockFace = getEvent().getClickedBlockFace();
            
            // 检查是否有有效的目标方块
            if (clickedBlock == null || clickedBlockFace != BlockFace.UP) {
                getEvent().getPlayer().sendMessage("§c请指向一个有效的方块顶部来放置陷阱！");
                stop();
                return;
            }
            
            // 获取放置位置（目标方块上方）
            Block placeBlock = clickedBlock.getRelative(clickedBlockFace);
            
            // 检查放置位置是否为空气
            if (placeBlock.getType() != Material.AIR) {
                getEvent().getPlayer().sendMessage("§c无法在此处放置陷阱！");
                stop();
                return;
            }
            
            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(getEvent().getPlayer());
            if (arena == null) {
                stop();
                return;
            }
            
            // 放置线陷阱
            placeBlock.setType(Material.TRIPWIRE);
            this.placedBlock = placeBlock;
            
            // 记录陷阱位置和所有者
            trapLocations.put(placeBlock.getLocation(), getEvent().getPlayer().getUniqueId());
            
            // 标记方块为玩家放置，这样游戏结束时会被清理
            arena.setBlockPlayerPlaced(placeBlock, true);
            
            getEvent().getPlayer().sendMessage("§a成功放置线陷阱！");
            
            // 消耗物品
            takeItem();
        }
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        // 检查玩家是否踩到了陷阱
        Player player = event.getPlayer();
        Block block = player.getLocation().getBlock();
        
        // 如果玩家踩到了线
        if (block.getType() == Material.TRIPWIRE) {
            Location trapLocation = block.getLocation();
            
            // 检查这个位置是否是我们的陷阱
            if (trapLocations.containsKey(trapLocation)) {
                UUID ownerUUID = trapLocations.get(trapLocation);
                Player owner = player.getServer().getPlayer(ownerUUID);
                
                // 获取玩家所在的竞技场
                Arena playerArena = GameAPI.get().getArenaByPlayer(player);
                Arena ownerArena = owner != null ? GameAPI.get().getArenaByPlayer(owner) : null;
                
                // 检查是否在同一个竞技场
                if (playerArena != null && playerArena.equals(ownerArena)) {
                    // 获取玩家和陷阱所有者的队伍
                    Team playerTeam = playerArena.getPlayerTeam(player);
                    Team ownerTeam = owner != null ? playerArena.getPlayerTeam(owner) : null;
                    
                    // 如果不是同一个队伍的玩家触发了陷阱
                    if (playerTeam != null && ownerTeam != null && !playerTeam.equals(ownerTeam)) {
                        // 应用效果
                        if (config != null) {
                            // 从配置中获取效果
                            ItemConfigManager.EffectConfig blindnessEffect = config.getEffect("blindness");
                            ItemConfigManager.EffectConfig weaknessEffect = config.getEffect("weakness");
                            ItemConfigManager.EffectConfig slownessEffect = config.getEffect("slowness");
                            
                            if (blindnessEffect != null) {
                                player.addPotionEffect(new PotionEffect(
                                        PotionEffectType.BLINDNESS, 
                                        blindnessEffect.getDuration() * 20, 
                                        blindnessEffect.getLevel()));
                            }
                            
                            if (weaknessEffect != null) {
                                player.addPotionEffect(new PotionEffect(
                                        PotionEffectType.WEAKNESS, 
                                        weaknessEffect.getDuration() * 20, 
                                        weaknessEffect.getLevel()));
                            }
                            
                            if (slownessEffect != null) {
                                player.addPotionEffect(new PotionEffect(
                                        PotionEffectType.SLOW, 
                                        slownessEffect.getDuration() * 20, 
                                        slownessEffect.getLevel()));
                            }
                        } else {
                            // 使用默认效果
                            player.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 100, 0)); // 失明 5秒
                            player.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, 100, 0)); // 虚弱 5秒
                            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOW, 100, 1)); // 缓慢II 5秒
                        }
                        
                        // 移除陷阱
                        block.setType(Material.AIR);
                        trapLocations.remove(trapLocation);
                        
                        // 通知玩家
                        player.sendMessage("§c你触发了敌人的线陷阱！");
                        if (owner != null && owner.isOnline()) {
                            owner.sendMessage("§a敌人触发了你的线陷阱！");
                        }
                    }
                }
            }
        }
    }
}
