package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

import java.io.File;
import java.util.Random;

/**
 * 玩家起飞事件
 * 起飞50格
 */
public class LaunchPlayerEvent implements LuckyEvent {
    
    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;
    
    public LaunchPlayerEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }
            
            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }
            
            // 配置文件路径
            File configFile = new File(badDir, "launch_player.yml");
            
            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/launch_player.yml", false);
                plugin.getLogger().info("已生成玩家起飞事件配置文件: " + configFile.getPath());
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载玩家起飞事件配置");
            
        } catch (Exception e) {
            plugin.getLogger().severe("加载玩家起飞事件配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public EventType getType() {
        return EventType.BAD;
    }
    
    @Override
    public String getName() {
        return "LAUNCH_PLAYER";
    }
    
    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 35) : 35;
    }
    
    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
    
    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }
        
        // 获取配置参数
        double velocityY = config != null ? config.getDouble("launch.velocity_y", 3.0) : 3.0;
        double velocityRandomX = config != null ? config.getDouble("launch.velocity_random_x", 0.5) : 0.5;
        double velocityRandomZ = config != null ? config.getDouble("launch.velocity_random_z", 0.5) : 0.5;
        
        // 应用保护效果
        applyProtectionEffects(player);
        
        // 播放音效
        playLaunchSound(player);
        
        // 显示粒子效果
        showLaunchParticles(player.getLocation());
        
        // 计算起飞向量
        double randomX = (random.nextDouble() - 0.5) * 2 * velocityRandomX;
        double randomZ = (random.nextDouble() - 0.5) * 2 * velocityRandomZ;
        Vector launchVector = new Vector(randomX, velocityY, randomZ);
        
        // 起飞！
        player.setVelocity(launchVector);
        
        plugin.getLogger().info("玩家 " + player.getName() + " 触发了起飞事件");
    }
    
    /**
     * 应用保护效果
     */
    private void applyProtectionEffects(Player player) {
        if (config == null) return;
        
        // 缓降效果
        if (config.getBoolean("launch.feather_falling.enabled", true)) {
            int level = config.getInt("launch.feather_falling.level", 4);
            int duration = config.getInt("launch.feather_falling.duration", 200);
            
            // 注意：缓降效果实际上是通过给予跳跃提升来模拟的
            // 因为Minecraft没有直接的缓降药水效果
            PotionEffect jumpBoost = new PotionEffect(PotionEffectType.JUMP, duration, level - 1);
            player.addPotionEffect(jumpBoost);
        }
        
        // 抗性提升
        if (config.getBoolean("launch.resistance.enabled", true)) {
            int level = config.getInt("launch.resistance.level", 2);
            int duration = config.getInt("launch.resistance.duration", 200);
            
            PotionEffect resistance = new PotionEffect(PotionEffectType.DAMAGE_RESISTANCE, duration, level - 1);
            player.addPotionEffect(resistance);
        }
    }
    
    /**
     * 播放起飞音效
     */
    private void playLaunchSound(Player player) {
        if (config == null) return;
        
        if (config.getBoolean("launch.sound.enabled", true)) {
            String soundType = config.getString("launch.sound.sound_type", "ENTITY_FIREWORK_ROCKET_LAUNCH");
            float volume = (float) config.getDouble("launch.sound.volume", 1.0);
            float pitch = (float) config.getDouble("launch.sound.pitch", 1.0);
            
            try {
                Sound sound = Sound.valueOf(soundType);
                player.playSound(player.getLocation(), sound, volume, pitch);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的音效类型: " + soundType);
            }
        }
    }
    
    /**
     * 显示起飞粒子效果
     */
    private void showLaunchParticles(Location location) {
        if (config == null) return;
        
        if (config.getBoolean("launch.particles.enabled", true)) {
            String particleType = config.getString("launch.particles.particle_type", "EXPLOSION_LARGE");
            int count = config.getInt("launch.particles.count", 10);
            
            try {
                Particle particle = Particle.valueOf(particleType);
                location.getWorld().spawnParticle(particle, location, count);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的粒子类型: " + particleType);
            }
        }
    }
    
    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null) return true;
        return config.getBoolean("messages.send_message", true);
    }
    
    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null) return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }
    
    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null) return "§c准备起飞！3...2...1...发射！";
        return config.getString("messages.event_message", "§c准备起飞！3...2...1...发射！");
    }
}
