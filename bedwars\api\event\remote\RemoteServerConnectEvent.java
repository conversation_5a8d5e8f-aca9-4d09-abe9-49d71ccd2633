package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemoteServer;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called whenever we successfully connected with another server.
 * <p>
 *     Keep in mind that this event is async.
 * </p>
 */
public class RemoteServerConnectEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemoteServer server;

  public RemoteServerConnectEvent(RemoteServer server) {
    super(true);

    this.server = server;
  }

  /**
   * Gets the server that has connected.
   *
   * @return The involved server
   */
  public RemoteServer getServer() {
    return this.server;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
