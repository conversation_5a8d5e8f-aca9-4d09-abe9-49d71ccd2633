package cn.acebrand.acevotemode.commands;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import cn.acebrand.acevotemode.gamemode.terror.CountdownManager;
import cn.acebrand.acevotemode.gamemode.terror.HealthAnnouncementManager;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.BedwarsAPI;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 恐怖降临公告配置命令
 * 用于管理倒计时和血量公告设置
 */
public class TerrorAnnouncementCommand implements CommandExecutor, TabCompleter {

    private final AceVoteMode plugin;

    public TerrorAnnouncementCommand(AceVoteMode plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("acevotemode.admin")) {
            sender.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
            return true;
        }

        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "countdown":
                handleCountdownCommand(sender, args);
                break;
            case "health":
                handleHealthCommand(sender, args);
                break;
            case "test":
                handleTestCommand(sender, args);
                break;
            case "status":
                handleStatusCommand(sender, args);
                break;
            case "reload":
                handleReloadCommand(sender);
                break;
            default:
                sendHelp(sender);
                break;
        }

        return true;
    }

    /**
     * 处理倒计时命令
     */
    private void handleCountdownCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /terrorannounce countdown <add|remove|list> [时间(秒)]");
            return;
        }

        String action = args[1].toLowerCase();
        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();

        switch (action) {
            case "add":
                if (args.length < 3) {
                    sender.sendMessage(ChatColor.RED + "请指定要添加的倒计时时间（秒）");
                    return;
                }
                try {
                    int seconds = Integer.parseInt(args[2]);
                    if (seconds <= 0) {
                        sender.sendMessage(ChatColor.RED + "时间必须大于0秒！");
                        return;
                    }

                    // 添加到配置
                    List<Integer> currentTimes = terrorMode.getConfig()
                            .getIntegerList("messages.countdown.announce-times");
                    if (!currentTimes.contains(seconds)) {
                        currentTimes.add(seconds);
                        currentTimes.sort((a, b) -> b.compareTo(a)); // 降序排列
                        terrorMode.getConfig().set("messages.countdown.announce-times", currentTimes);
                        terrorMode.saveConfigPublic();

                        sender.sendMessage(ChatColor.GREEN + "已添加倒计时公告时间点: " + seconds + "秒");
                    } else {
                        sender.sendMessage(ChatColor.YELLOW + "倒计时时间点 " + seconds + "秒 已存在！");
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage(ChatColor.RED + "无效的数字: " + args[2]);
                }
                break;

            case "remove":
                if (args.length < 3) {
                    sender.sendMessage(ChatColor.RED + "请指定要移除的倒计时时间（秒）");
                    return;
                }
                try {
                    int seconds = Integer.parseInt(args[2]);
                    List<Integer> currentTimes = terrorMode.getConfig()
                            .getIntegerList("messages.countdown.announce-times");
                    if (currentTimes.remove(Integer.valueOf(seconds))) {
                        terrorMode.getConfig().set("messages.countdown.announce-times", currentTimes);
                        terrorMode.saveConfigPublic();
                        sender.sendMessage(ChatColor.GREEN + "已移除倒计时公告时间点: " + seconds + "秒");
                    } else {
                        sender.sendMessage(ChatColor.YELLOW + "倒计时时间点 " + seconds + "秒 不存在！");
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage(ChatColor.RED + "无效的数字: " + args[2]);
                }
                break;

            case "list":
                List<Integer> times = terrorMode.getConfig().getIntegerList("messages.countdown.announce-times");
                sender.sendMessage(ChatColor.GOLD + "=== 倒计时公告时间点 ===");
                if (times.isEmpty()) {
                    sender.sendMessage(ChatColor.GRAY + "无配置的时间点");
                } else {
                    times.sort((a, b) -> b.compareTo(a));
                    for (int time : times) {
                        String formatted = formatTime(time);
                        sender.sendMessage(ChatColor.YELLOW + "- " + time + "秒 (" + formatted + ")");
                    }
                }
                break;

            default:
                sender.sendMessage(ChatColor.RED + "无效的操作: " + action);
                break;
        }
    }

    /**
     * 处理血量命令
     */
    private void handleHealthCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /terrorannounce health <add|remove|list> [百分比]");
            return;
        }

        String action = args[1].toLowerCase();
        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();

        switch (action) {
            case "add":
                if (args.length < 3) {
                    sender.sendMessage(ChatColor.RED + "请指定要添加的血量百分比");
                    return;
                }
                try {
                    int percentage = Integer.parseInt(args[2]);
                    if (percentage <= 0 || percentage > 100) {
                        sender.sendMessage(ChatColor.RED + "血量百分比必须在1-100之间！");
                        return;
                    }

                    List<Integer> currentPercentages = terrorMode.getConfig()
                            .getIntegerList("messages.boss-health-announcements.health-percentages");
                    if (!currentPercentages.contains(percentage)) {
                        currentPercentages.add(percentage);
                        currentPercentages.sort((a, b) -> b.compareTo(a)); // 降序排列
                        terrorMode.getConfig().set("messages.boss-health-announcements.health-percentages",
                                currentPercentages);
                        terrorMode.saveConfigPublic();

                        sender.sendMessage(ChatColor.GREEN + "已添加血量公告点: " + percentage + "%");
                    } else {
                        sender.sendMessage(ChatColor.YELLOW + "血量公告点 " + percentage + "% 已存在！");
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage(ChatColor.RED + "无效的数字: " + args[2]);
                }
                break;

            case "remove":
                if (args.length < 3) {
                    sender.sendMessage(ChatColor.RED + "请指定要移除的血量百分比");
                    return;
                }
                try {
                    int percentage = Integer.parseInt(args[2]);
                    List<Integer> currentPercentages = terrorMode.getConfig()
                            .getIntegerList("messages.boss-health-announcements.health-percentages");
                    if (currentPercentages.remove(Integer.valueOf(percentage))) {
                        terrorMode.getConfig().set("messages.boss-health-announcements.health-percentages",
                                currentPercentages);
                        terrorMode.saveConfigPublic();
                        sender.sendMessage(ChatColor.GREEN + "已移除血量公告点: " + percentage + "%");
                    } else {
                        sender.sendMessage(ChatColor.YELLOW + "血量公告点 " + percentage + "% 不存在！");
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage(ChatColor.RED + "无效的数字: " + args[2]);
                }
                break;

            case "list":
                List<Integer> percentages = terrorMode.getConfig()
                        .getIntegerList("messages.boss-health-announcements.health-percentages");
                sender.sendMessage(ChatColor.GOLD + "=== 血量公告点 ===");
                if (percentages.isEmpty()) {
                    sender.sendMessage(ChatColor.GRAY + "无配置的血量点");
                } else {
                    percentages.sort((a, b) -> b.compareTo(a));
                    for (int percentage : percentages) {
                        sender.sendMessage(ChatColor.YELLOW + "- " + percentage + "%");
                    }
                }
                break;

            default:
                sender.sendMessage(ChatColor.RED + "无效的操作: " + action);
                break;
        }
    }

    /**
     * 处理测试命令
     */
    private void handleTestCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "此命令只能由玩家执行！");
            return;
        }

        Player player = (Player) sender;
        Arena arena = BedwarsAPI.getGameAPI().getArenaByPlayer(player);

        if (arena == null) {
            sender.sendMessage(ChatColor.RED + "你不在任何竞技场中！");
            return;
        }

        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
        if (!terrorMode.isArenaActive(arena)) {
            sender.sendMessage(ChatColor.RED + "当前竞技场未启用恐怖降临模式！");
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "用法: /terrorannounce test <countdown|health> [参数]");
            return;
        }

        String testType = args[1].toLowerCase();

        switch (testType) {
            case "countdown":
                if (args.length < 3) {
                    sender.sendMessage(ChatColor.RED + "用法: /terrorannounce test countdown <秒数>");
                    return;
                }
                try {
                    int seconds = Integer.parseInt(args[2]);
                    CountdownManager countdownManager = terrorMode.getCountdownManager(arena);
                    if (countdownManager != null) {
                        countdownManager.startCountdown(seconds);
                        sender.sendMessage(ChatColor.GREEN + "已开始测试倒计时: " + seconds + "秒");
                    } else {
                        sender.sendMessage(ChatColor.RED + "无法获取倒计时管理器！");
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage(ChatColor.RED + "无效的数字: " + args[2]);
                }
                break;

            default:
                sender.sendMessage(ChatColor.RED + "无效的测试类型: " + testType);
                break;
        }
    }

    /**
     * 处理状态命令
     */
    private void handleStatusCommand(CommandSender sender, String[] args) {
        TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();

        sender.sendMessage(ChatColor.GOLD + "=== 恐怖降临公告状态 ===");

        // 倒计时配置状态
        boolean countdownEnabled = terrorMode.getConfig().getBoolean("messages.countdown.enabled", true);
        List<Integer> countdownTimes = terrorMode.getConfig().getIntegerList("messages.countdown.announce-times");

        sender.sendMessage(
                ChatColor.YELLOW + "倒计时公告: " + (countdownEnabled ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用"));
        sender.sendMessage(ChatColor.YELLOW + "倒计时时间点数量: " + ChatColor.WHITE + countdownTimes.size());

        // 血量公告配置状态
        boolean healthEnabled = terrorMode.getConfig().getBoolean("messages.boss-health-announcements.enabled", true);
        List<Integer> healthPercentages = terrorMode.getConfig()
                .getIntegerList("messages.boss-health-announcements.health-percentages");

        sender.sendMessage(
                ChatColor.YELLOW + "血量公告: " + (healthEnabled ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用"));
        sender.sendMessage(ChatColor.YELLOW + "血量公告点数量: " + ChatColor.WHITE + healthPercentages.size());

        // 活跃竞技场状态
        sender.sendMessage(ChatColor.YELLOW + "活跃竞技场数量: " + ChatColor.WHITE + terrorMode.getActiveArenas().size());
    }

    /**
     * 处理重载命令
     */
    private void handleReloadCommand(CommandSender sender) {
        try {
            TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
            terrorMode.reloadConfig();
            sender.sendMessage(ChatColor.GREEN + "恐怖降临模式配置已重载！");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "重载配置时发生错误: " + e.getMessage());
        }
    }

    /**
     * 发送帮助信息
     */
    private void sendHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== 恐怖降临公告管理命令 ===");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce countdown add <秒数> " + ChatColor.GRAY + "- 添加倒计时公告时间点");
        sender.sendMessage(
                ChatColor.YELLOW + "/terrorannounce countdown remove <秒数> " + ChatColor.GRAY + "- 移除倒计时公告时间点");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce countdown list " + ChatColor.GRAY + "- 列出所有倒计时时间点");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce health add <百分比> " + ChatColor.GRAY + "- 添加血量公告点");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce health remove <百分比> " + ChatColor.GRAY + "- 移除血量公告点");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce health list " + ChatColor.GRAY + "- 列出所有血量公告点");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce test countdown <秒数> " + ChatColor.GRAY + "- 测试倒计时");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce status " + ChatColor.GRAY + "- 查看公告状态");
        sender.sendMessage(ChatColor.YELLOW + "/terrorannounce reload " + ChatColor.GRAY + "- 重载配置");
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(int seconds) {
        if (seconds >= 60) {
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;
            if (remainingSeconds == 0) {
                return minutes + "分钟";
            } else {
                return minutes + "分" + remainingSeconds + "秒";
            }
        } else {
            return seconds + "秒";
        }
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            completions.addAll(Arrays.asList("countdown", "health", "test", "status", "reload"));
        } else if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            switch (subCommand) {
                case "countdown":
                case "health":
                    completions.addAll(Arrays.asList("add", "remove", "list"));
                    break;
                case "test":
                    completions.addAll(Arrays.asList("countdown", "health"));
                    break;
            }
        } else if (args.length == 3) {
            String subCommand = args[0].toLowerCase();
            String action = args[1].toLowerCase();

            if ("countdown".equals(subCommand)) {
                if ("add".equals(action)) {
                    completions.addAll(Arrays.asList("300", "180", "120", "60", "30", "10", "5", "3", "1"));
                } else if ("remove".equals(action)) {
                    TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
                    List<Integer> times = terrorMode.getConfig().getIntegerList("messages.countdown.announce-times");
                    for (int time : times) {
                        completions.add(String.valueOf(time));
                    }
                }
            } else if ("health".equals(subCommand)) {
                if ("add".equals(action)) {
                    completions.addAll(Arrays.asList("90", "75", "50", "25", "10", "5"));
                } else if ("remove".equals(action)) {
                    TerrorDescentMode terrorMode = TerrorDescentMode.getInstance();
                    List<Integer> percentages = terrorMode.getConfig()
                            .getIntegerList("messages.boss-health-announcements.health-percentages");
                    for (int percentage : percentages) {
                        completions.add(String.valueOf(percentage));
                    }
                }
            } else if ("test".equals(subCommand) && "countdown".equals(action)) {
                completions.addAll(Arrays.asList("60", "30", "10", "5"));
            }
        }

        return completions;
    }
}
