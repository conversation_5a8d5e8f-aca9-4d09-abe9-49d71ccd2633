package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.player.PlayerEarnAchievementEvent;
import java.time.OffsetDateTime;
import org.jetbrains.annotations.Nullable;

import java.util.Set;
import java.util.UUID;

public interface PlayerAchievements {

  /**
   * Returns the UUID of the player.
   *
   * @return The UUID of the player who earned these achievements
   */
  UUID getPlayerUUID();

  /**
   * Returns if the player has a specific achievement.
   *
   * @param achievement The achievement that we want to check
   * @return <code>true</code> if he has earned the achievement
   */
  boolean has(PlayerAchievement achievement);

  /**
   * Will give the achievement to the player.
   * Also displays a chat message that he earned if he's online.
   * <p>
   *   The operation might fail when the player already has the achievement or a plugin cancelled it via the {@link PlayerEarnAchievementEvent}.
   *   Additionally, prizers may be disabled within an arena ({@link Arena#isPrizeForMatchEnabled()}).
   * </p>
   *
   * @param achievement The achievmeent that he shall earn
   * @return <code>true</code> when the operation was successful
   * @throws IllegalArgumentException When the id of the achievement doesn't fit the format
   */
  default boolean earn(PlayerAchievement achievement) {
    return earn(achievement, false);
  }

  /**
   * Will give the achievement to the player.
   * Also displays a chat message that he earned if he's online and when <code>silent</code> is true.
   * <p>
   *   The operation might fail when the player already has the achievement or a plugin cancelled it via the {@link PlayerEarnAchievementEvent}.
   *   Additionally, prizers may be disabled within an arena ({@link Arena#isPrizeForMatchEnabled()}).
   * </p>
   *
   * @param achievement The achievmeent that he shall earn
   * @param silent If it should notify the player or not
   * @return <code>true</code> when the operation was successful
   * @throws IllegalArgumentException When the id of the achievement doesn't fit the format
   */
  boolean earn(PlayerAchievement achievement, boolean silent);

  /**
   * Removes the achievement from the earned list.
   *
   * @param achievement The achievement that he once earned
   * @return <code>false</code> if he hasn't earned it. Otherwise <code>true</code>
   */
  boolean remove(PlayerAchievement achievement);

  /**
   * Returns the date when he earned the achievement.
   *
   * @param achievement The achievement
   * @return The date when he earned it. Null if he never earned it before
   */
  @Nullable
  OffsetDateTime getEarnDate(PlayerAchievement achievement);

  /**
   * Sets the earn date of the achievement.
   *
   * @param achievement The achievement
   * @param date The new date
   * @return <code>false</code> if he never earned the achievement before
   */
  boolean setEarnDate(PlayerAchievement achievement, OffsetDateTime date);

  /**
   * Returns the ids of all achievements that he earned.
   *
   * @return The id of all earned achievements
   */
  Set<String> getEarnedIds();

  /**
   * Any changes made to this instance will not be saved, even if tried.
   * <p>
   *   While it is possible to change the values, MBedwars' auto-saving
   *   and {@link #save()} will not actually do anything and any changes
   *   will be lost with the next loading.
   * </p>
   * <p>
   *   Reasons for read-only are:
   *   <ul>
   *     <li>We failed to load it fully (due to an error).
   *     Reasons for that may include e.g. a disconnection to the storage server.</li>
   *     <li>The player might be online on another server and we
   *     don't want to intercept with whatever the other server is doing.
   *     Although cross-server support can be obtained using the ProxySync addon and
   *     making sure that player-data-syncing is enabled within it, in which case this
   *     instance won't be set as read-only.</li>
   *   </ul>
   *
   * @return Whether this instance is read-only and changes won't be saved
   */
  boolean isReadOnly();

  /**
   * Sets whether this instance is read-only.
   * <p>
   *   This mechanism exists to prevent desyncs between multiple servers.
   *   Use it with caution! Read {@link #isReadOnly()} for more info.
   * </p>
   *
   * @param readOnly Whether this instance should be read-only
   */
  void setReadOnly(boolean readOnly);

  /**
   * Asynchronously saves these achievements.
   * <p>
   * It's usually not needed to call this method when the player is currently on the server as the plugin will already handle it.
   */
  default void save() {
    save(null);
  }

  /**
   * Asynchronously saves these achievements.
   * <p>
   * It's usually not needed to call this method when the player is currently on the server as the plugin will already handle it.
   * Important: The callback doesn't get synced to the main thread.
   *
   * @param callback Gets called when the operation was finished
   */
  void save(@Nullable Runnable callback);
}
