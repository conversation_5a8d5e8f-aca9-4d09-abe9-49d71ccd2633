package de.marcely.bedwars.api.command;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.tools.CommandSenderWrapper;
import org.bukkit.command.CommandSender;
import org.jetbrains.annotations.Nullable;

/**
 * A (sub)command you find under /bw.
 * <p>
 * Basically anything you're able to give a name is a command.
 * This also includes a collection of commands (e.g. /bw or /bw arena).
 * Those collections are being implemented by {@link CommandsCollection} and are also executable.
 */
public interface SubCommand {

  /**
   * The main name of the command, which will be displayed in e.g. /bw help.
   * <p>
   * Example: If this returns "banana" then you'd have to type in /bw banana.
   *
   * @return The name of the command
   */
  String getName();

  /**
   * A command must be added into a collection otherwise it's not accessible.
   * <p>
   * As the system is built in a tree-structure-way each command must have a parent node/command.
   * There's only one instance that doesn't have a parent and that's the root node which you obtain with {@link BedwarsAPI#getRootCommandsCollection()}.
   *
   * @return The parent collection to which this command has been added to
   */
  @Nullable CommandsCollection getParent();

  /**
   * Returns the "full name" as if you'd type it in.
   * <p>
   * E.g. it returns "/bw arena banana" where "bw" is the label which you pass in as a parameter.
   *
   * @param label The actual command you'd use to access the command (e.g. "bw")
   * @return The full command name
   */
  default String getFullName(String label) {
    final CommandsCollection parent = getParent();

    if (parent != null)
      return parent.getFullName(label) + " " + getName();
    else
      return "/" + label;
  }

  /**
   * Other possible names for accessing this command.
   * <p>
   * Keep in mind that it's possible that aliases overlap themselves and there's no guarantee that this one will be executed if this occurs.
   * Names of a command have a higher priority than aliases.
   * <p>
   * These won't be shown in /bw help
   *
   * @return Aliases of the command
   */
  String[] getAliases();

  /**
   * Set the new aliases of the command. You don't have to include the name of the command in the array.
   * <p>
   * Keep in mind that it's possible that aliases overlap themselves and there's no guarantee that this one will be executed if this occurs.
   * Names of a command have a higher priority than aliases.
   * <p>
   * These won't be shown in /bw help
   *
   * @param aliases The new aliases of the command
   */
  void setAliases(String... aliases);

  /**
   * The permission needed to use the command.
   * Sender will see an error if he does not have the permission and the execution won't be passed to the handler.
   * <p>
   * <code>null</code> means that no permission is needed to run the command
   *
   * @return The permission needed to execute the command. <code>null</code> meaning that none is needed
   */
  @Nullable String getPermission();

  /**
   * The permission needed to use the command.
   * Sender will see an error if he does not have the permission and the execution won't be passed to the handler.
   * <p>
   * <code>null</code> means that no permission is needed to run the command
   *
   * @param permission The new permission needed to execute the command. <code>null</code> meaning that none is needed
   */
  void setPermission(@Nullable String permission);

  /**
   * Returns the default parameters the command expects.
   * <p>
   *  Use &lt;&gt; for parameters that are strictly needed, &#91;&#93; for optional ones.<br>
   *  Example: /teleport &lt;target&gt; &#91;source&#93;<br>
   *  We must add a target parameter, but we are not forced to add a source parameter.<br>
   *  This example would return with this method "&gt;target&lt; &#91;source&#93;"
   * </p>
   *
   * @return The usage of the command
   */
  default String getUsage() {
    return getUsage(null);
  }

  /**
   * Returns the parameters the command expects for a specific sender.
   * <p>
   *   By default if no sender is being passed, the same usage that has been defined using {@link #setUsage(String)}.
   *   However, you may also add conditional usages by overriding {@link CommandHandler#getContentAmount(CommandSender)}.
   * </p>
   *
   * @param senderWrapper The sender we are getting the usage for. Maybe be <code>null</code> if we want to use the default usage
   * @return The usage of the command
   */
  String getUsage(@Nullable CommandSenderWrapper senderWrapper);

  /**
   * Set the parameters the command expects
   * <p>
   * Use &lt;&gt; for parameters that are strictly needed, &#91;&#93; for optional ones.<br>
   * Example: /teleport &lt;target&gt; &#91;source&#93;<br>
   * We must add a target parameter, but we are not forced to add a source parameter.<br>
   * This example would return with this method "&gt;target&lt; &#91;source&#93;"
   *
   * @param usage The new usage of the command
   */
  void setUsage(String usage);

  /**
   * The handler handles what should happen when the command is e.g. getting executed
   *
   * @return The handler of the command
   */
  CommandHandler getHandler();

  /**
   * The handler handles what should happen when the command is e.g. getting executed
   *
   * @param handler The new handler of this command
   */
  void setHandler(CommandHandler handler);

  /**
   * Whether or not only players are able to execute the command
   *
   * @return If the command is only for normal players and not for the console
   */
  boolean isOnlyForPlayers();

  /**
   * Whether or not only players are able to execute the command
   *
   * @param onlyForPlayers The new value
   */
  void setOnlyForPlayers(boolean onlyForPlayers);

  /**
   * Returns whether or not this command is visible in help.
   * <p>
   * Interesting for debug or hidden commands which shouldn't be shown to normal players
   *
   * @return Whether or not the command is visible in /bw [...] help
   */
  boolean isVisible();

  /**
   * Set whether or not this command is visible in help.
   * <p>
   * Interesting for debug or hidden commands which shouldn't be shown to normal players
   *
   * @param visible The new value
   */
  void setVisible(boolean visible);

  /**
   * Returns whether or not it'll display the amount of entries in /bw [...] help.
   * <p>
   * The amount is getting fetched by the current handler using {@link CommandHandler#getContentAmount(org.bukkit.command.CommandSender)}
   *
   * @return If it'll display the amount of content or entries in /bw [...] help
   */
  boolean hasContentAmount();

  /**
   * Set whether or not it'll display the amount of entries in /bw [...] help.
   * <p>
   * The amount is getting fetched by the current handler using {@link CommandHandler#getContentAmount(org.bukkit.command.CommandSender)}
   *
   * @param hasContentAmount New value
   */
  void setHasContentAmount(boolean hasContentAmount);
}