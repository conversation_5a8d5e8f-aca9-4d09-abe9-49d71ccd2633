package de.marcely.bedwars.api.game.spectator;

/**
 * Reasons or causes why a player stopped spectating
 */
public enum KickSpectatorReason {

  /**
   * Player manually left the arena
   */
  LEAVE,

  /**
   * Player disconnected from the server
   */
  SERVER_DISCONNECT,

  /**
   * Arena stopped
   */
  ARENA_STOP,

  /**
   * Server/plugin is about to stop or to be reloaded
   */
  PLUGIN_STOP,

  /**
   * Player has been kicked
   */
  KICK,

  /**
   * Player used the "next arena" item
   */
  CHANGE_ARENA,

  /**
   * Player was only temporarily a spectator because he died. He respawned now
   */
  DEATH_RESPAWN,

  /**
   * Player is a member of a party and the party leader entered an arena. Player is now leaving the Spectating mode to enter his arena.
   */
  FOLLOW_PARTY,

  /**
   * {@link de.marcely.bedwars.api.remote.AddRemotePlayerInfo#setForcefully(boolean)} forced him to switch to another arena.
   */
  FORCE_SWITCH_ARENA,

  /**
   * The match ended and players that died during the match - thus become spectators - get re-added to the end lobby.
   *
   * @see de.marcely.bedwars.api.arena.RejoinPlayerCause#END_LOBBY
   */
  END_LOBBY,

  /**
   * He joined an arena (e.g. with /bw join).
   */
  JOIN_ARENA,

  /**
   * A plugin kicked him
   */
  PLU<PERSON>N;


  /**
   * Get whether the player is being teleported to the hub with this reason.
   * <p>
   *   In certain situations it does not make sense to teleport the player to the hub.
   *   Note there may be other factors that cause the player to get teleported
   * </p>
   *
   * @return Whether the player would get teleported with this reason
   */
  public boolean isTeleportingToHub() {
    switch (this) {
      case CHANGE_ARENA:
      case JOIN_ARENA:
      case FOLLOW_PARTY:
      case END_LOBBY:
      case DEATH_RESPAWN:
        return false;
      default:
        return true;
    }
  }

  /**
   * Get whether the player's stored inventory does get applied with this reason.
   * <p>
   *   Depending on the server's configuration, the players inventory is being backed up before he spectates an arena.
   *   And when he leaves, it is possibly being applied again. This might not happen every time, as this could possibly cause bugs.
   * </p>
   *
   * @return Whether the player's backed up inventory would get applied again
   */
  public boolean isApplyingStoredInventory() {
    switch (this) {
      case CHANGE_ARENA:
      case DEATH_RESPAWN:
      case END_LOBBY:
        return false;
      default:
        return true;
    }
  }

  /**
   * Get whether the leave message shall be sent to the player with this reason.
   * <p>
   *   It might not be neccessary to display the leave message (Spectator_Leave) in some cases.
   * </p>
   *
   * @return Whether the leave message shall be sent with this reason
   */
  public boolean isDisplayingLeaveMessage() {
    switch (this) {
      case CHANGE_ARENA:
      case JOIN_ARENA:
      case END_LOBBY:
      case DEATH_RESPAWN:
        return false;
      default:
        return true;
    }
  }
}
