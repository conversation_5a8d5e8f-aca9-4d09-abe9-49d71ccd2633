@echo off
echo ========================================
echo    AceVoteMode 构建脚本
echo ========================================
echo.

:: 检查 Maven 是否安装
where mvn >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到 Maven，请确保 Maven 已安装并添加到 PATH 环境变量中
    echo 下载地址: https://maven.apache.org/download.cgi
    pause
    exit /b 1
)

:: 检查 libs 目录是否存在
if not exist "libs" (
    echo [错误] 未找到 libs 目录，请确保 MBedwars.jar 等依赖文件在 libs 目录中
    pause
    exit /b 1
)

:: 检查 MBedwars.jar 是否存在
if not exist "libs\MBedwars.jar" (
    echo [警告] 未找到 libs\MBedwars.jar，请确保 MBedwars 插件文件存在
    echo 继续构建可能会失败...
    echo.
)

echo [信息] 开始构建 AceVoteMode...
echo.

:: 清理并编译
echo [步骤 1/3] 清理旧的构建文件...
call mvn clean
if %errorlevel% neq 0 (
    echo [错误] 清理失败
    pause
    exit /b 1
)

echo.
echo [步骤 2/3] 编译源代码...
call mvn compile
if %errorlevel% neq 0 (
    echo [错误] 编译失败
    pause
    exit /b 1
)

echo.
echo [步骤 3/3] 打包插件...
call mvn package
if %errorlevel% neq 0 (
    echo [错误] 打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo    构建完成！
echo ========================================
echo.

:: 检查输出文件
if exist "target\AceVoteMode-1.0.0.jar" (
    echo [成功] 插件已成功构建！
    echo 输出文件: target\AceVoteMode-1.0.0.jar
    echo.
    echo 文件大小:
    for %%A in ("target\AceVoteMode-1.0.0.jar") do echo %%~zA 字节
    echo.
    echo 你可以将此文件复制到服务器的 plugins 目录中
) else (
    echo [错误] 未找到输出文件，构建可能失败
)

echo.
echo 按任意键退出...
pause >nul
