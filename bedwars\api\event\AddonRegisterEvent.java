package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.BedwarsAddon;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when an addon is being registered using {@link BedwarsAddon#register()}.
 */
public class AddonRegisterEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final BedwarsAddon addon;

  @Getter @Setter
  private boolean cancelled = false;

  public AddonRegisterEvent(BedwarsAddon addon) {
    this.addon = addon;
  }

  /**
   * Returns the addon that has been registered.
   *
   * @return The addon involved in this event
   */
  public BedwarsAddon getAddon() {
    return this.addon;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
