package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.arena.AddPlayerIssue;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.remote.RemotePlayer;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called right before a player is being pulled into this server to join an arena.
 * <p>
 *   Keep in mind that this event is async.
 * </p>
 */
public class RemotePlayerPreJoinLocalArenaEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemotePlayer player;
  private final Arena arena;

  private AddPlayerIssue issue;

  public RemotePlayerPreJoinLocalArenaEvent(RemotePlayer player, Arena arena) {
    super(true);

    this.player = player;
    this.arena = arena;
  }

  /**
   * The player that is about to join the arena.
   *
   * @return The involved player
   */
  public RemotePlayer getPlayer() {
    return this.player;
  }

  /**
   * The arena that he is about to join.
   *
   * @return The involved arena
   */
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Add an issue that may prevent the player from joining the arena.
   *
   * @param issue The new issue. <code>null</code> if there is not supposed to be any issue
   */
  public void setIssue(@Nullable AddPlayerIssue issue) {
    this.issue = issue;
  }

  /**
   * Get the issue that may prevent the player from joining the arena.
   *
   * @return The issue. <code>null</code> if there is not supposed to be any issue
   */
  @Nullable
  public AddPlayerIssue getIssue() {
    return this.issue;
  }

  /**
   * Get if any issue has been added so far, that'd prevent the player from joining.
   *
   * @return <code>true</code> if there's an issue
   */
  public boolean hasIssue() {
    return this.issue != null;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
