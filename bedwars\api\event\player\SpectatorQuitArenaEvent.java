package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.spectator.KickSpectatorReason;
import de.marcely.bedwars.api.game.spectator.Spectator;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a spectator leaves the arena
 */
public class SpectatorQuitArenaEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Spectator spectator;
  private final KickSpectatorReason reason;

  public SpectatorQuitArenaEvent(Spectator spectator, KickSpectatorReason reason) {
    super(spectator.getPlayer());

    this.spectator = spectator;
    this.reason = reason;
  }

  /**
   * Returns the spectator.
   *
   * @return The spectator that left the arena
   */
  public Spectator getSpectator() {
    return this.spectator;
  }

  /**
   * Returns the reason why he left the arena.
   *
   * @return The reason
   */
  public KickSpectatorReason getReason() {
    return this.reason;
  }

  /**
   * Returns the arena that he left.
   *
   * @return The arena involved in this
   */
  public Arena getArena() {
    return this.spectator.getArena();
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
