package de.marcely.bedwars.api.arena;

import org.jetbrains.annotations.Nullable;

/**
 * The "regeneration type" or just simply "type" describes the form of an arena
 */
public enum RegenerationType {

  /**
   * Game fills the complete world
   */
  WORLD,

  /**
   * Game fills only a given rectangular region
   */
  REGION,

  /**
   * Arena is not actually an arena in which you play. Only uses its lobby to vote for the arena in which you'll play.
   */
  VOTING;

  public String getId() {
    return name().toLowerCase();
  }

  public boolean isNormal() {
    switch (this) {
      case WORLD:
      case REGION:
        return true;

      default:
        return false;
    }
  }

  public static @Nullable RegenerationType fromId(String id) {
    switch (id.toLowerCase()) {
      case "world":
        return WORLD;
      case "region":
        return REGION;
      case "voting":
      case "map_vote":
      case "map_voting":
      case "mapvote":
        return VOTING;
      default:
        return null;
    }
  }
}
