package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import lombok.Getter;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a player or a plugin creates an arena
 */
public class ArenaCreateEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final CommandSender sender;

  public ArenaCreateEvent(Arena arena, @Nullable CommandSender sender) {
    this.arena = arena;
    this.sender = sender;
  }

  /**
   * Returns the person who created the arena.
   *
   * @return The person who created the arena. Can be null if not a player created it
   */
  public @Nullable CommandSender getSender() {
    return this.sender;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}