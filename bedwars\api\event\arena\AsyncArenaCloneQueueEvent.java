package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.QueuedCloningArena;
import de.marcely.bedwars.tools.Validate;
import java.util.Queue;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when the auto clone system is looking for the next queue of arenas.
 * <p>
 *   The system automatically performs checks every few seconds.
 *   In case new arenas (at least one) that fit the requirements have been found, this event will be called.
 * </p>
 */
public class AsyncArenaCloneQueueEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Queue<QueuedCloningArena> addingQueue;

  @Getter @Setter
  private boolean cancelled = false;

  public AsyncArenaCloneQueueEvent(Queue<QueuedCloningArena> addingQueue) {
    super(true);

    this.addingQueue = addingQueue;
  }

  /**
   * Returns the queue of arenas that are going to be added to the system.
   * <p>
   *   This queue is mutable and can be modified.
   * </p>
   *
   * @return The queue of arenas
   */
  public Queue<QueuedCloningArena> getAddingQueue() {
    return this.addingQueue;
  }

  /**
   * Sets the queue of arenas that are going to be added to the system.
   *
   * @param addingQueue The queue of arenas
   */
  public void setAddingQueue(Queue<QueuedCloningArena> addingQueue) {
    Validate.notNull(addingQueue, "Adding queue can't be null");

    this.addingQueue.clear();
    this.addingQueue.addAll(addingQueue);
  }

  /**
   * Adds an arena to the end of queue of arenas that are going to be added to the system.
   *
   * @param arena The arena to add
   */
  public void addToAddingQueue(QueuedCloningArena arena) {
    Validate.notNull(arena, "Arena can't be null");

    this.addingQueue.add(arena);
  }

  /**
   * Removes a single arena from the queue of arenas that are going to be added to the system.
   *
   * @param arena The arena to remove
   * @return <code>true</code> if the arena was removed, <code>false</code> otherwise
   */
  public boolean removeFromAddingQueue(QueuedCloningArena arena) {
    Validate.notNull(arena, "Arena can't be null");

    return this.addingQueue.remove(arena);
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
