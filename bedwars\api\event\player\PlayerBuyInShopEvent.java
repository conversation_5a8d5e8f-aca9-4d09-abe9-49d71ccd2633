package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.api.game.shop.ShopOpenCause;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.List;

/**
 * Gets called when a player is buying an item from the shop
 */
public class PlayerBuyInShopEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private final ShopItem item, clonedItem;
  private final ShopOpenCause openCause;
  private final boolean shiftClick;
  private final Integer targetInvSlot;

  private int multiplier;
  private final List<Problem> problems;
  private boolean giveProducts;
  private boolean takePayments;

  public PlayerBuyInShopEvent(
      Player player,
      Arena arena,
      ShopItem item,
      @Nullable ShopItem clonedItem,
      ShopOpenCause openCause,
      boolean shiftClick,
      @Nullable Integer targetInvSlot,
      int multiplier,
      List<Problem> problems,
      boolean giveProducts,
      boolean takePayments) {

    super(player);

    this.arena = arena;
    this.item = item;
    this.clonedItem = clonedItem;
    this.openCause = openCause;
    this.shiftClick = shiftClick;
    this.targetInvSlot = targetInvSlot;
    this.multiplier = multiplier;
    this.problems = problems;
    this.giveProducts = giveProducts;
    this.takePayments = takePayments;
  }

  /**
   * Returns the arena in which the player is playing.
   *
   * @return The player's arena. Can be <code>null</code> if the player is using the shop outside of an arena.
   */
  public @Nullable Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the item that the player wants to buy.
   *
   * @return The item that he's trying to buy
   */
  public ShopItem getItem() {
    return this.item;
  }

  /**
   * Get the cloned item that was used within {@link PlayerOpenShopEvent#getClonedPage()}.
   *
   * @return If known, the cloned item that was used to render the shop. Otherwise <code>null</code>.
   */
  @Nullable
  public ShopItem getClonedItem() {
    return this.clonedItem;
  }

  /**
   * Returns they way the player opened the shop.
   *
   * @return The open cause
   */
  public ShopOpenCause getOpenCause() {
    return this.openCause;
  }

  /**
   * Returns whether or not the player shift-clicked while buying the item
   * and by that tries to buy a whole stack.
   *
   * @return If he shift-clicked
   */
  public boolean isShiftClick() {
    return this.shiftClick;
  }

  /**
   * Player clicked on a specific number on his keyboard while buying.
   * <p>
   *   The given number represents the hotbar slot he wants to put the item in.
   * </p>
   *
   * @return The target inv slot to which the bought item shall be added to. May be <code>null</code> if that doesn't matter
   */
  @Nullable
  public Integer getTargetInvSlot() {
    return this.targetInvSlot;
  }

  /**
   * Returns how much it has been multiplied by shift-clicking.
   * <p>
   * It's always 1 by default when not shift clicked.
   *
   * @return How many he tries to buy
   */
  public int getMultiplier() {
    return this.multiplier;
  }

  /**
   * Set how often/many items he should receive the item.
   *
   * @param multiplier The new multiplier value
   * @throws IllegalArgumentException When the new value is less than 1
   */
  public void setMultiplier(int multiplier) {
    Validate.isTrue(multiplier >= 1, "multiplier is less than 1");

    this.multiplier = multiplier;
  }

  /**
   * Returns the problems that are preventing the item from being bought
   *
   * @return The problems
   */
  public List<Problem> getProblems() {
    return Collections.unmodifiableList(this.problems);
  }

  /**
   * Add a problem that will prevent the item from being bought.
   * <p>
   * Act's similar as {@link Cancellable#setCancelled(boolean)}.
   *
   * @param problem The problem why he can't buy it
   * @return <code>false</code> if it already has been added
   */
  public boolean addProblem(Problem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.add(problem);
  }

  /**
   * Add a problem that will prevent the item from being bought.
   * <p>
   * Act's similar as {@link Cancellable#setCancelled(boolean)}.
   *
   * @param problem The problem why he can't buy it
   * @return <code>false</code> if it already has been added
   */
  public boolean addProblem(DefaultProblem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.add(problem.get());
  }

  /**
   * Removes a problem.
   *
   * @param problem The problem
   * @return If it has been removed or not
   */
  public boolean removeProblem(Problem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.remove(problem);
  }

  /**
   * Removes a problem.
   *
   * @param problem The problem
   * @return If it has been removed or not
   */
  public boolean removeProblem(DefaultProblem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.remove(problem.get());
  }

  /**
   * Removes all problems and makes it buyable by that.
   *
   * @return The amount of problems that have been removed
   */
  public int removeAllProblems() {
    final int amount = this.problems.size();

    this.problems.clear();

    return amount;
  }

  /**
   * Returns whether or not the player will receive the products of the item.
   * <p>
   * Does not concern when there are problems as he won't receive them if there are problems anyways.
   *
   * @return If he'll get the products or not
   */
  public boolean isGivingProducts() {
    return this.giveProducts;
  }

  /**
   * Define whether or not if he'll get products.
   * <p>
   * Keep in mind that problems can prevent products being given to him.
   *
   * @param giveProducts The new value
   */
  public void setGivingProducts(boolean giveProducts) {
    this.giveProducts = giveProducts;
  }

  /**
   * Returns whether or not the it'll take the payments from the player.
   * <p>
   * Does not concern when there are problems as they're preventing them from being taken anyways.
   *
   * @return If the payments will be taken or not
   */
  public boolean isTakingPayments() {
    return this.takePayments;
  }

  /**
   * Define whether or not the payments will be taken from the player.
   * <p>
   * Keep in mind that problems can prevent this
   *
   * @param takePayments The new value
   */
  public void setTakingPayments(boolean takePayments) {
    this.takePayments = takePayments;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  public static abstract class Problem {

    /**
     * Returns the plugin that has created this problem
     *
     * @return The plugin that created it
     */
    public abstract Plugin getPlugin();

    /**
     * Notify the player about the problem
     *
     * @param event The event that happened before
     */
    public abstract void handleNotification(PlayerBuyInShopEvent event);
  }

  /**
   * Default problems used and provided by the plugin
   */
  public enum DefaultProblem {

    NOT_ENOUGH_ITEMS,
    NO_INVENTORY_SPACE,
    INSUFFICIENT_PERMISSIONS,
    ONETIMEPURCHASE,
    BUYGROUP,
    NOT_IN_RUNNING_ARENA,
    SILENT;

    private Problem instance;

    /**
     * Returns the {@link PlayerBuyUpgradeEvent.Problem} instanced backed by this type
     *
     * @return The Problem instance of this type
     */
    public Problem get() {
      return this.instance;
    }
  }
}
