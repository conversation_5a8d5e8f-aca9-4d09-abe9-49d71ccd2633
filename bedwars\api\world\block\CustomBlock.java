package de.marcely.bedwars.api.world.block;

import com.google.gson.JsonObject;
import de.marcely.bedwars.api.event.SpecialBlockAddEvent;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * A type created for devs being able to create custom blocks with custom handler
 *
 * Use {@link SpecialBlockAddEvent} if you're looking to remove it when you think that the conditions aren't met
 * (like when e.g. the material of the block isn't the needed one)
 */
public interface CustomBlock extends SpecialBlock {

  /**
   * Returns the type id of this custom block.<br>
   * A plugin might have custom blocks but it's also important to be able to distinguish them
   *
   * @return The type id of this custom block
   */
  String getId();

  /**
   * Set the type id of this custom block.<br>
   * A plugin might have custom blocks but it's also important to be able to distinguish them
   *
   * @param id The new type id
   */
  void setId(String id);

  /**
   * Returns the handler of this CustomBlock. The handler is responsible for adding live to the block
   *
   * @return The set handler. <code>null</code> when {@link #hasHandler()} returns false
   */
  @Nullable CustomBlock.Handler getHandler();

  /**
   * Set the handler of this CustomBlock. The handler is responsible for adding live to the block
   *
   * @param handler The new handler. <code>null</code> to remove the handler
   */
  void setHandler(@Nullable CustomBlock.Handler handler);

  /**
   * Returns if a handler has been set for this block with {@link #setHandler(Handler)}
   *
   * @return If a handler has been set
   */
  default boolean hasHandler() {
    return getHandler() != null;
  }


  /**
   * The handler for custom SpecialBlocks ({@link CustomBlock})
   */
  interface Handler {

    /**
     * The plugin that created this handler
     *
     * @return The plugin that created it
     */
    Plugin getPlugin();

    /**
     * Apply stuff to the real world.
     * <p>
     * E.g. when you're displaying info stats on a sign then this method shall update the lines on the sign.
     * The plugin does NOT automatically call this method automatically.
     * Use {@link SpecialBlock#update()} to update it manually
     *
     * @param block The block on which the update is being called
     */
    void update(CustomBlock block);

    /**
     * A player may interact with the block. This is getting called at the same moment as Bukkit calls {@link PlayerInteractEvent}
     *
     * @param block The block that's getting interacted
     * @param player The player who interacts with the block
     * @param parentEvent The event that was used to detect it
     */
    void onInteract(CustomBlock block, Player player, PlayerInteractEvent parentEvent);

    /**
     * You may use the storage feature of the plugin.<br>
     * This method is getting called when the data is getting loaded.<br>
     * Load stuff from the JsonObject into the given block
     *
     * @param block The block to which you shall load the data
     * @param object The data that was stored
     */
    void load(CustomBlock block, JsonObject object);

    /**
     * You may use the storage feature of the plugin.<br>
     * This method is getting called when the data is getting stored.<br>
     * Load stuff from the given block into the JsonObject
     *
     * @param block The block from which you shall obtain the data
     * @param object The data that will be stored
     */
    void save(CustomBlock block, JsonObject object);
  }
}
