package de.marcely.bedwars.api.game.shop.product;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.game.shop.ShopItem;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;

public interface ShopProduct {

  /**
   * Returns the item to which the product has been added to
   *
   * @return The item to which the product was added to
   */
  ShopItem getItem();

  /**
   * Products may vary on what they effectively give to the player.<br>
   * This returns what exactly it'll be giving to the player
   *
   * @return The type of this product
   */
  ShopProductType getType();

  /**
   * Returns the unique id of this instance.
   * <p>
   * Keep in mind that this will change whenever the plugin gets reloaded!
   *
   * @return The unique id of this product
   */
  UUID getId();

  /**
   * Returns the amount that will be given to the player
   *
   * @return The amount that will be given to the player
   */
  int getAmount();

  /**
   * Set the amount that will be given to the player.<br>
   * Not any product type does support this operation, those who don't support it will return false
   *
   * @param amount The new amount
   * @return If this operation is being supported by the type
   */
  boolean setAmount(int amount);

  /**
   * Returns the name of this product that can be presented to the given sender.<br>
   * This method might return something like "Iron", "Bronze", "A command" or something else configured by the user
   *
   * @param sender Uses his local info to return the name in the right language
   * @return The display name of this product
   */
  String getDisplayName(@Nullable CommandSender sender);

  /**
   * Returns the name of this product in the default configured language.<br>
   * This method might return something like "Iron", "Bronze", "A command" or something else configured by the user
   *
   * @return The display name of this product
   */
  default String getDisplayName() {
    return getDisplayName(null);
  }

  /**
   * Returns whether or not this item is automatically getting worn on purchase
   *
   * @return If this item is being auto worn
   */
  boolean isAutoWear();

  /**
   * Set whether or not this item shall be automatically worn on purchase
   *
   * @param autowear The new value
   */
  void setAutoWear(boolean autowear);

  /**
   * Returns if this item is able to obtain any damage
   *
   * @return If the item obtains any damage on use
   */
  boolean isUnbreakable();

  /**
   * Set whether or not this item shall be able to obtain damage
   *
   * @param unbreakable The new value
   */
  void setUnbreakable(boolean unbreakable);

  /**
   * Gives the product to a player.
   * <p>
   * The team and the arena parameters are optional.
   * Some product features won't be applied if they're <code>null</code>.
   * This includes: automatic dying and enchantments gained from upgrades
   * </p>
   *
   * @param player The player to which the item shall be given to. Names will also be automatically translated into the language of this player
   * @param team The team in which the player is in. Can be <code>null</code>
   * @param arena The arena in which the player is in. Can be <code>null</code>
   * @param multiplier How many times this product shall be given to the player. Should be at least 1, or otherwise no effect will be seen
   * @see #give(org.bukkit.entity.Player, de.marcely.bedwars.api.arena.Team, de.marcely.bedwars.api.arena.Arena, int, Integer)
   */
  void give(Player player, @Nullable Team team, @Nullable Arena arena, int multiplier);

  /**
   * Gives the product to a player.
   * <p>
   * The team and the arena parameters are optional.
   * Some product features won't be applied if they're <code>null</code>.
   * This includes: automatic dying and enchantments gained from upgrades
   * </p>
   *
   * @param player The player to which the item shall be given to. Names will also be automatically translated into the language of this player
   * @param team The team in which the player is in. Can be <code>null</code>
   * @param arena The arena in which the player is in. Can be <code>null</code>
   * @param multiplier How many times this product shall be given to the player. Should be at least 1, or otherwise no effect will be seen
   * @param targetInvSlot The slot to which the item shall be (at least tried) added to. Can be <code>null</code> if that doesn't matter
   */
  void give(Player player, @Nullable Team team, @Nullable Arena arena, int multiplier, @Nullable Integer targetInvSlot);

  /**
   * Gets the ItemStacks the player will actually receive.
   * <p>
   * The team and the arena parameters are optional.
   * Some product features won't be applied if they're <code>null</code>.
   * This includes: automatic dying and enchantments gained from upgrades
   *
   * @param player The player to which the item shall be given to. Names will also be automatically translated into the language of this player
   * @param team The team in which the player is in. Can be <code>null</code>
   * @param arena The arena in which the player is in. Can be <code>null</code>
   * @param multiplier How many times this product shall be given to the player. Should be at least 1, or otherwise we will return an empty array
   */
  ItemStack[] getGivingItems(Player player, @Nullable Team team, @Nullable Arena arena, int multiplier);
}
