package de.marcely.bedwars.tools.gui.type;

import de.marcely.bedwars.api.event.player.PlayerTradeVillagerGUIEvent;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.tools.Validate;
import de.marcely.bedwars.tools.gui.GUI;
import de.marcely.bedwars.tools.gui.VillagerOffer;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.function.Consumer;

@SuppressWarnings("deprecation")
public class VillagerGUI implements GUI {

  @Getter
  private final List<Consumer<Player>> closeListeners = new ArrayList<>();
  private Set<Player> openPlayers = new HashSet<>();
  private String title;
  private Set<VillagerOffer> offers = new LinkedHashSet<>();
  private Set<Consumer<PlayerTradeVillagerGUIEvent>> tradeListeners = new LinkedHashSet<>();

  public VillagerGUI() {
    this("");
  }

  public VillagerGUI(String title) {
    Validate.notNull(title, "title");

    this.title = title;
  }

  @Override
  public void open(Player player) {
    BedwarsAPILayer.INSTANCE.open(this, player);

    this.openPlayers.add(player);
  }

  @Override
  public void closeAll() {
    for (Player player : this.openPlayers)
      player.closeInventory();

    this.openPlayers.clear();
  }

  @Override
  public void setTitle(String title) {
    Validate.notNull(title, "title");
  }

  @Override
  public String getTitle() {
    return this.title;
  }

  @Override
  public boolean areItemsMoveable() {
    return true;
  }

  @Override
  public Collection<Player> getPlayers() {
    return Collections.unmodifiableCollection(this.openPlayers);
  }

  @Override
  public boolean hasOpen(Player player) {
    Validate.notNull(player, "player");

    return this.openPlayers.contains(player);
  }

  @Override
  public boolean addCloseListener(Consumer<Player> callback) {
    Validate.notNull(callback, "callback");

    if (closeListeners.contains(callback))
      return false;

    return closeListeners.add(callback);
  }

  @Override
  public boolean removeCloseListener(Consumer<Player> callback) {
    Validate.notNull(callback, "callback");

    return closeListeners.remove(callback);
  }

  @Override
  public void clear() {
    this.offers.clear();
  }

  @Override
  public final void onClose(Player player) {
    this.openPlayers.remove(player);
  }

  /**
   * Returns all offers that have been added to this GUI.
   *
   * @return All added offers
   */
  public Set<VillagerOffer> getOffers() {
    return Collections.unmodifiableSet(this.offers);
  }

  /**
   * Returns the offer in this GUI whose id is equal to the given as a parameter
   *
   * @param id The id of the offer
   * @return The offer whose id is equal. <code>null</code> if there's none
   */
  public @Nullable VillagerOffer getOffer(UUID id) {
    for (VillagerOffer offer : this.offers) {
      if (offer.getId().equals(id))
        return offer;
    }

    return null;
  }

  /**
   * Adds an offer that can be bought by a player.
   * <p>
   * Keep in mind that you'll have to reopen the GUI for it to get updated.
   *
   * @param offer The buyable offer given to the player
   * @return <code>false</code> if the offer has been already added before
   */
  public boolean addOffer(VillagerOffer offer) {
    Validate.notNull(offer, "offer");

    return this.offers.add(offer);
  }

  /**
   * Removes an existing offer.
   * <p>
   * Keep in mind that you'll have to reopen the GUI for it to get updated.
   *
   * @param offer The offer that shall be removed
   * @return <code>true</code> if it has been removed
   */
  public boolean removeOffer(VillagerOffer offer) {
    return this.offers.remove(offer);
  }

  /**
   * Returns all registered trade events that were explicitly added to this GUI.
   * <p>
   * Keep in mind that those listeners get run AFTER Bukkit's event queue.
   * Cancelling them causes these listeners to noWt get run.<p>
   * It's safe to modify the returned set.
   *
   * @return All registered trade events that were explicitly added to this GUI
   */
  public Set<Consumer<PlayerTradeVillagerGUIEvent>> getTradeListeners() {
    return this.tradeListeners;
  }

  /**
   * Registers a listener that will be called when a player successfully executes a trade.
   * <p>
   * Keep in mind that the listener get run AFTER Bukkit's event queue.
   * Cancelling them causes these listeners to noWt get run.
   *
   * @param listener The listener that shall be registered
   * @return <code>true</code> if it's not already registered
   */
  public boolean registerTradeListener(Consumer<PlayerTradeVillagerGUIEvent> listener) {
    return this.tradeListeners.add(listener);
  }

  /**
   * Unregisters an already registered trade listener
   *
   * @param listener The listener that shall be unregistered
   * @return <code>false</code> if it's not registered
   */
  public boolean unregisterTradeListener(Consumer<PlayerTradeVillagerGUIEvent> listener) {
    return this.tradeListeners.remove(listener);
  }
}
