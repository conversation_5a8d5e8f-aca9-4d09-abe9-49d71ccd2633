package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.api.remote.RemotePlayer;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * A player left an arena that is located on another server.
 * <p>
 *   Keep in mind that this event is async.
 * </p>
 */
public class RemotePlayerQuitArenaEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemotePlayer player;
  private final RemoteArena arena;

  public RemotePlayerQuitArenaEvent(RemotePlayer player, RemoteArena arena) {
    super(true);

    this.player = player;
    this.arena = arena;
  }

  /**
   * The player that quit it.
   *
   * @return The involved player
   */
  public RemotePlayer getPlayer() {
    return this.player;
  }

  /**
   * The arena he has quit.
   *
   * @return The involved arena
   */
  public RemoteArena getArena() {
    return this.arena;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
