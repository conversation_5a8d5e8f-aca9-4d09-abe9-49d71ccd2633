package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.tools.Validate;
import java.util.List;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when the voting pool of an arena gets updated.
 * <p>
 *   Note that this event is not called when all players have left an arena,
 *   whereby the voting pool gets cleared.
 * </p>
 */
public class ArenaVotingPoolUpdateEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private final int maxPoolSize;
  private final List<Arena> potentialPool;

  private List<Arena> pool;

  public ArenaVotingPoolUpdateEvent(Arena arena, int maxPoolSize, List<Arena> potentialPool, List<Arena> pool) {
    this.arena = arena;
    this.maxPoolSize = maxPoolSize;
    this.potentialPool = potentialPool;
    this.pool = pool;
  }

  @Override
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the maximum amount of choices you can have when voting.
   *
   * @return The maximum amount of choices you can have when voting.
   */
  public int getMaxPoolSize() {
    return this.maxPoolSize;
  }

  /**
   * Returns all arenas that are potential choices for the voting pool.
   * <p>
   *   Unlike {@link #getPool()}, this one hasn't already been reduced to
   *   the maximum amount of choices you can have when voting.
   *   Arenas, that may not be voted for, as e.g. they're already in another
   *   pool, not joinable or whatever critera there might be already have been filtered.
   *   Work with this list and reduce it however you like if
   *   you want to implement your custom pool using {@link #setPool(List)}.
   * </p>
   *
   * @return All potential arenas for the voting pool.
   */
  public List<Arena> getPotentialPool() {
    return this.potentialPool;
  }

  /**
   * Get the new voting pool of the arena.
   * <p>
   *   It is safe to modify the returned collection.
   *   Do note that it restricted to max amount of choices you can have when voting.
   *   Use {@link #getPotentialPool()} if you want to set your custom pool.
   * </p>
   *
   * @return The new pool of the arena.
   */
  public List<Arena> getPool() {
    return this.pool;
  }

  /**
   * Set the new voting pool of the arena.
   *
   * @param pool The new pool of the arena.
   */
  public void setPool(List<Arena> pool) {
    Validate.notNull(pool, "pool");

    this.pool = pool;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
