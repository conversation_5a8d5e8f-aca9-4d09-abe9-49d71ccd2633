package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.MBedwarsProp;
import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.Dispenser;
import org.bukkit.block.data.BlockData;
import org.bukkit.block.data.Directional;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

public class DefenseTowerHandler implements SpecialItemUseHandler, Listener {

    private final MBedwarsProp plugin;
    private final Map<Location, DefenseTower> towers = new HashMap<>();
    private final Map<UUID, List<Location>> playerTowers = new HashMap<>();

    // 配置信息
    private int defaultRadius = 8;
    private int defaultDuration = 30;
    private int attackInterval = 20;
    private double damage = 3.0;
    private boolean showParticles = true;
    private boolean playSound = true;
    private boolean showLaser = true;
    private int maxAmmo = 20;

    public DefenseTowerHandler(MBedwarsProp plugin) {
        this.plugin = plugin;

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        // 加载配置
        loadConfig();
    }

    // 从配置文件加载设置
    private void loadConfig() {
        try {
            // 获取防御塔配置
            ItemConfigManager.ItemConfig config = plugin.itemConfigManager.getItemConfig("defense_tower");
            if (config == null) {
                plugin.getLogger().warning("无法加载防御塔配置，使用默认设置");
                return;
            }

            // 读取基本设置
            ItemConfigManager.EffectConfig radiusConfig = config.getEffect("radius");
            if (radiusConfig != null) {
                defaultRadius = radiusConfig.getLevel();
            }

            ItemConfigManager.EffectConfig durationConfig = config.getEffect("duration");
            if (durationConfig != null) {
                defaultDuration = durationConfig.getDuration();
            }

            ItemConfigManager.EffectConfig intervalConfig = config.getEffect("attack-interval");
            if (intervalConfig != null) {
                attackInterval = intervalConfig.getLevel();
            }

            ItemConfigManager.EffectConfig damageConfig = config.getEffect("damage");
            if (damageConfig != null) {
                damage = damageConfig.getLevel();
            }

            ItemConfigManager.EffectConfig particlesConfig = config.getEffect("show-particles");
            if (particlesConfig != null) {
                showParticles = particlesConfig.getLevel() > 0;
            }

            ItemConfigManager.EffectConfig soundConfig = config.getEffect("play-sound");
            if (soundConfig != null) {
                playSound = soundConfig.getLevel() > 0;
            }

            ItemConfigManager.EffectConfig laserConfig = config.getEffect("show-laser");
            if (laserConfig != null) {
                showLaser = laserConfig.getLevel() > 0;
            }

            ItemConfigManager.EffectConfig ammoConfig = config.getEffect("max-ammo");
            if (ammoConfig != null) {
                maxAmmo = ammoConfig.getLevel();
            }

        } catch (Exception e) {
            plugin.getLogger().severe("加载防御塔配置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        // 检查是否破坏了防御塔
        Block block = event.getBlock();
        if (block.getType() == Material.DISPENSER) {
            Location location = block.getLocation();

            // 检查是否是防御塔
            if (towers.containsKey(location)) {
                DefenseTower tower = towers.get(location);

                // 取消防御塔效果
                tower.cancel();

                // 移除防御塔
                towers.remove(location);

                // 通知玩家
                Player player = event.getPlayer();
                player.sendMessage("§c你破坏了一个防御塔！");
            }
        }
    }

    // 防御塔类
    private class DefenseTower {
        private final Location location;
        private final int radius;
        private final Team ownerTeam;
        private final Player owner;
        private final BlockFace ownerFacing; // 添加玩家面对的方向
        private final BukkitTask attackTask;
        private final BukkitTask particleTask;
        private final BukkitTask expirationTask;
        private final ArmorStand armorStand;
        private int ammo;
        private Player currentTarget;
        private BukkitTask laserTask;

        public DefenseTower(Location location, int radius, int duration, Team ownerTeam, Player owner) {
            this.location = location;
            this.radius = radius;
            this.ownerTeam = ownerTeam;
            this.owner = owner;
            this.ownerFacing = getPlayerFacing(owner); // 初始化玩家面对的方向
            this.ammo = maxAmmo;

            // 创建隐形盔甲架作为塔的标记
            this.armorStand = (ArmorStand) location.getWorld().spawnEntity(
                    location.clone().add(0.5, 1.5, 0.5),
                    EntityType.ARMOR_STAND);

            // 设置盔甲架属性
            this.armorStand.setVisible(false);
            this.armorStand.setGravity(false);
            this.armorStand.setInvulnerable(true);
            this.armorStand.setCustomName("§c防御塔");
            this.armorStand.setCustomNameVisible(true);

            // 创建攻击任务
            this.attackTask = new BukkitRunnable() {
                @Override
                public void run() {
                    attackNearbyEnemies();
                }
            }.runTaskTimer(plugin, attackInterval, attackInterval);

            // 创建粒子效果任务
            this.particleTask = new BukkitRunnable() {
                @Override
                public void run() {
                    if (showParticles) {
                        showRangeParticles();
                    }
                }
            }.runTaskTimer(plugin, 0L, 20L);

            // 创建过期任务
            this.expirationTask = new BukkitRunnable() {
                @Override
                public void run() {
                    cancel();
                    towers.remove(location);

                    // 移除发射器方块
                    Block block = location.getBlock();
                    if (block.getType() == Material.DISPENSER) {
                        // 先标记为非玩家放置的方块，这样可以确保能被移除
                        try {
                            // 遍历所有竹战场找到匹配的世界
                            for (Arena arena : GameAPI.get().getArenas()) {
                                if (arena.getGameWorld() != null && arena.getGameWorld().equals(location.getWorld())) {
                                    arena.setBlockPlayerPlaced(block, false);
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("移除发射器时出错: " + e.getMessage());
                        }

                        // 移除方块
                        block.setType(Material.AIR);

                        // 播放取消音效
                        World world = location.getWorld();
                        if (world != null && playSound) {
                            world.playSound(location, Sound.BLOCK_DISPENSER_FAIL, 1.0f, 0.5f);
                        }
                    }
                }
            }.runTaskLater(plugin, duration * 20L);
        }

        // 攻击附近的敌人
        private void attackNearbyEnemies() {
            if (ammo <= 0) {
                // 弹药用尽，停止攻击
                if (this.attackTask != null && !this.attackTask.isCancelled()) {
                    this.attackTask.cancel();
                }
                return;
            }

            World world = location.getWorld();
            if (world == null)
                return;

            // 获取范围内的玩家
            List<Entity> nearbyEntities = world.getNearbyEntities(
                    location.clone().add(0.5, 0.5, 0.5),
                    radius, radius, radius).stream().filter(entity -> entity instanceof Player)
                    .collect(Collectors.toList());

            Player target = null;
            double closestDistance = Double.MAX_VALUE;

            // 寻找最近的敌方玩家
            for (Entity entity : nearbyEntities) {
                if (!(entity instanceof Player))
                    continue;

                Player player = (Player) entity;

                // 检查玩家是否在游戏中
                Arena arena = GameAPI.get().getArenaByPlayer(player);
                if (arena == null || arena.getStatus() != ArenaStatus.RUNNING)
                    continue;

                // 检查玩家是否是敌方玩家
                Team playerTeam = arena.getPlayerTeam(player);
                if (playerTeam == null || playerTeam.equals(ownerTeam))
                    continue;

                // 计算距离
                double distance = player.getLocation().distance(location);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    target = player;
                }
            }

            // 如果找到目标，攻击它
            if (target != null) {
                attackPlayer(target);
                currentTarget = target;
            } else {
                currentTarget = null;
                if (laserTask != null && !laserTask.isCancelled()) {
                    laserTask.cancel();
                    laserTask = null;
                }
            }
        }

        // 攻击玩家
        private void attackPlayer(Player target) {
            // 减少弹药
            ammo--;

            // 对玩家造成伤害
            target.damage(damage, owner);

            // 播放攻击音效
            if (playSound) {
                location.getWorld().playSound(location, Sound.ENTITY_ARROW_SHOOT, 1.0f, 1.2f);
                target.getWorld().playSound(target.getLocation(), Sound.ENTITY_ARROW_HIT_PLAYER, 1.0f, 1.0f);
            }

            // 显示激光效果
            if (showLaser) {
                showLaserEffect(target);
            }

            // 更新盔甲架名称显示弹药信息
            armorStand.setCustomName("§c防御塔 §7[§e" + ammo + "§7/§e" + maxAmmo + "§7]");
        }

        // 显示激光效果
        private void showLaserEffect(Player target) {
            if (laserTask != null && !laserTask.isCancelled()) {
                laserTask.cancel();
            }

            laserTask = new BukkitRunnable() {
                private int ticks = 0;

                @Override
                public void run() {
                    if (ticks >= 10 || currentTarget != target) {
                        cancel();
                        laserTask = null;
                        return;
                    }

                    // 获取起点和终点
                    Location start = location.clone().add(0.5, 1.0, 0.5);
                    Location end = target.getEyeLocation();

                    // 计算方向向量
                    Vector direction = end.toVector().subtract(start.toVector());
                    double distance = direction.length();
                    direction.normalize();

                    // 显示粒子效果
                    for (double d = 0; d < distance; d += 0.5) {
                        Location particleLoc = start.clone().add(direction.clone().multiply(d));
                        start.getWorld().spawnParticle(
                                Particle.REDSTONE,
                                particleLoc,
                                1,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(Color.RED, 1.0f));
                    }

                    ticks++;
                }
            }.runTaskTimer(plugin, 0L, 2L);
        }

        // 显示范围粒子效果
        private void showRangeParticles() {
            World world = location.getWorld();
            if (world == null)
                return;

            // 在防御塔周围显示粒子效果
            for (double angle = 0; angle < Math.PI * 2; angle += Math.PI / 16) {
                double x = location.getX() + 0.5 + Math.cos(angle) * radius;
                double z = location.getZ() + 0.5 + Math.sin(angle) * radius;

                Location particleLoc = new Location(world, x, location.getY() + 0.5, z);
                world.spawnParticle(
                        Particle.REDSTONE,
                        particleLoc,
                        1,
                        0, 0, 0,
                        0,
                        new Particle.DustOptions(Color.RED, 1.0f));
            }
        }

        // 取消防御塔效果
        public void cancel() {
            // 取消任务
            if (attackTask != null && !attackTask.isCancelled()) {
                attackTask.cancel();
            }

            if (particleTask != null && !particleTask.isCancelled()) {
                particleTask.cancel();
            }

            if (expirationTask != null && !expirationTask.isCancelled()) {
                expirationTask.cancel();
            }

            if (laserTask != null && !laserTask.isCancelled()) {
                laserTask.cancel();
            }

            // 移除盔甲架
            if (armorStand != null && !armorStand.isDead()) {
                armorStand.remove();
            }

            // 播放取消音效
            World world = location.getWorld();
            if (world != null && playSound) {
                world.playSound(location, Sound.BLOCK_DISPENSER_FAIL, 1.0f, 0.5f);
            }
        }
    }

    // 获取玩家面对的方向
    private BlockFace getPlayerFacing(Player player) {
        // 获取玩家的方向
        float yaw = player.getLocation().getYaw();

        // 将方向角度转换为 BlockFace
        // 将 yaw 转换为 0-360 范围
        yaw = (yaw % 360 + 360) % 360;

        // 根据角度范围确定方向
        if (yaw >= 315 || yaw < 45) {
            return BlockFace.SOUTH; // 面向南
        } else if (yaw >= 45 && yaw < 135) {
            return BlockFace.WEST; // 面向西
        } else if (yaw >= 135 && yaw < 225) {
            return BlockFace.NORTH; // 面向北
        } else { // yaw >= 225 && yaw < 315
            return BlockFace.EAST; // 面向东
        }
    }

    private class Session extends SpecialItemUseSession {

        private Block placedBlock;
        private DefenseTower tower;

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            if (this.placedBlock == null)
                return;

            // 如果会话被强制停止，清理防御塔
            if (this.tower != null) {
                this.tower.cancel();
                towers.remove(this.placedBlock.getLocation());
            }

            // 移除方块
            this.placedBlock.setType(Material.AIR);
        }

        public void run() {
            Player player = getEvent().getPlayer();
            Block clickedBlock = getEvent().getClickedBlock();

            // 检查是否点击了方块
            if (clickedBlock == null) {
                player.sendMessage("§c请点击一个方块来放置防御塔！");
                stop();
                return;
            }

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(player);
            if (arena == null) {
                player.sendMessage("§c你必须在游戏中才能使用此道具！");
                stop();
                return;
            }

            // 检查竞技场状态
            if (arena.getStatus() != ArenaStatus.RUNNING) {
                player.sendMessage("§c只能在游戏进行中使用此道具！");
                stop();
                return;
            }

            // 获取玩家队伍
            Team team = arena.getPlayerTeam(player);
            if (team == null) {
                player.sendMessage("§c你必须在一个队伍中才能使用此道具！");
                stop();
                return;
            }

            // 获取配置
            int radius = defaultRadius;
            int duration = defaultDuration;

            // 放置防御塔
            Block placeBlock = clickedBlock.getRelative(BlockFace.UP);
            Location placeLoc = placeBlock.getLocation();

            // 检查方块是否可以被替换
            if (placeBlock.getType() != Material.AIR) {
                player.sendMessage("§c无法在此处放置防御塔！");
                stop();
                return;
            }

            // 放置发射器
            placeBlock.setType(Material.DISPENSER);
            this.placedBlock = placeBlock;

            // 立即标记为玩家放置的方块
            arena.setBlockPlayerPlaced(placeBlock, true);
            plugin.getLogger().info("已将发射器放置在方块上方并标记为玩家放置");

            // 设置发射器朝向为玩家面对的方向
            Dispenser dispenser = (Dispenser) placeBlock.getState();
            BlockData blockData = dispenser.getBlockData();
            if (blockData instanceof Directional) {
                // 获取玩家面对的方向
                BlockFace playerFacing = DefenseTowerHandler.this.getPlayerFacing(player);
                ((Directional) blockData).setFacing(playerFacing);
                dispenser.setBlockData(blockData);
                plugin.getLogger().info("设置发射器朝向为: " + playerFacing);
            }
            dispenser.update();

            // 再次确认标记为玩家放置的方块
            arena.setBlockPlayerPlaced(placeBlock, true);

            // 延迟检查发射器是否还存在
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (placedBlock != null && placedBlock.getType() != Material.DISPENSER) {
                        plugin.getLogger().warning("发射器已被移除！当前方块类型: " + placedBlock.getType());
                        // 尝试重新放置发射器
                        placedBlock.setType(Material.DISPENSER);
                        arena.setBlockPlayerPlaced(placedBlock, true);
                        plugin.getLogger().info("尝试重新放置发射器");

                        // 重新设置发射器朝向
                        Dispenser newDispenser = (Dispenser) placedBlock.getState();
                        BlockData newBlockData = newDispenser.getBlockData();
                        if (newBlockData instanceof Directional) {
                            // 保持原来的朝向
                            BlockFace originalFacing = tower.ownerFacing;
                            ((Directional) newBlockData).setFacing(originalFacing);
                            newDispenser.setBlockData(newBlockData);
                            plugin.getLogger().info("重新设置发射器朝向为: " + originalFacing);
                        }
                        newDispenser.update();
                    } else {
                        plugin.getLogger().info("发射器仍然存在");
                        // 再次确认标记为玩家放置的方块
                        arena.setBlockPlayerPlaced(placedBlock, true);
                    }
                }
            }.runTaskLater(plugin, 5L);

            // 创建防御塔
            this.tower = new DefenseTower(placeLoc, radius, duration, team, player);
            towers.put(placeLoc, this.tower);

            // 记录玩家的防御塔
            if (!playerTowers.containsKey(player.getUniqueId())) {
                playerTowers.put(player.getUniqueId(), new ArrayList<>());
            }
            playerTowers.get(player.getUniqueId()).add(placeLoc);

            // 播放放置音效
            if (playSound) {
                player.getWorld().playSound(placeLoc, Sound.BLOCK_DISPENSER_DISPENSE, 1.0f, 1.0f);
                player.getWorld().playSound(placeLoc, Sound.BLOCK_ANVIL_PLACE, 0.5f, 1.2f);
            }

            // 显示成功消息
            player.sendMessage("§a成功放置防御塔！");
            player.sendMessage("§a防御塔将攻击 " + radius + " 格范围内的敌方玩家，持续时间: " + duration + "秒");

            // 消耗道具
            takeItem();

            // 结束会话
            stop();
        }
    }
}
