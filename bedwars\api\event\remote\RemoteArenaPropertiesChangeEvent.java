package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemoteArena;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

import java.util.Set;

/**
 * Gets called whenever properties of a remote arena changed.
 * <p>
 *     Keep in mind that this event is async.
 * </p>
 */
public class RemoteArenaPropertiesChangeEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemoteArena arena;
  private final Set<Property> properties;

  public RemoteArenaPropertiesChangeEvent(RemoteArena arena, Set<Property> properties) {
    super(true);

    this.arena = arena;
    this.properties = properties;
  }

  /**
   * Gets the arena of which the properties have change.
   *
   * @return The involved arena
   */
  public RemoteArena getArena() {
    return this.arena;
  }

  /**
   * Gets the properties that have changed.
   *
   * @return The changed properties
   */
  public Set<Property> getProperties() {
    return this.properties;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  /**
   * Some properties of the arena.
   */
  public enum Property {

    /**
     * {@link RemoteArena#getName()} or {@link RemoteArena#getDisplayName()}
     */
    NAME,

    /**
     * {@link RemoteArena#getStatus()}
     */
    STATUS,

    /**
     * {@link RemoteArena#getPlayersPerTeam()}
     */
    PLAYERS_PER_TEAM,

    /**
     * {@link RemoteArena#getPlayersPerTeam()}
     */
    MIN_PLAYERS,

    /**
     * {@link RemoteArena#getMaxPlayers()}
     */
    MAX_PLAYERS,

    /**
     * {@link RemoteArena#getEnabledTeams()}
     */
    ENABLED_TEAMS,

    /**
     * {@link RemoteArena#getAuthors()}
     */
    AUTHORS,

    /**
     * {@link RemoteArena#getIcon()}
     */
    ICON,

    /**
     * {@link RemoteArena#getRegenerationType()}
     */
    REGENERATION_TYPE,

    /**
     * {@link RemoteArena#getIssues()}
     */
    ISSUES,

    /**
     * {@link RemoteArena#getGameWorldName()}
     */
    GAME_WORLD_NAME,

    /**
     * A plugin manually called it using {@link de.marcely.bedwars.api.arena.Arena#broadcastCustomPropertyChange()}
     */
    API_CUSTOM
  }
}
