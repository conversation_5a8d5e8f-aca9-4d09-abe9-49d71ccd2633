package cn.acebrand.acevotemode.events.neutral;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.spawner.DropType;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Zombie;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.Set;

/**
 * 资源点怪物生成事件 - 中立事件
 * 在随机的钻石/绿宝石资源点生成怪物群
 */
public class ResourceSpawnerMonsterEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;

    public ResourceSpawnerMonsterEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File neutralDir = new File(eventsDir, "neutral");
            if (!neutralDir.exists()) {
                neutralDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(neutralDir, "resource_spawner_monster.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/neutral/resource_spawner_monster.yml", false);
                plugin.getLogger().info("已生成资源点怪物生成事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载资源点怪物生成事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载资源点怪物生成事件配置失败: " + e.getMessage());
        }
    }

    @Override
    public void execute(Player player, Location location) {
        // 获取竞技场
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        if (arena == null) {
            plugin.getLogger().warning("玩家不在竞技场中，无法执行资源点怪物生成事件");
            return;
        }

        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }

        // 获取所有匹配的资源点并生成怪物
        List<Location> spawnerLocations = getAllResourceSpawnerLocations(arena);
        if (!spawnerLocations.isEmpty()) {
            int totalMonsters = 0;
            for (Location spawnerLocation : spawnerLocations) {
                int monstersSpawned = spawnMonsterGroup(spawnerLocation);
                totalMonsters += monstersSpawned;
            }

            String successMessage = "在 " + spawnerLocations.size() + " 个资源点生成了 " + totalMonsters + " 只怪物！";
            player.sendMessage(successMessage);
        } else {
            String noSpawnerMessage = getNoSpawnerMessage();
            player.sendMessage(noSpawnerMessage);
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 触发了资源点怪物生成事件");
    }

    /**
     * 获取所有匹配的资源生成器位置
     */
    private List<Location> getAllResourceSpawnerLocations(Arena arena) {
        List<String> targetTypes = config != null ? config.getStringList("spawn.target_spawner_types")
                : List.of("diamond", "emerald");

        List<Location> validLocations = new ArrayList<>();

        // 收集所有符合条件的生成器
        plugin.getLogger().info("开始搜索资源生成器，目标类型: " + targetTypes);
        for (Spawner spawner : arena.getSpawners()) {
            DropType dropType = spawner.getDropType();
            String dropTypeId = dropType.getId();

            plugin.getLogger().info("发现生成器类型: " + dropTypeId + " 位置: " + spawner.getLocation());

            if (targetTypes.contains(dropTypeId)) {
                Location location = spawner.getLocation().toLocation(arena.getGameWorld());
                validLocations.add(location);
                plugin.getLogger().info("匹配的资源生成器: " + dropTypeId + " 位置: " + location);
            }
        }

        plugin.getLogger().info("找到 " + validLocations.size() + " 个匹配的生成器");

        if (validLocations.isEmpty()) {
            plugin.getLogger().warning("没有找到匹配的资源生成器");
        }

        return validLocations;
    }

    /**
     * 在指定位置生成怪物群
     * 
     * @return 生成的怪物数量
     */
    private int spawnMonsterGroup(Location spawnerLocation) {
        // 选择怪物组
        String selectedGroup = selectMonsterGroup();
        if (selectedGroup == null)
            return 0;

        ConfigurationSection groupSection = config.getConfigurationSection("monster_groups." + selectedGroup);
        if (groupSection == null)
            return 0;

        // 获取生成配置
        double spawnRadius = config.getDouble("spawn.radius", 3.0);
        boolean burnInDaylight = config.getBoolean("spawn.burn_in_daylight", false);
        String namePrefix = config.getString("spawn.name_prefix", "§c愤怒的");
        int lifetime = config.getInt("spawn.lifetime", 300); // 15分钟

        List<LivingEntity> spawnedMonsters = new ArrayList<>();

        // 生成怪物组中的每种怪物
        Set<String> monsterKeys = groupSection.getKeys(false);
        for (String monsterKey : monsterKeys) {
            ConfigurationSection monsterConfig = groupSection.getConfigurationSection(monsterKey);
            if (monsterConfig == null)
                continue;

            String entityType = monsterConfig.getString("type", "ZOMBIE");
            String displayName = monsterConfig.getString("display_name", monsterKey);
            int count = monsterConfig.getInt("count", 1);
            double health = monsterConfig.getDouble("health", 20.0);

            // 生成指定数量的怪物
            for (int i = 0; i < count; i++) {
                Location spawnLoc = getRandomSpawnLocation(spawnerLocation, spawnRadius);
                if (spawnLoc != null) {
                    try {
                        EntityType type = EntityType.valueOf(entityType);

                        // 为烈焰人调整生成高度
                        if (type == EntityType.BLAZE) {
                            spawnLoc = spawnLoc.clone().add(0, 3, 0); // 烈焰人生成在更高位置
                            plugin.getLogger().info("烈焰人生成位置调整到: " + spawnLoc);
                        }

                        LivingEntity entity = (LivingEntity) spawnLoc.getWorld().spawnEntity(spawnLoc, type);

                        // 设置怪物属性
                        setupMonster(entity, health, namePrefix + displayName, burnInDaylight);
                        spawnedMonsters.add(entity);

                    } catch (Exception e) {
                        plugin.getLogger().warning("生成怪物失败: " + entityType + " - " + e.getMessage());
                    }
                }
            }
        }

        // 设置怪物生存时间
        if (lifetime > 0 && !spawnedMonsters.isEmpty()) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    for (LivingEntity monster : spawnedMonsters) {
                        if (monster.isValid()) {
                            monster.remove();
                        }
                    }
                }
            }.runTaskLater(plugin, lifetime * 20L);
        }

        plugin.getLogger().info("在位置 " + spawnerLocation + " 生成了 " + spawnedMonsters.size() + " 只怪物");
        return spawnedMonsters.size();
    }

    /**
     * 选择怪物组
     */
    private String selectMonsterGroup() {
        if (config == null)
            return null;

        ConfigurationSection groupsSection = config.getConfigurationSection("monster_groups");
        if (groupsSection == null)
            return null;

        // 计算总权重
        int totalWeight = 0;
        Set<String> groupNames = groupsSection.getKeys(false);
        for (String groupName : groupNames) {
            totalWeight += config.getInt("monster_groups." + groupName + ".weight", 1);
        }

        // 随机选择
        int roll = random.nextInt(totalWeight);
        int current = 0;

        for (String groupName : groupNames) {
            current += config.getInt("monster_groups." + groupName + ".weight", 1);
            if (roll < current) {
                return groupName;
            }
        }

        return groupNames.iterator().next(); // 回退选择
    }

    /**
     * 获取随机生成位置
     */
    private Location getRandomSpawnLocation(Location center, double radius) {
        for (int attempts = 0; attempts < 10; attempts++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = random.nextDouble() * radius;

            double x = center.getX() + Math.cos(angle) * distance;
            double z = center.getZ() + Math.sin(angle) * distance;

            Location spawnLoc = new Location(center.getWorld(), x, center.getY(), z);

            // 寻找合适的Y坐标
            for (int y = 0; y < 10; y++) {
                Location checkLoc = spawnLoc.clone().add(0, y, 0);
                if (checkLoc.getBlock().getType().isAir() &&
                        checkLoc.clone().add(0, 1, 0).getBlock().getType().isAir() &&
                        !checkLoc.clone().subtract(0, 1, 0).getBlock().getType().isAir()) {
                    return checkLoc;
                }
            }
        }

        return center.clone().add(0, 1, 0); // 回退位置
    }

    /**
     * 设置怪物属性
     */
    private void setupMonster(LivingEntity entity, double health, String customName, boolean burnInDaylight) {
        // 设置生命值
        try {
            entity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).setBaseValue(health);
            entity.setHealth(health);
        } catch (Exception e) {
            // 兼容旧版本
            entity.setMaxHealth(health);
            entity.setHealth(health);
        }

        // 设置自定义名称
        entity.setCustomName(customName);
        entity.setCustomNameVisible(true);

        // 标记为幸运方块怪物（用于死亡时不掉落物品和经验）
        entity.setMetadata("lucky_block_monster", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

        // 防止白天燃烧
        if (!burnInDaylight && entity instanceof Zombie) {
            // 给僵尸戴头盔防止燃烧
            Zombie zombie = (Zombie) entity;
            zombie.getEquipment().setHelmet(new org.bukkit.inventory.ItemStack(org.bukkit.Material.LEATHER_HELMET));
        }

        // 烈焰人使用原版机制，不添加特殊保护
        // 这样可以避免服务器性能问题
    }

    // 消息相关方法
    private boolean shouldSendMessage() {
        return config != null ? config.getBoolean("messages.send_message", true) : true;
    }

    private String getMessagePrefix() {
        return config != null ? config.getString("messages.message_prefix", "§e[幸运方块] §f") : "§e[幸运方块] §f";
    }

    private String getEventMessage() {
        return config != null ? config.getString("messages.event_message", "§c资源点出现了怪物！") : "§c资源点出现了怪物！";
    }

    private String getSuccessMessage() {
        return config != null ? config.getString("messages.success_message", "§7怪物群已在资源点生成！") : "§7怪物群已在资源点生成！";
    }

    private String getNoSpawnerMessage() {
        return config != null ? config.getString("messages.no_spawner_message", "§7找不到合适的资源点...") : "§7找不到合适的资源点...";
    }

    @Override
    public String getName() {
        return "RESOURCE_SPAWNER_MONSTER";
    }

    @Override
    public EventType getType() {
        return EventType.NEUTRAL;
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 30) : 30;
    }

    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }
}
