package de.marcely.bedwars.api.event.remote;

import de.marcely.bedwars.api.remote.RemotePlayer;
import de.marcely.bedwars.api.remote.RemoteServer;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when a remote player left a server on the network.
 * <p>
 *     This does not include the local one.
 *     Keep in mind that this event is async.
 * </p>
 */
public class RemotePlayerQuitServerEvent extends Event {

  private static final HandlerList HANDLERS = new HandlerList();

  private final RemotePlayer player;

  public RemotePlayerQuitServerEvent(RemotePlayer player) {
    super(true);

    this.player = player;
  }

  /**
   * Gets the player that has left a server.
   *
   * @return The involved player
   */
  public RemotePlayer getPlayer() {
    return this.player;
  }

  /**
   * Gets the server that he left.
   *
   * @return The involved server
   */
  public RemoteServer getServer() {
    return this.player.getServer();
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}