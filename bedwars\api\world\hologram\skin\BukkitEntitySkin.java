package de.marcely.bedwars.api.world.hologram.skin;

import org.bukkit.entity.Entity;

/**
 * Represents a hologram skin that allows to make use of Bukkit's entity API.
 */
public interface BukkitEntitySkin {

  /**
   * Get the entity from Bukkit's API that is being used under the hood.
   * <p>
   *   The entity still doesn't actually exist in the world!
   *   Changes won't be reflected in the world. You may want
   *   to make use of {@link #updateBukkitMetadata()}.
   * </p>
   *
   * @return The entity that is being used under the hood
   */
  Entity getBukkitEntity();

  /**
   * Update the metadata of the Bukkit's entity.
   * <p>
   *   By this, the entity's metadata are being sent to the players that
   *   see the hologram.
   * </p>
   *
   * @see #getBukkitEntity()
   */
  void updateBukkitMetadata();
}
