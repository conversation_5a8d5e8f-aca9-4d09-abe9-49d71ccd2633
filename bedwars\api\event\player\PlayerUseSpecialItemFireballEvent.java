package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemType;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.Location;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.projectiles.ProjectileSource;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a player is about to use the {@link SpecialItemType#FIREBALL} special item.
 * <p>
 * It is not possible to cancel this event. Listen to {@link PlayerUseSpecialItemEvent} instead.
 * </p>
 */
public class PlayerUseSpecialItemFireballEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final PlayerUseSpecialItemEvent origin;
  private ProjectileSource shooter;
  private Location spawnLocation;
  private Vector direction;
  private float yield;
  private double flightSpeed;
  private boolean flyStraight;
  private Integer autoIgnitionTicks;

  public PlayerUseSpecialItemFireballEvent(
      PlayerUseSpecialItemEvent origin,
      ProjectileSource shooter,
      Location spawnLocation,
      Vector direction,
      float yield,
      double flightSpeed,
      boolean flyStraight,
      @Nullable Integer autoIgnitionTicks) {

    super(origin.getPlayer());

    this.origin = origin;
    this.shooter = shooter;
    this.spawnLocation = spawnLocation;
    this.direction = direction;
    this.yield = yield;
    this.flightSpeed = flightSpeed;
    this.flyStraight = flyStraight;
    this.autoIgnitionTicks = autoIgnitionTicks;
  }

  @Override
  public Arena getArena() {
    return this.origin.getArena();
  }

  /**
   * Returns the event that was called right before this event.
   *
   * @return The origin event
   */
  public PlayerUseSpecialItemEvent getOriginEvent() {
    return this.origin;
  }

  /**
   * Returns the entity from whom the fireball shall come from.
   * <p>
   * By default it's equal to the player who has used the special item.
   * </p>
   *
   * @return The entity from whom the fireball shall come from
   */
  public ProjectileSource getShooter() {
    return this.shooter;
  }

  /**
   * Sets the entity from whom the fireball shall come from.
   *
   * @param shooter The new entity from whom the fireball shall come from
   */
  public void setShooter(ProjectileSource shooter) {
    Validate.notNull(shooter, "shooter");

    this.shooter = shooter;
  }

  /**
   * Returns the location where the fireball shall spawn.
   * <p>
   *  By default it's nearby the player's eye location.
   * </p>
   *
   * @return The location where the fireball shall spawn
   */
  public Location getSpawnLocation() {
    return this.spawnLocation;
  }

  /**
   * Sets the location where the fireball shall spawn.
   *
   * @param spawnLocation The new location where the fireball shall spawn
   */
  public void setSpawnLocation(Location spawnLocation) {
    Validate.notNull(spawnLocation, "spawnLocation");
    Validate.isTrue(spawnLocation.getWorld().equals(this.player.getWorld()), "spawnLocation's world doesn't match the player's world");

    this.spawnLocation = spawnLocation.clone();
  }

  /**
   * Returns the direction in which the fireball shall fly.
   * <p>
   *  By default it's equal to the player's looking direction.
   * </p>
   *
   * @return The direction in which the fireball shall fly
   */
  public Vector getDirection() {
    return this.direction;
  }

  /**
   * Sets the direction in which the fireball shall fly.
   *
   * @param direction The new direction in which the fireball shall fly
   */
  public void setDirection(Vector direction) {
    Validate.notNull(direction, "direction");

    this.direction = direction.clone();
  }

  /**
   * Returns the yield of the explosion.
   * <p>
   * Uses {@link org.bukkit.entity.Fireball#setYield(float)}.
   * </p>
   *
   * @return The yield of the explosion
   */
  public float getYield() {
    return this.yield;
  }

  /**
   * Sets the yield of the explosion.
   * <p>
   * Uses {@link org.bukkit.entity.Fireball#setYield(float)}.
   * </p>
   *
   * @param yield The new yield of the explosion
   */
  public void setYield(float yield) {
    this.yield = yield;
  }

  /**
   * Returns the speed of the fireball.
   *
   * @return The speed of the fireball
   */
  public double getFlightSpeed() {
    return this.flightSpeed;
  }

  /**
   * Sets the speed of the fireball.
   *
   * @param flightSpeed The new speed of the fireball
   */
  public void setFlightSpeed(double flightSpeed) {
    this.flightSpeed = flightSpeed;
  }

  /**
   * Returns whether the fireball shall fly straight.
   * <p>
   * Vanilla adds a small random deviation to the fireball's trajectory.
   * </p>
   *
   * @return If the fireball shall fly straight
   */
  public boolean isFlyingStraight() {
    return this.flyStraight;
  }

  /**
   * Set the amount of ticks after which the fireball shall ignite itself.
   *
   * @return The amount of ticks until it ignites. <code>null</code> if it shouldn't ignite itself
   */
  @Nullable
  public Integer getAutoIgnitionTicks() {
    return this.autoIgnitionTicks;
  }

  /**
   * Set the amount of ticks after which the fireball shall ignite itself.
   *
   * @param autoIgnitionTicks The amount of ticks until it ignites. <code>null</code> if it shouldn't ignite itself
   */
  public void setAutoIgnitionTicks(@Nullable Integer autoIgnitionTicks) {
    this.autoIgnitionTicks = autoIgnitionTicks;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
