package cn.acebrand.acevotemode.gamemode;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.spawner.SpawnerDurationModifier;

import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.event.arena.RoundEndEvent;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitTask;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 无限火力模式
 * 特点：资源生成速度大幅提升，爆炸物品无限使用
 */
public class UnlimitedFireMode extends GameModeBase implements Listener {

    // 资源生成配置
    private Map<String, ResourceConfig> resourceConfigs;

    // 生成器升级系统
    private Map<Arena, UpgradeState> arenaUpgradeStates;

    // ResourceBoosterHandler移植的字段
    private final String MODIFIER_ID = "acevotemode:unlimited-fire";
    private final Map<Spawner, SpawnerDurationModifier> spawnerModifiers = new HashMap<>();
    private final Map<Spawner, BukkitTask> spawnerTasks = new HashMap<>();
    private final Map<Spawner, Double> originalDropDurations = new HashMap<>();

    // 全局唯一计数器，确保每个NBT标签都不同
    private static final AtomicLong globalCounter = new AtomicLong(0);

    // 静态实例，用于PAPI访问
    private static UnlimitedFireMode instance;

    public UnlimitedFireMode(AceVoteMode plugin) {
        super(plugin, "unlimited-fire", "无限火力");
        this.arenaUpgradeStates = new ConcurrentHashMap<>();
        instance = this;
        loadResourceConfigs();

        // 注册事件监听器来监听RoundStart和RoundEnd事件
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 获取升级状态（用于PAPI）
     */
    public static UpgradeState getUpgradeState(Arena arena) {
        if (instance == null)
            return null;
        return instance.arenaUpgradeStates.get(arena);
    }

    @Override
    protected void createDefaultConfig() {
        // 添加配置文件头部注释
        addConfigComments();

        // 基础配置
        config.set("enabled", true);
        config.set("description", "资源生成速度大幅提升，快速收集资源享受激烈战斗");

        // 资源生成配置 - 铁锭（使用SpawnerDurationModifier统一管理，像cn源代码的资源加速器一样）
        config.set("resources.iron.interval", 2); // 生成间隔（秒）
        config.set("resources.iron.amount", 4); // 每次生成数量
        config.set("resources.iron.enabled", true); // 是否启用加速
        config.set("resources.iron.max-nearby-items", 64); // 生成器附近最大物品数量，防止卡服
        config.set("resources.iron.display-name", "&a经验"); // 自定义显示名称（可选）

        // 资源生成配置 - 金锭（使用SpawnerDurationModifier统一管理，像cn源代码的资源加速器一样）
        config.set("resources.gold.interval", 6); // 生成间隔（秒）
        config.set("resources.gold.amount", 2); // 每次生成数量
        config.set("resources.gold.enabled", true); // 是否启用加速
        config.set("resources.gold.max-nearby-items", 32); // 生成器附近最大物品数量，防止卡服
        config.set("resources.gold.display-name", "&a经验"); // 自定义显示名称（可选）

        // 注意：钻石和绿宝石的配置现在由升级系统管理，不需要在resources中配置
        // 所有资源类型现在都使用统一的SpawnerDurationModifier机制，参考cn源代码的资源加速器实现

        // 消息配置
        config.set("messages.mode-start", "&c&l无限火力模式已启动！");
        config.set("messages.mode-description", "&7资源生成速度大幅提升，快速收集资源享受激烈战斗！");
        config.set("messages.upgrade-messages-enabled", true); // 是否启用升级消息提示

        // 生成器升级配置
        addUpgradeConfigs();

        saveConfig();

        // 保存后添加详细注释到文件
        addDetailedCommentsToFile();
    }

    @Override
    protected void onConfigReload() {
        loadResourceConfigs();
    }

    /**
     * 加载资源配置
     */
    private void loadResourceConfigs() {
        plugin.getLogger().info("[无限火力] 开始加载资源配置");
        resourceConfigs = new HashMap<>();

        // 加载铁锭和金锭的配置（从resources配置节）
        if (config.getConfigurationSection("resources") != null) {
            plugin.getLogger().info("[无限火力] 找到resources配置节");
            for (String resourceType : config.getConfigurationSection("resources").getKeys(false)) {
                String path = "resources." + resourceType;

                boolean enabled = config.getBoolean(path + ".enabled", false);
                int interval = config.getInt(path + ".interval", 10);
                int amount = config.getInt(path + ".amount", 1);
                int maxNearbyItems = config.getInt(path + ".max-nearby-items", 64); // 默认64个物品

                ResourceConfig resourceConfig = new ResourceConfig(resourceType, interval, amount, enabled,
                        maxNearbyItems);
                resourceConfigs.put(resourceType, resourceConfig);

            }
        } else {

        }

        // 为钻石和绿宝石创建占位符配置（实际值由升级系统管理）
        // 这些配置主要用于标识这些资源类型是启用的
        resourceConfigs.put("diamond", new ResourceConfig("diamond", 20, 1, true));
        resourceConfigs.put("emerald", new ResourceConfig("emerald", 45, 1, true));

    }

    @Override
    public void onGameStart(Arena arena) {

        // 检查是否已经启动过，防止重复执行
        if (arenaUpgradeStates.containsKey(arena)) {

            return;
        }

        // 向所有玩家发送模式开始消息（正确处理颜色代码）
        plugin.getLogger().info("[无限火力] 步骤1: 发送启动消息给玩家");
        String startMessage = config.getString("messages.mode-start", "&c&l无限火力模式已启动！");
        String description = config.getString("messages.mode-description", "&7资源生成速度大幅提升，爆炸物品无限使用！");

        for (Player player : arena.getPlayers()) {
            // 使用简化的颜色代码处理
            player.sendMessage(translateColors(startMessage));
            player.sendMessage(translateColors(description));
        }

        // 注册事件监听器（用于监听SpawnerDropEvent）

        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // 修改生成器时间（禁用原版生成，使用我们的配置）

        modifySpawnerTimes(arena);

        // 启动升级系统（包含游戏开始时的立即升级）

        startUpgradeSystem(arena);

    }

    @Override
    public void onGameEnd(Arena arena) {

        // 恢复生成器原始时间
        restoreSpawnerTimes(arena);

        // 清理升级状态
        cleanupUpgradeState(arena);

        // 清理自定义事件任务
        plugin.getCustomEventManager().cleanupArenaTasks(arena);

    }

    /**
     * 为生成器设置自定义全息显示（参考TweaksAddon的实现）
     */
    private void setCustomHologramForSpawner(Spawner spawner, ResourceConfig resourceConfig, Arena arena) {
        try {
            // 获取生成器的基本信息
            String spawnerName = spawner.getDropType().getConfigName();
            String displayName = getResourceDisplayName(resourceConfig.getType());

            // 获取当前等级信息
            String tierInfo = getCurrentTierInfo(arena, resourceConfig.getType());

            // 创建自定义全息文本（使用MBedwars的占位符系统）
            String translatedTierInfo = ChatColor.translateAlternateColorCodes('&', tierInfo);

            String[] customLines = new String[] {
                    ChatColor.translateAlternateColorCodes('&', spawnerName), // 转换生成器名称的颜色代码
                    translatedTierInfo, // 转换等级信息
                    ChatColor.translateAlternateColorCodes('&', "&7生成间隔: &a" + resourceConfig.getInterval() + "秒"),
                    ChatColor.translateAlternateColorCodes('&', "&7下次生成: &c{time}") // {time}会被MBedwars自动替换
            };

            // 设置自定义全息文本
            spawner.setOverridingHologramLines(customLines);

        } catch (Exception e) {
        }
    }

    /**
     * 获取当前等级信息
     */
    private String getCurrentTierInfo(Arena arena, String resourceType) {
        UpgradeState upgradeState = arenaUpgradeStates.get(arena);
        if (upgradeState == null) {
            return "&e等级 I"; // 默认1级，使用罗马数字，使用&符号
        }

        UpgradeInfo currentUpgrade = upgradeState.getCurrentUpgrade();
        if (currentUpgrade != null && currentUpgrade.resourceType.equals(resourceType)) {
            // 显示下一个升级的倒计时
            int seconds = upgradeState.getSecondsToNextUpgrade();
            if (seconds > 0) {
                int min = seconds / 60;
                String sec = String.valueOf(seconds - (min * 60));
                if (sec.length() == 1)
                    sec = "0" + sec;

                // 获取当前等级和下一个等级
                int currentTier = getCurrentTier(arena, resourceType);
                String currentTierDisplay = getTierRomanDisplay(currentTier);
                String nextTier = getTierRomanDisplay(currentUpgrade.tier);

                return "&e等级 " + currentTierDisplay + " &7→ &c" + nextTier + " &7在 &a" + min + ":" + sec;
            }
        }

        // 检查已完成的升级，确定当前等级
        int currentTier = getCurrentTier(arena, resourceType);
        String tierDisplay = getTierRomanDisplay(currentTier);
        return "&e等级 " + tierDisplay;
    }

    /**
     * 获取等级的罗马数字显示
     */
    private String getTierRomanDisplay(int tier) {
        switch (tier) {
            case 1:
                return "I";
            case 2:
                return "II";
            case 3:
                return "III";
            case 4:
                return "IV";
            case 5:
                return "V";
            default:
                return String.valueOf(tier);
        }
    }

    /**
     * 获取当前等级
     */
    private int getCurrentTier(Arena arena, String resourceType) {
        UpgradeState upgradeState = arenaUpgradeStates.get(arena);
        if (upgradeState == null) {
            return 1; // 游戏开始时默认等级1（已应用默认升级）
        }

        // 检查已完成的升级，找到该资源类型的最高等级
        int maxTier = 1; // 默认等级1（游戏开始时已应用）

        // 遍历升级序列，找到已完成的最高等级
        for (int i = 0; i < upgradeState.getCurrentUpgradeIndex(); i++) {
            UpgradeInfo upgrade = getUpgradeByIndex(i);
            if (upgrade != null && upgrade.resourceType.equals(resourceType)) {
                maxTier = Math.max(maxTier, upgrade.tier);
            }
        }

        return maxTier;
    }

    /**
     * 根据索引获取升级信息
     */
    private UpgradeInfo getUpgradeByIndex(int index) {
        try {
            // 先尝试从升级序列中读取
            String path = "upgrade-sequence." + (index + 1);
            if (config.contains(path + ".type")) {
                String resourceType = config.getString(path + ".type");
                int tier = config.getInt(path + ".tier");
                double timeSeconds = config.getDouble(path + ".time");
                int interval = config.getInt(path + ".interval");
                int amount = config.getInt(path + ".amount");
                String message = config.getString(path + ".message", "");
                boolean showMessage = config.getBoolean(path + ".show-message", true);

                return new UpgradeInfo(resourceType, tier, timeSeconds, interval, amount, message, showMessage);
            }

            // 尝试从自定义事件中读取
            path = "custom-events." + (index + 1);
            if (config.contains(path + ".type")) {
                String eventType = config.getString(path + ".type");
                double timeSeconds = config.getDouble(path + ".time");
                String message = config.getString(path + ".message", "");
                boolean showMessage = config.getBoolean(path + ".show-message", true);
                int dragonCount = config.getInt(path + ".dragon-count", 1);
                boolean destroyGenerators = config.getBoolean(path + ".destroy-generators", false);
                int gameEndCountdown = config.getInt(path + ".game-end-countdown", 30);
                double dragonSpeed = config.getDouble(path + ".dragon-speed", 0.8);
                double dragonBlockDestroyRadius = config.getDouble(path + ".dragon-block-destroy-radius", 2.0);
                boolean spawnDefaultDragon = config.getBoolean(path + ".spawn-default-dragon", false);
                boolean disableDragonDeathSound = config.getBoolean(path + ".disable-dragon-death-sound", true);

                return new UpgradeInfo(eventType, timeSeconds, message, showMessage, dragonCount, destroyGenerators,
                        gameEndCountdown, dragonSpeed, dragonBlockDestroyRadius, spawnDefaultDragon,
                        disableDragonDeathSound);
            }

            return null; // 没有找到配置
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 监听生成器掉落事件，修改生成数量
     */

    /**
     * 监听回合结束事件
     */
    @EventHandler
    public void onRoundEnd(RoundEndEvent event) {
        Arena arena = event.getArena();

        // 检查是否是无限火力模式
        if (!isUnlimitedFireArena(arena)) {
            return;
        }

        // 恢复生成器原始时间
        restoreSpawnerTimes(arena);

        // 清理升级状态
        cleanupUpgradeState(arena);

    }

    // 移除SpawnerDropEvent处理，改用ResourceBoosterHandler的方式
    // 通过setupResourceMultiplier方法来处理资源数量倍增

    /**
     * 检查竞技场是否是无限火力模式
     */
    private boolean isUnlimitedFireArena(Arena arena) {
        // 通过GameModeManager检查当前模式
        String currentMode = plugin.getGameModeManager().getCurrentMode(arena);
        return "unlimited-fire".equals(currentMode);
    }

    /**
     * 转换颜色代码
     */
    private String translateColors(String text) {
        if (text == null)
            return "";
        return text.replace("&", "§");
    }

    /**
     * 获取资源显示名称（支持自定义名称如"&a经验"等）
     */
    private String getResourceDisplayName(String resourceType) {
        // 首先检查配置文件中是否有自定义显示名称
        String customName = config.getString("resources." + resourceType + ".display-name");
        if (customName != null && !customName.isEmpty()) {
            return translateColors(customName);
        }

        // 如果没有自定义名称，使用默认名称
        switch (resourceType.toLowerCase()) {
            case "diamond":
                return "钻石";
            case "emerald":
                return "绿宝石";
            case "iron":
                return "铁锭";
            case "gold":
                return "金锭";
            default:
                return resourceType;
        }
    }

    /**
     * 清理升级状态
     */
    private void cleanupUpgradeState(Arena arena) {
        UpgradeState state = arenaUpgradeStates.remove(arena);
        if (state != null) {
            state.cancelAllTasks();
        }
    }

    /**
     * 恢复生成器原始时间（移植ResourceBoosterHandler的清理逻辑）
     */
    private void restoreSpawnerTimes(Arena arena) {
        try {
            plugin.getLogger().info("[无限火力] 开始清理竞技场 " + arena.getName() + " 的生成器");

            for (Spawner spawner : arena.getSpawners()) {
                String typeId = spawner.getDropType().getId();

                // 取消资源倍增任务（移植自ResourceBoosterHandler）
                BukkitTask task = spawnerTasks.remove(spawner);
                if (task != null) {
                    task.cancel();
                    plugin.getLogger().info("[无限火力] 已取消 " + typeId + " 生成器的倍增任务");
                }

                // 移除禁用原始生成的修改器
                try {
                    List<SpawnerDurationModifier> modifiers = new ArrayList<>(spawner.getDropDurationModifiers());
                    for (SpawnerDurationModifier mod : modifiers) {
                        if (mod.getId().startsWith(MODIFIER_ID)) {
                            spawner.removeDropDurationModifier(mod);
                            plugin.getLogger().info("[无限火力] 已移除 " + typeId + " 生成器的修改器: " + mod.getId());
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("[无限火力] 移除 " + typeId + " 生成器修改器失败: " + e.getMessage());
                }

                // 清理原始间隔记录
                originalDropDurations.remove(spawner);
                plugin.getLogger().info("[无限火力] 已清理 " + typeId + " 生成器的原始间隔记录");

                // 恢复原始全息显示
                spawner.setOverridingHologramLines(null);
                plugin.getLogger().info("[无限火力] 已恢复 " + typeId + " 生成器全息显示");
            }

            plugin.getLogger().info("[无限火力] 竞技场 " + arena.getName() + " 生成器清理完成");
        } catch (Exception e) {
            plugin.getLogger().severe("[无限火力] 清理生成器时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取生成器的默认间隔时间
     */
    private double getDefaultInterval(String typeId) {
        switch (typeId.toLowerCase()) {
            case "iron":
                return 1.0; // 铁锭默认1秒
            case "gold":
                return 8.0; // 金锭默认8秒
            case "diamond":
                return 30.0; // 钻石默认30秒
            case "emerald":
                return 60.0; // 绿宝石默认60秒
            default:
                return 10.0; // 其他默认10秒
        }
    }

    /**
     * 修改生成器时间（移植ResourceBoosterHandler的技术）
     */
    private void modifySpawnerTimes(Arena arena) {
        try {
            plugin.getLogger().info("[无限火力] 开始为竞技场 " + arena.getName() + " 修改生成器时间");

            for (Spawner spawner : arena.getSpawners()) {
                if (!spawner.exists()) {
                    continue;
                }

                DropType dropType = spawner.getDropType();
                String typeId = dropType.getId();
                String normalizedType = getNormalizedResourceName(typeId);

                // 获取对应的配置
                ResourceConfig resourceConfig = resourceConfigs.get(typeId);
                if (resourceConfig != null && resourceConfig.isEnabled()) {

                    // 记录原始生成间隔
                    double originalInterval = spawner.getCurrentDropDuration();
                    originalDropDurations.put(spawner, originalInterval);
                    plugin.getLogger().info("[无限火力] " + typeId + " 原始间隔: " + originalInterval + "秒，将使用自定义生成: "
                            + resourceConfig.getInterval() + "秒");

                    // 设置自定义资源生成（完全替换原始生成）
                    setupCustomResourceGeneration(spawner, resourceConfig, normalizedType);

                    // 为所有生成器设置自定义全息显示
                    setCustomHologramForSpawner(spawner, resourceConfig, arena);
                    plugin.getLogger().info("[无限火力] " + typeId + " 生成器配置完成，间隔: " + resourceConfig.getInterval()
                            + "秒，数量: " + resourceConfig.getAmount() + "个");

                } else {
                    plugin.getLogger().info("[无限火力] " + typeId + " 生成器未配置或已禁用");
                }
            }
            plugin.getLogger().info("[无限火力] 竞技场 " + arena.getName() + " 生成器时间修改完成");
        } catch (Exception e) {
            plugin.getLogger().severe("[无限火力] 修改生成器时间时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取标准化的资源名称（移植自ResourceBoosterHandler）
     */
    private String getNormalizedResourceName(String typeId) {
        switch (typeId.toLowerCase()) {
            case "iron":
            case "iron_ingot":
                return "iron";
            case "gold":
            case "gold_ingot":
                return "gold";
            case "diamond":
                return "diamond";
            case "emerald":
                return "emerald";
            default:
                return typeId.toLowerCase();
        }
    }

    /**
     * 设置自定义资源生成（完全替换原始生成逻辑）
     */
    private void setupCustomResourceGeneration(Spawner spawner, ResourceConfig resourceConfig, String resourceType) {
        plugin.getLogger().info("[无限火力] 为 " + resourceType + " 生成器设置自定义生成: 间隔" + resourceConfig.getInterval() + "秒，数量"
                + resourceConfig.getAmount() + "个");

        // 禁用原始生成器（设置极长的间隔）
        try {
            spawner.addDropDurationModifier(
                    MODIFIER_ID + ":disable",
                    plugin,
                    SpawnerDurationModifier.Operation.SET,
                    999999.0); // 设置极长间隔来禁用原始生成
            plugin.getLogger().info("[无限火力] " + resourceType + " 原始生成器已禁用");
        } catch (Exception e) {
            plugin.getLogger().warning("[无限火力] " + resourceType + " 禁用原始生成器失败: " + e.getMessage());
        }

        // 创建自定义生成任务
        long intervalTicks = (long) (resourceConfig.getInterval() * 20); // 转换为tick

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查游戏是否还在进行中
                    Arena arena = spawner.getArena();
                    if (arena == null || arena.getStatus() != de.marcely.bedwars.api.arena.ArenaStatus.RUNNING) {
                        plugin.getLogger().info("[无限火力] 游戏已结束，取消 " + resourceType + " 生成器任务");
                        // 取消当前任务
                        BukkitTask currentTask = spawnerTasks.get(spawner);
                        if (currentTask != null && !currentTask.isCancelled()) {
                            currentTask.cancel();
                            spawnerTasks.remove(spawner);
                        }
                        return;
                    }

                    // 检查生成器是否仍然存在
                    if (!spawner.exists()) {

                        return;
                    }

                    // 检查生成器附近是否有太多物品（使用配置中的max-nearby-items）
                    int maxNearbyItems = resourceConfig.getMaxNearbyItems();
                    int currentNearbyItems = UnlimitedFireMode.this.countNearbyResourceItems(spawner, resourceType);

                    // 添加详细的调试日志

                    if (currentNearbyItems >= maxNearbyItems) {

                        return;
                    }

                    // 生成指定数量的资源（添加NBT防止堆叠）
                    for (int i = 0; i < resourceConfig.getAmount(); i++) {
                        // 先正常掉落
                        spawner.drop(true);

                        // 延迟添加NBT标签防止堆叠
                        final int dropIndex = i;
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            addNBTToNearbyItems(spawner, resourceType, dropIndex);
                        }, 1L);
                    }

                } catch (Exception e) {

                }
            }
        }, intervalTicks, intervalTicks); // 按配置的间隔执行

        // 保存任务引用以便后续清理
        spawnerTasks.put(spawner, task);

    }

    /**
     * 为附近的物品添加NBT标签防止堆叠
     */
    private void addNBTToNearbyItems(Spawner spawner, String resourceType, int dropIndex) {
        try {
            // 获取生成器附近的物品实体
            de.marcely.bedwars.tools.location.XYZ spawnerXYZ = spawner.getLocation();
            org.bukkit.Location spawnerLoc = spawnerXYZ.toLocation(spawner.getArena().getGameWorld());
            java.util.Collection<org.bukkit.entity.Entity> nearbyEntities = spawnerLoc.getWorld()
                    .getNearbyEntities(spawnerLoc, 2.0, 2.0, 2.0);

            for (org.bukkit.entity.Entity entity : nearbyEntities) {
                if (entity instanceof org.bukkit.entity.Item) {
                    org.bukkit.entity.Item item = (org.bukkit.entity.Item) entity;
                    org.bukkit.inventory.ItemStack itemStack = item.getItemStack();

                    // 检查是否是我们要处理的资源类型
                    if (isTargetResourceType(itemStack, resourceType)) {
                        // 添加唯一NBT标签
                        org.bukkit.inventory.meta.ItemMeta meta = itemStack.getItemMeta();
                        if (meta != null) {
                            // 添加隐藏的NBT数据
                            meta.setDisplayName("§r" + meta.getDisplayName()); // 重置显示名称

                            // 使用PersistentDataContainer添加唯一标识
                            // 组合多个因素确保绝对唯一：时间戳 + 全局计数器 + 随机数 + dropIndex
                            long uniqueId = globalCounter.incrementAndGet();
                            long timestamp = System.currentTimeMillis();
                            int randomNum = (int) (Math.random() * 10000);
                            String uniqueValue = resourceType + "_" + timestamp + "_" + uniqueId + "_" + randomNum + "_"
                                    + dropIndex;

                            org.bukkit.NamespacedKey key = new org.bukkit.NamespacedKey(plugin, "unlimited_fire_id");
                            meta.getPersistentDataContainer().set(key,
                                    org.bukkit.persistence.PersistentDataType.STRING,
                                    uniqueValue);

                            itemStack.setItemMeta(meta);
                            item.setItemStack(itemStack);

                        }
                    }
                }
            }
        } catch (Exception e) {

        }
    }

    /**
     * 检查物品是否是目标资源类型
     */
    private boolean isTargetResourceType(org.bukkit.inventory.ItemStack itemStack, String resourceType) {
        if (itemStack == null || itemStack.getType() == org.bukkit.Material.AIR) {
            return false;
        }

        org.bukkit.Material material = itemStack.getType();
        switch (resourceType.toLowerCase()) {
            case "iron":
                return material == org.bukkit.Material.IRON_INGOT;
            case "gold":
                return material == org.bukkit.Material.GOLD_INGOT;
            case "diamond":
                return material == org.bukkit.Material.DIAMOND;
            case "emerald":
                return material == org.bukkit.Material.EMERALD;
            default:
                return false;
        }
    }

    /**
     * 使用反射获取生成器内部时钟（移植自ResourceBoosterHandler）
     */
    private int getInternalClock(Spawner spawner) {
        try {
            java.lang.reflect.Method method = spawner.getClass().getMethod("getInternalClock");
            method.setAccessible(true);
            return (Integer) method.invoke(spawner);
        } catch (Exception e) {
            // 如果反射失败，返回默认值
            return 0;
        }
    }

    /**
     * 使用反射获取生成器剩余掉落时间（移植自ResourceBoosterHandler）
     */
    private int getRemainingNextDropTime(Spawner spawner) {
        try {
            java.lang.reflect.Method method = spawner.getClass().getMethod("getRemainingNextDropTime");
            method.setAccessible(true);
            return (Integer) method.invoke(spawner);
        } catch (Exception e) {
            // 如果反射失败，返回默认值
            return Integer.MAX_VALUE;
        }
    }

    /**
     * 计算生成器附近的特定资源物品数量
     */
    private int countNearbyResourceItems(Spawner spawner, String resourceType) {
        try {
            // 获取生成器位置
            de.marcely.bedwars.tools.location.XYZ spawnerXYZ = spawner.getLocation();
            org.bukkit.Location spawnerLoc = spawnerXYZ.toLocation(spawner.getArena().getGameWorld());

            // 获取附近的实体
            java.util.Collection<org.bukkit.entity.Entity> nearbyEntities = spawnerLoc.getWorld()
                    .getNearbyEntities(spawnerLoc, 3.0, 3.0, 3.0); // 3x3x3 范围

            int count = 0;
            for (org.bukkit.entity.Entity entity : nearbyEntities) {
                if (entity instanceof org.bukkit.entity.Item) {
                    org.bukkit.entity.Item item = (org.bukkit.entity.Item) entity;
                    org.bukkit.inventory.ItemStack itemStack = item.getItemStack();

                    // 检查是否是目标资源类型
                    if (isTargetResourceType(itemStack, resourceType)) {
                        count += itemStack.getAmount();
                    }
                }
            }

            return count;
        } catch (Exception e) {

            return 0;
        }
    }

    /**
     * 添加升级配置
     */
    private void addUpgradeConfigs() {
        // 升级事件序列配置（使用相对时间逻辑）
        // time 表示相对于上一个升级完成后的等待时间（秒）

        // 游戏开始时立即应用的默认升级（等级1）
        config.set("upgrade-sequence.1.type", "diamond");
        config.set("upgrade-sequence.1.tier", 1);
        config.set("upgrade-sequence.1.time", 0); // 0秒（游戏开始立即执行）
        config.set("upgrade-sequence.1.interval", 20); // 每20秒生成
        config.set("upgrade-sequence.1.amount", 2); // 每次2个
        config.set("upgrade-sequence.1.message", "&6钻石生成器已设置为无限火力模式！");
        config.set("upgrade-sequence.1.show-message", true); // 是否显示此事件的聊天消息

        config.set("upgrade-sequence.2.type", "emerald");
        config.set("upgrade-sequence.2.tier", 1);
        config.set("upgrade-sequence.2.time", 0); // 0秒（第1个升级完成后立即执行）
        config.set("upgrade-sequence.2.interval", 45); // 每45秒生成
        config.set("upgrade-sequence.2.amount", 2); // 每次2个
        config.set("upgrade-sequence.2.message", "&a绿宝石生成器已设置为无限火力模式！");
        config.set("upgrade-sequence.2.show-message", true); // 是否显示此事件的聊天消息

        // 钻石和绿宝石的自定义显示名称配置（可选）
        config.set("resources.diamond.display-name", "&b钻石"); // 自定义显示名称
        config.set("resources.emerald.display-name", "&a绿宝石"); // 自定义显示名称

        // 后续的真正升级（相对时间）
        config.set("upgrade-sequence.3.type", "diamond");
        config.set("upgrade-sequence.3.tier", 2);
        config.set("upgrade-sequence.3.time", 20); // 第2个升级完成后等待20秒
        config.set("upgrade-sequence.3.interval", 15); // 每15秒生成
        config.set("upgrade-sequence.3.amount", 1); // 每次1个
        config.set("upgrade-sequence.3.message", "&6钻石生成器已升级到 &c2级&6！");
        config.set("upgrade-sequence.3.show-message", true); // 是否显示此事件的聊天消息

        config.set("upgrade-sequence.4.type", "emerald");
        config.set("upgrade-sequence.4.tier", 2);
        config.set("upgrade-sequence.4.time", 20); // 第3个升级完成后等待20秒
        config.set("upgrade-sequence.4.interval", 30); // 每30秒生成
        config.set("upgrade-sequence.4.amount", 1); // 每次1个
        config.set("upgrade-sequence.4.message", "&a绿宝石生成器已升级到 &c2级&a！");
        config.set("upgrade-sequence.4.show-message", true); // 是否显示此事件的聊天消息

        config.set("upgrade-sequence.5.type", "diamond");
        config.set("upgrade-sequence.5.tier", 3);
        config.set("upgrade-sequence.5.time", 20); // 第4个升级完成后等待20秒
        config.set("upgrade-sequence.5.interval", 10); // 每10秒生成
        config.set("upgrade-sequence.5.amount", 2); // 每次2个
        config.set("upgrade-sequence.5.message", "&6钻石生成器已升级到 &c3级&6！");
        config.set("upgrade-sequence.5.show-message", true); // 是否显示此事件的聊天消息

        config.set("upgrade-sequence.6.type", "emerald");
        config.set("upgrade-sequence.6.tier", 3);
        config.set("upgrade-sequence.6.time", 20); // 第5个升级完成后等待20秒
        config.set("upgrade-sequence.6.interval", 20); // 每20秒生成
        config.set("upgrade-sequence.6.amount", 2); // 每次2个
        config.set("upgrade-sequence.6.message", "&a绿宝石生成器已升级到 &c3级&a！");
        config.set("upgrade-sequence.6.show-message", true); // 是否显示此事件的聊天消息

        // 自定义事件配置
        config.set("custom-events.7.type", "dragon");
        config.set("custom-events.7.time", 30); // 第6个升级完成后等待30秒
        config.set("custom-events.7.message", "&c&l末影龙已降临战场！");
        config.set("custom-events.7.show-message", true);
        config.set("custom-events.7.dragon-count", 1); // 每队生成1条龙
        config.set("custom-events.7.destroy-generators", true); // 是否摧毁生成器
        config.set("custom-events.7.dragon-speed", 1.0); // 龙的速度（无限火力模式更快）
        config.set("custom-events.7.dragon-block-destroy-radius", 2.5); // 方块摧毁半径
        config.set("custom-events.7.spawn-default-dragon", true); // 生成默认龙
        config.set("custom-events.7.disable-dragon-death-sound", true); // 禁用龙死亡音效

        config.set("custom-events.8.type", "bed-destroy");
        config.set("custom-events.8.time", 60); // 龙事件后等待60秒
        config.set("custom-events.8.message", "&4&l所有床铺已被摧毁！");
        config.set("custom-events.8.show-message", true);

        config.set("custom-events.9.type", "game-end");
        config.set("custom-events.9.time", 120); // 床摧毁后等待120秒
        config.set("custom-events.9.message", "&c&l游戏即将结束！");
        config.set("custom-events.9.show-message", true);
        config.set("custom-events.9.game-end-countdown", 30); // 游戏结束倒计时30秒

        // 可以继续添加更多升级事件...
        // config.set("upgrade-sequence.10.type", "diamond");
        // config.set("upgrade-sequence.10.tier", 4);
        // config.set("upgrade-sequence.10.time", 120);
        // ...
    }

    /**
     * 添加配置文件注释
     */
    private void addConfigComments() {
        // 这个方法可以在未来扩展，用于添加配置文件头部注释
    }

    /**
     * 添加详细注释到配置文件
     */
    private void addDetailedCommentsToFile() {
        try {
            // 读取现有配置文件内容
            java.util.List<String> lines = java.nio.file.Files.readAllLines(configFile.toPath());
            java.util.List<String> newLines = new java.util.ArrayList<>();

            // 添加文件头部注释
            newLines.add("# ========================================");
            newLines.add("# 无限火力模式配置文件");
            newLines.add("# AceVoteMode Plugin - Unlimited Fire Mode");
            newLines.add("# ========================================");
            newLines.add("");
            newLines.add("# 基础配置");
            newLines.add("# enabled: 是否启用此游戏模式");
            newLines.add("# description: 模式描述");

            // 处理每一行，添加相应注释
            for (String line : lines) {
                if (line.trim().startsWith("resources:")) {
                    newLines.add("");
                    newLines.add("# ========================================");
                    newLines.add("# 资源生成配置");
                    newLines.add("# interval: 生成间隔（秒）- 数值越小生成越快");
                    newLines.add("# amount: 每次生成数量 - 每次生成多少个物品");
                    newLines.add("# enabled: 是否启用加速 - true启用，false使用默认速度");
                    newLines.add("# ========================================");
                } else if (line.trim().startsWith("iron:")) {
                    newLines.add("  # 铁锭配置 - 基础资源，建议快速生成");
                } else if (line.trim().startsWith("gold:")) {
                    newLines.add("  # 金锭配置 - 中级资源，适中生成速度");
                } else if (line.trim().startsWith("diamond:")) {
                    newLines.add("  # 钻石配置 - 高级资源，较慢生成");
                } else if (line.trim().startsWith("emerald:")) {
                    newLines.add("  # 绿宝石配置 - 顶级资源，最慢生成");
                } else if (line.trim().startsWith("messages:")) {
                    newLines.add("");
                    newLines.add("# ========================================");
                    newLines.add("# 消息配置");
                    newLines.add("# 支持颜色代码：&a绿色 &c红色 &e黄色 &7灰色 等");
                    newLines.add("# 支持格式代码：&l粗体 &o斜体 &n下划线 等");
                    newLines.add("# upgrade-messages-enabled: 是否在聊天栏显示升级消息");
                    newLines.add("#   true = 显示升级消息, false = 不显示升级消息");
                    newLines.add("# ========================================");
                }
                newLines.add(line);
            }

            // 写回文件
            java.nio.file.Files.write(configFile.toPath(), newLines);

        } catch (Exception e) {
        }
    }

    /**
     * 资源配置类
     */
    /**
     * 启动升级系统
     */
    private void startUpgradeSystem(Arena arena) {
        // 检查是否已经有升级状态，如果有则先清理
        UpgradeState existingState = arenaUpgradeStates.get(arena);
        if (existingState != null) {
            existingState.cancelAllTasks();
        }

        UpgradeState upgradeState = new UpgradeState();
        arenaUpgradeStates.put(arena, upgradeState);

        // 安排第一个升级
        scheduleNextUpgrade(arena);

    }

    /**
     * 安排下一个升级（按顺序）
     */
    private void scheduleNextUpgrade(Arena arena) {
        UpgradeState state = arenaUpgradeStates.get(arena);
        if (state == null)
            return;

        // 获取下一个升级信息
        UpgradeInfo nextUpgrade = getNextUpgradeInfo(state.getCurrentUpgradeIndex());
        if (nextUpgrade == null) {
            // 清理当前升级状态，表示所有升级已完成
            state.setCurrentUpgrade(null);
            state.setNextUpgradeTime(0);
            return;
        }

        // time 是相对于当前时间的延迟时间（秒）
        long delayMs = (long) (nextUpgrade.timeSeconds * 1000);
        long delayTicks = delayMs / 50; // 50ms = 1 tick

        // 计算升级的绝对时间
        long upgradeAbsoluteTime = System.currentTimeMillis() + delayMs;

        BukkitTask task = Bukkit.getScheduler().runTaskLater(plugin, () -> {
            executeUpgrade(arena, nextUpgrade);
            // 升级完成后，安排下一个升级
            state.incrementUpgradeIndex();
            scheduleNextUpgrade(arena);
        }, delayTicks);

        state.addTask(task);
        state.setNextUpgrade(nextUpgrade, upgradeAbsoluteTime);

    }

    /**
     * 获取下一个升级信息（从配置序列中读取，包括自定义事件）
     */
    private UpgradeInfo getNextUpgradeInfo(int upgradeIndex) {
        // 使用 getUpgradeByIndex 方法，它能正确处理升级事件和自定义事件
        return getUpgradeByIndex(upgradeIndex);
    }

    /**
     * 执行升级
     */
    private void executeUpgrade(Arena arena, UpgradeInfo upgradeInfo) {
        try {
            // 检查是否是自定义事件
            if (!"upgrade".equals(upgradeInfo.eventType)) {
                executeCustomEvent(arena, upgradeInfo);
                return;
            }

            // 更新资源配置中的数量和间隔
            ResourceConfig resourceConfig = resourceConfigs.get(upgradeInfo.resourceType);
            if (resourceConfig != null) {
                resourceConfig.setInterval(upgradeInfo.interval);
                resourceConfig.setAmount(upgradeInfo.amount);
            }

            // 升级所有对应类型的生成器
            DropType dropType = GameAPI.get().getDropTypeById(upgradeInfo.resourceType);
            if (dropType != null) {
                for (Spawner spawner : arena.getSpawners()) {
                    if (spawner.getDropType().equals(dropType)) {
                        // 移除旧的修改器
                        SpawnerDurationModifier oldModifier = spawner
                                .getDropDurationModifier("acevotemode:unlimited-fire");
                        if (oldModifier != null) {
                            spawner.removeDropDurationModifier(oldModifier);
                        }

                        // 添加新的修改器
                        spawner.addDropDurationModifier(
                                "acevotemode:unlimited-fire",
                                plugin,
                                SpawnerDurationModifier.Operation.SET,
                                upgradeInfo.interval);

                        // 更新全息显示
                        updateHologramForUpgrade(spawner, upgradeInfo.resourceType, upgradeInfo.tier,
                                upgradeInfo.interval);
                    }
                }
            }

            // 广播升级消息（如果此事件启用消息显示）
            for (Player player : arena.getPlayers()) {
                if (upgradeInfo.showMessage) {
                    player.sendMessage(translateColors(upgradeInfo.message));
                }
                // 播放升级音效
                try {
                    player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_ANVIL_USE, 1.0f, 1.0f);
                } catch (Exception e) {
                    // 忽略音效错误
                }
            }

        } catch (Exception e) {
        }
    }

    /**
     * 执行自定义事件
     */
    private void executeCustomEvent(Arena arena, UpgradeInfo upgradeInfo) {
        try {
            // 创建事件配置
            cn.acebrand.acevotemode.events.EventConfig eventConfig = new cn.acebrand.acevotemode.events.EventConfig(
                    upgradeInfo.eventType,
                    upgradeInfo.timeSeconds,
                    upgradeInfo.message,
                    upgradeInfo.showMessage,
                    upgradeInfo.dragonCount,
                    upgradeInfo.destroyGenerators,
                    upgradeInfo.gameEndCountdown,
                    upgradeInfo.dragonSpeed,
                    upgradeInfo.dragonBlockDestroyRadius,
                    upgradeInfo.spawnDefaultDragon,
                    upgradeInfo.disableDragonDeathSound);

            // 执行自定义事件
            plugin.getCustomEventManager().executeEvent(upgradeInfo.eventType, arena, eventConfig);

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to execute custom event " + upgradeInfo.eventType + " in arena "
                    + arena.getName() + ": " + e.getMessage());
        }
    }

    /**
     * 更新升级后的全息显示
     */
    private void updateHologramForUpgrade(Spawner spawner, String resourceType, int tier, int interval) {
        String tierDisplay = getTierRomanDisplay(tier);
        String[] customLines = new String[] {
                ChatColor.translateAlternateColorCodes('&', spawner.getDropType().getConfigName()),
                ChatColor.translateAlternateColorCodes('&', "&e等级 " + tierDisplay),
                ChatColor.translateAlternateColorCodes('&', "&7生成间隔: &a" + interval + "秒"),
                ChatColor.translateAlternateColorCodes('&', "&7下次生成: &c{time}")
        };
        spawner.setOverridingHologramLines(customLines);
    }

    private static class ResourceConfig {
        private final String type;
        private double interval; // 生成间隔（秒）- 可变，改为double支持小数
        private int amount; // 每次生成数量- 可变
        private final boolean enabled; // 是否启用
        private int maxNearbyItems; // 生成器附近最大物品数量

        public ResourceConfig(String type, double interval, int amount, boolean enabled, int maxNearbyItems) {
            this.type = type;
            this.interval = interval;
            this.amount = amount;
            this.enabled = enabled;
            this.maxNearbyItems = maxNearbyItems;
        }

        // 兼容旧的构造函数
        public ResourceConfig(String type, double interval, int amount, boolean enabled) {
            this(type, interval, amount, enabled, 64); // 默认64个物品
        }

        public String getType() {
            return type;
        }

        public double getInterval() {
            return interval;
        }

        public int getAmount() {
            return amount;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public int getMaxNearbyItems() {
            return maxNearbyItems;
        }

        // 添加setter方法
        public void setInterval(double interval) {
            this.interval = interval;
        }

        public void setAmount(int amount) {
            this.amount = amount;
        }

        public void setMaxNearbyItems(int maxNearbyItems) {
            this.maxNearbyItems = maxNearbyItems;
        }
    }

    /**
     * 升级状态类
     */
    public static class UpgradeState {
        private final List<BukkitTask> tasks = new ArrayList<>();
        private UpgradeInfo currentUpgrade = null;
        private int currentUpgradeIndex = 0;
        private long nextUpgradeTime = 0;
        private long gameStartTime = System.currentTimeMillis();

        public void addTask(BukkitTask task) {
            tasks.add(task);
        }

        public long getGameStartTime() {
            return gameStartTime;
        }

        public void setNextUpgrade(UpgradeInfo upgradeInfo) {
            this.currentUpgrade = upgradeInfo;
            this.nextUpgradeTime = System.currentTimeMillis() + (long) (upgradeInfo.timeSeconds * 1000);
        }

        public void setNextUpgrade(UpgradeInfo upgradeInfo, long actualUpgradeTime) {
            this.currentUpgrade = upgradeInfo;
            this.nextUpgradeTime = actualUpgradeTime;
        }

        public int getCurrentUpgradeIndex() {
            return currentUpgradeIndex;
        }

        public void incrementUpgradeIndex() {
            currentUpgradeIndex++;
        }

        public UpgradeInfo getCurrentUpgrade() {
            return currentUpgrade;
        }

        public void setCurrentUpgrade(UpgradeInfo upgradeInfo) {
            this.currentUpgrade = upgradeInfo;
        }

        public void setNextUpgradeTime(long time) {
            this.nextUpgradeTime = time;
        }

        public int getSecondsToNextUpgrade() {
            if (nextUpgradeTime == 0)
                return 0;
            return (int) Math.max(0, (nextUpgradeTime - System.currentTimeMillis()) / 1000);
        }

        public void cancelAllTasks() {
            for (BukkitTask task : tasks) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            tasks.clear();
        }
    }

    /**
     * 升级信息类
     */
    public static class UpgradeInfo {
        public final String resourceType;
        public final int tier;
        public final double timeSeconds;
        public final int interval;
        public final int amount;
        public final String message;
        public final boolean showMessage;
        public final String eventType; // 事件类型 ("upgrade", "dragon", "bed-destroy", "game-end")
        public final int dragonCount; // 龙的数量
        public final boolean destroyGenerators; // 是否摧毁生成器
        public final int gameEndCountdown; // 游戏结束倒计时
        public final double dragonSpeed; // 龙的速度
        public final double dragonBlockDestroyRadius; // 龙的方块摧毁半径
        public final boolean spawnDefaultDragon; // 是否生成默认龙
        public final boolean disableDragonDeathSound; // 是否禁用龙死亡音效

        // 升级事件构造器
        public UpgradeInfo(String resourceType, int tier, double timeSeconds, int interval, int amount, String message,
                boolean showMessage) {
            this.resourceType = resourceType;
            this.tier = tier;
            this.timeSeconds = timeSeconds;
            this.interval = interval;
            this.amount = amount;
            this.message = message;
            this.showMessage = showMessage;
            this.eventType = "upgrade";
            this.dragonCount = 1;
            this.destroyGenerators = false;
            this.gameEndCountdown = 30;
            this.dragonSpeed = 0.8;
            this.dragonBlockDestroyRadius = 2.0;
            this.spawnDefaultDragon = false;
            this.disableDragonDeathSound = true;
        }

        // 自定义事件构造器（简化版）
        public UpgradeInfo(String eventType, double timeSeconds, String message, boolean showMessage,
                int dragonCount, boolean destroyGenerators, int gameEndCountdown) {
            this(eventType, timeSeconds, message, showMessage, dragonCount, destroyGenerators, gameEndCountdown, 0.8,
                    2.0, false, true);
        }

        // 自定义事件构造器（完整版）
        public UpgradeInfo(String eventType, double timeSeconds, String message, boolean showMessage,
                int dragonCount, boolean destroyGenerators, int gameEndCountdown,
                double dragonSpeed, double dragonBlockDestroyRadius, boolean spawnDefaultDragon,
                boolean disableDragonDeathSound) {
            this.eventType = eventType;
            this.timeSeconds = timeSeconds;
            this.message = message;
            this.showMessage = showMessage;
            this.dragonCount = dragonCount;
            this.destroyGenerators = destroyGenerators;
            this.gameEndCountdown = gameEndCountdown;
            this.dragonSpeed = dragonSpeed;
            this.dragonBlockDestroyRadius = dragonBlockDestroyRadius;
            this.spawnDefaultDragon = spawnDefaultDragon;
            this.disableDragonDeathSound = disableDragonDeathSound;
            // 其他字段设为默认值
            this.resourceType = "";
            this.tier = 0;
            this.interval = 0;
            this.amount = 0;
        }
    }

    /**
     * 检查该模式是否在指定竞技场中激活
     */
    public boolean isActiveInArena(Arena arena) {
        return arenaUpgradeStates.containsKey(arena);
    }

    /**
     * 获取资源配置
     */
    private ResourceConfig getResourceConfig(String resourceType) {
        return resourceConfigs.get(resourceType);
    }

    /**
     * 更新生成器速度（响应spawner_multiplier升级）
     */
    public void updateSpawnerSpeed(Spawner spawner, String resourceType, double amplifier) {
        try {
            // 获取当前资源配置
            ResourceConfig resourceConfig = getResourceConfig(resourceType);
            if (resourceConfig == null || !resourceConfig.isEnabled()) {
                return;
            }

            // 计算新的生成间隔（原间隔除以倍数）
            double originalInterval = resourceConfig.getInterval();
            double newInterval = originalInterval / amplifier;

            // 取消现有的生成任务
            BukkitTask existingTask = spawnerTasks.get(spawner);
            if (existingTask != null && !existingTask.isCancelled()) {
                existingTask.cancel();
                spawnerTasks.remove(spawner);
            }

            // 更新现有配置的间隔
            resourceConfig.setInterval(newInterval);

            // 重新启动生成器
            String normalizedType = getNormalizedResourceName(resourceType);
            setupCustomResourceGeneration(spawner, resourceConfig, normalizedType);

        } catch (Exception e) {

        }
    }
}
