package cn.acebrand.acevotemode.events;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.BedDestructionInfo;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.message.Message;

/**
 * 床摧毁事件
 * 自动摧毁竞技场中所有队伍的床
 */
public class BedDestroyEvent implements CustomEvent {
    
    @Override
    public void execute(AceVoteMode plugin, Arena arena, EventConfig eventConfig) {
        plugin.getLogger().info("Executing bed destroy event in arena: " + arena.getName());
        
        // 广播消息
        if (eventConfig.isShowMessage()) {
            broadcastMessage(arena, eventConfig.getMessage());
        }
        
        // 播放音效
        playSound(arena, org.bukkit.Sound.ENTITY_WITHER_BREAK_BLOCK, 1.0f, 0.8f);
        
        // 摧毁所有队伍的床
        int bedsDestroyed = 0;
        for (Team team : arena.getEnabledTeams()) {
            if (destroyTeamBed(arena, team)) {
                bedsDestroyed++;
            }
        }
        
        plugin.getLogger().info("Destroyed " + bedsDestroyed + " beds in arena: " + arena.getName());
        
        // 额外的音效和视觉效果
        playDestructionEffects(arena);
    }
    
    @Override
    public String getEventType() {
        return "bed-destroy";
    }
    
    @Override
    public String getEventName() {
        return "床摧毁事件";
    }
    
    /**
     * 摧毁指定队伍的床
     */
    private boolean destroyTeamBed(Arena arena, Team team) {
        try {
            // 检查床是否已经被摧毁
            if (arena.isBedDestroyed(team)) {
                return false; // 床已经被摧毁
            }

            // 创建床摧毁信息
            BedDestructionInfo info = BedDestructionInfo.construct(team);

            // 设置摧毁者名称为事件名称
            info.setDestroyerName(Message.build("&c&l自动床摧毁事件").done());

            // 摧毁床
            arena.destroyBedNaturally(info);

            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 播放摧毁效果
     */
    private void playDestructionEffects(Arena arena) {
        // 延迟播放额外音效
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("AceVoteMode"), 
            () -> {
                playSound(arena, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.2f);
                
                // 再次延迟播放最终音效
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("AceVoteMode"), 
                    () -> playSound(arena, org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.6f, 1.0f),
                    10L
                );
            }, 
            20L
        );
    }
}
