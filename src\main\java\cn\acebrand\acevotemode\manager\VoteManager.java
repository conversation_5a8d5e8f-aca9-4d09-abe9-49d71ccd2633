package cn.acebrand.acevotemode.manager;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.model.GameMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 投票管理器
 * 负责管理投票数据和逻辑
 */
public class VoteManager {
    
    private final AceVoteMode plugin;
    
    // 每个竞技场的投票数据
    private final Map<Arena, ArenaVoteData> arenaVotes;
    
    public VoteManager(AceVoteMode plugin) {
        this.plugin = plugin;
        this.arenaVotes = new ConcurrentHashMap<>();
    }
    
    /**
     * 获取竞技场的投票数据
     */
    public ArenaVoteData getArenaVoteData(Arena arena) {
        return arenaVotes.computeIfAbsent(arena, k -> new ArenaVoteData());
    }
    
    /**
     * 玩家投票
     */
    public VoteResult vote(Player player, Arena arena, String gameModeId) {
        ArenaVoteData voteData = getArenaVoteData(arena);
        
        // 检查是否已经投过票
        if (voteData.hasVoted(player)) {
            return VoteResult.ALREADY_VOTED;
        }
        
        // 检查投票是否已关闭
        if (voteData.isVotingClosed()) {
            return VoteResult.VOTING_CLOSED;
        }
        
        // 检查游戏模式是否存在
        if (!plugin.getConfigManager().getGameModes().containsKey(gameModeId)) {
            return VoteResult.INVALID_MODE;
        }
        
        // 记录投票
        voteData.addVote(player, gameModeId);
        return VoteResult.SUCCESS;
    }
    
    /**
     * 关闭竞技场的投票
     */
    public void closeVoting(Arena arena) {
        ArenaVoteData voteData = getArenaVoteData(arena);
        voteData.setVotingClosed(true);
    }
    
    /**
     * 获取获胜的游戏模式
     */
    public GameMode getWinningMode(Arena arena) {
        ArenaVoteData voteData = getArenaVoteData(arena);

        // 检查是否有投票
        if (voteData.getTotalVotes() == 0) {
            // 无人投票：从所有可用模式中随机选择一个
            return getRandomModeFromAll(arena, "无人投票");
        } else {
            // 有投票：获取获胜模式（可能需要从平票模式中随机选择）
            String winningModeId = voteData.getWinningMode();
            return winningModeId != null ? plugin.getConfigManager().getGameModes().get(winningModeId) : null;
        }
    }

    /**
     * 从所有可用模式中随机选择一个
     */
    private GameMode getRandomModeFromAll(Arena arena, String reason) {
        Map<String, GameMode> gameModes = plugin.getConfigManager().getGameModes();
        if (gameModes.isEmpty()) {
            return null;
        }

        List<String> modeIds = new ArrayList<>(gameModes.keySet());
        String selectedModeId = modeIds.get(new Random().nextInt(modeIds.size()));

        // 记录日志
        if (plugin.getConfigManager().isEnableRandomSelectionLogs()) {
            plugin.getLogger().info(reason + " for arena " + arena.getName() +
                    ", randomly selected: " + selectedModeId);
        }

        return gameModes.get(selectedModeId);
    }
    
    /**
     * 移除玩家的投票
     */
    public boolean removePlayerVote(Player player, Arena arena) {
        ArenaVoteData voteData = getArenaVoteData(arena);
        return voteData.removePlayerVote(player);
    }

    /**
     * 清理竞技场数据
     */
    public void clearArenaData(Arena arena) {
        arenaVotes.remove(arena);
    }
    
    /**
     * 清理所有数据
     */
    public void cleanup() {
        arenaVotes.clear();
    }
    
    /**
     * 重载管理器
     */
    public void reload() {
        // 重载时保留现有投票数据，只清理无效的模式投票
        for (ArenaVoteData voteData : arenaVotes.values()) {
            voteData.cleanupInvalidVotes(plugin.getConfigManager().getGameModes().keySet());
        }
    }
    
    /**
     * 投票结果枚举
     */
    public enum VoteResult {
        SUCCESS,
        ALREADY_VOTED,
        VOTING_CLOSED,
        INVALID_MODE
    }
    
    /**
     * 竞技场投票数据
     */
    public static class ArenaVoteData {
        private final Map<UUID, String> playerVotes; // 玩家UUID -> 投票的模式ID
        private final Map<String, Integer> voteCounts; // 模式ID -> 票数
        private boolean votingClosed;
        
        public ArenaVoteData() {
            this.playerVotes = new ConcurrentHashMap<>();
            this.voteCounts = new ConcurrentHashMap<>();
            this.votingClosed = false;
        }
        
        /**
         * 添加投票
         */
        public void addVote(Player player, String gameModeId) {
            UUID playerId = player.getUniqueId();
            
            // 如果玩家之前投过票，先移除旧投票
            String oldVote = playerVotes.get(playerId);
            if (oldVote != null) {
                voteCounts.put(oldVote, Math.max(0, voteCounts.getOrDefault(oldVote, 0) - 1));
            }
            
            // 添加新投票
            playerVotes.put(playerId, gameModeId);
            voteCounts.put(gameModeId, voteCounts.getOrDefault(gameModeId, 0) + 1);
        }
        
        /**
         * 检查玩家是否已投票
         */
        public boolean hasVoted(Player player) {
            return playerVotes.containsKey(player.getUniqueId());
        }

        /**
         * 移除玩家的投票
         */
        public boolean removePlayerVote(Player player) {
            UUID playerId = player.getUniqueId();
            String oldVote = playerVotes.remove(playerId);

            if (oldVote != null) {
                // 减少对应模式的票数
                int currentVotes = voteCounts.getOrDefault(oldVote, 0);
                if (currentVotes > 0) {
                    voteCounts.put(oldVote, currentVotes - 1);
                    // 如果票数为0，移除该条目
                    if (currentVotes == 1) {
                        voteCounts.remove(oldVote);
                    }
                }
                return true;
            }

            return false;
        }
        
        /**
         * 获取玩家的投票
         */
        public String getPlayerVote(Player player) {
            return playerVotes.get(player.getUniqueId());
        }
        
        /**
         * 获取模式的票数
         */
        public int getVoteCount(String gameModeId) {
            return voteCounts.getOrDefault(gameModeId, 0);
        }
        
        /**
         * 获取所有票数
         */
        public Map<String, Integer> getAllVoteCounts() {
            return new HashMap<>(voteCounts);
        }
        
        /**
         * 获取获胜模式
         */
        public String getWinningMode() {
            if (voteCounts.isEmpty()) {
                return null;
            }

            // 找到最高票数
            int maxVotes = voteCounts.values().stream()
                    .mapToInt(Integer::intValue)
                    .max()
                    .orElse(0);

            // 找到所有具有最高票数的模式
            List<String> topModes = voteCounts.entrySet().stream()
                    .filter(entry -> entry.getValue() == maxVotes)
                    .map(Map.Entry::getKey)
                    .collect(java.util.stream.Collectors.toList());

            // 如果有多个模式票数相同（平票），随机选择一个
            if (topModes.size() > 1) {
                return topModes.get(new Random().nextInt(topModes.size()));
            } else if (topModes.size() == 1) {
                return topModes.get(0);
            }

            return null;
        }

        /**
         * 获取票数最高的所有模式（用于检查是否有平票情况）
         */
        public List<String> getTopModes() {
            if (voteCounts.isEmpty()) {
                return new ArrayList<>();
            }

            int maxVotes = voteCounts.values().stream()
                    .mapToInt(Integer::intValue)
                    .max()
                    .orElse(0);

            return voteCounts.entrySet().stream()
                    .filter(entry -> entry.getValue() == maxVotes)
                    .map(Map.Entry::getKey)
                    .collect(java.util.stream.Collectors.toList());
        }

        /**
         * 获取最高票数
         */
        public int getMaxVotes() {
            return voteCounts.values().stream()
                    .mapToInt(Integer::intValue)
                    .max()
                    .orElse(0);
        }
        
        /**
         * 获取总投票数
         */
        public int getTotalVotes() {
            return voteCounts.values().stream().mapToInt(Integer::intValue).sum();
        }
        
        /**
         * 是否投票已关闭
         */
        public boolean isVotingClosed() {
            return votingClosed;
        }

        /**
         * 设置投票关闭状态
         */
        public void setVotingClosed(boolean votingClosed) {
            this.votingClosed = votingClosed;
        }


        
        /**
         * 清理无效的投票
         */
        public void cleanupInvalidVotes(Set<String> validModeIds) {
            // 移除无效模式的投票
            voteCounts.entrySet().removeIf(entry -> !validModeIds.contains(entry.getKey()));
            
            // 移除投票了无效模式的玩家记录
            playerVotes.entrySet().removeIf(entry -> !validModeIds.contains(entry.getValue()));
        }
        
        /**
         * 清理所有数据
         */
        public void clear() {
            playerVotes.clear();
            voteCounts.clear();
            votingClosed = false;
        }
    }
}
