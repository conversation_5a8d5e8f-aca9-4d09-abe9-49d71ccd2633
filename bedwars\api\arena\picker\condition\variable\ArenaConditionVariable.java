package de.marcely.bedwars.api.arena.picker.condition.variable;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.api.arena.picker.ArenaPickerAPI;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.plugin.Plugin;

/**
 * Represents a variable that persists of a name and a type and is able to fetch a specific property of an arena at any time.
 * <p>
 *     Make sure to register it when constructing a custom one using {@link ArenaPickerAPI#registerConditionVariable(ArenaConditionVariable)}.
 * </p>
 * @param <T> inherits ArenaConditionVariableValue. It's equal to {@link #getValueClass()}
 */
public abstract class ArenaConditionVariable<T extends ArenaConditionVariableValue<?>> {

  private final Plugin plugin;
  private final String name;
  private final Class<T> valueClass;

  /**
   * Construct a new variable type.
   *
   * @param plugin The plugin that is constructing it
   * @param name Must be in the following format: a-z, _, @, :. It is automatically being put into lower-case
   * @param valueClass Must be a ArenaConditionVariableTypeNumber or ArenaConditionVariableTypeSring
   */
  public ArenaConditionVariable(Plugin plugin, String name, Class<T> valueClass) {
    Validate.notNull(plugin, "plugin");
    Validate.notNull(name, "name");
    Validate.notNull(valueClass, "valueClass");

    this.plugin = plugin;
    this.name = name.toLowerCase();
    this.valueClass = valueClass;
  }

  /**
   * Returns the type of this variable.
   *
   * @return The type
   */
  public ArenaConditionVariableType getType() {
    return ArenaConditionVariableType.PLUGIN;
  }

  /**
   * The plugin that has constructed this instance.
   *
   * @return The plugin behind its existence
   */
  public final Plugin getPlugin() {
    return this.plugin;
  }

  /**
   * The name of the variable.
   * <p>
   *     It is always in lower-case and follows this format: a-z, _, @, :
   * </p>
   *
   * @return The name
   */
  public final String getName() {
    return this.name;
  }

  /**
   * The class of the value that is being fetched.
   *
   * @return The value class that this variable returns
   */
  public final Class<T> getValueClass() {
    return this.valueClass;
  }

  /**
   * Returns whether this instance is registered.
   *
   * @return Whether it is currently registered and useable
   * @see ArenaPickerAPI#registerConditionVariable(ArenaConditionVariable)
   * @see ArenaPickerAPI#unregisterConditionVariable(ArenaConditionVariable)
   */
  public final boolean isRegistered() {
    return ArenaPickerAPI.get().isConditionVariableRegistered(this);
  }

  /**
   * Fetches the value of a local arena.
   * <p>
   *     The class of the returned object is equal to {@link #getValueClass()}.
   * </p>
   *
   * @param arena The local arena
   * @return The given property of this current moment
   */
  public abstract T getValue(Arena arena);

  /**
   * Fetches the value of a remote arena.
   * <p>
   *     The class of the returned object is equal to {@link #getValueClass()}.
   * </p>
   *
   * @param arena The remote arena
   * @return The given property of this current moment
   */
  public abstract T getValue(RemoteArena arena);
}