package de.marcely.bedwars.tools.location;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.configuration.serialization.ConfigurationSerializable;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents a 3-dimensional position
 */
@EqualsAndHashCode
@ToString
public class XYZ implements Cloneable, ConfigurationSerializable {

  protected double x, y, z;

  public XYZ() {
    this(0, 0, 0);
  }

  public XYZ(Location loc) {
    this(loc.getX(), loc.getY(), loc.getZ());
  }

  public XYZ(Vector vec) {
    this(vec.getX(), vec.getY(), vec.getZ());
  }

  public XYZ(XYZ xyz) {
    this(xyz.x, xyz.y, xyz.z);
  }

  public XYZ(IntXYZ xyz) {
    this(xyz.x, xyz.y, xyz.z);
  }

  public XYZ(double x, double y, double z) {
    this.x = x;
    this.y = y;
    this.z = z;
  }

  /**
   * Returns the x-coordinate
   *
   * @return The x value
   */
  public double getX() {
    return this.x;
  }

  /**
   * Returns the y-coordinate
   *
   * @return The y value
   */
  public double getY() {
    return this.y;
  }

  /**
   * Returns the z-coordinate
   *
   * @return The z value
   */
  public double getZ() {
    return this.z;
  }

  /**
   * Returns the floored x-coordinate
   *
   * @return The block x-coordinate
   */
  public int getBlockX() {
    return NumberConversions.floor(this.x);
  }

  /**
   * Returns the floored y-coordinate
   *
   * @return The block y-coordinate
   */
  public int getBlockY() {
    return NumberConversions.floor(this.y);
  }

  /**
   * Returns the floored z-coordinate
   *
   * @return The block z-coordinate
   */
  public int getBlockZ() {
    return NumberConversions.floor(this.z);
  }

  /**
   * Sets the x-coordinate
   *
   * @param x The new x-coordinate
   * @return This XYZ instance
   */
  public XYZ setX(double x) {
    this.x = x;

    return this;
  }

  /**
   * Sets the y-coordinate
   *
   * @param y The new y-coordinate
   * @return This XYZ instance
   */
  public XYZ setY(double y) {
    this.y = y;

    return this;
  }

  /**
   * Sets the z-coordinate
   *
   * @param z The new z-coordinate
   * @return This XYZ instance
   */
  public XYZ setZ(double z) {
    this.z = z;

    return this;
  }

  /**
   * Sets the new coordinates
   *
   * @param x The new x-coordinate
   * @param y The new y-coordinate
   * @param z The new z-coordinate
   * @return This XYZ instance
   */
  public XYZ set(double x, double y, double z) {
    this.x = x;
    this.y = y;
    this.z = z;

    return this;
  }

  /**
   * Copies and sets the xyz coordinates from the given object
   *
   * @param xyz The object from which it shall be taken from
   * @return This XYZ instance
   */
  public XYZ set(XYZ xyz) {
    this.x = xyz.x;
    this.y = xyz.y;
    this.z = xyz.z;

    return this;
  }

  /**
   * Copies and sets the xyz coordinates from the given object
   *
   * @param xyz The object from which it shall be taken from
   * @return This XYZ instance
   */
  public XYZ set(IntXYZ xyz) {
    this.x = xyz.x;
    this.y = xyz.y;
    this.z = xyz.z;

    return this;
  }

  /**
   * Copies and sets the xyz coordinates from the given object
   *
   * @param loc The object from which it shall be taken from
   * @return This XYZ instance
   */
  public XYZ set(Location loc) {
    this.x = loc.getX();
    this.y = loc.getY();
    this.z = loc.getZ();

    return this;
  }

  /**
   * Adds the x-coordinates
   *
   * @param x The added x-coordinate
   * @param y The added y-coordinate
   * @param z The added z-coordinate
   * @return This XYZ instance
   */
  public XYZ add(double x, double y, double z) {
    this.x += x;
    this.y += y;
    this.z += z;

    return this;
  }

  public XYZ add(XYZ xyz) {
    this.x += xyz.x;
    this.y += xyz.y;
    this.z += xyz.z;

    return this;
  }

  public XYZ add(IntXYZ xyz) {
    this.x += xyz.x;
    this.y += xyz.y;
    this.z += xyz.z;

    return this;
  }

  public XYZ add(Location loc) {
    this.x += loc.getX();
    this.y += loc.getY();
    this.z += loc.getZ();

    return this;
  }

  public XYZ add(Vector vec) {
    this.x += vec.getX();
    this.y += vec.getY();
    this.z += vec.getZ();

    return this;
  }

  public XYZ subtract(double x, double y, double z) {
    this.x -= x;
    this.y -= y;
    this.z -= z;

    return this;
  }

  public XYZ subtract(XYZ xyz) {
    this.x -= xyz.x;
    this.y -= xyz.y;
    this.z -= xyz.z;

    return this;
  }

  public XYZ subtract(IntXYZ xyz) {
    this.x -= xyz.x;
    this.y -= xyz.y;
    this.z -= xyz.z;

    return this;
  }

  public XYZ subtract(Location loc) {
    this.x -= loc.getX();
    this.y -= loc.getY();
    this.z -= loc.getZ();

    return this;
  }

  public XYZ subtract(Vector vec) {
    this.x -= vec.getX();
    this.y -= vec.getY();
    this.z -= vec.getZ();

    return this;
  }

  public XYZ multiply(double amount) {
    this.x *= amount;
    this.y *= amount;
    this.z *= amount;

    return this;
  }

  public XYZ multiply(double x, double y, double z) {
    this.x *= x;
    this.y *= y;
    this.z *= z;

    return this;
  }

  /**
   * Sets all coordinates to 0
   *
   * @return This XYZ instance
   */
  public XYZ zero() {
    this.x = 0;
    this.y = 0;
    this.z = 0;

    return this;
  }

  /**
   * Get whether all coordinates are set to 0
   *
   * @return <code>true</code> if X, Y and Z are set to 0.0D
   */
  public boolean isZero() {
    return this.x == 0D && this.y == 0D && this.z == 0D;
  }

  public double distance(Location o) {
    return Math.sqrt(distanceSquared(o));
  }

  public double distanceSquared(Location o) {
    return NumberConversions.square(x - o.getX()) +
        NumberConversions.square(y - o.getY()) + NumberConversions.square(z - o.getZ());
  }

  public double distance(XYZ o) {
    return Math.sqrt(distanceSquared(o));
  }

  public double distanceSquared(XYZ o) {
    return NumberConversions.square(x - o.x) +
        NumberConversions.square(y - o.y) + NumberConversions.square(z - o.z);
  }

  /**
   * Converts this XYZ object to a new Location object
   *
   * @param world The world needed for creating the Location
   * @return A new location whose coordinates where taken from this object
   */
  public Location toLocation(World world) {
    return new Location(world, this.x, this.y, this.z);
  }

  /**
   * Converts this XYZ object to a new Vector object
   *
   * @return A new vector whose coordinates where taken from this object
   */
  public Vector toVector() {
    return new Vector(this.x, this.y, this.z);
  }

  /**
   * Get the Bukkit block that you can find at the given position.
   *
   * @param world The world in which we want to look up the block
   * @return The matching block
   */
  public Block getBlock(World world) {
    return world.getBlockAt((int) this.x, (int) this.y, (int) this.z);
  }

  /**
   * Check whether the block coords (i.a. {@link #getBlockX()}) match.
   *
   * @param xyz The other object to compare with
   * @return <code>true</code> if the floored coordinates match
   */
  public boolean equalsBlockCoords(XYZ xyz) {
    return getBlockX() == xyz.getBlockX() && getBlockY() == xyz.getBlockY() && getBlockZ() == xyz.getBlockZ();
  }

  /**
   * Check whether the block coords (i.a. {@link #getBlockX()}) match.
   *
   * @param loc The other object to compare with
   * @return <code>true</code> if the floored coordinates match
   */
  public boolean equalsBlockCoords(Location loc) {
    return getBlockX() == loc.getBlockX() && getBlockY() == loc.getBlockY() && getBlockZ() == loc.getBlockZ();
  }

  /**
   * Check whether the block coords (i.a. {@link #getBlockX()}) match.
   *
   * @param block The other object to compare with
   * @return <code>true</code> if the floored coordinates match
   */
  public boolean equalsBlockCoords(Block block) {
    return getBlockX() == block.getX() && getBlockY() == block.getY() && getBlockZ() == block.getZ();
  }

  @Override
  public XYZ clone() {
    return new XYZ(this.x, this.y, this.z);
  }

  @Override
  public Map<String, Object> serialize() {
    final Map<String, Object> data = new HashMap<>();

    data.put("x", this.x);
    data.put("y", this.y);
    data.put("z", this.z);

    return data;
  }

  /**
   * Required method for deserialization
   *
   * @param data map to deserialize
   * @return deserialized xyz instance
   */
  public static XYZ deserialize(Map<String, Object> data) {
    return new XYZ(
        NumberConversions.toDouble(data.get("x")),
        NumberConversions.toDouble(data.get("y")),
        NumberConversions.toDouble(data.get("z")));
  }
}
