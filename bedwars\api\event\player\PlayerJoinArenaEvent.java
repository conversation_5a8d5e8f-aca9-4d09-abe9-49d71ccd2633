package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.AddPlayerCause;
import de.marcely.bedwars.api.arena.AddPlayerIssue;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

import java.util.Set;

/**
 * Gets called when a player joins an arena
 */
public class PlayerJoinArenaEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final AddPlayerCause cause;

  private Team team;
  private Set<AddPlayerIssue> issues;

  public PlayerJoinArenaEvent(Player player, Arena arena, AddPlayerCause cause, Team team, Set<AddPlayerIssue> issues) {
    super(player);

    this.arena = arena;
    this.cause = cause;
    this.team = team;
    this.issues = issues;
  }

  /**
   * Returns what has made the player to join the arena.
   *
   * @return The cause of the player entering the arena
   */
  public AddPlayerCause getCause() {
    return this.cause;
  }

  /**
   * Returns the team to which the player is automatically getting added to.
   * Can be <code>null</code> when the player isn't in any team.
   *
   * @return The team of the player, might be <code>null</code>
   */
  public @Nullable Team getTeam() {
    return this.team;
  }

  /**
   * Set the team to which the player shall automatically get added to.
   * Can be <code>null</code> when he shouldn't be added to one.
   *
   * @param team His new team
   */
  public void setTeam(@Nullable Team team) {
    this.team = team;
  }

  /**
   * Returns if the player has any issues entering the arena.
   *
   * @return If there are issues which cause the player not being able to enter the arena
   */
  public boolean hasIssues() {
    return !this.issues.isEmpty();
  }

  /**
   * Returns all issues there are for the player to enter the arena.
   *
   * @return The issues why the player can't enter the arena
   */
  public Set<AddPlayerIssue> getIssues() {
    return this.issues;
  }

  /**
   * Adds an issue which causes the player to not be able to enter the arena.
   *
   * @param issue The issue that shall be added
   * @return <code>false</code> if that issue already existed before
   */
  public boolean addIssue(AddPlayerIssue issue) {
    Validate.notNull(issue, "issue");

    return this.issues.add(issue);
  }

  /**
   * Removes an issue. If all were removed the player will be able to enter the arena.
   *
   * @param issue The issue that shall be removed
   * @return <code>true</code> if it has been removed
   */
  public boolean removeIssue(AddPlayerIssue issue) {
    Validate.notNull(issue, "issue");

    return this.issues.remove(issue);
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
