package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.MigrationProcess;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when a process of migration has been started using {@link MigrationProcess#run()}.
 */
public class MigrationProcessRunEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final MigrationProcess process;

  private boolean cancel = false;

  public MigrationProcessRunEvent(MigrationProcess process) {
    this.process = process;
  }

  /**
   * Returns the process that has started.
   *
   * @return The process behind all of this
   */
  public MigrationProcess getProcess() {
    return this.process;
  }

  @Override
  public void setCancelled(boolean bool) {
    this.cancel = bool;
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
