package cn.acebrand.acevotemode.gamemode;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

/**
 * 普通模式
 * 标准的起床战争体验，无任何修改
 */
public class NormalMode extends GameModeBase {
    
    private static NormalMode instance;
    
    public NormalMode(AceVoteMode plugin) {
        super(plugin, "normal-mode", "普通模式");
        instance = this;
    }
    
    /**
     * 获取实例（用于PAPI）
     */
    public static NormalMode getInstance() {
        return instance;
    }
    
    @Override
    protected void createDefaultConfig() {
        // 添加配置文件头部注释
        addConfigComments();

        // 基础配置
        config.set("enabled", true);
        config.set("description", "标准的起床战争体验，无任何修改，享受原汁原味的游戏乐趣");

        // 消息配置
        config.set("messages.mode-start", "&f&l普通模式已启动！");
        config.set("messages.mode-description", "&7标准的起床战争体验，无任何修改");
        config.set("messages.welcome", "&a欢迎来到经典起床战争！");

        // 显示配置
        config.set("display.show-start-message", true);
        config.set("display.show-welcome-message", true);

        saveConfig();

        // 保存后添加详细注释到文件
        addDetailedCommentsToFile();
    }
    
    @Override
    protected void onConfigReload() {
        // 普通模式无需重载任何特殊配置
    }
    
    @Override
    public void onGameStart(Arena arena) {

        // 检查是否显示开始消息
        if (config.getBoolean("display.show-start-message", true)) {
            // 向所有玩家发送模式开始消息
            String startMessage = config.getString("messages.mode-start", "&f&l普通模式已启动！");
            String description = config.getString("messages.mode-description", "&7标准的起床战争体验，无任何修改");

            for (Player player : arena.getPlayers()) {
                player.sendMessage(translateColors(startMessage));
                player.sendMessage(translateColors(description));
            }
        }

        // 检查是否显示欢迎消息
        if (config.getBoolean("display.show-welcome-message", true)) {
            String welcomeMessage = config.getString("messages.welcome", "&a欢迎来到经典起床战争！");
            for (Player player : arena.getPlayers()) {
                player.sendMessage(translateColors(welcomeMessage));
            }
        }

    }

    @Override
    public void onGameEnd(Arena arena) {
        // 普通模式无需清理任何特殊数据
    }

    @Override
    public void onPlayerJoin(Player player, Arena arena) {
        // 普通模式无需对新加入的玩家进行特殊处理
        if (config.getBoolean("display.show-welcome-message", true)) {
            String welcomeMessage = config.getString("messages.welcome", "&a欢迎来到经典起床战争！");
            player.sendMessage(translateColors(welcomeMessage));
        }
    }

    @Override
    public void onPlayerQuit(Player player, Arena arena) {
        // 普通模式无需对离开的玩家进行特殊处理
    }

    /**
     * 颜色代码转换
     */
    private String translateColors(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 添加配置文件注释
     */
    private void addConfigComments() {
        // 普通模式的配置注释
    }

    /**
     * 添加详细注释到配置文件
     */
    private void addDetailedCommentsToFile() {
        // 普通模式的详细配置说明
    }
}
