package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when we want to know whether a player is permitted to modify a block.
 * <p>
 *     It may gets called directly with Bukkit's {@link BlockPlaceEvent} and {@link BlockBreakEvent}.
 *     In other cases, it may get called e.g. if a special item places multiple blocks, in which case {@link #getBukkitEvent()}
 *     returns null.
 * </p>
 */
public class PlayerModifyBlockPermissionEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private final Block block;
  private final BlockEvent bukkitEvent;

  private byte bitset = 0;

  public PlayerModifyBlockPermissionEvent(Player player, Arena arena, Block block, @Nullable BlockEvent bukkitEvent) {
    super(player);

    this.arena = arena;
    this.block = block;
    this.bukkitEvent = bukkitEvent;
  }

  @Override
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the original block event with which we're dealing now.
   * <p>
   *     Cancelling it at this point has no effect, as we're manually overriding it directly after this event.
   * </p>
   *
   * @return The original bukkit event. May be <code>null</code> if this check was done outside of a block event
   */
  @Nullable
  public BlockEvent getBukkitEvent() {
    return this.bukkitEvent;
  }

  /**
   * Returns the block with which we're dealing now.
   *
   * @return The block involved in this event
   */
  public Block getBlock() {
    return this.block;
  }

  /**
   * Whether the player tries to place a block.
   *
   * @return <code>true</code> when the player tries to place a block
   */
  public boolean isPlaceEvent() {
    return this.bukkitEvent instanceof BlockPlaceEvent;
  }

  /**
   * Whether the player tries to break a block.
   *
   * @return <code>true</code> when the player tries to break a block
   */
  public boolean isBreakEvent() {
    return this.bukkitEvent instanceof BlockBreakEvent;
  }

  /**
   * Returns whether the given issue is present.
   * <p>
   *     Issues prevent the BlockEvent from progressing, meaning that block won't be placed or breaked.
   * </p>
   *
   * @param issue The issue
   * @return <code>true</code> when it's actually an issue
   */
  public boolean isIssuePresent(Issue issue) {
    Validate.notNull(issue, "issue");

    return (this.bitset & (1 << issue.ordinal())) != 0;
  }

  /**
   * Set whether an issue is present.
   * <p>
   *     Issues prevent the BlockEvent from progressing, meaning that block won't be placed or breaked.
   * </p>
   *
   * @param issue The issue
   * @param present <code>true</code> when it's present
   */
  public void setIssuePresent(Issue issue, boolean present) {
    Validate.notNull(issue, "issue");

    if (present)
      this.bitset |= (1 << issue.ordinal());
    else
      this.bitset &= ~(1 << issue.ordinal());
  }

  /**
   * Returns when there are any issues that cause the original BukkitEvent to get cancelled.
   *
   * @return <code>true</code>w hen there's at least one issue
   */
  public boolean hasIssues() {
    return this.bitset != 0;
  }

  /**
   * Removes all existing issues.
   */
  public void removeIssues() {
    this.bitset = 0;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  /**
   * Reasons that cause the BlocKEvent to not get performed.
   */
  public enum Issue {

    /**
     * Block that gets broken/placed is inside a region in which you may not do that (such as near the team spawn).
     */
    INSIDE_NON_BUILD_RADIUS,

    /**
     * A block gets destroyed that hasn't been placed by a player (it's a part of the map).
     */
    NON_PLAYER_PLACED,

    /**
     * Block can't be placed/broken because we don't want that type of block.
     */
    BLACKLISTED_MATERIAL,

    /**
     * The block has been placed/broken outside the playable area.
     */
    OUTSIDE_ARENA,

    /**
     * A custom reason added using the API.
     */
    PLUGIN
  }
}
