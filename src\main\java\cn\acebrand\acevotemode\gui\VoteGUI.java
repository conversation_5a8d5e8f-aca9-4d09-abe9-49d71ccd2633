package cn.acebrand.acevotemode.gui;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.manager.VoteManager;
import cn.acebrand.acevotemode.model.GameMode;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 投票GUI界面
 */
public class VoteGUI implements Listener {
    
    private final AceVoteMode plugin;
    private final Player player;
    private final Arena arena;
    private final Inventory inventory;
    
    public VoteGUI(AceVoteMode plugin, Player player, Arena arena) {
        this.plugin = plugin;
        this.player = player;
        this.arena = arena;
        
        // 创建GUI界面
        this.inventory = Bukkit.createInventory(null, 27, ChatColor.DARK_GREEN + "选择游戏模式");
        
        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
        
        // 初始化界面
        setupInventory();
    }
    
    /**
     * 设置界面内容
     */
    private void setupInventory() {
        // 清空界面
        inventory.clear();
        
        Map<String, GameMode> gameModes = plugin.getConfigManager().getGameModes();
        VoteManager.ArenaVoteData voteData = plugin.getVoteManager().getArenaVoteData(arena);
        
        // 获取玩家当前的投票
        String playerVote = voteData.getPlayerVote(player);
        
        int slot = 10; // 从第二行开始放置
        for (GameMode gameMode : gameModes.values()) {
            if (slot >= 17) break; // 最多显示7个模式
            
            int votes = voteData.getVoteCount(gameMode.getId());
            ItemStack icon = gameMode.getIconWithVotes(votes);
            
            // 如果是玩家投票的模式，添加特殊标记
            if (gameMode.getId().equals(playerVote)) {
                ItemMeta meta = icon.getItemMeta();
                if (meta != null) {
                    List<String> lore = meta.getLore();
                    if (lore == null) lore = new ArrayList<>();
                    
                    lore.add("");
                    lore.add(ChatColor.GREEN + "✓ 你已投票给此模式");
                    meta.setLore(lore);
                    icon.setItemMeta(meta);
                }
            }
            
            inventory.setItem(slot, icon);
            slot++;
        }
        
        // 添加信息物品
        addInfoItems(voteData);
    }
    
    /**
     * 添加信息物品
     */
    private void addInfoItems(VoteManager.ArenaVoteData voteData) {
        // 投票状态信息
        ItemStack infoItem = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = infoItem.getItemMeta();
        if (infoMeta != null) {
            infoMeta.setDisplayName(ChatColor.YELLOW + "投票信息");
            
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "总投票数: " + ChatColor.WHITE + voteData.getTotalVotes());
            
            if (voteData.hasVoted(player)) {
                String votedMode = voteData.getPlayerVote(player);
                GameMode gameMode = plugin.getConfigManager().getGameModes().get(votedMode);
                if (gameMode != null) {
                    // 转换颜色代码
                    String coloredName = ChatColor.translateAlternateColorCodes('&', gameMode.getName());
                    lore.add(ChatColor.GRAY + "你的投票: " + coloredName);
                }
            } else {
                lore.add(ChatColor.GRAY + "你还没有投票");
            }
            
            lore.add("");
            lore.add(ChatColor.YELLOW + "点击游戏模式图标进行投票");
            if (voteData.isVotingClosed()) {
                lore.add(ChatColor.RED + "投票已关闭！");
            }
            
            infoMeta.setLore(lore);
            infoItem.setItemMeta(infoMeta);
        }
        
        inventory.setItem(4, infoItem);
        
        // 关闭按钮
        ItemStack closeItem = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = closeItem.getItemMeta();
        if (closeMeta != null) {
            closeMeta.setDisplayName(ChatColor.RED + "关闭");
            closeItem.setItemMeta(closeMeta);
        }
        inventory.setItem(22, closeItem);
    }
    
    /**
     * 打开GUI
     */
    public void open() {
        player.openInventory(inventory);
    }
    
    /**
     * 刷新GUI
     */
    public void refresh() {
        setupInventory();
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getInventory().equals(inventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player clickedPlayer = (Player) event.getWhoClicked();
        if (!clickedPlayer.equals(player)) {
            return;
        }
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            return;
        }
        
        // 处理关闭按钮
        if (clickedItem.getType() == Material.BARRIER) {
            player.closeInventory();
            return;
        }
        
        // 处理游戏模式投票
        Map<String, GameMode> gameModes = plugin.getConfigManager().getGameModes();
        for (GameMode gameMode : gameModes.values()) {
            if (clickedItem.getType() == gameMode.getIcon().getType()) {
                handleVote(gameMode.getId());
                return;
            }
        }
    }
    
    /**
     * 处理投票
     */
    private void handleVote(String gameModeId) {
        VoteManager.VoteResult result = plugin.getVoteManager().vote(player, arena, gameModeId);
        
        switch (result) {
            case SUCCESS:
                GameMode gameMode = plugin.getConfigManager().getGameModes().get(gameModeId);
                if (gameMode != null) {
                    player.sendMessage(plugin.getConfigManager().getMessage("vote-success", 
                            "mode", gameMode.getPlainName()));
                }
                refresh(); // 刷新界面
                break;
                
            case ALREADY_VOTED:
                player.sendMessage(plugin.getConfigManager().getMessage("already-voted"));
                break;
                
            case VOTING_CLOSED:
                player.sendMessage(plugin.getConfigManager().getMessage("vote-closed"));
                player.closeInventory();
                break;
                
            case INVALID_MODE:
                player.sendMessage(ChatColor.RED + "无效的游戏模式！");
                break;
        }
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().equals(inventory)) {
            // 注销事件监听器
            HandlerList.unregisterAll(this);
        }
    }
}
