package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;

/**
 * Get<PERSON> called when a player teleports himself out of the arena during a game.
 * <p>
 *  Canceling the event will prevent the player from getting kicked, but will still let him teleport the new location
 *  if granted by the {@link org.bukkit.event.player.PlayerTeleportEvent}.
 * </p>
 */
public class PlayerTeleportOutOfArenaEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Location from;
  private final TeleportCause cause;

  private Location to;
  private boolean cancel = false;

  public PlayerTeleportOutOfArenaEvent(Player player, Arena arena, Location from, Location to, TeleportCause cause) {
    super(player);

    this.arena = arena;
    this.from = from;
    this.to = to;
    this.cause = cause;
  }

  /**
   * Returns the current location of the player.
   *
   * @return His location before this event fired
   */
  public Location getFrom() {
    return this.from;
  }

  /**
   * Returns the location to which the player is trying to teleport to.
   *
   * @return His new location
   */
  public Location getTo() {
    return this.to;
  }

  /**
   * Set the new location to which the player shall get teleported to.
   *
   * @param to His new location
   */
  public void setTo(Location to) {
    Validate.notNull(to, "to");

    this.to = to;
  }

  /**
   * Returns what caused him to teleport.
   *
   * @return The teleport cause
   */
  public TeleportCause getCause() {
    return this.cause;
  }

  @Override
  public void setCancelled(boolean bool) {
    this.cancel = bool;
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
