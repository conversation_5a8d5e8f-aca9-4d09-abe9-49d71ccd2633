package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called when a running lobby countdown is cancelled
 * <p>
 *   This can happen when players leave and there's now less than the
 *   required min players ({@link Arena#getMinPlayers()}).
 * </p>
 */
public class ArenaLobbyCountdownCancelEvent extends Event implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;

  @Getter @Setter
  private boolean cancelled = false;

  public ArenaLobbyCountdownCancelEvent(Arena arena) {
    this.arena = arena;
  }

  @Override
  public Arena getArena() {
    return this.arena;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
