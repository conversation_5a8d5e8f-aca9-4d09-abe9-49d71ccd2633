package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.spectator.SpectateReason;
import de.marcely.bedwars.api.game.spectator.Spectator;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a player tries to enter an arena as a spectator
 */
public class SpectatorJoinArenaEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Spectator spectator;

  private boolean cancel = false;

  public SpectatorJoinArenaEvent(Spectator spectator) {
    super(spectator.getPlayer());

    this.spectator = spectator;
  }

  /**
   * Returns the arena to which the player wants to join.
   *
   * @return The arena in which the player wants to enter
   */
  public Arena getArena() {
    return this.spectator.getArena();
  }

  /**
   * Returns the spectator who's trying to join.
   *
   * @return The involved spectator
   */
  public Spectator getSpectator() {
    return this.spectator;
  }

  /**
   * Returns the reason why he got into a spectator.
   *
   * @return The reason
   */
  public SpectateReason getReason() {
    return this.spectator.getReason();
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public void setCancelled(boolean bool) {
    this.cancel = bool;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
