package de.marcely.bedwars.tools.location;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.util.Vector;

/**
 * Extends {@link Location} and forbids any modifications done to it
 */
public class ImmutableLocation extends Location {

  public ImmutableLocation(Location loc) {
    this(loc.getWorld(), loc.getX(), loc.getY(), loc.getZ(), loc.getYaw(), loc.getPitch());
  }

  public ImmutableLocation(World world, double x, double y, double z) {
    super(world, x, y, z);
  }

  public ImmutableLocation(World world, double x, double y, double z, float yaw, float pitch) {
    super(world, x, y, z, yaw, pitch);
  }

  @Override
  public void setWorld(World world) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public void setX(double x) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public void setY(double y) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public void setZ(double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public void setYaw(float yaw) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public void setPitch(float pitch) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location setDirection(Vector vector) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location add(Location vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location add(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location add(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location subtract(Location vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location subtract(Vector vec) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location subtract(double x, double y, double z) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location multiply(double m) {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public Location zero() {
    throw new UnsupportedOperationException("Trying to modify an immutable object");
  }

  @Override
  public ImmutableLocation clone() {
    return (ImmutableLocation) super.clone();
  }

  public Location cloneMutable() {
    return new Location(
        this.getWorld(),
        this.getX(),
        this.getY(),
        this.getZ(),
        this.getYaw(),
        this.getPitch()
    );
  }
}
