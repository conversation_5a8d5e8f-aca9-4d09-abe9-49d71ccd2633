# 物品奖励事件配置
# 好事件 - 物品奖励

# 事件基本信息
event:
  name: "ITEM_REWARD"
  type: "GOOD"
  weight: 10
  enabled: true

# 消息设置
messages:
  # 是否发送奖励消息到聊天栏
  send_reward_message: true
  # 是否发送BUFF效果消息到聊天栏
  send_buff_message: true
  # 奖励消息前缀
  message_prefix: "§a[幸运方块] §f"

# 奖励配置
rewards:
  # 随机剑类奖励
  random_sword:
    weight: 20
    display_name: "§6幸运之剑"
    lore:
      - "§7来自幸运方块的神秘武器"
    materials:
      - "WOODEN_SWORD"
      - "STONE_SWORD"
      - "IRON_SWORD"
      - "DIAMOND_SWORD"
    enchantments: []

  # 击退棒
  knockback_stick:
    weight: 15
    material: "STICK"
    display_name: "§c击退棒"
    lore:
      - "§7一根神奇的木棒"
      - "§7能够击退敌人"
    enchantments:
      - "KNOCKBACK:2"

  # 试作型神器森罗万象虫洞吞噬者MK-2
  special_fishing_rod:
    weight: 15
    material: "FISHING_ROD"
    display_name: "§d试作型神器森罗万象虫洞吞噬者MK-2"
    lore:
      - "§7传说中的神器"
      - "§7拥有神秘的力量"
      - "§c击退 I"
      - "§c锋利 III"
    enchantments:
      - "KNOCKBACK:1"
      - "DAMAGE_ALL:3"

  # 腐竹赐予的神剑
  divine_sword:
    weight: 15
    material: "DIAMOND_SWORD"
    display_name: "§b腐竹赐予的神剑"
    lore:
      - "§7腐竹亲自锻造的神器"
      - "§7蕴含着强大的力量"
      - "§c锋利 III"
      - "§c击退 I"
    enchantments:
      - "DAMAGE_ALL:3"
      - "KNOCKBACK:1"

  # 秒人斧
  instant_kill_axe:
    weight: 10
    material: "GOLDEN_AXE"
    display_name: "§6秒人斧"
    lore:
      - "§c危险！极其锋利的斧头"
      - "§c一击必杀！"
      - "§7但是很容易损坏..."
      - "§c锋利 255"
    enchantments:
      - "DAMAGE_ALL:255"
    durability: 1  # 耐久值设为1

  # 鸽鸽的蛋
  pigeon_eggs:
    weight: 15
    material: "EGG"
    amount: 12
    display_name: "§f鸽鸽的蛋"
    lore:
      - "§7来自神秘鸽子的蛋"
      - "§7据说有特殊的力量"
      - "§e数量: 12个"
    enchantments: []

  # 普通弓
  normal_bow:
    weight: 10
    material: "BOW"
    display_name: "§a幸运之弓"
    lore:
      - "§7来自幸运方块的弓"
      - "§7射击精准度很高"
    enchantments: []

  # 力量弓
  power_bow:
    weight: 10
    material: "BOW"
    display_name: "§c力量之弓"
    lore:
      - "§7蕴含强大力量的弓"
      - "§7箭矢威力大幅提升"
      - "§c力量 I"
    enchantments:
      - "ARROW_DAMAGE:1"

  # 腐竹送你的神弓
  divine_bow:
    weight: 10
    material: "BOW"
    display_name: "§b腐竹送你的神弓"
    lore:
      - "§7腐竹亲自制作的神弓"
      - "§7拥有强大的威力和冲击力"
      - "§c力量 I"
      - "§c冲击 I"
    enchantments:
      - "ARROW_DAMAGE:1"
      - "ARROW_KNOCKBACK:1"

  # 箭矢
  arrows:
    weight: 15
    material: "ARROW"
    amount: 16
    display_name: "§f箭矢"
    lore:
      - "§7精制的箭矢"
      - "§7射击精准度很高"
      - "§e数量: 16支"
    enchantments: []

  # 随机工具
  random_tool:
    weight: 12
    display_name: "§a幸运工具"
    lore:
      - "§7来自幸运方块的工具"
      - "§7随机获得有用的工具"
    tools:
      # 斧头 (权重40)
      axe:
        weight: 40
        materials:
          - "STONE_AXE"
          - "IRON_AXE"
          - "GOLDEN_AXE"
          - "DIAMOND_AXE"
        display_name: "§6幸运斧头"
        lore:
          - "§7来自幸运方块的斧头"
          - "§7砍伐效率很高"
      # 镐子 (权重40)
      pickaxe:
        weight: 40
        materials:
          - "STONE_PICKAXE"
          - "IRON_PICKAXE"
          - "GOLDEN_PICKAXE"
          - "DIAMOND_PICKAXE"
        display_name: "§6幸运镐子"
        lore:
          - "§7来自幸运方块的镐子"
          - "§7挖掘效率很高"
      # 剪刀 (权重20)
      shears:
        weight: 20
        material: "SHEARS"
        display_name: "§6幸运剪刀"
        lore:
          - "§7来自幸运方块的剪刀"
          - "§7剪切效率很高"

  # 世界吞噬者
  world_eater:
    weight: 8
    material: "DIAMOND_PICKAXE"
    display_name: "§5世界吞噬者"
    lore:
      - "§7传说中的神器镐子"
      - "§7能够吞噬一切方块"
      - "§c效率 V"
    enchantments:
      - "DIG_SPEED:5"

  # 喝的药水
  beneficial_potion:
    weight: 12
    display_name: "§d神秘药水"
    lore:
      - "§7来自幸运方块的神秘药水"
      - "§7饮用后获得有益效果"
    potions:
      # 瞬间恢复
      instant_heal:
        weight: 20
        material: "POTION"
        effect: "HEAL"
        level: 2
        duration: 0  # 瞬间效果
        display_name: "§c瞬间恢复药水"
        lore:
          - "§7立即恢复生命值"
          - "§c瞬间恢复 II"
      # 再生
      regeneration:
        weight: 18
        material: "POTION"
        effect: "REGENERATION"
        level: 1
        duration: 300  # 15秒
        display_name: "§d再生药水"
        lore:
          - "§7缓慢恢复生命值"
          - "§d再生 I (15秒)"
      # 隐身
      invisibility:
        weight: 15
        material: "POTION"
        effect: "INVISIBILITY"
        level: 1
        duration: 400  # 20秒
        display_name: "§7隐身药水"
        lore:
          - "§7让你变得隐形"
          - "§7隐身 (20秒)"
      # 速度
      speed:
        weight: 17
        material: "POTION"
        effect: "SPEED"
        level: 2
        duration: 2400  # 2分钟
        display_name: "§b速度药水"
        lore:
          - "§7大幅提升移动速度"
          - "§b速度 II (2分钟)"
      # 跳跃
      jump_boost:
        weight: 15
        material: "POTION"
        effect: "JUMP"
        level: 5
        duration: 1200  # 60秒
        display_name: "§a跳跃药水"
        lore:
          - "§7极大提升跳跃能力"
          - "§a跳跃提升 V (60秒)"
      # 力量
      strength:
        weight: 15
        material: "POTION"
        effect: "INCREASE_DAMAGE"
        level: 1
        duration: 1200  # 60秒
        display_name: "§c力量药水"
        lore:
          - "§7提升攻击伤害"
          - "§c力量 I (60秒)"

  # 喷溅型药水
  splash_potion:
    weight: 10
    display_name: "§8喷溅药水"
    lore:
      - "§7来自幸运方块的喷溅药水"
      - "§7投掷后产生负面效果"
    potions:
      # 缓慢
      slowness:
        weight: 35
        material: "SPLASH_POTION"
        effect: "SLOW"
        level: 1
        duration: 300  # 15秒
        display_name: "§7喷溅型缓慢药水"
        lore:
          - "§7投掷后使敌人缓慢"
          - "§7缓慢 I (15秒)"
      # 中毒
      poison:
        weight: 35
        material: "SPLASH_POTION"
        effect: "POISON"
        level: 1
        duration: 300  # 15秒
        display_name: "§2喷溅型中毒药水"
        lore:
          - "§7投掷后使敌人中毒"
          - "§2中毒 I (15秒)"
      # 虚弱
      weakness:
        weight: 30
        material: "SPLASH_POTION"
        effect: "WEAKNESS"
        level: 1
        duration: 500  # 25秒
        display_name: "§8喷溅型虚弱药水"
        lore:
          - "§7投掷后使敌人虚弱"
          - "§8虚弱 I (25秒)"

  # TNT
  tnt:
    weight: 8
    material: "TNT"
    amount: 2
    display_name: "§cTNT"
    lore:
      - "§7危险的爆炸物"
      - "§7小心使用！"
      - "§e数量: 2个"
    enchantments: []

  # 火焰弹
  fire_charge:
    weight: 10
    special_item_id: "Fireball"
    amount: 4
    display_name: "§6火焰弹"
    lore:
      - "§7燃烧的弹药"
      - "§7投掷后产生爆炸"
      - "§e数量: 4个"
    enchantments: []

  # 末影珍珠
  ender_pearl:
    weight: 6
    material: "ENDER_PEARL"
    amount: 1
    display_name: "§5末影珍珠"
    lore:
      - "§7神秘的传送道具"
      - "§7投掷后瞬间传送"
    enchantments: []

  # 蜘蛛网
  cobweb:
    weight: 8
    material: "COBWEB"
    amount: 1
    display_name: "§f蜘蛛网"
    lore:
      - "§7粘性的陷阱"
      - "§7可以减慢敌人速度"
    enchantments: []

  # 水桶
  water_bucket:
    weight: 7
    material: "WATER_BUCKET"
    amount: 1
    display_name: "§9水桶"
    lore:
      - "§7装满水的桶"
      - "§7可以用来灭火或阻挡敌人"
    enchantments: []

  # 牛排
  steak:
    weight: 12
    material: "COOKED_BEEF"
    amount: 12
    display_name: "§6牛排"
    lore:
      - "§7美味的熟牛肉"
      - "§7恢复大量饥饿值"
      - "§e数量: 12个"
    enchantments: []

  # 金苹果
  golden_apple:
    weight: 5
    material: "GOLDEN_APPLE"
    amount: 4
    display_name: "§6金苹果"
    lore:
      - "§7珍贵的治疗食物"
      - "§7提供强大的恢复效果"
      - "§e数量: 4个"
    enchantments: []

  # 救援平台
  rescue_platform:
    weight: 6
    special_item_id: "RescuePlatform"
    amount: 2
    display_name: "§a救援平台"
    lore:
      - "§7紧急救援道具"
      - "§7可以快速搭建平台"
      - "§e数量: 2个"
    enchantments: []

  # 搭桥蛋
  bridge_egg:
    weight: 7
    special_item_id: "egg-bridger"
    amount: 1
    display_name: "§e搭桥蛋"
    lore:
      - "§7神奇的搭桥道具"
      - "§7投掷后自动搭建桥梁"
    enchantments: []

  # 铁傀儡守卫
  iron_golem_guard:
    weight: 4
    special_item_id: "GuardDog"
    amount: 1
    display_name: "§7铁傀儡守卫"
    lore:
      - "§7忠诚的守护者"
      - "§7会保护你免受敌人攻击"
    enchantments: []

  # 蠹虫
  silverfish:
    weight: 5
    special_item_id: "silverfish"
    amount: 1
    display_name: "§8蠹虫"
    lore:
      - "§7投掷后召唤友善的蠹虫"
      - "§7它们会帮助你攻击敌人"
    enchantments: []

  # 自爆羊
  tnt_sheep:
    weight: 4
    special_item_id: "TNTSheep"
    amount: 1
    display_name: "§c自爆羊"
    lore:
      - "§7危险的爆炸羊"
      - "§7会追击敌人并爆炸"
    enchantments: []

  # 直接生效BUFF
  instant_buff:
    weight: 8
    type: "INSTANT_BUFF"
    display_name: "§d神秘祝福"
    lore:
      - "§7来自幸运方块的神秘力量"
      - "§7立即获得有益效果"
    buffs:
      # 瞬间恢复2
      instant_heal:
        weight: 20
        effect: "HEAL"
        level: 2
        duration: 0  # 瞬间效果
        display_name: "§c瞬间恢复 II"
        message: "§a[幸运方块] §f你获得了瞬间恢复效果！"

      # 再生20s
      regeneration:
        weight: 18
        effect: "REGENERATION"
        level: 1
        duration: 400  # 20秒
        display_name: "§d再生 I"
        message: "§a[幸运方块] §f你获得了再生效果！持续20秒"

      # 隐身30s
      invisibility:
        weight: 15
        effect: "INVISIBILITY"
        level: 1
        duration: 600  # 30秒
        display_name: "§7隐身"
        message: "§a[幸运方块] §f你获得了隐身效果！持续30秒"

      # 速度2（3分钟）
      speed:
        weight: 17
        effect: "SPEED"
        level: 2
        duration: 3600  # 3分钟
        display_name: "§b速度 II"
        message: "§a[幸运方块] §f你获得了速度提升效果！持续3分钟"

      # 跳跃5（90秒）
      jump_boost:
        weight: 15
        effect: "JUMP"
        level: 5
        duration: 1800  # 90秒
        display_name: "§a跳跃提升 V"
        message: "§a[幸运方块] §f你获得了跳跃提升效果！持续90秒"

      # 抗性提升15s
      resistance:
        weight: 15
        effect: "DAMAGE_RESISTANCE"
        level: 1
        duration: 300  # 15秒
        display_name: "§6抗性提升 I"
        message: "§a[幸运方块] §f你获得了抗性提升效果！持续15秒"

# 消息配置
messages:
  success: "§a[幸运方块] §f你获得了: {item_name}"
  drop_failed: "§c[幸运方块] §f物品掉落失败，已放入背包"
