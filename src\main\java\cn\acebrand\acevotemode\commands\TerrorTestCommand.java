package cn.acebrand.acevotemode.commands;

import cn.acebrand.acevotemode.AceVoteMode;
import io.lumine.mythic.bukkit.MythicBukkit;
import io.lumine.mythic.api.mobs.MythicMob;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Optional;

/**
 * 恐怖降临测试命令
 * 用于测试MythicMobs Boss是否正确配置
 */
public class TerrorTestCommand implements CommandExecutor {
    
    private final AceVoteMode plugin;
    
    public TerrorTestCommand(AceVoteMode plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "此命令只能由玩家执行！");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (!player.hasPermission("acevotemode.admin")) {
            player.sendMessage(ChatColor.RED + "您没有权限使用此命令！");
            return true;
        }
        
        if (args.length == 0) {
            sendHelp(player);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "check":
                checkBossConfiguration(player);
                break;
            case "spawn":
                if (args.length < 2) {
                    player.sendMessage(ChatColor.RED + "用法: /terrortest spawn <boss_id>");
                    return true;
                }
                spawnBoss(player, args[1]);
                break;
            case "list":
                listAvailableBosses(player);
                break;
            default:
                sendHelp(player);
                break;
        }
        
        return true;
    }
    
    private void sendHelp(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== 恐怖降临测试命令 ===");
        player.sendMessage(ChatColor.YELLOW + "/terrortest check - 检查Boss配置");
        player.sendMessage(ChatColor.YELLOW + "/terrortest spawn <boss_id> - 生成指定Boss");
        player.sendMessage(ChatColor.YELLOW + "/terrortest list - 列出可用的Boss");
    }
    
    private void checkBossConfiguration(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== 检查恐怖降临Boss配置 ===");
        
        String[] bossIds = {"LightHerald1", "LightHerald2", "LightCore", "LightWarrior", "Spear", "Spear2", "Beam1", "Beam2", "LightProjectile"};
        
        for (String bossId : bossIds) {
            Optional<MythicMob> mythicMobOpt = MythicBukkit.inst().getMobManager().getMythicMob(bossId);
            if (mythicMobOpt.isPresent()) {
                player.sendMessage(ChatColor.GREEN + "✓ " + bossId + " - 配置正确");
            } else {
                player.sendMessage(ChatColor.RED + "✗ " + bossId + " - 配置缺失");
            }
        }
        
        player.sendMessage(ChatColor.GOLD + "检查完成！");
    }
    
    private void spawnBoss(Player player, String bossId) {
        Optional<MythicMob> mythicMobOpt = MythicBukkit.inst().getMobManager().getMythicMob(bossId);
        if (!mythicMobOpt.isPresent()) {
            player.sendMessage(ChatColor.RED + "Boss " + bossId + " 不存在！");
            return;
        }
        
        try {
            MythicBukkit.inst().getAPIHelper().spawnMythicMob(bossId, player.getLocation());
            player.sendMessage(ChatColor.GREEN + "成功生成Boss: " + bossId);
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "生成Boss失败: " + e.getMessage());
            plugin.getLogger().warning("生成Boss失败: " + e.getMessage());
        }
    }
    
    private void listAvailableBosses(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== 恐怖降临Boss列表 ===");
        player.sendMessage(ChatColor.YELLOW + "主要Boss:");
        player.sendMessage(ChatColor.WHITE + "- LightHerald1 (第一阶段)");
        player.sendMessage(ChatColor.WHITE + "- LightHerald2 (第二阶段)");
        player.sendMessage(ChatColor.WHITE + "- LightCore (最终阶段)");
        player.sendMessage(ChatColor.YELLOW + "召唤物:");
        player.sendMessage(ChatColor.WHITE + "- LightWarrior (光明战士)");
        player.sendMessage(ChatColor.WHITE + "- Spear/Spear2 (光明长矛)");
        player.sendMessage(ChatColor.WHITE + "- Beam1/Beam2 (光明光束)");
        player.sendMessage(ChatColor.WHITE + "- LightProjectile (光明投射物)");
    }
}
