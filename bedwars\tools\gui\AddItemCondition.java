package de.marcely.bedwars.tools.gui;

import lombok.Getter;

/**
 * Optional parameter when adding items into a GUI to force them to be in a specific area
 */
public class AddItemCondition {

  private static final byte TYPE_WITHIN_X = 0;
  private static final byte TYPE_WITHIN_Y = 1;
  private static final byte TYPE_WITHIN = 2;

  @Getter
  private final byte type;
  @Getter
  private final int[] parameters;

  private AddItemCondition(byte type, int[] params) {
    this.type = type;
    this.parameters = params;
  }

  /**
   * Forces the item to be within the X range
   *
   * @param xMin The minimum X value (this position is included within the range)
   * @param xMax The maximum X value (this position is included within the range)
   * @return The condition that you can use within the GUI
   */
  public static AddItemCondition withinX(int xMin, int xMax) {
    final int[] params = new int[4];

    params[0] = xMin;
    params[1] = xMax;
    params[2] = 0;
    params[3] = 5;

    return new AddItemCondition(TYPE_WITHIN_X, params);
  }

  /**
   * Forces the item to be within the Y range
   *
   * @param yMin The minimum Y value (this position is included within the range)
   * @param yMax The maximum Y value (this position is included within the range)
   * @return The condition that you can use within the GUI
   */
  public static AddItemCondition withinY(int yMin, int yMax) {
    final int[] params = new int[4];

    params[0] = 0;
    params[1] = 8;
    params[2] = yMin;
    params[3] = yMax;

    return new AddItemCondition(TYPE_WITHIN_Y, params);
  }

  /**
   * Forces the item to be within the X and Y range
   *
   * @param xMin The minimum X value (this position is included within the range)
   * @param xMax The maximum X value (this position is included within the range)
   * @param yMin The minimum Y value (this position is included within the range)
   * @param yMax The maximum Y value (this position is included within the range)
   * @return The condition that you can use within the GUI
   */
  public static AddItemCondition within(int xMin, int xMax, int yMin, int yMax) {
    final int[] params = new int[4];

    params[0] = xMin;
    params[1] = xMax;
    params[2] = yMin;
    params[3] = yMax;

    return new AddItemCondition(TYPE_WITHIN, params);
  }
}
