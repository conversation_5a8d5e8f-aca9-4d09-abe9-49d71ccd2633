package de.marcely.bedwars.tools;

import java.util.function.Supplier;

/**
 * Represents a lazy reference that will only be initialized when it's being accessed.
 * <p>
 *   After the initial access, the reference will be cached and returned on every subsequent access.
 * </p>
 *
 * @param <T> The type of the reference
 */
public class LazyReference<T> implements Supplier<T> {

  private final Supplier<T> factory;
  private T cached;

  /**
   * Creates a new lazy reference.
   *
   * @param factory The factory that will be called when the reference is being accessed
   */
  public LazyReference(Supplier<T> factory) {
    this.factory = factory;
  }

  /**
   * Gets the reference.
   *
   * @return The reference
   * @throws IllegalStateException If the factory returns null
   */
  @Override
  public T get() {
    if (this.cached != null)
      return this.cached;

    this.cached = this.factory.get();

    if (this.cached == null)
      throw new IllegalStateException("Factory may not return null");

    return this.cached;
  }

  /**
   * Clears the cached reference.
   */
  public void clear() {
    this.cached = null;
  }

  /**
   * Creates a new lazy reference.
   *
   * @param factory The factory that will be called when the reference is being accessed
   * @param <T> The type of the reference
   * @return The new lazy reference
   */
  public static <T> Supplier<T> of(Supplier<T> factory) {
    return new LazyReference<>(factory);
  }
}
