package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called whenever an existing arena shall be renamed.
 */
public class ArenaRenameEvent extends Event implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private final String oldName, newName;

  @Getter @Setter
  private boolean cancelled = false;

  public ArenaRenameEvent(Arena arena, String oldName, String newName) {
    this.arena = arena;
    this.oldName = oldName;
    this.newName = newName;
  }

  @Override
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Gets the current name of the arena.
   *
   * @return The old name
   */
  public String getOldName() {
    return this.oldName;
  }

  /**
   * Gets the possibly new name of the arena.
   *
   * @return The new name
   */
  public String getNewName() {
    return this.newName;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
