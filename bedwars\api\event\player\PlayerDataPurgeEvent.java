package de.marcely.bedwars.api.event.player;

import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

import java.util.UUID;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called right before some (or all) parts of player's data gets cleared.
 */
public class PlayerDataPurgeEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final CommandSender executingSender;
  private final UUID affectedPlayerUUID;
  private final boolean purgeStats;
  private final boolean purgeAchievements;
  private final boolean purgeProperties;
  private final boolean cacheOnly;
  private final Set<String> statSetIds;
  @Getter @Setter
  private boolean cancelled = false;

  public PlayerDataPurgeEvent(
      @Nullable CommandSender executingSender,
      @Nullable UUID affectedPlayerUUID,
      boolean purgeStats,
      boolean purgeAchievements,
      boolean purgeProperties,
      boolean isCacheOnly,
      @Nullable Set<String> statSetIds) {

    this.executingSender = executingSender;
    this.affectedPlayerUUID = affectedPlayerUUID;
    this.purgeStats = purgeStats;
    this.purgeAchievements = purgeAchievements;
    this.purgeProperties = purgeProperties;
    this.cacheOnly = isCacheOnly;
    this.statSetIds = statSetIds;
  }

  /**
   * The {@link CommandSender} who sent the command to reset all player stats.
   * <p>
   *   Maybe be <code>null</code> if it e.g. has been executed via the API.
   * </p>
   *
   * @return The (optional) {@link CommandSender} trying to clear all player stats
   */
  @Nullable
  public CommandSender getExecutingSender() {
    return this.executingSender;
  }

  /**
   * The UUID of the player that is affected with this event.
   * <p>
   *   <code>null</code> means that ALL players are affected by this purge.
   *   If all players are affected, {@link #isAffectingAllPlayers()} returns <code>true</code>.
   * </p>
   *
   * @return The (optional) uuid of the player from whom the data shall be purged
   */
  @Nullable
  public UUID getAffectedPlayerUUID() {
    return this.affectedPlayerUUID;
  }

  /**
   * Whether all players are affected from this purge, and not just a single one.
   * <p>
   *   In case {@link #isCacheOnly()} returns true, this method only refers to players that are on this single server.
   * </p>
   *
   * @return <code>true</code> in case this purge will affect all players
   */
  public boolean isAffectingAllPlayers() {
    return this.affectedPlayerUUID == null;
  }

  /**
   * Whether stats shall be purged.
   *
   * @return <code>true</code> if the stats are being affected
   */
  public boolean isAffectingStats() {
      return purgeStats;
  }

  /**
   * Whether properties shall be purged.
   *
   * @return <code>true</code> if the properties are being affected
   */
  public boolean isAffectingProperties() {
      return purgeProperties;
  }

  /**
   * Whether achievements shall be purged.
   *
   * @return <code>true</code> if the achievements are being affected
   */
  public boolean isAffectingAchievements() {
      return purgeAchievements;
  }

  /**
   * Whether only the local cache is affected.
   * <p>
   *   <code>true</code> would mean that the applied properties affect ALL servers and the WHOLE permanent database.
   *   <code>false</code> would mean that it'd only apply it for all locally (on this server) cached players.
   * </p>
   *
   * @return Whether only the local cache shall be purged
   */
  public boolean isCacheOnly() {
    return this.cacheOnly;
  }

  /**
   * Get the stat sets that shall be purged.
   * <p>
   *   It may occur that only certain stat sets shall be purged.
   *   In this case, {@link #isAffectingStats()} returns true and this method
   *   returns a non-null set of the affected stat set ids.
   * </p>
   * <p>
   *   The returned set is not modifiable.
   * </p>
   *
   * @return The set of stat set ids that shall be purged. May be <code>null</code> if there's no restriction
   */
  @Nullable
  public Set<String> getRestrictedStatSetIds() {
    return this.statSetIds;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
