package de.marcely.bedwars.api.message;

import de.marcely.bedwars.tools.Validate;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Contains a collection of messages that'll be used when the user tries to obtain a message from his messages file that is however not present.
 * <p>
 *     This class is helpful for plugins that want to retain the users ability to customize the message.
 *     You may directly link it to your internal messages.yml file making it easier using {@link #loadInternalYAML(Plugin, InputStream)}.
 * </p>
 */
public final class DefaultMessageMappings implements Map<String, String> {

  private final Plugin plugin;
  private final Map<String, String> entries = new HashMap<>();

  public DefaultMessageMappings(Plugin plugin) {
    Validate.notNull(plugin, "plugin");

    this.plugin = plugin;
  }

  /**
   * Returns the plugin that has constructed this instance.
   *
   * @return The plugin behind these mappings
   */
  public Plugin getPlugin() {
    return this.plugin;
  }

  @Override
  public int size() {
    return this.entries.size();
  }

  @Override
  public boolean isEmpty() {
    return this.entries.isEmpty();
  }

  @Override
  public boolean containsKey(Object key) {
    return this.entries.containsKey(((String) key).toLowerCase());
  }

  @Override
  public boolean containsValue(Object value) {
    return this.entries.containsValue(value);
  }

  @Override
  public String get(Object key) {
    Validate.notNull(key, "key");

    return this.entries.get(((String) key).toLowerCase());
  }

  @Nullable
  @Override
  public String put(String key, String value) {
    Validate.notNull(key, "key");
    Validate.notNull(value, "value");

    return this.entries.put(key.toLowerCase(), value);
  }

  @Override
  public String remove(Object key) {
    Validate.notNull(key, "key");

    return this.entries.remove(key);
  }

  @Override
  public void putAll(@NotNull Map<? extends String, ? extends String> m) {
    Validate.notNull(m, "map");

    m.forEach((key, value) -> put(key.toLowerCase(), value));
  }

  @Override
  public void clear() {
    this.entries.clear();
  }

  @NotNull
  @Override
  public Set<String> keySet() {
    return this.entries.keySet();
  }

  @NotNull
  @Override
  public Collection<String> values() {
    return this.entries.values();
  }

  @NotNull
  @Override
  public Set<Entry<String, String>> entrySet() {
    return this.entries.entrySet();
  }

  /**
   * Tries to load the YAML file as a language file.
   * <p>
   *     Automatically closes the stream.
   * </p>
   *
   * @param stream The stream from which shall be read
   * @param plugin The plugin that wants them to get read
   * @return The read mappings
   * @throws IOException Might fail to load it
   */
  public static DefaultMessageMappings loadInternalYAML(Plugin plugin, InputStream stream) throws IOException {
    Validate.notNull(stream, "stream");

    try {
      Validate.notNull(plugin, "plugin");
      Validate.isTrue(plugin.isEnabled(), "plugin is not enabled");

      final YamlConfiguration config = YamlConfiguration.loadConfiguration(new InputStreamReader(stream, StandardCharsets.UTF_8));
      final DefaultMessageMappings mappings = new DefaultMessageMappings(plugin);

      for (String key : config.getKeys(false)) {
        if (config.isString(key)) {
          mappings.put(
              key,
              config.getString(key));

        } else if (config.isList(key)) {
          mappings.put(
              key,
              String.join("\\n", config.getStringList(key)));

        } else {
          throw new IOException("Entry \"" + key + "\" has a invalid type (neither a String nor a List)");
        }
      }

      return mappings;
    } catch (IOException e) {
      throw e;
    } catch (Exception e) {
      throw new IOException(e);
    } finally {
      stream.close();
    }
  }
}
