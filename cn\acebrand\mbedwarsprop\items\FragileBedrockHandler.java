package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class FragileBedrockHandler implements SpecialItemUseHandler {

    private final Plugin plugin;
    private final ItemConfigManager.ItemConfig config;
    private final Map<Location, BukkitTask> bedrockTasks = new HashMap<>();

    public FragileBedrockHandler(Plugin plugin, ItemConfigManager.ItemConfig config) {
        this.plugin = plugin;
        this.config = config;
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    private class Session extends SpecialItemUseSession {

        private Block placedBlock;
        private BukkitTask decayTask;

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            if (this.placedBlock == null)
                return;

            // 如果会话被强制停止，清理方块和任务
            if (this.decayTask != null && !this.decayTask.isCancelled()) {
                this.decayTask.cancel();
            }

            bedrockTasks.remove(this.placedBlock.getLocation());
            this.placedBlock.setType(Material.AIR);
        }

        protected void run() {
            final Block clickedBlock = getEvent().getClickedBlock();
            final BlockFace clickedBlockFace = getEvent().getClickedBlockFace();

            // 检查是否有有效的目标方块
            if (clickedBlock == null || clickedBlockFace != BlockFace.UP) {
                getEvent().getPlayer().sendMessage("§c请指向一个有效的方块顶部来放置基岩！");
                stop();
                return;
            }

            // 获取放置位置（目标方块上方）
            Block placeBlock = clickedBlock.getRelative(clickedBlockFace);

            // 检查放置位置是否为空气
            if (placeBlock.getType() != Material.AIR) {
                getEvent().getPlayer().sendMessage("§c无法在此处放置基岩！");
                stop();
                return;
            }

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(getEvent().getPlayer());
            if (arena == null) {
                stop();
                return;
            }

            // 放置基岩
            placeBlock.setType(Material.BEDROCK);
            this.placedBlock = placeBlock;

            // 标记方块为玩家放置，这样游戏结束时会被清理
            arena.setBlockPlayerPlaced(placeBlock, true);

            // 获取配置
            // 默认值
            int durationValue = 20; // 默认20秒
            Material decayMaterialValue = Material.OBSIDIAN; // 默认黑曜石

            if (config != null) {
                // 获取持续时间
                ItemConfigManager.EffectConfig durationConfig = config.getEffect("duration");
                if (durationConfig != null) {
                    // 尝试使用 getDuration() 方法
                    int duration = durationConfig.getDuration();
                    if (duration > 0) {
                        durationValue = duration;
                    } else {
                        // 如果 duration 为 0，尝试使用 getLevel() 方法
                        int level = durationConfig.getLevel();
                        if (level > 0) {
                            durationValue = level;
                        }
                    }

                    // 输出调试信息
                    plugin.getLogger().info("基岩持续时间: " + durationValue + " 秒");
                }

                // 获取退化后的方块类型
                ItemConfigManager.EffectConfig decayMaterialConfig = config.getEffect("decay-material");
                if (decayMaterialConfig != null) {
                    String materialName = decayMaterialConfig.toString().toUpperCase();
                    try {
                        Material material = Material.valueOf(materialName);
                        decayMaterialValue = material;
                    } catch (IllegalArgumentException e) {
                        // 使用默认值
                        plugin.getLogger().warning("无效的材质名称: " + materialName + ", 使用默认值: OBSIDIAN");
                    }

                    // 输出调试信息
                    plugin.getLogger().info("基岩退化后的方块类型: " + decayMaterialValue.name());
                }
            }

            // 使用final变量传递给匿名内部类
            final int duration = durationValue;
            final Material decayMaterial = decayMaterialValue;

            // 创建退化任务
            this.decayTask = new BukkitRunnable() {
                private int remainingSeconds = duration;

                @Override
                public void run() {
                    if (remainingSeconds <= 0 || placedBlock.getType() != Material.BEDROCK) {
                        // 时间到或方块被破坏，变成黑曜石
                        if (placedBlock.getType() == Material.BEDROCK) {
                            placedBlock.setType(decayMaterial);

                            // 播放效果
                            placedBlock.getWorld().playSound(placedBlock.getLocation(), Sound.BLOCK_STONE_BREAK, 1.0f, 0.5f);
                            placedBlock.getWorld().spawnParticle(Particle.BLOCK_CRACK,
                                    placedBlock.getLocation().add(0.5, 0.5, 0.5),
                                    20, 0.5, 0.5, 0.5, 0, decayMaterial.createBlockData());
                        }

                        bedrockTasks.remove(placedBlock.getLocation());
                        cancel();
                        return;
                    }

                    // 每5秒显示一次效果
                    if (remainingSeconds % 5 == 0 || remainingSeconds <= 3) {
                        // 播放效果
                        placedBlock.getWorld().playSound(placedBlock.getLocation(), Sound.BLOCK_STONE_HIT, 0.5f, 1.0f);
                        placedBlock.getWorld().spawnParticle(Particle.BLOCK_CRACK,
                                placedBlock.getLocation().add(0.5, 0.5, 0.5),
                                10, 0.5, 0.5, 0.5, 0, Material.BEDROCK.createBlockData());
                    }

                    remainingSeconds--;
                }
            }.runTaskTimer(plugin, 20L, 20L); // 每秒执行一次

            // 保存任务
            bedrockTasks.put(placeBlock.getLocation(), this.decayTask);

            getEvent().getPlayer().sendMessage("§a成功放置脆弱的基岩！将在" + duration + "秒后变成黑曜石");

            // 消耗物品
            takeItem();
        }
    }
}
