package de.marcely.bedwars.api.world.block;

import org.jetbrains.annotations.Nullable;

/**
 * SpecialBlocks may very from what they represent
 */
public enum BlockType {

  /**
   * A sign which displays informations of a place in a leaderboard.
   * <p>
   * Represented by {@link RankingSignBlock}
   */
  RANKING_SIGN("ranking_sign"),

  /**
   * A skull of a player on a specific rank. Used for the leaderboard.
   * <p>
   * Represented by {@link RankingSkullBlock}
   */
  RANKING_SKULL("ranking_skull"),

  /**
   * A sign which makes the player join the arena once he interacts with it.
   * <p>
   * Represented by {@link JoinArenaSignBlock}
   */
  JOIN_ARENA_SIGN("join_arena_sign"),

  /**
   * A block with a custom handler. The only purpose for this is to allow devs to create custom special blocks with custom handlers.
   * <p>
   * Represented by {@link CustomBlock}
   */
  CUSTOM("custom");

  private final String id;

  BlockType(String id) {
    this.id = id;
  }

  /**
   * Returns the id of this type
   *
   * @return The id
   */
  public String getId() {
    return this.id;
  }

  /**
   * Returns the BlockType that has the given id
   *
   * @param id The id that shall match with the returning BlockType
   * @return The BlockType with the given id. <code>null</code> if there's none
   */
  public static @Nullable BlockType fromId(String id) {
    for (BlockType type : values()) {
      if (type.id.equalsIgnoreCase(id)) {
        return type;
      }
    }

    return null;
  }
}
