package de.marcely.bedwars.api.world.block;

import de.marcely.bedwars.api.world.WorldStorage;
import de.marcely.bedwars.tools.PersistentStorage;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;

import java.util.function.Consumer;
import org.bukkit.metadata.Metadatable;

/**
 * A SpecialBlock represents a normal block with special (maybe even interactable) features.
 * <p>
 * They get automatically saved and loaded and it's possible to create custom variants using {@link CustomBlock}
 */
public interface SpecialBlock extends Metadatable, PersistentStorage.Holder {

  /**
   * SpecialBlocks vary from the type they represent.
   * You may cast to the corresponding type after matching its type
   *
   * @return The type of this SpecialBlock
   */
  BlockType getType();

  /**
   * Returns the world to which this block initially was added.<br>
   * Keep in mind that the block might have been removed.
   * You can verify it by calling {@link #exists()}
   *
   * @return The world to which this block was added to
   */
  WorldStorage getWorld();

  /**
   * Returns Bukkit's variant of the block that's located at the same location as this one
   *
   * @return The Block located at the same location as this one
   */
  Block asBukkit();

  /**
   * Returns Bukkit's variant of the block that's located at the same location as this one.
   * <p>
   *     If supported, it will uses paper's async chunk loading feature.
   * </p>
   *
   * @param callback The callback that is being called on the main thread
   */
  void asBukkitAsync(Consumer<Block> callback);

  /**
   * Returns the x coordinate of this block
   *
   * @return The x coordinate
   */
  int getX();

  /**
   * Returns the y coordinate of this block
   *
   * @return The y coordinate
   */
  short getY();

  /**
   * Returns the z coordinate of this block
   *
   * @return The z coordinate
   */
  int getZ();

  /**
   * Tries to apply the given informations into the game world.<br>
   * By this e.g. a sign gets updated with the newly changed stats
   */
  void update();

  /**
   * Call this if you want to simulate an interaction of the player
   *
   * @param player The player who clicked on the sign
   * @param parentEvent The event which might contain info needed for the block
   */
  void onInteract(Player player, PlayerInteractEvent parentEvent);

  /**
   * Returns if this block is still existing inside its WorldStorage.<br>
   * Keep in mind that this varies per instance and that this instance is forever garbage when it returns false
   *
   * @return If this block still exists
   */
  boolean exists();

  /**
   * Tries to remove this block from its WorldStorage
   *
   * @return <code>false</code> when it already has been removed. Otherwise true
   */
  boolean remove();
}
