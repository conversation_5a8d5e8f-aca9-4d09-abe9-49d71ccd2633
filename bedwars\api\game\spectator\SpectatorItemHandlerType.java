package de.marcely.bedwars.api.game.spectator;

import org.jetbrains.annotations.Nullable;

/**
 * Represents the type of {@link SpectatorItemHandler}.
 * <p>
 * Custom ones use {@link #PLUGIN}
 */
public enum SpectatorItemHandlerType {

  /**
   * Clicking on causes the fly/movement speed of a player to get changed.
   */
  CHANGE_SPEED("bedwars:change_speed"),

  /**
   * Player leaves the spectating mode when clicking on it.
   */
  LEAVE("bedwars:leave"),

  /**
   * Clicking on it causes the player to get sent to a new game.
   */
  NEXT_ROUND("bedwars:next_round"),

  /**
   * Opens a GUI which contains all players currently playing inside the arena.
   */
  VIEW_PLAYERS("bedwars:view_players"),

  /**
   * A custom type created by something accessing the API.
   */
  PLUGIN(null);

  private final String id;

  SpectatorItemHandlerType(@Nullable String id) {
    this.id = id;
  }

  /**
   * Returns the id that's being used for the handler.
   * <code>null</code> is being returned when passing {@link #PLUGIN}.
   *
   * @return The id of this handler type
   */
  public @Nullable String getId() {
    return this.id;
  }
}
