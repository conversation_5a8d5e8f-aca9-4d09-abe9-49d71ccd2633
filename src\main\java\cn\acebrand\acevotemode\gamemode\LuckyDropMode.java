package cn.acebrand.acevotemode.gamemode;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.LuckyEventManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.player.PlayerOpenShopEvent;
import de.marcely.bedwars.api.event.player.PlayerShopProductGivingDetermineEvent;

import de.marcely.bedwars.api.game.shop.ShopItem;
import de.marcely.bedwars.api.game.shop.ShopPage;
import de.marcely.bedwars.api.game.spawner.DropType;
// 移除了特殊物品相关导入
import org.bukkit.ChatColor;
import org.bukkit.DyeColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 幸运降临模式
 * 特点：商店仅出售基础方块和装备，其他物品通过幸运方块获得
 */
public class LuckyDropMode extends GameModeBase implements Listener {

    // 移除了特殊物品，现在使用普通海绵

    // 记录每个竞技场的活跃状态
    private final Set<Arena> activeArenas = ConcurrentHashMap.newKeySet();

    // 允许在商店中出售的物品类别
    private final Set<String> allowedShopCategories = new HashSet<>();

    // 记录放置的幸运方块位置
    private final Set<Location> placedLuckyBlocks = ConcurrentHashMap.newKeySet();

    // 事件管理器
    private LuckyEventManager eventManager;

    public LuckyDropMode(AceVoteMode plugin) {
        super(plugin, "lucky-drop", "幸运降临");

        // 初始化允许的商店类别
        initAllowedCategories();

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // 初始化事件管理器
        this.eventManager = new LuckyEventManager(plugin);

        // 不再注册特殊物品，幸运方块作为普通商店物品

    }

    /**
     * 初始化允许的商店物品类别
     */
    private void initAllowedCategories() {
        // 基础方块类别
        allowedShopCategories.add("blocks");
        allowedShopCategories.add("wool");
        allowedShopCategories.add("clay");
        allowedShopCategories.add("glass");
        allowedShopCategories.add("stone");
        allowedShopCategories.add("wood");
        allowedShopCategories.add("endstone");
        allowedShopCategories.add("ladder");
        allowedShopCategories.add("obsidian");

        // 护甲类别
        allowedShopCategories.add("armor");
        allowedShopCategories.add("helmet");
        allowedShopCategories.add("chestplate");
        allowedShopCategories.add("leggings");
        allowedShopCategories.add("boots");

        // 拆家工具类别
        allowedShopCategories.add("tools");
        allowedShopCategories.add("pickaxe");
        allowedShopCategories.add("axe");
        allowedShopCategories.add("shears");

        // 基础武器
        allowedShopCategories.add("sword");
        allowedShopCategories.add("stick");

        // 幸运方块
        allowedShopCategories.add("lucky-block");
    }

    // 移除了特殊物品注册，现在使用湿海绵

    /**
     * 创建幸运方块物品（自定义玩家头颅）
     */
    private ItemStack createLuckyBlockItem() {
        ItemStack luckyBlock = new ItemStack(Material.PLAYER_HEAD);
        org.bukkit.inventory.meta.SkullMeta meta = (org.bukkit.inventory.meta.SkullMeta) luckyBlock.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(ChatColor.YELLOW + "✦ " + ChatColor.GREEN + "幸运方块" + ChatColor.YELLOW + " ✦");

            // 获取价格配置
            String[] priceInfo = getLuckyBlockPriceInfo();
            String priceText = priceInfo[0]; // 价格文本

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "放置在地上然后破坏获得随机奖励！");
            lore.add(ChatColor.YELLOW + "运气也是实力的一部分");
            lore.add(ChatColor.DARK_GRAY + "右键放置，左键破坏");
            lore.add("");
            lore.add(ChatColor.YELLOW + "🧽 神秘的海绵方块 🧽");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + priceText);
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            meta.setLore(lore);
            luckyBlock.setItemMeta(meta);
        }

        plugin.getLogger().info("幸运方块已创建，使用海绵材质");
        return luckyBlock;
    }

    /**
     * 创建可叠加的幸运方块物品（使用湿海绵，确保数据组件一致）
     */
    private ItemStack createStackableLuckyBlockItem() {
        ItemStack luckyBlock = new ItemStack(Material.WET_SPONGE, 1);
        ItemMeta meta = luckyBlock.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(ChatColor.YELLOW + "✦ " + ChatColor.GREEN + "幸运方块" + ChatColor.YELLOW + " ✦");

            // 添加简化的lore（用于叠加的物品）
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "放置在地上然后破坏获得随机奖励！");
            lore.add(ChatColor.YELLOW + "运气也是实力的一部分");
            lore.add(ChatColor.DARK_GRAY + "右键放置，左键破坏");
            lore.add("");
            lore.add(ChatColor.GOLD + "⚡ 神秘的幸运方块 ⚡");

            meta.setLore(lore);

            // 设置自定义纹理
            try {
                setSkullTexture((org.bukkit.inventory.meta.SkullMeta) meta,
                        "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvODc5ZTU0Y2JlODc4NjdkMTRiMmZiZGYzZjE4NzA4OTQzNTIwNDhkZmVjZDk2Mjg0NmRlYTg5M2IyMTU0Yzg1In19fQ==");
                plugin.getLogger().info("幸运方块自定义纹理设置成功！");
            } catch (Exception e) {
                plugin.getLogger().warning("设置纹理失败，使用默认玩家头颅: " + e.getMessage());
            }

            luckyBlock.setItemMeta(meta);

            // 验证物品是否正确创建
            if (luckyBlock.getItemMeta() != null && luckyBlock.getItemMeta().hasDisplayName()) {
                plugin.getLogger().info("自定义头颅幸运方块创建成功，显示名称: " +
                        ChatColor.stripColor(luckyBlock.getItemMeta().getDisplayName()));
            } else {
                plugin.getLogger().warning("自定义头颅幸运方块创建可能存在问题");
            }
        }

        return luckyBlock;
    }

    /**
     * 检查物品是否是幸运方块
     */
    private boolean isLuckyBlockItem(ItemStack item) {
        if (item == null || item.getType() != Material.PLAYER_HEAD) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }

        String displayName = ChatColor.stripColor(meta.getDisplayName());
        return displayName.contains("幸运方块");
    }

    /**
     * 获取幸运方块价格信息
     * 
     * @return [价格文本, 货币类型]
     */
    private String[] getLuckyBlockPriceInfo() {
        // 检查配置中的货币类型，按优先级顺序
        if (config.contains("lucky-block.price.gold")) {
            int price = config.getInt("lucky-block.price.gold", 5);
            return new String[] { price + " Gold", "gold" };
        } else if (config.contains("lucky-block.price.iron")) {
            int price = config.getInt("lucky-block.price.iron", 10);
            return new String[] { price + " Iron", "iron" };
        } else if (config.contains("lucky-block.price.diamond")) {
            int price = config.getInt("lucky-block.price.diamond", 2);
            return new String[] { price + " Diamond", "diamond" };
        } else if (config.contains("lucky-block.price.emerald")) {
            int price = config.getInt("lucky-block.price.emerald", 1);
            return new String[] { price + " Emerald", "emerald" };
        } else {
            // 默认使用铁锭
            return new String[] { "10 Iron", "iron" };
        }
    }

    /**
     * 设置幸运方块商店价格
     */
    private void setLuckyBlockPrice(ShopItem shopItem) {
        // 检查配置中的货币类型，按优先级顺序
        if (config.contains("lucky-block.price.gold")) {
            int price = config.getInt("lucky-block.price.gold", 5);
            shopItem.addPriceSpawner(GameAPI.get().getDropTypeById("gold"), price);
            plugin.getLogger().info("幸运方块价格设置为: " + price + " Gold");
        } else if (config.contains("lucky-block.price.iron")) {
            int price = config.getInt("lucky-block.price.iron", 10);
            shopItem.addPriceSpawner(GameAPI.get().getDropTypeById("iron"), price);
            plugin.getLogger().info("幸运方块价格设置为: " + price + " Iron");
        } else if (config.contains("lucky-block.price.diamond")) {
            int price = config.getInt("lucky-block.price.diamond", 2);
            shopItem.addPriceSpawner(GameAPI.get().getDropTypeById("diamond"), price);
            plugin.getLogger().info("幸运方块价格设置为: " + price + " Diamond");
        } else if (config.contains("lucky-block.price.emerald")) {
            int price = config.getInt("lucky-block.price.emerald", 1);
            shopItem.addPriceSpawner(GameAPI.get().getDropTypeById("emerald"), price);
            plugin.getLogger().info("幸运方块价格设置为: " + price + " Emerald");
        } else {
            // 默认使用铁锭
            shopItem.addPriceSpawner(GameAPI.get().getDropTypeById("iron"), 10);
            plugin.getLogger().info("幸运方块价格设置为默认: 10 Iron");
        }
    }

    @Override
    protected void createDefaultConfig() {
        if (config == null)
            return;

        // 基础配置
        config.set("enabled", true);
        config.set("description", "商店仅出售基础方块和装备，其他物品全靠幸运方块获得");

        // 消息配置
        config.set("messages.mode-start", "&a&l幸运降临模式已启动！");
        config.set("messages.mode-description", "&7商店仅出售基础方块和装备，其他物品全靠幸运方块获得！");
        config.set("messages.lucky-block-use", "&a&l你破坏了幸运方块！");
        config.set("messages.lucky-block-reward", "&e&l恭喜获得: &f{reward}");
        config.set("messages.lucky-block-placed", "&a&l幸运方块已放置！破坏它来获得奖励！");

        // 幸运方块事件配置 - 新的事件系统
        // 好事件 (35%)
        config.set("lucky-events.good.chance", 35);
        config.set("lucky-events.good.events", Arrays.asList(
                "ITEM_REWARD",
                "BUFF_EFFECT",
                "RESOURCE_BONUS"));

        // 坏事件 (60%)
        config.set("lucky-events.bad.chance", 60);
        config.set("lucky-events.bad.events", Arrays.asList(
                "EXPLOSION",
                "DEBUFF_EFFECT",
                "MONSTER_SPAWN"));

        // 中立事件 (5%)
        config.set("lucky-events.neutral.chance", 5);
        config.set("lucky-events.neutral.events", Arrays.asList(
                "TELEPORT",
                "WEATHER_CHANGE",
                "SOUND_EFFECT"));

        // 旧的配置已移除，现在使用新的配置系统

        // 创建可配置的商店系统
        createConfigurableShopSystem();

        saveConfig();
    }

    /**
     * 创建可配置的商店系统配置
     */
    private void createConfigurableShopSystem() {
        // 方块页面配置
        createBlockPageConfig();

        // 武器页面配置
        createWeaponPageConfig();

        // 盔甲页面配置
        createArmorPageConfig();

        // 剑页面配置
        createToolPageConfig();

        // 镐子页面配置
        createPickaxePageConfig();

        // 食物页面配置
        createFoodPageConfig();

        // 特殊物品页面配置
        createSpecialPageConfig();
    }

    /**
     * 创建方块页面配置
     */
    private void createBlockPageConfig() {
        String basePath = "shop.blocks.items";

        // 羊毛配置
        config.set(basePath + ".wool.material", "WHITE_WOOL");
        config.set(basePath + ".wool.display-name", "&f羊毛");
        config.set(basePath + ".wool.lore", Arrays.asList(
                "&7队伍颜色羊毛",
                "&7用于建造和防御",
                "&b购买时会给予你队伍颜色的羊毛"));
        config.set(basePath + ".wool.price.currency", "iron");
        config.set(basePath + ".wool.price.amount", 4);
        config.set(basePath + ".wool.product.amount", 16);
        config.set(basePath + ".wool.team-colored", true);
        config.set(basePath + ".wool.slot", 27);

        // 原木配置
        config.set(basePath + ".wood.material", "OAK_WOOD");
        config.set(basePath + ".wood.display-name", "&f原木");
        config.set(basePath + ".wood.lore", Arrays.asList(
                "&7基础建筑材料",
                "&7坚固且实用"));
        config.set(basePath + ".wood.price.currency", "iron");
        config.set(basePath + ".wood.price.amount", 4);
        config.set(basePath + ".wood.product.amount", 16);
        config.set(basePath + ".wood.slot", 28);

        // 硬化粘土配置
        config.set(basePath + ".terracotta.material", "WHITE_TERRACOTTA");
        config.set(basePath + ".terracotta.display-name", "&f硬化粘土");
        config.set(basePath + ".terracotta.lore", Arrays.asList(
                "&7队伍颜色硬化粘土",
                "&7爆炸抗性强，适合防御",
                "&b购买时会给予你队伍颜色的陶瓦"));
        config.set(basePath + ".terracotta.price.currency", "iron");
        config.set(basePath + ".terracotta.price.amount", 12);
        config.set(basePath + ".terracotta.product.amount", 16);
        config.set(basePath + ".terracotta.team-colored", true);
        config.set(basePath + ".terracotta.slot", 29);

        // 防爆玻璃配置
        config.set(basePath + ".glass.material", "WHITE_STAINED_GLASS");
        config.set(basePath + ".glass.display-name", "&f防爆玻璃");
        config.set(basePath + ".glass.lore", Arrays.asList(
                "&7队伍颜色防爆玻璃",
                "&7透明且坚固，适合观察",
                "&b购买时会给予你队伍颜色的玻璃"));
        config.set(basePath + ".glass.price.currency", "iron");
        config.set(basePath + ".glass.price.amount", 12);
        config.set(basePath + ".glass.product.amount", 16);
        config.set(basePath + ".glass.team-colored", true);
        config.set(basePath + ".glass.slot", 30);

        // 黑曜石配置
        config.set(basePath + ".obsidian.material", "OBSIDIAN");
        config.set(basePath + ".obsidian.display-name", "&5黑曜石");
        config.set(basePath + ".obsidian.lore", Arrays.asList(
                "&7最坚固的防御方块",
                "&7极高的爆炸抗性",
                "&c挖掘速度极慢"));
        config.set(basePath + ".obsidian.price.currency", "emerald");
        config.set(basePath + ".obsidian.price.amount", 4);
        config.set(basePath + ".obsidian.product.amount", 4);

        // 蓝冰配置
        config.set(basePath + ".blue_ice.material", "BLUE_ICE");
        config.set(basePath + ".blue_ice.display-name", "&b蓝冰");
        config.set(basePath + ".blue_ice.lore", Arrays.asList(
                "&7超级滑溜的冰块",
                "&7移动速度极快",
                "&b永不融化"));
        config.set(basePath + ".blue_ice.price.currency", "emerald");
        config.set(basePath + ".blue_ice.price.amount", 6);
        config.set(basePath + ".blue_ice.product.amount", 4);

        // 粘液块配置
        config.set(basePath + ".slime.material", "SLIME_BLOCK");
        config.set(basePath + ".slime.display-name", "&a粘液块");
        config.set(basePath + ".slime.lore", Arrays.asList(
                "&7弹性方块",
                "&7可以弹跳和减少摔落伤害",
                "&a适合制作陷阱"));
        config.set(basePath + ".slime.price.currency", "emerald");
        config.set(basePath + ".slime.price.amount", 2);
        config.set(basePath + ".slime.product.amount", 4);

        // 箱子配置
        config.set(basePath + ".chest.material", "CHEST");
        config.set(basePath + ".chest.display-name", "&e箱子");
        config.set(basePath + ".chest.lore", Arrays.asList(
                "&7存储物品的容器",
                "&727个存储槽位",
                "&e团队共享存储"));
        config.set(basePath + ".chest.price.currency", "iron");
        config.set(basePath + ".chest.price.amount", 2);
        config.set(basePath + ".chest.product.amount", 1);

        // 末影箱配置
        config.set(basePath + ".ender_chest.material", "ENDER_CHEST");
        config.set(basePath + ".ender_chest.display-name", "&5末影箱");
        config.set(basePath + ".ender_chest.lore", Arrays.asList(
                "&7个人专属存储",
                "&727个存储槽位",
                "&5只有你能访问",
                "&e全地图同步"));
        config.set(basePath + ".ender_chest.price.currency", "emerald");
        config.set(basePath + ".ender_chest.price.amount", 1);
        config.set(basePath + ".ender_chest.product.amount", 1);

        // 蜘蛛网配置
        config.set(basePath + ".cobweb.material", "COBWEB");
        config.set(basePath + ".cobweb.display-name", "&f蜘蛛网");
        config.set(basePath + ".cobweb.lore", Arrays.asList(
                "&7减速陷阱方块",
                "&7大幅降低移动速度",
                "&c适合制作陷阱",
                "&e可以阻挡敌人"));
        config.set(basePath + ".cobweb.price.currency", "emerald");
        config.set(basePath + ".cobweb.price.amount", 2);
        config.set(basePath + ".cobweb.product.amount", 1);

        // 末地石配置
        config.set(basePath + ".end_stone.material", "END_STONE");
        config.set(basePath + ".end_stone.display-name", "&e末地石");
        config.set(basePath + ".end_stone.lore", Arrays.asList(
                "&7来自末地的神秘石头",
                "&7具有很高的爆炸抗性",
                "&7适合建造坚固防御"));
        config.set(basePath + ".end_stone.price.currency", "emerald");
        config.set(basePath + ".end_stone.price.amount", 7);
        config.set(basePath + ".end_stone.product.amount", 12);
    }

    /**
     * 创建弓页面配置（仅箭）
     */
    private void createWeaponPageConfig() {
        String basePath = "shop.weapons.items";

        // 箭配置
        config.set(basePath + ".arrow.material", "ARROW");
        config.set(basePath + ".arrow.display-name", "&f箭");
        config.set(basePath + ".arrow.lore", Arrays.asList(
                "&7弓的弹药",
                "&7必备的远程武器补给"));
        config.set(basePath + ".arrow.price.currency", "gold");
        config.set(basePath + ".arrow.price.amount", 2);
        config.set(basePath + ".arrow.product.amount", 8);
        config.set(basePath + ".arrow.slot", 27);
    }

    /**
     * 创建盔甲页面配置
     */
    private void createArmorPageConfig() {
        String basePath = "shop.armor.items";

        // 皮革甲配置
        config.set(basePath + ".leather_armor.material", "LEATHER_BOOTS");
        config.set(basePath + ".leather_armor.display-name", "&f皮革甲");
        config.set(basePath + ".leather_armor.lore", Arrays.asList(
                "&7基础防护套装",
                "&7提供基本保护",
                "&7包含全套皮革装备"));
        config.set(basePath + ".leather_armor.price.currency", "iron");
        config.set(basePath + ".leather_armor.price.amount", 35);
        config.set(basePath + ".leather_armor.buy-group.name", "Armor");
        config.set(basePath + ".leather_armor.buy-group.level", 0);
        config.set(basePath + ".leather_armor.keep-on-death", true);
        config.set(basePath + ".leather_armor.products", Arrays.asList(
                "LEATHER_BOOTS:1:auto-wear:unbreakable",
                "LEATHER_LEGGINGS:1:auto-wear:unbreakable",
                "LEATHER_CHESTPLATE:1:auto-wear:unbreakable",
                "LEATHER_HELMET:1:auto-wear:unbreakable"));

        // 锁链甲配置
        config.set(basePath + ".chainmail_armor.material", "CHAINMAIL_BOOTS");
        config.set(basePath + ".chainmail_armor.display-name", "&f锁链甲");
        config.set(basePath + ".chainmail_armor.lore", Arrays.asList(
                "&7中级防护套装",
                "&7提供中等保护",
                "&7包含锁链靴子和护腿"));
        config.set(basePath + ".chainmail_armor.price.currency", "iron");
        config.set(basePath + ".chainmail_armor.price.amount", 35);
        config.set(basePath + ".chainmail_armor.buy-group.name", "Armor");
        config.set(basePath + ".chainmail_armor.buy-group.level", 1);
        config.set(basePath + ".chainmail_armor.keep-on-death", true);
        config.set(basePath + ".chainmail_armor.products", Arrays.asList(
                "CHAINMAIL_BOOTS:1:auto-wear:unbreakable",
                "CHAINMAIL_LEGGINGS:1:auto-wear:unbreakable",
                "LEATHER_CHESTPLATE:1:auto-wear:unbreakable",
                "LEATHER_HELMET:1:auto-wear:unbreakable"));
        config.set(basePath + ".chainmail_armor.slot", 27);

        // 铁甲配置
        config.set(basePath + ".iron_armor.material", "IRON_BOOTS");
        config.set(basePath + ".iron_armor.display-name", "&f铁甲");
        config.set(basePath + ".iron_armor.lore", Arrays.asList(
                "&7高级防护套装",
                "&7提供强力保护",
                "&7包含铁靴子和护腿"));
        config.set(basePath + ".iron_armor.price.currency", "gold");
        config.set(basePath + ".iron_armor.price.amount", 12);
        config.set(basePath + ".iron_armor.buy-group.name", "Armor");
        config.set(basePath + ".iron_armor.buy-group.level", 2);
        config.set(basePath + ".iron_armor.keep-on-death", true);
        config.set(basePath + ".iron_armor.products", Arrays.asList(
                "IRON_BOOTS:1:auto-wear:unbreakable",
                "IRON_LEGGINGS:1:auto-wear:unbreakable",
                "LEATHER_CHESTPLATE:1:auto-wear:unbreakable",
                "LEATHER_HELMET:1:auto-wear:unbreakable"));
        config.set(basePath + ".iron_armor.slot", 28);

        // 钻石甲配置
        config.set(basePath + ".diamond_armor.material", "DIAMOND_BOOTS");
        config.set(basePath + ".diamond_armor.display-name", "&b钻石甲");
        config.set(basePath + ".diamond_armor.lore", Arrays.asList(
                "&7顶级防护套装",
                "&7提供最强保护",
                "&7包含钻石靴子和护腿"));
        config.set(basePath + ".diamond_armor.price.currency", "emerald");
        config.set(basePath + ".diamond_armor.price.amount", 8);
        config.set(basePath + ".diamond_armor.buy-group.name", "Armor");
        config.set(basePath + ".diamond_armor.buy-group.level", 3);
        config.set(basePath + ".diamond_armor.keep-on-death", true);
        config.set(basePath + ".diamond_armor.products", Arrays.asList(
                "DIAMOND_BOOTS:1:auto-wear:unbreakable",
                "DIAMOND_LEGGINGS:1:auto-wear:unbreakable",
                "LEATHER_CHESTPLATE:1:auto-wear:unbreakable",
                "LEATHER_HELMET:1:auto-wear:unbreakable"));
        config.set(basePath + ".diamond_armor.slot", 29);
    }

    /**
     * 创建工具页面配置
     */
    private void createToolPageConfig() {
        String basePath = "shop.tools.items";

        // 石剑配置
        config.set(basePath + ".stone_sword.material", "STONE_SWORD");
        config.set(basePath + ".stone_sword.display-name", "&f石剑");
        config.set(basePath + ".stone_sword.lore", Arrays.asList(
                "&7基础近战武器",
                "&7攻击力: 5"));
        config.set(basePath + ".stone_sword.price.currency", "iron");
        config.set(basePath + ".stone_sword.price.amount", 10);
        config.set(basePath + ".stone_sword.product.amount", 1);
        config.set(basePath + ".stone_sword.unbreakable", true);
        config.set(basePath + ".stone_sword.slot", 27);

        // 铁剑配置
        config.set(basePath + ".iron_sword.material", "IRON_SWORD");
        config.set(basePath + ".iron_sword.display-name", "&f铁剑");
        config.set(basePath + ".iron_sword.lore", Arrays.asList(
                "&7中级近战武器",
                "&7攻击力: 6"));
        config.set(basePath + ".iron_sword.price.currency", "gold");
        config.set(basePath + ".iron_sword.price.amount", 7);
        config.set(basePath + ".iron_sword.product.amount", 1);
        config.set(basePath + ".iron_sword.unbreakable", true);
        config.set(basePath + ".iron_sword.slot", 28);

    }

    /**
     * 创建镐子页面配置（木斧、木镐、剪刀）
     */
    private void createPickaxePageConfig() {
        String basePath = "shop.pickaxe.items";

        // 木斧配置
        config.set(basePath + ".wooden_axe.material", "WOODEN_AXE");
        config.set(basePath + ".wooden_axe.display-name", "&f木斧");
        config.set(basePath + ".wooden_axe.lore", Arrays.asList(
                "&7基础砍伐工具",
                "&7用于快速破坏木质方块"));
        config.set(basePath + ".wooden_axe.price.currency", "iron");
        config.set(basePath + ".wooden_axe.price.amount", 10);
        config.set(basePath + ".wooden_axe.product.amount", 1);
        config.set(basePath + ".wooden_axe.unbreakable", true);
        config.set(basePath + ".wooden_axe.slot", 27);

        // 木镐配置
        config.set(basePath + ".wooden_pickaxe.material", "WOODEN_PICKAXE");
        config.set(basePath + ".wooden_pickaxe.display-name", "&f木镐");
        config.set(basePath + ".wooden_pickaxe.lore", Arrays.asList(
                "&7基础挖掘工具",
                "&7用于快速破坏石质方块"));
        config.set(basePath + ".wooden_pickaxe.price.currency", "iron");
        config.set(basePath + ".wooden_pickaxe.price.amount", 10);
        config.set(basePath + ".wooden_pickaxe.product.amount", 1);
        config.set(basePath + ".wooden_pickaxe.unbreakable", true);
        config.set(basePath + ".wooden_pickaxe.slot", 28);

        // 剪刀配置
        config.set(basePath + ".shears.material", "SHEARS");
        config.set(basePath + ".shears.display-name", "&f剪刀");
        config.set(basePath + ".shears.lore", Arrays.asList(
                "&7多功能工具",
                "&7用于快速破坏羊毛和叶子",
                "&7也可以剪羊毛"));
        config.set(basePath + ".shears.price.currency", "iron");
        config.set(basePath + ".shears.price.amount", 20);
        config.set(basePath + ".shears.product.amount", 1);
        config.set(basePath + ".shears.unbreakable", true);
        config.set(basePath + ".shears.slot", 29);
    }

    /**
     * 创建食物页面配置
     */
    private void createFoodPageConfig() {
        String basePath = "shop.food.items";

        // 熟猪排配置
        config.set(basePath + ".cooked_porkchop.material", "COOKED_PORKCHOP");
        config.set(basePath + ".cooked_porkchop.display-name", "&f熟猪排");
        config.set(basePath + ".cooked_porkchop.lore", Arrays.asList(
                "&7美味的食物",
                "&7恢复饥饿值和生命值",
                "&7战斗必备补给"));
        config.set(basePath + ".cooked_porkchop.price.currency", "iron");
        config.set(basePath + ".cooked_porkchop.price.amount", 4);
        config.set(basePath + ".cooked_porkchop.product.amount", 1);
        config.set(basePath + ".cooked_porkchop.slot", 27);

        // 蛋糕配置
        config.set(basePath + ".cake.material", "CAKE");
        config.set(basePath + ".cake.display-name", "&e蛋糕");
        config.set(basePath + ".cake.lore", Arrays.asList(
                "&7甜美的蛋糕",
                "&7可以多次食用",
                "&7恢复大量饥饿值"));
        config.set(basePath + ".cake.price.currency", "gold");
        config.set(basePath + ".cake.price.amount", 3);
        config.set(basePath + ".cake.product.amount", 1);
        config.set(basePath + ".cake.slot", 28);

        // 金苹果配置
        config.set(basePath + ".golden_apple.material", "GOLDEN_APPLE");
        config.set(basePath + ".golden_apple.display-name", "&6金苹果");
        config.set(basePath + ".golden_apple.lore", Arrays.asList(
                "&7神奇的金苹果",
                "&7恢复生命值并提供增益效果",
                "&6吸收 II (0:10)",
                "&6再生 II (0:05)"));
        config.set(basePath + ".golden_apple.price.currency", "emerald");
        config.set(basePath + ".golden_apple.price.amount", 1);
        config.set(basePath + ".golden_apple.product.amount", 1);
        config.set(basePath + ".golden_apple.slot", 29);
    }

    /**
     * 创建特殊物品页面配置
     */
    private void createSpecialPageConfig() {
        String basePath = "special.items";

        // 强制创建幸运方块配置，确保路径存在
        config.set(basePath + ".lucky_block.material", "WET_SPONGE");
        config.set(basePath + ".lucky_block.display-name", "&e✦ &a幸运方块 &e✦");
        config.set(basePath + ".lucky_block.lore", Arrays.asList(
                "&7放置在地上然后破坏获得随机奖励！",
                "&e运气也是实力的一部分",
                "&8右键放置，左键破坏",
                "",
                "&e🧽 神秘的湿海绵方块 🧽"));
        config.set(basePath + ".lucky_block.price.currency", "gold");
        config.set(basePath + ".lucky_block.price.amount", 5);
        config.set(basePath + ".lucky_block.product.amount", 1);
        config.set(basePath + ".lucky_block.slot", 27);

        plugin.getLogger().info("已创建特殊物品配置: " + basePath);
    }

    @Override
    public void onGameStart(Arena arena) {
        // 标记竞技场为活跃状态
        activeArenas.add(arena);

        // 向所有玩家发送模式开始消息
        String startMessage = config.getString("messages.mode-start", "&a&l幸运降临模式已启动！");
        String description = config.getString("messages.mode-description", "&7商店仅出售基础方块和装备，其他物品全靠幸运方块获得！");

        for (Player player : arena.getPlayers()) {
            player.sendMessage(translateColors(startMessage));
            player.sendMessage(translateColors(description));
        }

        // 添加自定义商店物品到全局商店
        addCustomItemsToGlobalShop();
    }

    @Override
    public void onGameEnd(Arena arena) {
        // 移除竞技场的活跃状态
        activeArenas.remove(arena);

        // 清理该竞技场的幸运方块位置记录
        if (arena.getGameWorld() != null) {
            placedLuckyBlocks.removeIf(location -> location.getWorld() != null &&
                    location.getWorld().equals(arena.getGameWorld()));
        }
    }

    /**
     * 颜色代码转换
     */
    private String translateColors(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 添加幸运方块到全局商店（普通物品）
     */
    private void addLuckyBlockToGlobalShop(ShopPage page) {
        if (page == null)
            return;

        // 检查是否已经有幸运方块了
        for (ShopItem item : page.getItems()) {
            if (item.getName().contains("幸运方块")) {
                return; // 已经存在幸运方块，不重复添加
            }
        }

        // 使用湿海绵作为商店图标
        ItemStack shopIcon = createLuckyBlockItem();

        // 创建幸运方块商店物品
        ShopItem luckyBlockShopItem = page.addItem("幸运方块", shopIcon);

        // 确保图标正确设置
        luckyBlockShopItem.setIcon(shopIcon);

        // 设置价格 - 动态读取配置的货币类型
        setLuckyBlockPrice(luckyBlockShopItem);

        // 设置slot位置
        int slot = config.getInt("lucky-block.slot", 4);
        luckyBlockShopItem.setForceSlot(slot);
        plugin.getLogger().info("幸运方块设置到slot位置: " + slot);

        // 添加自定义头颅产品（可叠加）
        ItemStack headProduct = createStackableLuckyBlockItem();

        // 验证产品物品是否正确创建
        if (headProduct != null && headProduct.getType() == Material.WET_SPONGE &&
                headProduct.getItemMeta() != null && headProduct.getItemMeta().hasDisplayName()) {
            luckyBlockShopItem.addProductItem(headProduct, 1);
            plugin.getLogger().info("幸运方块已添加到全局商店（普通物品）！产品验证通过");
        } else {
            plugin.getLogger().warning("幸运方块产品创建失败，商店物品可能无法正常工作");
            // 仍然添加，但记录警告
            luckyBlockShopItem.addProductItem(headProduct, 1);
        }
    }

    /**
     * 添加方块物品到全局商店（使用配置系统）
     */
    private void addBlockItemsToGlobalShop(ShopPage page) {
        if (page == null)
            return;

        // 使用配置系统加载方块页面物品
        loadConfigurableShopItems(page, "shop.blocks.items");
    }

    /**
     * 添加全局羊毛到商店（使用白色羊毛，购买时会根据队伍颜色给予对应颜色）
     */
    private void addGlobalWool(ShopPage page) {
        // 从配置获取价格和数量
        int woolPrice = config.getInt("team-items.wool.price.iron", 4);
        int woolAmount = config.getInt("team-items.wool.amount", 16);

        // 创建商店图标（使用白色羊毛）
        ItemStack woolIcon = new ItemStack(Material.WHITE_WOOL, 1);
        ItemMeta iconMeta = woolIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.WHITE + "羊毛");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "队伍颜色羊毛");
            lore.add(ChatColor.GRAY + "用于建造和防御");
            lore.add(ChatColor.AQUA + "购买时会给予你队伍颜色的羊毛");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + woolPrice + " Iron");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!"); // 默认显示可购买

            iconMeta.setLore(lore);
            woolIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem woolShopItem = page.addItem("羊毛", woolIcon);

        // 确保图标正确设置
        woolShopItem.setIcon(woolIcon);

        // 设置价格
        woolShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("iron"), woolPrice);

        // 添加白色羊毛作为默认产品，实际产品在购买事件中替换
        ItemStack defaultWool = new ItemStack(Material.WHITE_WOOL, woolAmount);
        woolShopItem.addProductItem(defaultWool, woolAmount);

        plugin.getLogger().info("队伍羊毛已添加到全局商店！");
    }

    /**
     * 添加全局原木到商店
     */
    private void addGlobalWood(ShopPage page) {
        // 从配置获取价格和数量
        int woodPrice = config.getInt("team-items.wood.price.iron", 4);
        int woodAmount = config.getInt("team-items.wood.amount", 16);

        // 创建商店图标（单个原木用于显示）
        ItemStack woodIcon = new ItemStack(Material.OAK_WOOD, 1);
        ItemMeta iconMeta = woodIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.WHITE + "原木");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "基础建筑材料");
            lore.add(ChatColor.GRAY + "坚固且实用");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + woodPrice + " Iron");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            woodIcon.setItemMeta(iconMeta);
        }

        // 创建产品物品（实际给予的数量）
        ItemStack woodProduct = new ItemStack(Material.OAK_WOOD, woodAmount);

        // 创建商店物品，使用单个原木作为图标
        ShopItem woodShopItem = page.addItem("原木", woodIcon);

        // 确保图标正确设置
        woodShopItem.setIcon(woodIcon);

        // 设置价格
        woodShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("iron"), woodPrice);

        // 设置产品
        woodShopItem.addProductItem(woodProduct, woodAmount);

        plugin.getLogger().info("原木已添加到全局商店！");
    }

    /**
     * 添加全局硬化粘土（陶瓦）到商店（使用白色陶瓦，购买时会根据队伍颜色给予对应颜色）
     */
    private void addGlobalTerracotta(ShopPage page) {
        // 从配置获取价格和数量
        int terracottaPrice = config.getInt("team-items.terracotta.price.iron", 12);
        int terracottaAmount = config.getInt("team-items.terracotta.amount", 16);

        // 创建商店图标（使用白色陶瓦）
        ItemStack terracottaIcon = new ItemStack(Material.WHITE_TERRACOTTA, 1);
        ItemMeta iconMeta = terracottaIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.WHITE + "硬化粘土");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "队伍颜色硬化粘土");
            lore.add(ChatColor.GRAY + "爆炸抗性强，适合防御");
            lore.add(ChatColor.AQUA + "购买时会给予你队伍颜色的陶瓦");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + terracottaPrice + " Iron");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            terracottaIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem terracottaShopItem = page.addItem("硬化粘土", terracottaIcon);

        // 确保图标正确设置
        terracottaShopItem.setIcon(terracottaIcon);

        // 设置价格
        terracottaShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("iron"), terracottaPrice);

        // 添加白色陶瓦作为默认产品，实际产品在购买事件中替换
        ItemStack defaultTerracotta = new ItemStack(Material.WHITE_TERRACOTTA, terracottaAmount);
        terracottaShopItem.addProductItem(defaultTerracotta, terracottaAmount);

        plugin.getLogger().info("队伍硬化粘土已添加到全局商店！");
    }

    /**
     * 添加全局防爆玻璃到商店（使用白色玻璃，购买时会根据队伍颜色给予对应颜色）
     */
    private void addGlobalGlass(ShopPage page) {
        // 从配置获取价格和数量
        int glassPrice = config.getInt("team-items.glass.price.iron", 12);
        int glassAmount = config.getInt("team-items.glass.amount", 16);

        // 创建商店图标（使用白色玻璃）
        ItemStack glassIcon = new ItemStack(Material.WHITE_STAINED_GLASS, 1);
        ItemMeta iconMeta = glassIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.WHITE + "防爆玻璃");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "队伍颜色防爆玻璃");
            lore.add(ChatColor.GRAY + "透明且坚固，适合观察");
            lore.add(ChatColor.AQUA + "购买时会给予你队伍颜色的玻璃");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + glassPrice + " Iron");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            glassIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem glassShopItem = page.addItem("防爆玻璃", glassIcon);

        // 确保图标正确设置
        glassShopItem.setIcon(glassIcon);

        // 设置价格
        glassShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("iron"), glassPrice);

        // 添加白色玻璃作为默认产品，实际产品在购买事件中替换
        ItemStack defaultGlass = new ItemStack(Material.WHITE_STAINED_GLASS, glassAmount);
        glassShopItem.addProductItem(defaultGlass, glassAmount);

        plugin.getLogger().info("队伍防爆玻璃已添加到全局商店！");
    }

    /**
     * 添加黑曜石到商店
     */
    private void addGlobalObsidian(ShopPage page) {
        // 从配置获取价格和数量
        int obsidianPrice = config.getInt("block-items.obsidian.price.emerald", 4);
        int obsidianAmount = config.getInt("block-items.obsidian.amount", 4);

        // 创建商店图标
        ItemStack obsidianIcon = new ItemStack(Material.OBSIDIAN, 1);
        ItemMeta iconMeta = obsidianIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.DARK_PURPLE + "黑曜石");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "最坚固的防御方块");
            lore.add(ChatColor.GRAY + "极高的爆炸抗性");
            lore.add(ChatColor.RED + "挖掘速度极慢");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + obsidianPrice + " Emerald");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            obsidianIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem obsidianShopItem = page.addItem("黑曜石", obsidianIcon);

        // 确保图标正确设置
        obsidianShopItem.setIcon(obsidianIcon);

        // 设置价格
        obsidianShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("emerald"), obsidianPrice);

        // 设置产品
        ItemStack obsidianProduct = new ItemStack(Material.OBSIDIAN, obsidianAmount);
        obsidianShopItem.addProductItem(obsidianProduct, obsidianAmount);

        plugin.getLogger().info("黑曜石已添加到全局商店！");
    }

    /**
     * 添加蓝冰到商店
     */
    private void addGlobalBlueIce(ShopPage page) {
        // 从配置获取价格和数量
        int blueIcePrice = config.getInt("block-items.blue-ice.price.emerald", 6);
        int blueIceAmount = config.getInt("block-items.blue-ice.amount", 4);

        // 创建商店图标
        ItemStack blueIceIcon = new ItemStack(Material.BLUE_ICE, 1);
        ItemMeta iconMeta = blueIceIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.AQUA + "蓝冰");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "超级滑溜的冰块");
            lore.add(ChatColor.GRAY + "移动速度极快");
            lore.add(ChatColor.AQUA + "永不融化");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + blueIcePrice + " Emerald");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            blueIceIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem blueIceShopItem = page.addItem("蓝冰", blueIceIcon);

        // 确保图标正确设置
        blueIceShopItem.setIcon(blueIceIcon);

        // 设置价格
        blueIceShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("emerald"), blueIcePrice);

        // 设置产品
        ItemStack blueIceProduct = new ItemStack(Material.BLUE_ICE, blueIceAmount);
        blueIceShopItem.addProductItem(blueIceProduct, blueIceAmount);

        plugin.getLogger().info("蓝冰已添加到全局商店！");
    }

    /**
     * 添加粘液块到商店
     */
    private void addGlobalSlime(ShopPage page) {
        // 从配置获取价格和数量
        int slimePrice = config.getInt("block-items.slime.price.emerald", 2);
        int slimeAmount = config.getInt("block-items.slime.amount", 4);

        // 创建商店图标
        ItemStack slimeIcon = new ItemStack(Material.SLIME_BLOCK, 1);
        ItemMeta iconMeta = slimeIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.GREEN + "粘液块");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "弹性方块");
            lore.add(ChatColor.GRAY + "可以弹跳和减少摔落伤害");
            lore.add(ChatColor.GREEN + "适合制作陷阱");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + slimePrice + " Emerald");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            slimeIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem slimeShopItem = page.addItem("粘液块", slimeIcon);

        // 确保图标正确设置
        slimeShopItem.setIcon(slimeIcon);

        // 设置价格
        slimeShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("emerald"), slimePrice);

        // 设置产品
        ItemStack slimeProduct = new ItemStack(Material.SLIME_BLOCK, slimeAmount);
        slimeShopItem.addProductItem(slimeProduct, slimeAmount);

        plugin.getLogger().info("粘液块已添加到全局商店！");
    }

    /**
     * 添加箱子到商店
     */
    private void addGlobalChest(ShopPage page) {
        // 从配置获取价格和数量
        int chestPrice = config.getInt("block-items.chest.price.iron", 2);
        int chestAmount = config.getInt("block-items.chest.amount", 1);

        // 创建商店图标
        ItemStack chestIcon = new ItemStack(Material.CHEST, 1);
        ItemMeta iconMeta = chestIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.YELLOW + "箱子");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "存储物品的容器");
            lore.add(ChatColor.GRAY + "27个存储槽位");
            lore.add(ChatColor.YELLOW + "团队共享存储");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + chestPrice + " Iron");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            chestIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem chestShopItem = page.addItem("箱子", chestIcon);

        // 确保图标正确设置
        chestShopItem.setIcon(chestIcon);

        // 设置价格
        chestShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("iron"), chestPrice);

        // 设置产品
        ItemStack chestProduct = new ItemStack(Material.CHEST, chestAmount);
        chestShopItem.addProductItem(chestProduct, chestAmount);

        plugin.getLogger().info("箱子已添加到全局商店！");
    }

    /**
     * 添加末影箱到商店
     */
    private void addGlobalEnderChest(ShopPage page) {
        // 从配置获取价格和数量
        int enderChestPrice = config.getInt("block-items.ender-chest.price.emerald", 1);
        int enderChestAmount = config.getInt("block-items.ender-chest.amount", 1);

        // 创建商店图标
        ItemStack enderChestIcon = new ItemStack(Material.ENDER_CHEST, 1);
        ItemMeta iconMeta = enderChestIcon.getItemMeta();
        if (iconMeta != null) {
            iconMeta.setDisplayName(ChatColor.DARK_PURPLE + "末影箱");

            // 添加完整的lore
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "个人专属存储");
            lore.add(ChatColor.GRAY + "27个存储槽位");
            lore.add(ChatColor.DARK_PURPLE + "只有你能访问");
            lore.add(ChatColor.YELLOW + "全地图同步");
            lore.add("");
            lore.add(ChatColor.GRAY + "花费: " + enderChestPrice + " Emerald");
            lore.add("");
            lore.add(ChatColor.AQUA + "Shift点击以添加到快速购买栏");
            lore.add(ChatColor.LIGHT_PURPLE + "点击购买!");

            iconMeta.setLore(lore);
            enderChestIcon.setItemMeta(iconMeta);
        }

        // 创建商店物品
        ShopItem enderChestShopItem = page.addItem("末影箱", enderChestIcon);

        // 确保图标正确设置
        enderChestShopItem.setIcon(enderChestIcon);

        // 设置价格
        enderChestShopItem.addPriceSpawner(GameAPI.get().getDropTypeById("emerald"), enderChestPrice);

        // 设置产品
        ItemStack enderChestProduct = new ItemStack(Material.ENDER_CHEST, enderChestAmount);
        enderChestShopItem.addProductItem(enderChestProduct, enderChestAmount);

        plugin.getLogger().info("末影箱已添加到全局商店！");
    }

    /**
     * 添加武器到弓页面（使用配置系统）
     */
    private void addWeaponsToBowShop(ShopPage page) {
        // 使用配置系统加载武器页面物品
        loadConfigurableShopItems(page, "shop.weapons.items");
        plugin.getLogger().info("武器页面物品已从配置加载！");
    }

    /**
     * 根据染料颜色获取对应的羊毛材质
     */
    private Material getWoolMaterialByColor(DyeColor color) {
        switch (color) {
            case WHITE:
                return Material.WHITE_WOOL;
            case ORANGE:
                return Material.ORANGE_WOOL;
            case MAGENTA:
                return Material.MAGENTA_WOOL;
            case LIGHT_BLUE:
                return Material.LIGHT_BLUE_WOOL;
            case YELLOW:
                return Material.YELLOW_WOOL;
            case LIME:
                return Material.LIME_WOOL;
            case PINK:
                return Material.PINK_WOOL;
            case GRAY:
                return Material.GRAY_WOOL;
            case LIGHT_GRAY:
                return Material.LIGHT_GRAY_WOOL;
            case CYAN:
                return Material.CYAN_WOOL;
            case PURPLE:
                return Material.PURPLE_WOOL;
            case BLUE:
                return Material.BLUE_WOOL;
            case BROWN:
                return Material.BROWN_WOOL;
            case GREEN:
                return Material.GREEN_WOOL;
            case RED:
                return Material.RED_WOOL;
            case BLACK:
                return Material.BLACK_WOOL;
            default:
                return Material.WHITE_WOOL;
        }
    }

    /**
     * 根据染料颜色获取对应的陶瓦材质
     */
    private Material getTerracottaMaterialByColor(DyeColor color) {
        switch (color) {
            case WHITE:
                return Material.WHITE_TERRACOTTA;
            case ORANGE:
                return Material.ORANGE_TERRACOTTA;
            case MAGENTA:
                return Material.MAGENTA_TERRACOTTA;
            case LIGHT_BLUE:
                return Material.LIGHT_BLUE_TERRACOTTA;
            case YELLOW:
                return Material.YELLOW_TERRACOTTA;
            case LIME:
                return Material.LIME_TERRACOTTA;
            case PINK:
                return Material.PINK_TERRACOTTA;
            case GRAY:
                return Material.GRAY_TERRACOTTA;
            case LIGHT_GRAY:
                return Material.LIGHT_GRAY_TERRACOTTA;
            case CYAN:
                return Material.CYAN_TERRACOTTA;
            case PURPLE:
                return Material.PURPLE_TERRACOTTA;
            case BLUE:
                return Material.BLUE_TERRACOTTA;
            case BROWN:
                return Material.BROWN_TERRACOTTA;
            case GREEN:
                return Material.GREEN_TERRACOTTA;
            case RED:
                return Material.RED_TERRACOTTA;
            case BLACK:
                return Material.BLACK_TERRACOTTA;
            default:
                return Material.WHITE_TERRACOTTA;
        }
    }

    /**
     * 根据染料颜色获取对应的玻璃材质
     */
    private Material getGlassMaterialByColor(DyeColor color) {
        switch (color) {
            case WHITE:
                return Material.WHITE_STAINED_GLASS;
            case ORANGE:
                return Material.ORANGE_STAINED_GLASS;
            case MAGENTA:
                return Material.MAGENTA_STAINED_GLASS;
            case LIGHT_BLUE:
                return Material.LIGHT_BLUE_STAINED_GLASS;
            case YELLOW:
                return Material.YELLOW_STAINED_GLASS;
            case LIME:
                return Material.LIME_STAINED_GLASS;
            case PINK:
                return Material.PINK_STAINED_GLASS;
            case GRAY:
                return Material.GRAY_STAINED_GLASS;
            case LIGHT_GRAY:
                return Material.LIGHT_GRAY_STAINED_GLASS;
            case CYAN:
                return Material.CYAN_STAINED_GLASS;
            case PURPLE:
                return Material.PURPLE_STAINED_GLASS;
            case BLUE:
                return Material.BLUE_STAINED_GLASS;
            case BROWN:
                return Material.BROWN_STAINED_GLASS;
            case GREEN:
                return Material.GREEN_STAINED_GLASS;
            case RED:
                return Material.RED_STAINED_GLASS;
            case BLACK:
                return Material.BLACK_STAINED_GLASS;
            default:
                return Material.WHITE_STAINED_GLASS;
        }
    }

    /**
     * 获取颜色的中文名称
     */
    private String getColorName(DyeColor color) {
        switch (color) {
            case WHITE:
                return "白色";
            case ORANGE:
                return "橙色";
            case MAGENTA:
                return "品红色";
            case LIGHT_BLUE:
                return "淡蓝色";
            case YELLOW:
                return "黄色";
            case LIME:
                return "黄绿色";
            case PINK:
                return "粉红色";
            case GRAY:
                return "灰色";
            case LIGHT_GRAY:
                return "淡灰色";
            case CYAN:
                return "青色";
            case PURPLE:
                return "紫色";
            case BLUE:
                return "蓝色";
            case BROWN:
                return "棕色";
            case GREEN:
                return "绿色";
            case RED:
                return "红色";
            case BLACK:
                return "黑色";
            default:
                return "白色";
        }
    }

    /**
     * 添加自定义物品到全局商店
     */
    private void addCustomItemsToGlobalShop() {
        try {
            // 获取所有商店页面
            Collection<ShopPage> pages = GameAPI.get().getShopPages();

            // 调试：打印所有商店页面名称
            plugin.getLogger().info("=== 所有可用的商店页面 ===");
            for (ShopPage page : pages) {
                plugin.getLogger().info("页面名称: '" + page.getName() + "'");
            }
            plugin.getLogger().info("=== 页面列表结束 ===");

            // 添加幸运方块到扩展页面
            boolean foundSpecialPage = false;
            for (ShopPage page : pages) {
                String pageName = page.getName().toLowerCase();
                plugin.getLogger().info("检查页面: '" + pageName + "'");
                if (pageName.contains("扩展") || pageName.contains("special") ||
                        pageName.contains("extension") || pageName.contains("extra")) {
                    plugin.getLogger().info("找到特殊物品页面: " + page.getName());
                    loadConfigurableShopItems(page, "special.items");
                    foundSpecialPage = true;
                    break;
                }
            }

            if (!foundSpecialPage) {
                plugin.getLogger().warning("未找到特殊物品页面！尝试添加到第一个页面");
                if (!pages.isEmpty()) {
                    ShopPage firstPage = pages.iterator().next();
                    plugin.getLogger().info("使用第一个页面: " + firstPage.getName());
                    loadConfigurableShopItems(firstPage, "special.items");
                }
            }

            // 添加方块物品到方块页面
            for (ShopPage page : pages) {
                String pageName = page.getName().toLowerCase();
                if (pageName.contains("方块") || pageName.contains("block") ||
                        pageName.contains("建筑") || pageName.contains("building")) {
                    loadConfigurableShopItems(page, "shop.blocks.items");
                    break;
                }
            }

            // 添加箭到弓页面
            for (ShopPage page : pages) {
                String pageName = page.getName().toLowerCase();
                if (pageName.contains("弓") || pageName.contains("bow") ||
                        pageName.contains("远程") || pageName.contains("ranged")) {
                    loadConfigurableShopItems(page, "shop.weapons.items");
                    break;
                }
            }

            // 添加盔甲到盔甲页面
            for (ShopPage page : pages) {
                String pageName = page.getName().toLowerCase();
                if (pageName.contains("盔甲") || pageName.contains("armor") ||
                        pageName.contains("护甲") || pageName.contains("protection")) {
                    loadConfigurableShopItems(page, "shop.armor.items");
                    break;
                }
            }

            // 添加剑到剑页面
            for (ShopPage page : pages) {
                String pageName = page.getName().toLowerCase();
                if (pageName.contains("剑") || pageName.contains("sword") ||
                        pageName.contains("武器") || pageName.contains("weapon")) {
                    loadConfigurableShopItems(page, "shop.tools.items");
                    break;
                }
            }

            // 添加镐子工具到镐子页面
            for (ShopPage page : pages) {
                String pageName = page.getName().toLowerCase();
                if (pageName.contains("镐") || pageName.contains("pickaxe") ||
                        pageName.contains("工具") || pageName.contains("tool")) {
                    loadConfigurableShopItems(page, "shop.pickaxe.items");
                    break;
                }
            }

            // 添加食物到食物页面
            for (ShopPage page : pages) {
                String pageName = page.getName().toLowerCase();
                if (pageName.contains("食物") || pageName.contains("food") ||
                        pageName.contains("食品") || pageName.contains("吃的")) {
                    loadConfigurableShopItems(page, "shop.food.items");
                    break;
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("添加自定义商店物品失败: " + e.getMessage());
        }
    }

    /**
     * 商店打开事件处理 - 过滤不允许的页面和物品
     */
    @EventHandler
    public void onShopOpen(PlayerOpenShopEvent event) {
        Player player = event.getPlayer();
        Arena arena = GameAPI.get().getArenaByPlayer(player);

        // 只在幸运降临模式的竞技场中生效
        if (arena == null || !activeArenas.contains(arena)) {
            return;
        }

        // 检查当前页面是否被禁止
        ShopPage currentPage = event.getClonedPage();
        if (currentPage != null) {
            String pageName = currentPage.getName();

            // 禁止药水页面和扩展页面（食物页面现在开放）
            if (pageName.equals("%Shop_Page_Potion%") ||
                    pageName.equals("%Shop_Page_Extra%")) {
                event.setCancelled(true);
                player.sendMessage(translateColors("&c&l该页面在幸运降临模式中不可用！"));
                return;
            }
        }

        // 获取当前打开的页面中的所有物品
        List<? extends ShopItem> items = event.getItems();
        if (items == null)
            return;

        List<ShopItem> itemsToRemove = new ArrayList<>();

        for (ShopItem item : items) {
            if (!isItemAllowed(item)) {
                itemsToRemove.add(item);
            }
        }

        // 移除不允许的物品
        for (ShopItem item : itemsToRemove) {
            event.removeShopItem(item);
        }

        // 动态更新购买状态 - 延迟执行确保BedWars完全渲染
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            updatePurchaseStatus(event);
        }, 2L);
    }

    // 移除了动态lore更新方法，现在在创建时就添加完整的lore

    // 移除了updateWoolLore方法，现在在创建时就添加完整的lore

    /**
     * 动态更新购买状态
     */
    private void updatePurchaseStatus(PlayerOpenShopEvent event) {
        Player player = event.getPlayer();
        List<? extends ShopItem> items = event.getItems();

        if (items == null)
            return;

        // 获取玩家的铁锭数量
        int playerIron = getPlayerIronCount(player);
        plugin.getLogger().info("玩家 " + player.getName() + " 打开商店，铁锭数量: " + playerIron);

        for (ShopItem item : items) {
            String itemName = item.getName();

            // 只更新羊毛和木板的购买状态
            if (itemName.contains("羊毛") || itemName.contains("木板")) {
                plugin.getLogger().info("更新物品购买状态: " + itemName);
                updateItemPurchaseStatus(item, player, playerIron);
            }
        }
    }

    /**
     * 更新单个物品的购买状态
     */
    private void updateItemPurchaseStatus(ShopItem item, Player player, int playerIron) {
        ItemStack icon = item.getIcon();
        if (icon == null)
            return;

        ItemMeta meta = icon.getItemMeta();
        if (meta == null)
            return;

        List<String> lore = meta.getLore();
        if (lore == null)
            return;

        // 获取物品价格
        int itemPrice = getItemPrice(item.getName());
        boolean canAfford = playerIron >= itemPrice;

        // 查找并替换购买状态占位符
        boolean foundPlaceholder = false;
        for (int i = 0; i < lore.size(); i++) {
            String loreLine = lore.get(i);
            plugin.getLogger().info("检查lore第" + (i + 1) + "行: " + loreLine);

            if (loreLine.contains("点击购买!") || loreLine.contains("你没有足够的 Iron!")) {
                foundPlaceholder = true;
                if (canAfford) {
                    lore.set(i, ChatColor.LIGHT_PURPLE + "点击购买!");
                    plugin.getLogger().info("替换为: 点击购买!");
                } else {
                    lore.set(i, ChatColor.RED + "你没有足够的 Iron!");
                    plugin.getLogger().info("替换为: 你没有足够的 Iron!");
                }
                break;
            }
        }

        if (!foundPlaceholder) {
            plugin.getLogger().warning("未找到购买状态文本！");
        }

        meta.setLore(lore);
        icon.setItemMeta(meta);
        item.setIcon(icon);
    }

    /**
     * 获取物品价格
     */
    private int getItemPrice(String itemName) {
        if (itemName.contains("羊毛")) {
            return config.getInt("team-items.wool.price.iron", 4);
        } else if (itemName.contains("木板")) {
            return config.getInt("team-items.planks.price.iron", 4);
        }
        return 0;
    }

    /**
     * 获取玩家的铁锭数量
     */
    private int getPlayerIronCount(Player player) {
        int count = 0;
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == Material.IRON_INGOT) {
                count += item.getAmount();
            }
        }
        return count;
    }

    /**
     * 产品给予事件处理 - 动态处理队伍颜色物品
     */
    @EventHandler
    public void onShopProductGiving(PlayerShopProductGivingDetermineEvent event) {
        Arena arena = event.getArena();

        // 只在幸运降临模式的竞技场中生效
        if (arena == null || !activeArenas.contains(arena)) {
            return;
        }

        // 检查给予的物品中是否有需要队伍颜色处理的物品
        ItemStack[] givingItems = event.getGivingItems();
        boolean hasTeamColorItem = false;

        for (ItemStack item : givingItems) {
            if (item != null && (item.getType().name().contains("WOOL") ||
                    item.getType().name().contains("TERRACOTTA") ||
                    item.getType().name().contains("STAINED_GLASS"))) {
                hasTeamColorItem = true;
                break;
            }
        }

        if (hasTeamColorItem) {
            // 获取玩家队伍
            Team playerTeam = event.getTeam();
            if (playerTeam != null) {
                DyeColor teamColor = playerTeam.getDyeColor();
                if (teamColor != null) {
                    // 替换为队伍颜色的物品
                    replaceItemsWithTeamColor(event, teamColor);
                }
            }
        }
    }

    /**
     * 替换物品为队伍颜色（羊毛、陶瓦、玻璃）
     */
    private void replaceItemsWithTeamColor(PlayerShopProductGivingDetermineEvent event, DyeColor teamColor) {
        ItemStack[] givingItems = event.getGivingItems();
        ItemStack[] newItems = new ItemStack[givingItems.length];

        // 获取队伍颜色的各种材质
        Material woolMaterial = getWoolMaterialByColor(teamColor);
        Material terracottaMaterial = getTerracottaMaterialByColor(teamColor);
        Material glassMaterial = getGlassMaterialByColor(teamColor);

        String itemType = "";
        for (int i = 0; i < givingItems.length; i++) {
            ItemStack item = givingItems[i];
            if (item != null) {
                if (item.getType().name().contains("WOOL")) {
                    // 替换为队伍颜色的羊毛，保持数量不变
                    newItems[i] = new ItemStack(woolMaterial, item.getAmount());
                    itemType = "羊毛";
                } else if (item.getType().name().contains("TERRACOTTA")) {
                    // 替换为队伍颜色的陶瓦，保持数量不变
                    newItems[i] = new ItemStack(terracottaMaterial, item.getAmount());
                    itemType = "硬化粘土";
                } else if (item.getType().name().contains("STAINED_GLASS")) {
                    // 替换为队伍颜色的玻璃，保持数量不变
                    newItems[i] = new ItemStack(glassMaterial, item.getAmount());
                    itemType = "防爆玻璃";
                } else {
                    // 保持其他物品不变
                    newItems[i] = item;
                }
            } else {
                newItems[i] = item;
            }
        }

        // 设置新的给予物品
        event.setGivingItems(newItems);

        // 发送消息
        String colorName = getColorName(teamColor);
        if (!itemType.isEmpty()) {
            event.getPlayer().sendMessage(translateColors("&a&l获得了队伍颜色的" + colorName + itemType + "！"));
        }
    }

    /**
     * 检查物品是否允许在商店中出售
     */
    private boolean isItemAllowed(ShopItem item) {
        if (item == null)
            return false;

        String itemName = item.getName().toLowerCase();
        String itemId = item.getId().toLowerCase();

        // 检查是否是幸运方块 - 只在扩展页面允许
        if (itemId.contains("lucky-block") || itemName.contains("幸运方块")) {
            // 检查是否在扩展页面
            ShopPage page = item.getPage();
            if (page != null) {
                String pageName = page.getName().toLowerCase();
                return pageName.contains("扩展") || pageName.contains("special") ||
                        pageName.contains("extension") || pageName.contains("extra");
            }
            return false; // 如果不在扩展页面，不允许显示
        }

        // 检查物品材质
        ItemStack icon = item.getIcon();
        if (icon != null) {
            Material material = icon.getType();

            // 禁止药水类别
            if (isPotionRelated(material)) {
                return false;
            }

            // 允许的基础方块
            if (isBasicBlock(material)) {
                return true;
            }

            // 允许的护甲
            if (isArmor(material)) {
                return true;
            }

            // 允许的工具
            if (isTool(material)) {
                return true;
            }

            // 允许的基础武器
            if (isBasicWeapon(material)) {
                return true;
            }

            // 允许的食物
            if (isFood(material)) {
                return true;
            }

            // 允许的其他物品
            if (isOtherAllowedItem(material)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否是基础方块
     */
    private boolean isBasicBlock(Material material) {
        String name = material.name().toLowerCase();
        return name.contains("wool") ||
                name.contains("clay") ||
                name.contains("terracotta") ||
                name.contains("glass") ||
                name.contains("stone") ||
                name.contains("wood") ||
                name.contains("plank") ||
                name.contains("log") ||
                name.equals("end_stone") ||
                name.equals("ladder") ||
                name.equals("obsidian") ||
                name.equals("cobblestone") ||
                name.equals("sandstone");
    }

    /**
     * 检查是否是护甲
     */
    private boolean isArmor(Material material) {
        String name = material.name().toLowerCase();
        return name.contains("helmet") ||
                name.contains("chestplate") ||
                name.contains("leggings") ||
                name.contains("boots");
    }

    /**
     * 检查是否是工具
     */
    private boolean isTool(Material material) {
        String name = material.name().toLowerCase();
        return name.contains("pickaxe") ||
                name.contains("axe") ||
                name.equals("shears");
    }

    /**
     * 检查是否是基础武器
     */
    private boolean isBasicWeapon(Material material) {
        String name = material.name().toLowerCase();
        return name.contains("sword") ||
                name.equals("stick");
    }

    /**
     * 检查是否是药水相关物品
     */
    private boolean isPotionRelated(Material material) {
        String name = material.name().toLowerCase();
        return name.contains("potion") ||
                name.contains("splash") ||
                name.contains("lingering") ||
                name.equals("brewing_stand") ||
                name.equals("cauldron") ||
                name.contains("tipped_arrow");
    }

    /**
     * 检查是否是食物
     */
    private boolean isFood(Material material) {
        String name = material.name().toLowerCase();
        return name.equals("cooked_porkchop") ||
                name.equals("cake") ||
                name.equals("golden_apple") ||
                name.equals("bread") ||
                name.equals("apple") ||
                name.equals("cooked_beef") ||
                name.equals("cooked_chicken") ||
                name.equals("cooked_mutton") ||
                name.equals("cooked_salmon") ||
                name.equals("cooked_cod");
    }

    /**
     * 检查是否是其他允许的物品
     */
    private boolean isOtherAllowedItem(Material material) {
        String name = material.name().toLowerCase();
        return name.equals("arrow") ||
                name.equals("chest") ||
                name.equals("ender_chest") ||
                name.equals("cobweb") ||
                name.equals("slime_block") ||
                name.equals("blue_ice") ||
                // 移除了wet_sponge，现在幸运方块使用player_head
                name.equals("player_head"); // 自定义头颅
    }

    // 移除了特殊物品处理器，现在使用普通方块放置

    /**
     * 方块放置事件处理
     */
    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        Arena arena = GameAPI.get().getArenaByPlayer(player);

        // 只在幸运降临模式的竞技场中生效
        if (arena == null || !activeArenas.contains(arena)) {
            return;
        }

        Block block = event.getBlock();
        ItemStack item = event.getItemInHand();

        // 检查是否是幸运方块（玩家头颅）
        if (block.getType() == Material.PLAYER_HEAD && isLuckyBlockItem(item)) {

            Location blockLocation = block.getLocation();

            // 检查是否支持玩家方块标记功能
            if (GameAPI.get().isPlayerBlockMarkingSupported()) {
                // 标记方块为玩家放置的，这样玩家就可以破坏它
                arena.setBlockPlayerPlaced(block, true);
                plugin.getLogger().info("已标记幸运方块为玩家放置: " + blockLocation);
            } else {
                plugin.getLogger().warning("服务器未启用玩家方块标记功能，幸运方块可能无法正常破坏");
            }

            // 记录放置的幸运方块位置
            placedLuckyBlocks.add(blockLocation);

            // 发送放置消息
            String placedMessage = config.getString("messages.lucky-block-placed", "&a&l幸运方块已放置！破坏它来获得奖励！");
            player.sendMessage(translateColors(placedMessage));

            plugin.getLogger().info("玩家 " + player.getName() + " 放置了幸运方块在 " + blockLocation);
        }
    }

    /**
     * 方块破坏事件处理
     */
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Arena arena = GameAPI.get().getArenaByPlayer(player);

        // 只在幸运降临模式的竞技场中生效
        if (arena == null || !activeArenas.contains(arena)) {
            return;
        }

        Block block = event.getBlock();
        Location blockLocation = block.getLocation();

        // 检查是否是幸运方块
        if (placedLuckyBlocks.contains(blockLocation)) {
            // 验证是否为玩家放置的方块（如果支持的话）
            if (GameAPI.get().isPlayerBlockMarkingSupported()) {
                if (!arena.isBlockPlayerPlaced(block)) {
                    plugin.getLogger().warning("幸运方块未标记为玩家放置，可能存在问题: " + blockLocation);
                }
            }

            // 移除记录
            placedLuckyBlocks.remove(blockLocation);

            // 取消默认掉落
            event.setDropItems(false);

            // 处理幸运方块破坏
            handleLuckyBlockBreak(player, blockLocation);

            plugin.getLogger().info("玩家 " + player.getName() + " 破坏了幸运方块在 " + blockLocation);
        }
    }

    /**
     * 处理幸运方块破坏
     */
    private void handleLuckyBlockBreak(Player player, Location location) {
        Arena arena = GameAPI.get().getArenaByPlayer(player);

        if (arena == null || arena.getStatus() != ArenaStatus.RUNNING) {
            return;
        }

        // 发送破坏消息
        String useMessage = config.getString("messages.lucky-block-use", "&a&l你破坏了幸运方块！");
        player.sendMessage(translateColors(useMessage));

        // 播放音效
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.0f);

        // 触发随机事件（新的事件系统）
        eventManager.triggerRandomEvent(player, location);
    }

    /**
     * 从配置加载可配置的商店物品
     */
    private void loadConfigurableShopItems(ShopPage page, String configPath) {
        plugin.getLogger().info("尝试加载配置路径: " + configPath);

        if (config.getConfigurationSection(configPath) == null) {
            plugin.getLogger().warning("配置路径不存在: " + configPath);
            plugin.getLogger().info("可用的配置路径:");
            for (String key : config.getKeys(true)) {
                if (key.startsWith("special")) {
                    plugin.getLogger().info("  - " + key);
                }
            }
            return;
        }

        // 首先清除页面中的原版物品（避免冲突）
        clearOriginalItems(page, configPath);

        Set<String> itemKeys = config.getConfigurationSection(configPath).getKeys(false);
        plugin.getLogger().info("加载配置页面: " + configPath + ", 物品数量: " + itemKeys.size());
        plugin.getLogger().info("物品列表: " + itemKeys);

        for (String itemKey : itemKeys) {
            try {
                plugin.getLogger().info("创建物品: " + itemKey);
                createConfigurableShopItem(page, configPath + "." + itemKey, itemKey);
            } catch (Exception e) {
                plugin.getLogger().warning("创建商店物品失败: " + itemKey + " - " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 清除原版物品以避免冲突
     */
    private void clearOriginalItems(ShopPage page, String configPath) {
        try {
            // 在幸运降临模式中，完全清除所有原版商店物品
            List<ShopItem> itemsToRemove = new ArrayList<>();
            for (ShopItem item : page.getItems()) {
                itemsToRemove.add(item);
                plugin.getLogger().info("标记移除原版物品: " + item.getName());
            }

            // 移除所有原版物品
            for (ShopItem item : itemsToRemove) {
                page.removeItem(item);
                plugin.getLogger().info("已移除原版物品: " + item.getName());
            }

            plugin.getLogger().info("已清除页面 " + page.getName() + " 中的所有原版物品，共 " + itemsToRemove.size() + " 个");
        } catch (Exception e) {
            plugin.getLogger().warning("清除原版物品时出错: " + e.getMessage());
        }
    }

    /**
     * 创建可配置的商店物品
     */
    private void createConfigurableShopItem(ShopPage page, String itemPath, String itemKey) {
        plugin.getLogger().info("开始创建商店物品: " + itemKey + " 路径: " + itemPath);

        // 检查是否已存在
        String displayName = ChatColor
                .stripColor(translateColors(config.getString(itemPath + ".display-name", itemKey)));
        for (ShopItem existingItem : page.getItems()) {
            if (ChatColor.stripColor(existingItem.getName()).equals(displayName)) {
                plugin.getLogger().info("物品已存在，跳过: " + displayName);
                return; // 已存在，跳过
            }
        }

        // 获取基础配置
        String materialName = config.getString(itemPath + ".material", "STONE");
        String displayNameRaw = config.getString(itemPath + ".display-name", "&f" + itemKey);
        List<String> loreList = config.getStringList(itemPath + ".lore");
        String currency = config.getString(itemPath + ".price.currency", "iron");
        int priceAmount = config.getInt(itemPath + ".price.amount", 1);
        int productAmount = config.getInt(itemPath + ".product.amount", 1);
        boolean teamColored = config.getBoolean(itemPath + ".team-colored", false);
        boolean autoWear = config.getBoolean(itemPath + ".auto-wear", false);
        boolean unbreakable = config.getBoolean(itemPath + ".unbreakable", false);
        List<String> enchantments = config.getStringList(itemPath + ".enchantments");

        // 装甲特殊配置
        String buyGroupName = config.getString(itemPath + ".buy-group.name", "");
        int buyGroupLevel = config.getInt(itemPath + ".buy-group.level", -1);
        boolean keepOnDeath = config.getBoolean(itemPath + ".keep-on-death", false);
        List<String> products = config.getStringList(itemPath + ".products");

        // slot位置配置
        int slot = config.getInt(itemPath + ".slot", -1);

        // 创建材质
        Material material;
        try {
            material = Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的材质: " + materialName + " 对于物品: " + itemKey);
            return;
        }

        // 创建图标
        ItemStack icon = new ItemStack(material, 1);

        // 应用附魔
        if (!enchantments.isEmpty()) {
            for (String enchantStr : enchantments) {
                try {
                    String[] parts = enchantStr.split(":");
                    if (parts.length == 2) {
                        org.bukkit.enchantments.Enchantment enchant = org.bukkit.enchantments.Enchantment
                                .getByName(parts[0]);
                        int level = Integer.parseInt(parts[1]);
                        if (enchant != null) {
                            icon.addUnsafeEnchantment(enchant, level);
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("无效的附魔配置: " + enchantStr);
                }
            }
        }

        ItemMeta meta = icon.getItemMeta();
        if (meta != null) {
            // 设置显示名称
            meta.setDisplayName(translateColors(displayNameRaw));

            // 设置lore
            List<String> finalLore = new ArrayList<>();
            for (String loreLine : loreList) {
                finalLore.add(translateColors(loreLine));
            }

            // 添加价格和操作提示
            finalLore.add("");
            finalLore.add(translateColors("&7花费: &f" + priceAmount + " " + getCurrencyDisplayName(currency)));
            finalLore.add("");
            finalLore.add(translateColors("&b&lShift点击以添加到快速购买栏"));
            finalLore.add(translateColors("&d&l点击购买!"));

            meta.setLore(finalLore);

            // 设置不可破坏
            if (unbreakable) {
                try {
                    meta.setUnbreakable(true);
                } catch (Exception e) {
                    // 兼容旧版本
                    plugin.getLogger().info("使用兼容模式设置不可破坏属性");
                }
            }

            icon.setItemMeta(meta);
        }

        // 创建商店物品
        ShopItem shopItem = page.addItem(displayName, icon);
        shopItem.setIcon(icon);

        // 设置价格
        DropType dropType = GameAPI.get().getDropTypeById(currency);
        if (dropType != null) {
            shopItem.addPriceSpawner(dropType, priceAmount);
        } else {
            plugin.getLogger().warning("无效的货币类型: " + currency);
        }

        // 创建产品
        ItemStack product;
        if (itemKey.equals("lucky_block")) {
            // 幸运方块使用自定义物品
            product = createLuckyBlockItem();
        } else {
            product = new ItemStack(material, productAmount);
        }

        // 为产品应用相同的属性
        if (!enchantments.isEmpty() || unbreakable || autoWear) {
            ItemMeta productMeta = product.getItemMeta();
            if (productMeta != null) {
                // 应用附魔
                if (!enchantments.isEmpty()) {
                    for (String enchantStr : enchantments) {
                        try {
                            String[] parts = enchantStr.split(":");
                            if (parts.length == 2) {
                                org.bukkit.enchantments.Enchantment enchant = org.bukkit.enchantments.Enchantment
                                        .getByName(parts[0]);
                                int level = Integer.parseInt(parts[1]);
                                if (enchant != null) {
                                    product.addUnsafeEnchantment(enchant, level);
                                }
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("为产品应用附魔失败: " + enchantStr);
                        }
                    }
                }

                // 设置不可破坏
                if (unbreakable) {
                    try {
                        productMeta.setUnbreakable(true);
                    } catch (Exception e) {
                        plugin.getLogger().info("使用兼容模式为产品设置不可破坏属性");
                    }
                }

                product.setItemMeta(productMeta);
            }
        }

        // 设置商店物品的特殊属性
        if (!buyGroupName.isEmpty() && buyGroupLevel >= 0) {
            // 设置购买组等级（简化处理，直接设置等级）
            try {
                shopItem.setBuyGroupLevel(buyGroupLevel);
                plugin.getLogger().info("设置购买组等级: " + buyGroupLevel + " 对于物品: " + displayName);
            } catch (Exception e) {
                plugin.getLogger().warning("设置购买组等级失败: " + e.getMessage());
            }
        }

        // 设置死亡保留
        if (keepOnDeath) {
            shopItem.setKeptOnDeath(true);
            plugin.getLogger().info("设置死亡保留: " + displayName);
        }

        // 添加产品
        if (teamColored) {
            // 队伍颜色物品使用白色版本作为默认产品，实际在购买事件中处理
            shopItem.addProductItem(product, productAmount);
            plugin.getLogger().info("添加队伍颜色物品: " + displayName + " (将在购买时替换为队伍颜色)");
        } else if (!products.isEmpty()) {
            // 多个产品（装甲套装）
            for (String productStr : products) {
                de.marcely.bedwars.api.game.shop.product.ItemShopProduct armorProduct = createArmorProduct(shopItem,
                        productStr);
                if (armorProduct != null) {
                    plugin.getLogger().info("添加装甲产品: " + productStr);
                }
            }
        } else {
            // 单个产品
            de.marcely.bedwars.api.game.shop.product.ItemShopProduct itemProduct = shopItem.addProductItem(product,
                    productAmount);

            // 设置产品属性
            if (autoWear) {
                itemProduct.setAutoWear(true);
                plugin.getLogger().info("设置自动穿戴: " + displayName);
            }
            if (unbreakable) {
                itemProduct.setUnbreakable(true);
                plugin.getLogger().info("设置不可破坏: " + displayName);
            }
        }

        // 设置slot位置
        if (slot >= 0) {
            shopItem.setForceSlot(slot);
            plugin.getLogger().info("设置slot位置: " + slot + " 对于物品: " + displayName);
        }

        plugin.getLogger().info(
                "✓ 成功创建商店物品: " + displayName + " (材质: " + materialName + ", 价格: " + priceAmount + " " + currency
                        + ", slot: " + slot + ")");
    }

    /**
     * 创建装甲产品
     * 格式: MATERIAL:AMOUNT:auto-wear:unbreakable
     */
    private de.marcely.bedwars.api.game.shop.product.ItemShopProduct createArmorProduct(ShopItem shopItem,
            String productStr) {
        try {
            String[] parts = productStr.split(":");
            if (parts.length >= 2) {
                Material material = Material.valueOf(parts[0].toUpperCase());
                int amount = Integer.parseInt(parts[1]);

                ItemStack armorPiece = new ItemStack(material, amount);

                // 检查是否有特殊属性
                boolean hasAutoWear = false;
                boolean hasUnbreakable = false;

                for (int i = 2; i < parts.length; i++) {
                    if (parts[i].equals("auto-wear")) {
                        hasAutoWear = true;
                    } else if (parts[i].equals("unbreakable")) {
                        hasUnbreakable = true;
                    }
                }

                // 添加产品到商店物品
                de.marcely.bedwars.api.game.shop.product.ItemShopProduct armorProduct = shopItem
                        .addProductItem(armorPiece, amount);

                // 设置产品属性
                if (hasAutoWear) {
                    armorProduct.setAutoWear(true);
                }
                if (hasUnbreakable) {
                    armorProduct.setUnbreakable(true);
                }

                return armorProduct;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("创建装甲产品失败: " + productStr + " - " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取货币显示名称
     */
    private String getCurrencyDisplayName(String currency) {
        switch (currency.toLowerCase()) {
            case "iron":
                return "Iron";
            case "gold":
                return "Gold";
            case "emerald":
                return "Emerald";
            default:
                return currency;
        }
    }

    /**
     * 设置头颅纹理
     */
    private void setSkullTexture(org.bukkit.inventory.meta.SkullMeta meta, String texture) throws Exception {
        // 直接使用反射设置纹理
        setSkullTextureByReflection(meta, texture);
        plugin.getLogger().info("使用反射方法设置纹理成功");
    }

    /**
     * 使用反射设置头颅纹理
     */
    private void setSkullTextureByReflection(org.bukkit.inventory.meta.SkullMeta meta, String texture)
            throws Exception {
        // 使用反射设置头颅纹理
        java.lang.reflect.Field profileField = meta.getClass().getDeclaredField("profile");
        profileField.setAccessible(true);

        // 创建GameProfile
        Object profile = Class.forName("com.mojang.authlib.GameProfile")
                .getConstructor(java.util.UUID.class, String.class)
                .newInstance(java.util.UUID.randomUUID(), "LuckyBlock");

        // 创建Property
        Object property = Class.forName("com.mojang.authlib.properties.Property")
                .getConstructor(String.class, String.class)
                .newInstance("textures", texture);

        // 获取PropertyMap并添加属性
        Object propertyMap = profile.getClass().getMethod("getProperties").invoke(profile);
        propertyMap.getClass().getMethod("put", Object.class, Object.class).invoke(propertyMap, "textures", property);

        // 设置profile到meta
        profileField.set(meta, profile);
    }
}
