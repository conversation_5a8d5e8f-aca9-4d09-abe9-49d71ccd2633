package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import io.lumine.mythic.bukkit.MythicBukkit;
import io.lumine.mythic.bukkit.events.MythicMobDeathEvent;
import io.lumine.mythic.core.mobs.ActiveMob;
import io.lumine.mythic.api.mobs.MythicMob;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Boss管理系统
 * 负责Boss的生成、技能管理、位置设置等功能
 */
public class BossManager implements Listener {

    private final AceVoteMode plugin;
    private final TerrorDescentMode terrorMode;

    // Boss位置存储 (竞技场名称 -> 位置列表)
    private final Map<String, List<Location>> bossLocations = new ConcurrentHashMap<>();

    // 活跃的Boss (竞技场 -> Boss实体列表)
    private final Map<Arena, List<ActiveMob>> activeBosses = new ConcurrentHashMap<>();

    // Boss技能任务
    private final Map<ActiveMob, BukkitTask> bossSkillTasks = new ConcurrentHashMap<>();

    // MythicMobs API
    private boolean mythicMobsEnabled = false;

    public BossManager(AceVoteMode plugin, TerrorDescentMode terrorMode) {
        this.plugin = plugin;
        this.terrorMode = terrorMode;

        // 检查MythicMobs是否可用
        initializeMythicMobs();

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        // 加载Boss位置
        loadBossLocations();
    }

    /**
     * 初始化MythicMobs API
     */
    private void initializeMythicMobs() {
        if (Bukkit.getPluginManager().getPlugin("MythicMobs") != null) {
            try {
                // 检查MythicBukkit是否可用
                MythicBukkit.inst();
                this.mythicMobsEnabled = true;
                plugin.getLogger().info("MythicMobs API 已成功加载");
            } catch (Exception e) {
                plugin.getLogger().warning("无法加载MythicMobs API: " + e.getMessage());
                this.mythicMobsEnabled = false;
            }
        } else {
            plugin.getLogger().warning("MythicMobs 插件未找到，Boss功能将被禁用");
            this.mythicMobsEnabled = false;
        }
    }

    /**
     * 加载Boss位置配置
     */
    private void loadBossLocations() {
        FileConfiguration config = terrorMode.getConfig();

        // 从配置文件加载位置
        if (config.contains("boss.locations")) {
            for (String arenaName : config.getConfigurationSection("boss.locations").getKeys(false)) {
                List<String> locationStrings = config.getStringList("boss.locations." + arenaName);
                List<Location> locations = new ArrayList<>();

                for (String locationString : locationStrings) {
                    Location location = parseLocationString(locationString);
                    if (location != null) {
                        locations.add(location);
                    }
                }

                if (!locations.isEmpty()) {
                    bossLocations.put(arenaName, locations);
                }
            }
        }
    }

    /**
     * 解析位置字符串
     */
    private Location parseLocationString(String locationString) {
        try {
            String[] parts = locationString.split(",");
            if (parts.length >= 4) {
                World world = Bukkit.getWorld(parts[0]);
                double x = Double.parseDouble(parts[1]);
                double y = Double.parseDouble(parts[2]);
                double z = Double.parseDouble(parts[3]);
                float yaw = parts.length > 4 ? Float.parseFloat(parts[4]) : 0;
                float pitch = parts.length > 5 ? Float.parseFloat(parts[5]) : 0;

                return new Location(world, x, y, z, yaw, pitch);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法解析位置字符串: " + locationString);
        }
        return null;
    }

    /**
     * 生成Boss
     */
    public boolean spawnBoss(Arena arena) {
        if (!mythicMobsEnabled) {
            plugin.getLogger().warning("MythicMobs未启用，无法生成Boss");
            return false;
        }

        FileConfiguration config = terrorMode.getConfig();

        // 检查是否需要防止生成新Boss
        if (config.getBoolean("boss.prevent-spawn-if-alive", true)) {
            int aliveBossCount = getAliveBossCount(arena);
            int maxBossCount = config.getInt("boss.max-count", 1);

            if (aliveBossCount >= maxBossCount) {
                // 广播Boss生成被阻止的消息
                String preventMessage = config.getString("messages.boss-spawn-prevented",
                        "&e&l【提醒】&f 当前还有Boss存活，新Boss生成已取消！");
                String coloredMessage = ChatColor.translateAlternateColorCodes('&', preventMessage);

                for (Player player : arena.getPlayers()) {
                    player.sendMessage(coloredMessage);
                    player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 0.8f);
                }

                plugin.getLogger().info("竞技场 " + arena.getName() + " 已有 " + aliveBossCount + " 个Boss存活，取消生成新Boss");
                return false;
            }
        }

        String mythicMobId = config.getString("boss.mythic-mob-id", "LightHerald1");

        // 获取Boss生成位置
        Location spawnLocation = getBossSpawnLocation(arena);
        if (spawnLocation == null) {
            plugin.getLogger().warning("竞技场 " + arena.getName() + " 没有设置Boss生成位置");
            return false;
        }

        try {
            // 检查MythicMob是否存在
            Optional<MythicMob> mythicMobOpt = MythicBukkit.inst().getMobManager().getMythicMob(mythicMobId);
            if (!mythicMobOpt.isPresent()) {
                plugin.getLogger().warning("MythicMob " + mythicMobId + " 不存在");
                return false;
            }

            // 生成Boss
            Entity bossEntity = MythicBukkit.inst().getAPIHelper().spawnMythicMob(mythicMobId, spawnLocation);
            if (bossEntity != null) {
                // 获取ActiveMob实例
                ActiveMob boss = MythicBukkit.inst().getMobManager().getMythicMobInstance(bossEntity);
                if (boss == null) {
                    plugin.getLogger().warning("无法获取Boss的ActiveMob实例");
                    return false;
                }
                // 添加到活跃Boss列表
                activeBosses.computeIfAbsent(arena, k -> new ArrayList<>()).add(boss);

                // 应用配置修改
                applyBossModifications(boss, config);

                // 启动Boss技能系统
                startBossSkills(boss, arena);

                // 广播Boss生成消息
                if (config.getBoolean("boss.announce-spawn", true)) {
                    broadcastBossSpawn(arena);
                }

                // 播放Boss生成音效
                if (config.getBoolean("boss.spawn-sound.enabled", true)) {
                    playBossSpawnSound(arena);
                }

                plugin.getLogger().info("Boss " + mythicMobId + " 已在竞技场 " + arena.getName() + " 生成");
                return true;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("生成Boss时发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 生成指定类型的Boss
     */
    private boolean spawnSpecificBoss(Arena arena, String mythicMobId) {
        if (!mythicMobsEnabled) {
            plugin.getLogger().warning("MythicMobs未启用，无法生成Boss");
            return false;
        }

        // 获取Boss生成位置
        Location spawnLocation = getBossSpawnLocation(arena);
        if (spawnLocation == null) {
            plugin.getLogger().warning("竞技场 " + arena.getName() + " 没有设置Boss生成位置");
            return false;
        }

        try {
            // 检查MythicMob是否存在
            Optional<MythicMob> mythicMobOpt = MythicBukkit.inst().getMobManager().getMythicMob(mythicMobId);
            if (!mythicMobOpt.isPresent()) {
                plugin.getLogger().warning("MythicMob " + mythicMobId + " 不存在");
                return false;
            }

            // 生成Boss
            Entity bossEntity = MythicBukkit.inst().getAPIHelper().spawnMythicMob(mythicMobId, spawnLocation);
            if (bossEntity != null) {
                // 获取ActiveMob实例
                ActiveMob boss = MythicBukkit.inst().getMobManager().getMythicMobInstance(bossEntity);
                if (boss == null) {
                    plugin.getLogger().warning("无法获取Boss的ActiveMob实例");
                    return false;
                }
                // 添加到活跃Boss列表
                activeBosses.computeIfAbsent(arena, k -> new ArrayList<>()).add(boss);

                // 应用配置修改
                FileConfiguration config = terrorMode.getConfig();
                applyBossModifications(boss, config);

                // 启动Boss技能系统
                startBossSkills(boss, arena);

                plugin.getLogger().info("Boss " + mythicMobId + " 已在竞技场 " + arena.getName() + " 生成");
                return true;
            } else {
                plugin.getLogger().warning("无法生成Boss " + mythicMobId);
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("生成Boss时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取Boss生成位置
     */
    private Location getBossSpawnLocation(Arena arena) {
        List<Location> locations = bossLocations.get(arena.getName());
        if (locations != null && !locations.isEmpty()) {
            // 随机选择一个位置
            return locations.get(new Random().nextInt(locations.size()));
        }

        // 如果没有设置位置，尝试使用竞技场中心
        return getArenaCenterLocation(arena);
    }

    /**
     * 获取竞技场中心位置
     */
    private Location getArenaCenterLocation(Arena arena) {
        try {
            World world = arena.getGameWorld();
            if (world == null)
                return null;

            // 计算所有队伍出生点的平均位置
            List<Location> spawnLocations = new ArrayList<>();
            for (Team team : arena.getEnabledTeams()) {
                try {
                    de.marcely.bedwars.tools.location.XYZYP spawn = arena.getTeamSpawn(team);
                    if (spawn != null) {
                        spawnLocations.add(spawn.toLocation(world));
                    }
                } catch (Exception e) {
                    continue;
                }
            }

            if (!spawnLocations.isEmpty()) {
                double x = spawnLocations.stream().mapToDouble(Location::getX).average().orElse(0);
                double y = spawnLocations.stream().mapToDouble(Location::getY).average().orElse(100);
                double z = spawnLocations.stream().mapToDouble(Location::getZ).average().orElse(0);
                return new Location(world, x, y + 10, z); // 在中心上方10格生成
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法计算竞技场中心位置: " + e.getMessage());
        }

        return null;
    }

    /**
     * 应用Boss配置修改
     */
    private void applyBossModifications(ActiveMob boss, FileConfiguration config) {
        LivingEntity entity = (LivingEntity) boss.getEntity().getBukkitEntity();

        // 应用生命值倍数
        double healthMultiplier = config.getDouble("boss.health-multiplier", 1.0);
        if (healthMultiplier != 1.0) {
            try {
                double maxHealth = entity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getValue()
                        * healthMultiplier;
                entity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).setBaseValue(maxHealth);
                entity.setHealth(maxHealth);
            } catch (Exception e) {
                // 兼容旧版本
                double maxHealth = entity.getMaxHealth() * healthMultiplier;
                entity.setMaxHealth(maxHealth);
                entity.setHealth(maxHealth);
            }
        }

        // 设置Boss名称
        String bossName = ChatColor.translateAlternateColorCodes('&',
                config.getString("boss.display-name", "&4&l恐怖Boss"));
        entity.setCustomName(bossName);
        entity.setCustomNameVisible(true);

        // 防止Boss掉入虚空
        entity.setFallDistance(0);
    }

    /**
     * 启动Boss技能系统
     */
    private void startBossSkills(ActiveMob boss, Arena arena) {
        BukkitTask skillTask = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (boss.isDead() || !terrorMode.isArenaActive(arena)) {
                return;
            }

            LivingEntity bossEntity = (LivingEntity) boss.getEntity().getBukkitEntity();

            // 检查Boss是否掉入虚空
            if (bossEntity.getLocation().getY() < -10) {
                handleBossVoidFall(boss, arena);
                return;
            }

            // 检查Boss血量并发送公告
            checkBossHealthAndAnnounce(boss, arena);

            // 执行Boss技能
            executeBossSkills(boss, arena);

        }, 20L, 20L); // 每秒执行一次

        bossSkillTasks.put(boss, skillTask);
    }

    /**
     * 处理Boss掉入虚空
     */
    private void handleBossVoidFall(ActiveMob boss, Arena arena) {
        Location spawnLocation = getBossSpawnLocation(arena);
        if (spawnLocation != null) {
            LivingEntity bossEntity = (LivingEntity) boss.getEntity().getBukkitEntity();

            // 在传送前给予保护效果，防止摔死
            bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.DAMAGE_RESISTANCE, 100, 4, false, false));
            bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.SLOW_FALLING, 100, 4, false, false));
            bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.FIRE_RESISTANCE, 100, 0, false, false));

            // 传送Boss回到生成位置
            bossEntity.teleport(spawnLocation);

            // 传送后再次给予保护效果，确保不会受到任何伤害
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                if (!bossEntity.isDead()) {
                    bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.DAMAGE_RESISTANCE, 60, 4, false, false));
                    bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.SLOW_FALLING, 60, 4, false, false));

                    // 给予额外的愤怒效果
                    bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.INCREASE_DAMAGE, 200, 0, false, false));
                    bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.SPEED, 200, 0, false, false));

                    plugin.getLogger().info("Boss虚空返回保护效果已应用");
                }
            }, 5L); // 延迟5tick执行

            // 广播Boss从虚空爬回来的消息
            String message = terrorMode.getConfig().getString("messages.boss-void-return",
                    "&4&l💀【恐怖降临】💀 &c恐怖领主从虚空爬了回来！");
            String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

            for (Player player : arena.getPlayers()) {
                player.sendMessage(coloredMessage);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);
            }

            plugin.getLogger().info("Boss在竞技场 " + arena.getName() + " 从虚空返回，已给予保护效果");
        }
    }

    /**
     * 检查Boss血量并发送公告
     */
    private void checkBossHealthAndAnnounce(ActiveMob boss, Arena arena) {
        LivingEntity bossEntity = (LivingEntity) boss.getEntity().getBukkitEntity();
        double currentHealth = bossEntity.getHealth();
        double maxHealth = bossEntity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getValue();
        double healthPercentage = (currentHealth / maxHealth) * 100;

        // 在特定血量百分比时发送公告
        if (healthPercentage <= 75 && healthPercentage > 74) {
            broadcastBossHealthWarning(arena, "75%", "&e");
        } else if (healthPercentage <= 50 && healthPercentage > 49) {
            broadcastBossHealthWarning(arena, "50%", "&6");
        } else if (healthPercentage <= 25 && healthPercentage > 24) {
            broadcastBossHealthWarning(arena, "25%", "&c");
        } else if (healthPercentage <= 10 && healthPercentage > 9) {
            broadcastBossHealthWarning(arena, "10%", "&4");
        }
    }

    /**
     * 广播Boss血量警告
     */
    private void broadcastBossHealthWarning(Arena arena, String healthPercent, String colorCode) {
        String message = colorCode + "&l【警告】&f Boss血量剩余 " + colorCode + healthPercent + "&f！";
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

        for (Player player : arena.getPlayers()) {
            player.sendMessage(coloredMessage);
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 0.8f);
        }
    }

    /**
     * 执行Boss技能
     */
    private void executeBossSkills(ActiveMob boss, Arena arena) {
        LivingEntity bossEntity = (LivingEntity) boss.getEntity().getBukkitEntity();
        Location bossLocation = bossEntity.getLocation();
        double currentHealth = bossEntity.getHealth();
        double maxHealth = bossEntity.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getValue();
        double healthPercentage = (currentHealth / maxHealth) * 100;

        // 根据血量百分比使用不同技能
        if (healthPercentage <= 25) {
            // 低血量时使用狂暴技能
            executeRageSkills(boss, arena, bossLocation);
        } else if (healthPercentage <= 50) {
            // 中等血量时使用强化技能
            executeEnhancedSkills(boss, arena, bossLocation);
        } else {
            // 高血量时使用基础技能
            executeBasicSkills(boss, arena, bossLocation);
        }
    }

    /**
     * 执行基础技能（高血量时）
     */
    private void executeBasicSkills(ActiveMob boss, Arena arena, Location bossLocation) {
        // 每30秒执行一次基础技能
        if (System.currentTimeMillis() % 30000 < 1000) {
            // 寻找最近的玩家
            Player target = findNearestPlayer(arena, bossLocation);
            if (target != null && target.getLocation().distance(bossLocation) <= 20) {
                // 基础攻击技能 - 降低伤害
                target.damage(2.0);
                target.sendMessage(ChatColor.RED + "Boss对你造成了伤害！");
            }
        }
    }

    /**
     * 执行强化技能（中等血量时）
     */
    private void executeEnhancedSkills(ActiveMob boss, Arena arena, Location bossLocation) {
        // 每20秒执行一次强化技能
        if (System.currentTimeMillis() % 20000 < 1000) {
            // 范围攻击 - 降低伤害和范围
            for (Player player : arena.getPlayers()) {
                if (player.getLocation().distance(bossLocation) <= 12) {
                    player.damage(3.0);
                    player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.SLOW, 80, 0));
                }
            }

            // 广播技能使用消息
            String message = "&c&lBoss使用了强化攻击！";
            for (Player player : arena.getPlayers()) {
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
            }
        }
    }

    /**
     * 执行狂暴技能（低血量时）
     */
    private void executeRageSkills(ActiveMob boss, Arena arena, Location bossLocation) {
        // 每10秒执行一次狂暴技能
        if (System.currentTimeMillis() % 10000 < 1000) {
            LivingEntity bossEntity = (LivingEntity) boss.getEntity().getBukkitEntity();

            // 给Boss增加速度和力量效果
            bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.SPEED, 200, 1));
            bossEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.INCREASE_DAMAGE, 200, 0));

            // 对所有玩家造成伤害 - 大幅降低伤害
            for (Player player : arena.getPlayers()) {
                if (player.getLocation().distance(bossLocation) <= 20) {
                    player.damage(4.0);
                    player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.POISON, 60, 0));
                }
            }

            // 广播狂暴消息
            String message = "&4&l【狂暴】&c Boss进入狂暴状态！所有人小心！";
            for (Player player : arena.getPlayers()) {
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.5f);
            }
        }
    }

    /**
     * 寻找最近的玩家
     */
    private Player findNearestPlayer(Arena arena, Location location) {
        Player nearest = null;
        double nearestDistance = Double.MAX_VALUE;

        for (Player player : arena.getPlayers()) {
            if (player.isOnline() && !player.isDead()) {
                double distance = player.getLocation().distance(location);
                if (distance < nearestDistance) {
                    nearest = player;
                    nearestDistance = distance;
                }
            }
        }

        return nearest;
    }

    /**
     * 广播Boss生成消息
     */
    private void broadcastBossSpawn(Arena arena) {
        String message = terrorMode.getConfig().getString("messages.boss-spawn", "&c&l【恐怖降临】&f Boss已在中心降临！");
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

        for (Player player : arena.getPlayers()) {
            player.sendMessage(coloredMessage);
        }
    }

    /**
     * 播放Boss生成音效
     */
    private void playBossSpawnSound(Arena arena) {
        String soundName = terrorMode.getConfig().getString("boss.spawn-sound.sound", "ENTITY_ENDER_DRAGON_GROWL");
        float volume = (float) terrorMode.getConfig().getDouble("boss.spawn-sound.volume", 1.0);
        float pitch = (float) terrorMode.getConfig().getDouble("boss.spawn-sound.pitch", 1.0);

        try {
            org.bukkit.Sound sound = org.bukkit.Sound.valueOf(soundName);
            for (Player player : arena.getPlayers()) {
                player.playSound(player.getLocation(), sound, volume, pitch);
            }
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的音效名称: " + soundName);
        }
    }

    /**
     * 监听MythicMob死亡事件
     */
    @EventHandler
    public void onMythicMobDeath(MythicMobDeathEvent event) {
        ActiveMob deadMob = event.getMob();
        String mobType = deadMob.getType().getInternalName();

        plugin.getLogger().info("检测到MythicMob死亡: " + mobType);

        // 检查是否是我们管理的Boss
        Arena arena = findArenaByBoss(deadMob);
        if (arena != null) {
            plugin.getLogger().info("找到Boss所在竞技场: " + arena.getName() + ", 开始处理Boss死亡");
            handleBossKilled(arena, deadMob, event.getKiller());
        } else {
            plugin.getLogger().info("Boss不在我们管理的列表中，跳过处理");
        }
    }

    /**
     * 监听实体掉入虚空事件
     */
    @EventHandler(priority = org.bukkit.event.EventPriority.HIGHEST)
    public void onEntityDamage(org.bukkit.event.entity.EntityDamageEvent event) {
        if (event.getCause() != org.bukkit.event.entity.EntityDamageEvent.DamageCause.VOID) {
            return;
        }

        Entity entity = event.getEntity();

        // 检查是否是MythicMob
        if (!MythicBukkit.inst().getMobManager().isMythicMob(entity)) {
            return;
        }

        ActiveMob mythicMob = MythicBukkit.inst().getMobManager().getMythicMobInstance(entity);
        if (mythicMob == null) {
            return;
        }

        // 检查是否是我们的Boss
        Arena arena = findArenaByBoss(mythicMob);
        if (arena != null) {
            String mobType = mythicMob.getType().getInternalName();
            if (mobType.equals("LightHerald1") || mobType.equals("LightHerald2") || mobType.equals("LightCore")) {
                plugin.getLogger().info("检测到Boss " + mobType + " 掉入虚空，正在传送回竞技场...");

                // 取消虚空伤害
                event.setCancelled(true);

                // 传送Boss回到竞技场
                Location spawnLocation = getBossSpawnLocation(arena);
                if (spawnLocation != null) {
                    // 确保传送位置安全
                    spawnLocation.add(0, 1, 0); // 稍微提高一点位置
                    entity.teleport(spawnLocation);

                    // 给Boss一点无敌时间防止立即再次掉落
                    if (entity instanceof LivingEntity) {
                        LivingEntity livingEntity = (LivingEntity) entity;
                        livingEntity.setNoDamageTicks(60); // 3秒无敌时间
                    }

                    // 广播Boss返回消息
                    broadcastBossVoidReturn(arena, mobType);

                    plugin.getLogger().info("Boss " + mobType + " 已成功传送回竞技场");
                } else {
                    plugin.getLogger().warning("无法找到Boss生成位置，Boss可能会死亡");
                }
            }
        }
    }

    /**
     * 处理Boss被击杀
     */
    private void handleBossKilled(Arena arena, ActiveMob boss, LivingEntity killer) {
        // 移除Boss
        List<ActiveMob> bosses = activeBosses.get(arena);
        if (bosses != null) {
            bosses.remove(boss);
        }

        // 停止技能任务
        BukkitTask skillTask = bossSkillTasks.remove(boss);
        if (skillTask != null) {
            skillTask.cancel();
        }

        // 确定击杀者的队伍
        Team killerTeam = null;
        if (killer instanceof Player) {
            killerTeam = arena.getPlayerTeam((Player) killer);
        }

        // 检查是否是最终Boss (LightCore)
        String bossType = boss.getType().getInternalName();
        boolean isFinalBoss = "LightCore".equals(bossType);

        plugin.getLogger().info("Boss死亡处理 - Boss类型: " + bossType + ", 是否最终Boss: " + isFinalBoss + ", 击杀者队伍: " + (killerTeam != null ? killerTeam.getDisplayName() : "无"));

        if (killerTeam != null && isFinalBoss) {
            // 只有击杀最终Boss才给予队伍属性效果
            terrorMode.getTeamEffectManager().applyTeamEffects(arena, killerTeam);

            // 广播Boss死亡消息
            if (terrorMode.getConfig().getBoolean("boss.announce-death", true)) {
                broadcastBossDeath(arena, killerTeam);
            }

            // 清空所有资源点的怪物
            terrorMode.getMonsterSpawnManager().clearAllMonsters(arena);

            // 广播怪物清空消息
            String clearMessage = terrorMode.getConfig().getString("messages.monsters-cleared",
                    "&a&l【净化】&f Boss被击杀，所有恐怖怪物已被清除！");
            String coloredClearMessage = ChatColor.translateAlternateColorCodes('&', clearMessage);

            for (Player player : arena.getPlayers()) {
                player.sendMessage(coloredClearMessage);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
            }

            plugin.getLogger().info("最终Boss已被击杀在竞技场 " + arena.getName() + "，所有怪物已清除，队伍获得属性效果");
        } else if (killerTeam != null) {
            // 非最终Boss死亡，只广播阶段完成消息
            plugin.getLogger().info("开始广播阶段完成消息 - Boss类型: " + bossType + ", 队伍: " + killerTeam.getDisplayName());
            broadcastPhaseComplete(arena, killerTeam, bossType);
            plugin.getLogger().info("阶段Boss " + bossType + " 已被击杀在竞技场 " + arena.getName() + "，进入下一阶段");

            // 生成下一阶段Boss
            spawnNextPhaseBoss(arena, bossType);
        } else {
            plugin.getLogger().warning("Boss死亡但没有击杀者队伍信息 - Boss类型: " + bossType);
        }
    }

    /**
     * 生成下一阶段Boss
     */
    private void spawnNextPhaseBoss(Arena arena, String currentBossType) {
        String nextBossType = getNextPhaseBoss(currentBossType);
        if (nextBossType != null) {
            plugin.getLogger().info("准备生成下一阶段Boss: " + nextBossType + " (当前Boss: " + currentBossType + ")");

            // 延迟3秒生成下一阶段Boss，给玩家一些准备时间
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                plugin.getLogger().info("开始生成下一阶段Boss: " + nextBossType);
                if (spawnSpecificBoss(arena, nextBossType)) {
                    plugin.getLogger().info("成功生成下一阶段Boss: " + nextBossType);

                    // 广播阶段开始消息
                    broadcastPhaseStart(arena, nextBossType);
                } else {
                    plugin.getLogger().warning("无法生成下一阶段Boss: " + nextBossType);
                }
            }, 60L); // 3秒延迟
        } else {
            plugin.getLogger().info("当前Boss " + currentBossType + " 是最终阶段，无需生成下一阶段");
        }
    }

    /**
     * 获取下一阶段Boss类型
     */
    private String getNextPhaseBoss(String currentBossType) {
        switch (currentBossType) {
            case "LightHerald1":
                return "LightHerald2";
            case "LightHerald2":
                return "LightCore";
            case "LightCore":
                return null; // 最终阶段
            default:
                return null;
        }
    }

    /**
     * 广播阶段开始消息
     */
    private void broadcastPhaseStart(Arena arena, String bossType) {
        String message;
        switch (bossType) {
            case "LightHerald2":
                message = "&5&l🌙【第二阶段】🌙 &f光明先驱进入强化形态！";
                break;
            case "LightCore":
                message = "&c&l💀【最终阶段】💀 &f光明核心觉醒！准备迎接最终挑战！";
                break;
            default:
                message = "&4&l⚡【新阶段】⚡ &f新的挑战开始了！";
                break;
        }

        for (Player player : arena.getPlayers()) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
            // 播放阶段开始音效
            try {
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 1.2f);
            } catch (Exception e) {
                // 忽略音效错误
            }
        }
    }

    /**
     * 广播Boss死亡消息
     */
    private void broadcastBossDeath(Arena arena, Team killerTeam) {
        String message = terrorMode.getConfig().getString("messages.boss-death", "&a&l【胜利】&f {team} 队伍击杀了Boss！");
        message = message.replace("{team}", killerTeam.getDisplayName());
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

        for (Player player : arena.getPlayers()) {
            player.sendMessage(coloredMessage);
        }
    }

    /**
     * 获取竞技场中存活的Boss数量
     */
    public int getAliveBossCount(Arena arena) {
        List<ActiveMob> bosses = activeBosses.get(arena);
        if (bosses == null) {
            return 0;
        }

        // 清理已死亡的Boss
        bosses.removeIf(boss -> boss.isDead());

        return bosses.size();
    }

    /**
     * 获取竞技场中所有存活的Boss
     */
    public List<ActiveMob> getAliveBosses(Arena arena) {
        List<ActiveMob> bosses = activeBosses.get(arena);
        if (bosses == null) {
            return new ArrayList<>();
        }

        // 清理已死亡的Boss并返回存活的
        bosses.removeIf(boss -> boss.isDead());
        return new ArrayList<>(bosses);
    }

    /**
     * 根据Boss查找竞技场
     */
    public Arena findArenaByBoss(ActiveMob boss) {
        for (Map.Entry<Arena, List<ActiveMob>> entry : activeBosses.entrySet()) {
            if (entry.getValue().contains(boss)) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 设置Boss生成位置
     */
    public void setBossLocation(String arenaName, String locationName, Location location) {
        bossLocations.computeIfAbsent(arenaName, k -> new ArrayList<>()).add(location);
        saveBossLocations();
    }

    /**
     * 移除Boss生成位置
     */
    public void removeBossLocation(String arenaName, int index) {
        List<Location> locations = bossLocations.get(arenaName);
        if (locations != null && index >= 0 && index < locations.size()) {
            locations.remove(index);
            if (locations.isEmpty()) {
                bossLocations.remove(arenaName);
            }
            saveBossLocations();
        }
    }

    /**
     * 获取Boss位置列表
     */
    public List<Location> getBossLocations(String arenaName) {
        return bossLocations.getOrDefault(arenaName, new ArrayList<>());
    }

    /**
     * 保存Boss位置到配置文件
     */
    private void saveBossLocations() {
        FileConfiguration config = terrorMode.getConfig();

        // 清除现有配置
        config.set("boss.locations", null);

        // 保存新配置
        for (Map.Entry<String, List<Location>> entry : bossLocations.entrySet()) {
            List<String> locationStrings = new ArrayList<>();
            for (Location location : entry.getValue()) {
                String locationString = String.format("%s,%.2f,%.2f,%.2f,%.2f,%.2f",
                        location.getWorld().getName(),
                        location.getX(),
                        location.getY(),
                        location.getZ(),
                        location.getYaw(),
                        location.getPitch());
                locationStrings.add(locationString);
            }
            config.set("boss.locations." + entry.getKey(), locationStrings);
        }

        terrorMode.saveConfigPublic();
    }

    /**
     * 清理竞技场
     */
    public void cleanupArena(Arena arena) {
        // 移除所有Boss
        List<ActiveMob> bosses = activeBosses.remove(arena);
        if (bosses != null) {
            for (ActiveMob boss : bosses) {
                // 停止技能任务
                BukkitTask skillTask = bossSkillTasks.remove(boss);
                if (skillTask != null) {
                    skillTask.cancel();
                }

                // 移除Boss实体
                if (!boss.isDead()) {
                    boss.remove();
                }
            }
        }
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        loadBossLocations();
    }

    /**
     * 检查MythicMobs是否可用
     */
    public boolean isMythicMobsEnabled() {
        return mythicMobsEnabled;
    }

    /**
     * 广播Boss虚空返回消息
     */
    private void broadcastBossVoidReturn(Arena arena, String bossType) {
        String message;
        switch (bossType) {
            case "LightHerald1":
                message = terrorMode.getConfig().getString("messages.boss-void-return",
                    "&4&l⚡【恐怖降临】⚡ &c光明先驱从虚空爬了回来！");
                break;
            case "LightHerald2":
                message = terrorMode.getConfig().getString("messages.boss-void-return",
                    "&4&l⚡【恐怖降临】⚡ &c光明先驱(强化)从虚空爬了回来！");
                break;
            case "LightCore":
                message = terrorMode.getConfig().getString("messages.boss-void-return",
                    "&4&l⚡【恐怖降临】⚡ &c光明核心从虚空爬了回来！");
                break;
            default:
                message = terrorMode.getConfig().getString("messages.boss-void-return",
                    "&4&l⚡【恐怖降临】⚡ &cBoss从虚空爬了回来！");
                break;
        }

        for (Player player : arena.getPlayers()) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
        }
    }

    /**
     * 广播阶段完成消息
     */
    private void broadcastPhaseComplete(Arena arena, Team killerTeam, String bossType) {
        plugin.getLogger().info("广播阶段完成消息 - Boss类型: " + bossType + ", 队伍: " + killerTeam.getDisplayName() + ", 竞技场玩家数: " + arena.getPlayers().size());

        String message;
        switch (bossType) {
            case "LightHerald1":
                message = "&a&l🎉【阶段完成】🎉 &f" + killerTeam.getDisplayName() + " 队伍完成了第一阶段！";
                break;
            case "LightHerald2":
                message = "&a&l🎉【阶段完成】🎉 &f" + killerTeam.getDisplayName() + " 队伍完成了第二阶段！";
                break;
            default:
                message = "&a&l🎉【阶段完成】🎉 &f" + killerTeam.getDisplayName() + " 队伍完成了一个阶段！";
                break;
        }

        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);
        plugin.getLogger().info("发送阶段完成消息: " + coloredMessage);

        for (Player player : arena.getPlayers()) {
            player.sendMessage(coloredMessage);
            // 播放阶段完成音效
            try {
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
            } catch (Exception e) {
                // 忽略音效错误
            }
        }

        plugin.getLogger().info("阶段完成消息已发送给 " + arena.getPlayers().size() + " 个玩家");
    }
}
