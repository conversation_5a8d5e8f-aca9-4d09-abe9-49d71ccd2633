package de.marcely.bedwars.api.hook;

import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * Represents an extension handler for third-party plugins by hooking into their feature-set.
 */
public interface Hook {

  /**
   * Get the category of the hook.
   *
   * @return The category of the hook
   */
  HookCategory getCategory();

  /**
   * Get the plugin that is managing and created this hook.
   *
   * @return The plugin that created this hook
   */
  Plugin getManagingPlugin();

  /**
   * Get the plugin into which this hook class is hooking into.
   *
   * @return The plugin that we hook into. May be <code>null</code> if it's not a Bukkit plugin (e.g. if it's being handled by BungeeCord)
   */
  @Nullable
  Plugin getHookedPlugin();

  /**
   * Get whether the hook is currently registered and active.
   *
   * @return <code>true</code> in case the hook is active
   */
  default boolean isActive() {
    return BedwarsAPILayer.INSTANCE.isActive(this);
  }
}
