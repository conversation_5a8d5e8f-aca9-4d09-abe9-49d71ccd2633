package de.marcely.bedwars.tools.location;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.serialization.ConfigurationSerializable;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents a 3-dimensional position with a yaw and pitch direction
 */
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class XYZYP extends XYZ implements Cloneable, ConfigurationSerializable {

  private float yaw, pitch;

  public XYZYP(Location loc) {
    this(loc.getX(), loc.getY(), loc.getZ(), loc.getYaw(), loc.getPitch());
  }

  public XYZYP(Vector vec) {
    this(vec.getX(), vec.getY(), vec.getZ());
  }

  public XYZYP(XYZ xyz) {
    this(xyz.x, xyz.y, xyz.z);
  }

  public XYZYP(XYZYP xyz) {
    this(xyz.x, xyz.y, xyz.z, xyz.yaw, xyz.pitch);
  }

  public XYZYP() {
    this(0, 0, 0, 0F, 0F);
  }

  public XYZYP(double x, double y, double z) {
    this(x, y, z, 0F, 0F);
  }

  public XYZYP(double x, double y, double z, float yaw, float pitch) {
    super(x, y, z);

    this.yaw = yaw;
    this.pitch = pitch;
  }

  @Override
  public XYZYP setX(double x) {
    this.x = x;

    return this;
  }

  @Override
  public XYZYP setY(double y) {
    this.y = y;

    return this;
  }

  @Override
  public XYZYP setZ(double z) {
    this.z = z;

    return this;
  }

  @Override
  public XYZYP set(double x, double y, double z) {
    this.x = x;
    this.y = y;
    this.z = z;

    return this;
  }

  @Override
  public XYZYP set(XYZ xyz) {
    this.x = xyz.x;
    this.y = xyz.y;
    this.z = xyz.z;

    return this;
  }

  @Override
  public XYZYP set(Location loc) {
    this.x = loc.getX();
    this.y = loc.getY();
    this.z = loc.getZ();
    this.yaw = loc.getYaw();
    this.pitch = loc.getPitch();

    return this;
  }

  @Override
  public XYZYP add(double x, double y, double z) {
    this.x += x;
    this.y += y;
    this.z += z;

    return this;
  }

  @Override
  public XYZYP add(XYZ xyz) {
    this.x += xyz.x;
    this.y += xyz.y;
    this.z += xyz.z;

    return this;
  }

  @Override
  public XYZYP add(Location loc) {
    this.x += loc.getX();
    this.y += loc.getY();
    this.z += loc.getZ();

    return this;
  }

  @Override
  public XYZYP add(Vector vec) {
    this.x += vec.getX();
    this.y += vec.getY();
    this.z += vec.getZ();

    return this;
  }

  @Override
  public XYZYP subtract(double x, double y, double z) {
    this.x -= x;
    this.y -= y;
    this.z -= z;

    return this;
  }

  @Override
  public XYZYP subtract(XYZ xyz) {
    this.x -= xyz.x;
    this.y -= xyz.y;
    this.z -= xyz.z;

    return this;
  }

  @Override
  public XYZYP subtract(Location loc) {
    this.x -= loc.getX();
    this.y -= loc.getY();
    this.z -= loc.getZ();

    return this;
  }

  @Override
  public XYZYP subtract(Vector vec) {
    this.x -= vec.getX();
    this.y -= vec.getY();
    this.z -= vec.getZ();

    return this;
  }

  @Override
  public XYZYP multiply(double amount) {
    this.x *= amount;
    this.y *= amount;
    this.z *= amount;

    return this;
  }

  @Override
  public XYZYP multiply(double x, double y, double z) {
    this.x *= x;
    this.y *= y;
    this.z *= z;

    return this;
  }

  @Override
  public XYZYP zero() {
    this.x = 0;
    this.y = 0;
    this.z = 0;

    return this;
  }

  @Override
  public Location toLocation(World world) {
    return new Location(world, this.x, this.y, this.z, this.yaw, this.pitch);
  }

  /**
   * Copies and sets the xyz coordinates and yaw/pitch coordinates from the given object
   *
   * @param xyz The object from which it shall be taken from
   * @return This XYZ instance
   */
  public XYZYP set(XYZYP xyz) {
    this.x = xyz.x;
    this.y = xyz.y;
    this.z = xyz.z;
    this.yaw = xyz.yaw;
    this.pitch = xyz.pitch;

    return this;
  }

  public float getYaw() {
    return this.yaw;
  }

  public float getPitch() {
    return this.pitch;
  }

  public XYZYP setYaw(float yaw) {
    this.yaw = yaw;

    return this;
  }

  public XYZYP setPitch(float pitch) {
    this.pitch = pitch;

    return this;
  }

  @Override
  public XYZYP clone() {
    return new XYZYP(this.x, this.y, this.z, this.yaw, this.pitch);
  }

  @Override
  public Map<String, Object> serialize() {
    final Map<String, Object> data = new HashMap<String, Object>();

    data.put("x", this.x);
    data.put("y", this.y);
    data.put("z", this.z);
    data.put("yaw", this.yaw);
    data.put("pitch", this.pitch);

    return data;
  }

  /**
   * Required method for deserialization
   *
   * @param data map to deserialize
   * @return deserialized xyz instance
   * @see ConfigurationSerializable
   */
  public static XYZYP deserialize(Map<String, Object> data) {
    return new XYZYP(
        NumberConversions.toDouble(data.get("x")),
        NumberConversions.toDouble(data.get("y")),
        NumberConversions.toDouble(data.get("z")),
        NumberConversions.toFloat(data.get("yaw")),
        NumberConversions.toFloat(data.get("pitch")));
  }
}
