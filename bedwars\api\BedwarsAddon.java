package de.marcely.bedwars.api;

import de.marcely.bedwars.api.command.CommandsCollection;
import de.marcely.bedwars.api.event.AddonRegisterEvent;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.io.File;

/**
 * Represents as an addon for this plugin.
 * <p>
 * Plugins may not use the addons feature, but we suggest to use it to keep everything organized.
 */
@SuppressWarnings("deprecation")
public abstract class BedwarsAddon {

  private final Plugin plugin;

  private CommandsCollection commandsCollection;

  /**
   * @param plugin The plugin that constructs it
   */
  public BedwarsAddon(Plugin plugin) {
    this.plugin = plugin;
  }

  /**
   * Returns the plugin that's the owner of this addon.
   *
   * @return The plugin behind this addon.
   */
  public final Plugin getPlugin() {
    return this.plugin;
  }

  /**
   * Returns the name of this addon.
   * <p>
   * By default it uses the {@link #getPlugin()}'s name and cuts of "MBedwars_" (if it's in the name).
   * Plugins are able to provide a custom name by simply overriding this method.
   * <p>
   * Try to keep it unique as addons with equal names won't get registered.
   *
   * @return The name of this addon
   */
  public String getName() {
    return this.plugin.getName().replaceFirst("MBedwars_", "");
  }

  /**
   * Returns the version of this addon.
   * <p>
   * By default it's using the version of {@link #getPlugin()}, but it's safe for plugins to override this method.
   *
   * @return The version of this addon
   */
  public String getVersion() {
    return this.plugin.getDescription().getVersion();
  }

  /**
   * Returns the authors that created the addon.
   * <p>
   * By default it's using the authors provided by {@link #getPlugin()}, but it's safe for plugins to override this method.
   *
   * @return The authors that created this addon
   */
  public String[] getAuthors() {
    return this.plugin.getDescription().getAuthors().stream().toArray(String[]::new);
  }

  /**
   * Returns the CommandsCollection that's bound to this addon.
   * Might return <code>null</code> if the addon isn't registered.
   * <p>
   * Addons may add their own commands to this CommandsCollection.
   * You may also add it somewhere else, but we suggest you to add it here to keep everything in order.
   * <p>
   * It's safe for addons to replace this method and to just use an other collection.
   *
   * @return <code>null</code> if it's not registered, otherwise the CommandsCollection which contains all the admin commands of this addon
   */
  public @Nullable CommandsCollection getCommandsRoot() {
    if (this.commandsCollection == null)
      return this.commandsCollection = BedwarsAPILayer.INSTANCE.getAddonCommandsCollection(this);
    else if (isRegistered())
      return this.commandsCollection;
    else
      return null;
  }

  /**
   * Returns the data folder of this addon.
   * <p>
   * We suggest you to not override it to keep everything in order, but otherwise it's safe for addons to override it.
   *
   * @return The folder which contains all the data (e.g. configs) of this plugin
   */
  public File getDataFolder() {
    return BedwarsAPILayer.INSTANCE.getAddonDataFolder(this);
  }

  /**
   * Tries to register this addon.
   * <p>
   * Returns <code>false</code> if there's already an addon registered under this name or when it got cancelled during {@link AddonRegisterEvent}.
   *
   * @return <code>true</code> if it was successful
   * @throws IllegalStateException If there are issues with some of the properties of this addon
   */
  public final boolean register() {
    return BedwarsAPILayer.INSTANCE.registerAddon(this);
  }

  /**
   * Tries to unregister this addon.
   *
   * @return <code>false</code> if it's already not registered
   */
  public final boolean unregister() {
    return BedwarsAPILayer.INSTANCE.unregisterAddon(this);
  }

  /**
   * Returns whether or not this addon is registered and ready to be used.
   *
   * @return <code>true</code> if it's registered
   */
  public final boolean isRegistered() {
    return BedwarsAPILayer.INSTANCE.isRegisteredAddon(this);
  }
}
