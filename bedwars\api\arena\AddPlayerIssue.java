package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.tools.Validate;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.Nullable;

/**
 * Reasons why a player failed to enter an arena
 */
public class AddPlayerIssue {

  /**
   * Player failed to enter since the arena is already full
   */
  public static final AddPlayerIssue FULL = constructByKey(
      "bedwars:full", "JoinMessage_full");

  /**
   * Task failed since he's already inside an arena.
   */
  public static final AddPlayerIssue ALREADY_INSIDE = constructByKey(
      "bedwars:already_inside", "JoinMessage_alreadyInside");

  /**
   * Arena is a part of a voting pool.
   *
   * @see Arena#getParticipatingVotingPool()
   */
  public static final AddPlayerIssue VOTING_PARTICIPATING = constructByKey(
      "bedwars:voting_participating", "Arena_JoinIssue_VotingParticipating");

  /**
   * Beta mode is enabled and player doesn't have the {@link de.marcely.bedwars.api.Permissions#BETA_USER} permission.
   */
  public static final AddPlayerIssue NOT_BETA_USER = constructByKey(
      "bedwars:not_beta_user", "Only_BetaMember");

  /**
   * He left the server during the join-process (e.g. if a plugin kicked him during {@link de.marcely.bedwars.api.event.player.PlayerJoinArenaEvent}.
   */
  public static final AddPlayerIssue LEFT_SERVER = constructByKey(
      "bedwars:left_server", null);

  /**
   * There is no lobby we can teleport to.
   * <p>
   *   One reason might be that its world has been unloaded.
   * </p>
   */
  public static final AddPlayerIssue BROKEN_LOBBY = constructByKey(
      "bedwars:broken_lobby", null);

  private static final Map<String, AddPlayerIssue> defaults = new HashMap<>();

  private final String id;
  private final Message hintMessage;

  static {
    for (AddPlayerIssue issue : getDefaults())
      defaults.put(issue.getId(), issue);
  }

  private AddPlayerIssue(String id, @Nullable Message hintMessage) {
    Validate.notNull(id, "id");

    this.id = id;
    this.hintMessage = hintMessage != null ? hintMessage.cloneNonUpcyable() : null;
  }

  /**
   * Get the id of this issue that was passed during construction.
   * <p>
   *   The id should match the scenario of the issue, and never be random.
   * </p>
   *
   * @return The id of this issue
   */
  public String getId() {
    return this.id;
  }

  /**
   * Get the hint message that has been passed during the construction.
   *
   * @return The original hint message instance
   */
  public Message getUnformattedHintMessage() {
    return this.hintMessage;
  }

  /**
   * Returns the message that will be displayed to the player.
   * <p>
   * May be <code>null</code> when none will be displayed in the given case.
   * </p>
   *
   * @param arena The arena to which the player has been added
   * @return The message that will get displayed to the player. May be <code>null</code>
   */
  @Nullable
  public Message getHintMessage(Arena arena) {
    Validate.notNull(arena, "arena");

    return getHintMessage(arena.asRemote());
  }

  /**
   * Returns the message that will be displayed to the player.
   * <p>
   * May be <code>null</code> when none will be displayed in the given case.
   * </p>
   *
   * @param arena The arena to which the player has been added
   * @return The message that will get displayed to the player. May be <code>null</code>
   */
  @Nullable
  public Message getHintMessage(RemoteArena arena) {
    Validate.notNull(arena, "arena");

    if (this.hintMessage == null)
      return null;

    return this.hintMessage.clone()
        .placeholder("arena", arena.getDisplayName());
  }

  /**
   * Checks if the comparing object is equal to this issue.
   * <p>
   *   Two issues are equal if their ids are equal.
   * </p>
   *
   * @param obj The object to compare to
   * @return <code>true</code> if the object is equal to this issue
   */
  @Override
  public boolean equals(Object obj) {
    if (obj == this)
      return true;
    if (!(obj instanceof AddPlayerIssue))
      return false;

    return ((AddPlayerIssue) obj).id.equals(this.id);
  }

  /**
   * Returns the hash code of this issue.
   * <p>
   *   The hash code is based on the id of this issue.
   * </p>
   *
   * @return The hash code of this issue
   */
  @Override
  public int hashCode() {
    return this.id.hashCode();
  }

  /**
   * Constructs a new {@link AddPlayerIssue} with the given id and hint message.
   * <p>
   *   The id should match the scenario of the issue, and never be random.
   *   Its main purpose is to make it possible for plugins to identify custom issues.
   * </p>
   *
   * @param id The id of this issue
   * @param hintMessage Optionally, a message that will be displayed to the player
   * @return The constructed issue
   */
  public static AddPlayerIssue construct(String id, @Nullable String hintMessage) {
    return construct(id, hintMessage != null ? Message.build(hintMessage) : null);
  }

  /**
   * Constructs a new {@link AddPlayerIssue} with the given id and hint message.
   * <p>
   *   The id should match the scenario of the issue, and never be random.
   *   Its main purpose is to make it possible for plugins to identify custom issues.
   * </p>
   * <p>
   *   Note in case the id matches a default one: The default instance will be returned instead.
   *   The hintMessage has no affect in that case. This is to keep backwards compatibility for == matching.
   * </p>
   *
   * @param id The id of this issue
   * @param hintMessage Optionally, a message that will be displayed to the player
   * @return The constructed issue
   */
  public static AddPlayerIssue construct(String id, @Nullable Message hintMessage) {
    final AddPlayerIssue def = defaults.get(id);

    if (def != null)
      return def;

    return new AddPlayerIssue(id, hintMessage != null ? hintMessage : null);
  }

  private static AddPlayerIssue constructByKey(String id, @Nullable String hintMessage) {
    return new AddPlayerIssue(id, hintMessage != null ? Message.buildByKey(hintMessage) : null);
  }

  /**
   * Returns a collection of all default issues that are present with this class.
   *
   * @return A collection of all default issues that this plugin may make use of
   */
  public static Collection<AddPlayerIssue> getDefaults() {
    if (!defaults.isEmpty())
      return Collections.unmodifiableCollection(defaults.values());

    final List<AddPlayerIssue> issues = new ArrayList<>();

    try {
      for (Field f : AddPlayerIssue.class.getFields()) {
        if (Modifier.isStatic(f.getModifiers()) && f.getType() == AddPlayerIssue.class)
          issues.add((AddPlayerIssue) f.get(null));
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }

    return issues;
  }
}