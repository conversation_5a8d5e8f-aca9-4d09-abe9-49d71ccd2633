package cn.acebrand.acevotemode.commands;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.LuckyEventManager;
import cn.acebrand.acevotemode.events.good.ItemRewardEvent;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

/**
 * 幸运方块测试指令
 * 用法: /luckytest <事件类型> <奖励名称> [玩家]
 */
public class LuckyTestCommand implements CommandExecutor, TabCompleter {

    private final AceVoteMode plugin;
    private final Map<String, FileConfiguration> eventConfigs = new HashMap<>();

    public LuckyTestCommand(AceVoteMode plugin) {
        this.plugin = plugin;
        loadEventConfigs();
    }

    /**
     * 加载所有事件配置文件
     */
    private void loadEventConfigs() {
        // 加载好事件配置
        loadEventConfig("good", "item_reward");

        // 加载坏事件配置
        loadEventConfig("bad", "lightning_strike");
        loadEventConfig("bad", "creeper_swarm");
        loadEventConfig("bad", "instant_explosion");
        loadEventConfig("bad", "monster_horde");
        loadEventConfig("bad", "negative_buff");
        loadEventConfig("bad", "anvil_rain");
        loadEventConfig("bad", "cobweb_trap");
        loadEventConfig("bad", "launch_player");
        loadEventConfig("bad", "confiscate_item");
        loadEventConfig("bad", "iron_cage");
        loadEventConfig("bad", "obsidian_cage");
        loadEventConfig("bad", "tnt_spawn");
        // 移除了重复的debuff_effect，保留negative_buff

        // 加载中立事件配置
        loadEventConfig("neutral", "teleport");
        loadEventConfig("neutral", "weather_change");
        loadEventConfig("neutral", "sound_effect");
    }

    /**
     * 加载单个事件配置文件
     */
    private void loadEventConfig(String eventType, String eventName) {
        try {
            String configPath = "events/" + eventType + "/" + eventName + ".yml";
            InputStream configStream = plugin.getResource(configPath);
            if (configStream != null) {
                FileConfiguration config = YamlConfiguration.loadConfiguration(new InputStreamReader(configStream));
                eventConfigs.put(eventType + "_" + eventName, config);
                plugin.getLogger().info("已加载事件配置: " + configPath);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("加载事件配置失败: " + eventType + "_" + eventName + " - " + e.getMessage());
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 检查权限
        if (!sender.hasPermission("acevotemode.admin")) {
            sender.sendMessage("§c你没有权限使用此指令！");
            return true;
        }

        // 检查参数
        if (args.length < 2) {
            sender.sendMessage("§c用法: /luckytest <事件类型> <奖励名称> [玩家]");
            sender.sendMessage("§7事件类型: good, bad, neutral");
            sender.sendMessage("§7示例: /luckytest good random_sword");
            return true;
        }

        String eventType = args[0].toLowerCase();
        String rewardName = args[1].toLowerCase();

        // 确定目标玩家
        Player targetPlayer;
        if (args.length >= 3) {
            targetPlayer = plugin.getServer().getPlayer(args[2]);
            if (targetPlayer == null) {
                sender.sendMessage("§c玩家 " + args[2] + " 不在线！");
                return true;
            }
        } else if (sender instanceof Player) {
            targetPlayer = (Player) sender;
        } else {
            sender.sendMessage("§c控制台必须指定目标玩家！");
            return true;
        }

        // 执行测试
        boolean success = executeTest(eventType, rewardName, targetPlayer, sender);

        if (success) {
            sender.sendMessage("§a成功为玩家 " + targetPlayer.getName() + " 测试了奖励: " + eventType + "_" + rewardName);
        } else {
            sender.sendMessage("§c测试失败！请检查事件类型和奖励名称是否正确。");
        }

        return true;
    }

    /**
     * 执行测试
     */
    private boolean executeTest(String eventType, String rewardName, Player targetPlayer, CommandSender sender) {
        try {
            switch (eventType) {
                case "good":
                    return testGoodEvent(rewardName, targetPlayer, sender);
                case "bad":
                    return testBadEvent(rewardName, targetPlayer, sender);
                case "neutral":
                    return testNeutralEvent(rewardName, targetPlayer, sender);
                default:
                    sender.sendMessage("§c无效的事件类型: " + eventType);
                    return false;
            }
        } catch (Exception e) {
            sender.sendMessage("§c测试执行失败: " + e.getMessage());
            plugin.getLogger().warning("测试执行失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试好事件
     */
    private boolean testGoodEvent(String rewardName, Player targetPlayer, CommandSender sender) {
        // 测试物品奖励事件
        if (rewardName.equals("item_reward") || isItemReward(rewardName)) {
            if (rewardName.equals("item_reward")) {
                // 测试随机物品奖励
                ItemRewardEvent itemEvent = new ItemRewardEvent(plugin);
                itemEvent.execute(targetPlayer, targetPlayer.getLocation());
                sender.sendMessage("§a已为玩家 " + targetPlayer.getName() + " 触发随机物品奖励");
            } else {
                // 测试指定的物品奖励
                boolean success = testSpecificItemReward(rewardName, targetPlayer, sender);
                if (success) {
                    sender.sendMessage("§a已为玩家 " + targetPlayer.getName() + " 触发指定物品奖励: " + rewardName);
                } else {
                    sender.sendMessage("§c测试指定物品奖励失败: " + rewardName);
                    return false;
                }
            }
            return true;
        }

        sender.sendMessage("§c暂不支持测试此好事件: " + rewardName);
        return false;
    }

    /**
     * 测试指定的物品奖励
     */
    private boolean testSpecificItemReward(String rewardName, Player targetPlayer, CommandSender sender) {
        try {
            // 创建一个特殊的ItemRewardEvent实例来测试指定奖励
            TestItemRewardEvent testEvent = new TestItemRewardEvent(plugin, rewardName);
            testEvent.execute(targetPlayer, targetPlayer.getLocation());
            return true;
        } catch (Exception e) {
            sender.sendMessage("§c测试失败: " + e.getMessage());
            plugin.getLogger().warning("测试指定物品奖励失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试坏事件
     */
    private boolean testBadEvent(String rewardName, Player targetPlayer, CommandSender sender) {
        Location location = targetPlayer.getLocation();

        try {
            // 根据事件名称执行对应的坏事件
            switch (rewardName) {
                case "lightning_strike":
                    new cn.acebrand.acevotemode.events.bad.LightningStrikeEvent(plugin).execute(targetPlayer, location);
                    break;
                case "creeper_swarm":
                    new cn.acebrand.acevotemode.events.bad.CreeperSwarmEvent(plugin).execute(targetPlayer, location);
                    break;
                case "instant_explosion":
                    new cn.acebrand.acevotemode.events.bad.InstantExplosionEvent(plugin).execute(targetPlayer,
                            location);
                    break;
                case "monster_horde":
                    new cn.acebrand.acevotemode.events.bad.MonsterHordeEvent(plugin).execute(targetPlayer, location);
                    break;
                case "negative_buff":
                    new cn.acebrand.acevotemode.events.bad.NegativeBuffEvent(plugin).execute(targetPlayer, location);
                    break;
                case "anvil_rain":
                    new cn.acebrand.acevotemode.events.bad.AnvilRainEvent(plugin).execute(targetPlayer, location);
                    break;
                case "cobweb_trap":
                    new cn.acebrand.acevotemode.events.bad.CobwebTrapEvent(plugin).execute(targetPlayer, location);
                    break;
                case "launch_player":
                    new cn.acebrand.acevotemode.events.bad.LaunchPlayerEvent(plugin).execute(targetPlayer, location);
                    break;
                case "confiscate_item":
                    new cn.acebrand.acevotemode.events.bad.ConfiscateItemEvent(plugin).execute(targetPlayer, location);
                    break;
                case "iron_cage":
                    new cn.acebrand.acevotemode.events.bad.IronCageEvent(plugin).execute(targetPlayer, location);
                    break;
                case "obsidian_cage":
                    new cn.acebrand.acevotemode.events.bad.ObsidianCageEvent(plugin).execute(targetPlayer, location);
                    break;
                case "tnt_spawn":
                    new cn.acebrand.acevotemode.events.bad.TntSpawnEvent(plugin).execute(targetPlayer, location);
                    break;
                // 移除了重复的debuff_effect，使用negative_buff代替
                default:
                    sender.sendMessage("§c未知的坏事件: " + rewardName);
                    return false;
            }

            sender.sendMessage("§a[测试] §f已对玩家 " + targetPlayer.getName() + " 执行坏事件: " + rewardName);
            return true;

        } catch (Exception e) {
            sender.sendMessage("§c[测试] §f执行坏事件失败: " + e.getMessage());
            plugin.getLogger().warning("测试坏事件失败: " + rewardName + " - " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试中立事件
     */
    private boolean testNeutralEvent(String rewardName, Player targetPlayer, CommandSender sender) {
        Location location = targetPlayer.getLocation();

        try {
            // 根据事件名称执行对应的中立事件
            switch (rewardName) {
                case "nothing_happens":
                    new cn.acebrand.acevotemode.events.neutral.NothingHappensEvent(plugin).execute(targetPlayer,
                            location);
                    break;
                case "resource_clear":
                    new cn.acebrand.acevotemode.events.neutral.ResourceClearEvent(plugin).execute(targetPlayer,
                            location);
                    break;
                case "resource_spawner_monster":
                    new cn.acebrand.acevotemode.events.neutral.ResourceSpawnerMonsterEvent(plugin).execute(targetPlayer,
                            location);
                    break;
                default:
                    sender.sendMessage("§c未知的中立事件: " + rewardName);
                    return false;
            }

            sender.sendMessage("§a[测试] §f已对玩家 " + targetPlayer.getName() + " 执行中立事件: " + rewardName);
            return true;

        } catch (Exception e) {
            sender.sendMessage("§c[测试] §f执行中立事件失败: " + e.getMessage());
            plugin.getLogger().warning("测试中立事件失败: " + rewardName + " - " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否是物品奖励
     */
    private boolean isItemReward(String rewardName) {
        FileConfiguration config = eventConfigs.get("good_item_reward");
        if (config != null) {
            return config.contains("rewards." + rewardName);
        }
        return false;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (!sender.hasPermission("acevotemode.admin")) {
            return completions;
        }

        if (args.length == 1) {
            // 第一个参数：事件类型
            completions.addAll(Arrays.asList("good", "bad", "neutral"));
        } else if (args.length == 2) {
            // 第二个参数：奖励名称
            String eventType = args[0].toLowerCase();
            completions.addAll(getRewardNames(eventType));
        } else if (args.length == 3) {
            // 第三个参数：玩家名称
            for (Player player : plugin.getServer().getOnlinePlayers()) {
                completions.add(player.getName());
            }
        }

        // 过滤匹配的补全
        String currentArg = args[args.length - 1].toLowerCase();
        return completions.stream()
                .filter(completion -> completion.toLowerCase().startsWith(currentArg))
                .sorted()
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取指定事件类型的所有奖励名称
     */
    private List<String> getRewardNames(String eventType) {
        List<String> rewardNames = new ArrayList<>();

        switch (eventType) {
            case "good":
                rewardNames.addAll(getItemRewardNames());
                break;
            case "bad":
                rewardNames.addAll(Arrays.asList("lightning_strike", "creeper_swarm", "instant_explosion",
                        "monster_horde", "negative_buff", "anvil_rain", "cobweb_trap",
                        "launch_player", "confiscate_item", "iron_cage", "obsidian_cage",
                        "tnt_spawn"));
                // 移除了重复的debuff_effect
                break;
            case "neutral":
                rewardNames.addAll(Arrays.asList("nothing_happens", "resource_clear", "resource_spawner_monster"));
                break;
        }

        return rewardNames;
    }

    /**
     * 获取物品奖励的所有名称
     */
    private List<String> getItemRewardNames() {
        List<String> itemRewards = new ArrayList<>();
        FileConfiguration config = eventConfigs.get("good_item_reward");

        if (config != null && config.contains("rewards")) {
            Set<String> keys = config.getConfigurationSection("rewards").getKeys(false);
            itemRewards.addAll(keys);
        }

        // 添加通用的物品奖励事件名称
        itemRewards.add("item_reward");

        return itemRewards;
    }

    /**
     * 测试专用的物品奖励事件类
     */
    private static class TestItemRewardEvent extends ItemRewardEvent {
        private final String specificReward;

        public TestItemRewardEvent(AceVoteMode plugin, String specificReward) {
            super(plugin);
            this.specificReward = specificReward;
        }

        @Override
        public void execute(Player player, Location location) {
            try {
                // 特殊处理instant_buff
                if ("instant_buff".equals(specificReward)) {
                    giveInstantBuff(player);
                    return;
                }

                // 调用真实的奖励方法
                org.bukkit.inventory.ItemStack rewardItem = getSpecificReward(specificReward);

                if (rewardItem != null) {
                    // 掉落物品
                    if (location.getWorld() != null) {
                        location.getWorld().dropItemNaturally(location, rewardItem);
                    } else {
                        player.getInventory().addItem(rewardItem);
                    }

                    String itemName = rewardItem.getItemMeta() != null && rewardItem.getItemMeta().hasDisplayName()
                            ? rewardItem.getItemMeta().getDisplayName()
                            : rewardItem.getType().name();
                    player.sendMessage("§a[测试] §f你获得了: " + itemName);
                } else {
                    player.sendMessage("§c[测试] §f无法生成奖励: " + specificReward);
                }

            } catch (Exception e) {
                player.sendMessage("§c[测试] §f测试奖励生成失败: " + e.getMessage());
            }
        }

        /**
         * 获取指定的奖励物品
         */
        private org.bukkit.inventory.ItemStack getSpecificReward(String rewardName) {
            // 根据奖励名称调用对应的方法
            switch (rewardName.toLowerCase()) {
                case "random_sword":
                    return getRandomSword();
                case "knockback_stick":
                    return getKnockbackStick();
                case "special_fishing_rod":
                    return getSpecialFishingRod();
                case "divine_sword":
                    return getDivineSword();
                case "instant_kill_axe":
                    return getInstantKillAxe();
                case "pigeon_eggs":
                    return getPigeonEggs();
                case "normal_bow":
                    return getNormalBow();
                case "power_bow":
                    return getPowerBow();
                case "divine_bow":
                    return getDivineBow();
                case "arrows":
                    return getArrows();
                case "random_tool":
                    return getRandomTool();
                case "world_eater":
                    return getWorldEater();
                case "tnt":
                    return getTNT();
                case "fire_charge":
                    return getFireCharge();
                case "ender_pearl":
                    return getEnderPearl();
                case "cobweb":
                    return getCobweb();
                case "water_bucket":
                    return getWaterBucket();
                case "steak":
                    return getSteak();
                case "golden_apple":
                    return getGoldenApple();
                case "rescue_platform":
                    return getRescuePlatform();
                case "bridge_egg":
                    return getBridgeEgg();
                case "iron_golem_guard":
                    return getIronGolemGuard();
                case "silverfish":
                    return getSilverfish();
                case "tnt_sheep":
                    return getTNTSheep();
                case "beneficial_potion":
                    return getBeneficialPotion();
                case "splash_potion":
                    return getSplashPotion();
                case "instant_buff":
                    // instant_buff是直接生效的，不返回物品，在execute方法中处理
                    return null;
                default:
                    return createTestItem(rewardName);
            }
        }

        /**
         * 创建测试物品（当找不到对应方法时）
         */
        private org.bukkit.inventory.ItemStack createTestItem(String rewardName) {
            org.bukkit.inventory.ItemStack testItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.PAPER);
            org.bukkit.inventory.meta.ItemMeta meta = testItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName("§e未实现的测试奖励: " + rewardName);
                meta.setLore(Arrays.asList("§7这个奖励还没有实现", "§7奖励类型: " + rewardName));
                testItem.setItemMeta(meta);
            }
            return testItem;
        }
    }
}
