package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.MBedwarsProp;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class JumpPadHandler implements SpecialItemUseHandler, Listener {

    private final MBedwarsProp plugin;
    private final Map<UUID, List<Location>> playerJumpPads = new HashMap<>();
    private final Map<Location, Long> jumpPadExpirations = new HashMap<>();
    private final Map<Location, BukkitTask> jumpPadTasks = new HashMap<>();
    private final Map<UUID, Long> lastJumpTime = new HashMap<>(); // 防止连续弹跳
    private final Map<Location, ArmorStand> hologramMap = new HashMap<>(); // 全息显示
    private final Map<Location, BukkitTask> particleTaskMap = new HashMap<>(); // 粒子效果任务

    public JumpPadHandler(MBedwarsProp plugin) {
        this.plugin = plugin;

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        // 启动定期清理过期跳跃平台的任务
        new BukkitRunnable() {
            @Override
            public void run() {
                cleanupExpiredJumpPads();
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒检查一次
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        // 检查玩家是否踩在跳跃平台上
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();

        // 如果玩家只是转头，不是移动位置，则忽略
        if (from.getBlockX() == to.getBlockX() &&
                from.getBlockY() == to.getBlockY() &&
                from.getBlockZ() == to.getBlockZ())
            return;

        // 获取玩家脚下的方块
        Block block = player.getLocation().subtract(0, 1, 0).getBlock();
        Location blockLoc = block.getLocation();

        // 检查该方块是否是跳跃平台
        if (jumpPadExpirations.containsKey(blockLoc)) {
            // 防止连续弹跳（冷却时间0.5秒）
            long currentTime = System.currentTimeMillis();
            if (lastJumpTime.containsKey(player.getUniqueId()) &&
                    currentTime - lastJumpTime.get(player.getUniqueId()) < 500) {
                return;
            }

            // 获取玩家所在竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(player);
            if (arena == null)
                return;

            // 计算跳跃向量
            Vector jumpVector = player.getLocation().getDirection().setY(0).normalize();
            jumpVector.setY(1.0); // 向上的基础力量
            jumpVector.multiply(2.0); // 调整跳跃力度

            // 应用跳跃效果
            player.setVelocity(jumpVector);

            // 播放声音
            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_SLIME_JUMP, 1.0f, 1.0f);

            // 记录最后跳跃时间
            lastJumpTime.put(player.getUniqueId(), currentTime);
        }
    }

    // 清理过期的跳跃平台
    private void cleanupExpiredJumpPads() {
        long currentTime = System.currentTimeMillis();
        List<Location> expiredPads = new ArrayList<>();

        for (Map.Entry<Location, Long> entry : jumpPadExpirations.entrySet()) {
            if (currentTime > entry.getValue()) {
                expiredPads.add(entry.getKey());
            }
        }

        for (Location loc : expiredPads) {
            removeJumpPad(loc);
        }
    }

    // 创建全息显示
    private void createHologram(Location location) {
        // 先检查是否已经存在全息显示，如果存在则移除
        if (hologramMap.containsKey(location)) {
            ArmorStand existingHologram = hologramMap.get(location);
            if (existingHologram != null && !existingHologram.isDead()) {
                existingHologram.remove();
            }
            hologramMap.remove(location);
        }

        // 清除该位置附近的所有盾牌实体（防止重复）
        World world = location.getWorld();
        if (world != null) {
            for (Entity entity : world.getNearbyEntities(location.clone().add(0.5, 2.0, 0.5), 1, 1, 1)) {
                if (entity instanceof ArmorStand && entity.getCustomName() != null &&
                        entity.getCustomName().equals("§a跳跃平台")) {
                    entity.remove();
                }
            }
        }

        // 创建全息显示的位置（在跳跃平台上方）
        Location holoLoc = location.clone().add(0.5, 2.0, 0.5); // Y轴高出2格

        // 创建隐形盾牌实体
        ArmorStand hologram = (ArmorStand) location.getWorld().spawnEntity(holoLoc, EntityType.ARMOR_STAND);
        hologram.setVisible(false); // 隐形
        hologram.setGravity(false); // 无重力
        hologram.setCustomName("§a跳跃平台"); // 设置显示名称
        hologram.setCustomNameVisible(true); // 显示名称
        hologram.setMarker(true); // 标记模式，不可交互
        hologram.setSmall(true); // 设置为小型盾牌实体

        // 存储全息显示实体
        hologramMap.put(location, hologram);
    }

    // 创建粒子效果
    private void createParticleEffect(Location location) {
        // 创建定期任务显示粒子
        BukkitTask particleTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!jumpPadExpirations.containsKey(location)) {
                    cancel(); // 如果跳跃平台已经不存在，取消任务
                    return;
                }

                // 在跳跃平台上方显示粒子
                Location particleLoc = location.clone().add(0.5, 0.2, 0.5);
                World world = location.getWorld();

                if (world != null) {
                    // 显示粒子效果
                    world.spawnParticle(Particle.VILLAGER_HAPPY, particleLoc, 5, 0.4, 0.2, 0.4, 0.01);
                }
            }
        }.runTaskTimer(plugin, 0L, 10L); // 每0.5秒显示一次

        // 存储粒子效果任务
        particleTaskMap.put(location, particleTask);
    }

    // 移除跳跃平台
    private void removeJumpPad(Location location) {
        // 取消相关任务
        if (jumpPadTasks.containsKey(location)) {
            jumpPadTasks.get(location).cancel();
            jumpPadTasks.remove(location);
        }

        // 取消粒子效果任务
        if (particleTaskMap.containsKey(location)) {
            particleTaskMap.get(location).cancel();
            particleTaskMap.remove(location);
        }

        // 移除全息显示
        if (hologramMap.containsKey(location)) {
            ArmorStand hologram = hologramMap.get(location);
            if (hologram != null && !hologram.isDead()) {
                hologram.remove();
            }
            hologramMap.remove(location);
        }

        // 移除过期时间记录
        jumpPadExpirations.remove(location);

        // 恢复方块（如果世界有效）
        World world = location.getWorld();
        if (world != null) {
            Block block = location.getBlock();
            if (block.getType() == Material.SLIME_BLOCK) {
                // 播放声音
                world.playSound(location, Sound.BLOCK_SLIME_BLOCK_BREAK, 1.0f, 1.0f);

                // 恢复原始方块
                block.setType(Material.AIR);
            }
        }

        // 从玩家记录中移除
        for (List<Location> locations : playerJumpPads.values()) {
            locations.remove(location);
        }
    }

    private class Session extends SpecialItemUseSession {

        private List<Location> padLocations = new ArrayList<>();
        private BukkitTask expirationTask;

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            if (this.expirationTask != null && !this.expirationTask.isCancelled()) {
                this.expirationTask.cancel();
            }
        }

        public void run() {
            Player player = getEvent().getPlayer();
            Block clickedBlock = getEvent().getClickedBlock();

            // 检查是否点击了方块
            if (clickedBlock == null) {
                player.sendMessage("§c请点击一个方块来放置跳跃平台！");
                stop();
                return;
            }

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(player);
            if (arena == null) {
                player.sendMessage("§c你必须在游戏中才能使用此道具！");
                stop();
                return;
            }

            // 设置跳跃平台持续时间
            int duration = 30; // 默认30秒

            // 放置跳跃平台
            Location centerLocation = clickedBlock.getLocation().add(0, 1, 0);
            World world = centerLocation.getWorld();

            // 创建跳跃平台
            int radius = 1;
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    // 如果使用半径大于1，可以创建圆形而不是方形
                    if (radius > 1 && x * x + z * z > radius * radius) {
                        continue; // 跳过角落，创建圆形
                    }

                    Location padLocation = centerLocation.clone().add(x, 0, z);
                    Block padBlock = padLocation.getBlock();

                    // 检查方块是否可以被替换
                    if (padBlock.getType() == Material.AIR) {
                        // 放置跳跃平台方块
                        padBlock.setType(Material.SLIME_BLOCK);

                        // 标记为玩家放置的方块（这样游戏结束时会被清理）
                        arena.setBlockPlayerPlaced(padBlock, true);

                        // 记录跳跃平台位置
                        padLocations.add(padLocation);

                        // 设置过期时间
                        long expirationTime = System.currentTimeMillis() + (duration * 1000L);
                        jumpPadExpirations.put(padLocation, expirationTime);

                        // 创建全息显示
                        createHologram(padLocation);

                        // 创建粒子效果
                        createParticleEffect(padLocation);
                    }
                }
            }

            // 如果没有成功放置任何方块
            if (padLocations.isEmpty()) {
                player.sendMessage("§c无法在此处放置跳跃平台！");
                stop();
                return;
            }

            // 记录玩家的跳跃平台
            if (!playerJumpPads.containsKey(player.getUniqueId())) {
                playerJumpPads.put(player.getUniqueId(), new ArrayList<>());
            }
            playerJumpPads.get(player.getUniqueId()).addAll(padLocations);

            // 创建过期任务
            this.expirationTask = new BukkitRunnable() {
                @Override
                public void run() {
                    for (Location padLocation : padLocations) {
                        removeJumpPad(padLocation);
                    }
                }
            }.runTaskLater(plugin, duration * 20L);

            // 为每个跳跃平台方块记录任务
            for (Location padLocation : padLocations) {
                jumpPadTasks.put(padLocation, this.expirationTask);
            }

            // 播放放置声音
            world.playSound(centerLocation, Sound.BLOCK_SLIME_BLOCK_PLACE, 1.0f, 1.0f);

            // 显示成功消息
            player.sendMessage("§a成功放置跳跃平台！持续时间: " + duration + "秒");

            // 消耗道具
            takeItem();

            // 结束会话
            stop();
        }
    }
}
