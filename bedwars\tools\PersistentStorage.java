package de.marcely.bedwars.tools;

import java.util.Set;

/**
 * Represents a String-Map object for attaching custom and persistent data to its holding object
 */
public interface PersistentStorage extends StringMapSerializationHelper {

  /**
   * Gets all the keys that exist within this storage.
   *
   * @return All known keys
   */
  Set<String> getKeys();

  /**
   * Set an entry to the internal map.
   * <p>
   *   It is not possible to have multiple entries with the same key.
   *   Existing ones automatically get replaced with this new one.
   * </p>
   * <p>
   *   Following limitations exist:<br>
   *   - Key may not be longer than 255 characters<br>
   *   - Key must only persist of the following characters: <code>a-Z 0-9 ?!_-'#:@</code><br>
   *   - Value may not be longer than 65534 characters
   * </p>
   * @throws IllegalArgumentException If the limitations aren't met
   *
   * @param key The key of the value with which you may identify the value later on
   * @param value The value that you want to store
   */
  void set(String key, String value);

  /**
   * Removes everything we know about a given key-value pair.
   *
   * @param key The key that we want to remove
   * @return <code>true</code> if it has been found and removed
   */
  boolean remove(String key);

  /**
   * Forcefully save the storage.
   * <p>
   *   Depending on the implementation of the object holding the storage,
   *   it may also cause the holding object to be stored. The meaning of this
   *   method does not change because of that.
   * </p>
   */
  void saveAsync();


  /**
   * Classes implementing this, add the possibility of attaching custom and persistent data to it
   */
  public static interface Holder {

    /**
     * Get the persistent storage of this object.
     *
     * @return The persistent storage
     */
    public PersistentStorage getPersistentStorage();
  }
}
