package cn.acebrand.mbedwarsprop.items;

import cn.acebrand.mbedwarsprop.config.ItemConfigManager;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.player.PlayerUseSpecialItemEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseHandler;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

public class EmeraldOreHandler implements SpecialItemUseHandler, Listener {

    private final Plugin plugin;
    private final ItemConfigManager.ItemConfig config;
    private final Map<Location, UUID> oreLocations = new HashMap<>();
    private final Random random = new Random();

    public EmeraldOreHandler(Plugin plugin, ItemConfigManager.ItemConfig config) {
        this.plugin = plugin;
        this.config = config;

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    @Override
    public Plugin getPlugin() {
        return this.plugin;
    }

    @Override
    public SpecialItemUseSession openSession(PlayerUseSpecialItemEvent event) {
        // 创建会话
        final Session session = new Session(event);

        // 运行会话
        session.run();

        return session;
    }

    private class Session extends SpecialItemUseSession {

        private Block placedBlock;

        public Session(PlayerUseSpecialItemEvent event) {
            super(event);
        }

        @Override
        protected void handleStop() {
            if (this.placedBlock == null)
                return;

            // 如果会话被强制停止，清理方块
            oreLocations.remove(this.placedBlock.getLocation());
            this.placedBlock.setType(Material.AIR);
        }

        protected void run() {
            final Block clickedBlock = getEvent().getClickedBlock();
            final BlockFace clickedBlockFace = getEvent().getClickedBlockFace();

            // 检查是否有有效的目标方块
            if (clickedBlock == null || clickedBlockFace != BlockFace.UP) {
                getEvent().getPlayer().sendMessage("§c请指向一个有效的方块顶部来放置绿宝石矿石！");
                stop();
                return;
            }

            // 获取放置位置（目标方块上方）
            Block placeBlock = clickedBlock.getRelative(clickedBlockFace);

            // 检查放置位置是否为空气
            if (placeBlock.getType() != Material.AIR) {
                getEvent().getPlayer().sendMessage("§c无法在此处放置绿宝石矿石！");
                stop();
                return;
            }

            // 获取竞技场
            Arena arena = GameAPI.get().getArenaByPlayer(getEvent().getPlayer());
            if (arena == null) {
                stop();
                return;
            }

            // 放置绿宝石矿石
            placeBlock.setType(Material.EMERALD_ORE);
            this.placedBlock = placeBlock;

            // 记录矿石位置和所有者
            oreLocations.put(placeBlock.getLocation(), getEvent().getPlayer().getUniqueId());

            // 标记方块为玩家放置，这样游戏结束时会被清理
            arena.setBlockPlayerPlaced(placeBlock, true);

            getEvent().getPlayer().sendMessage("§a成功放置绿宝石矿石！打破后可获得绿宝石");

            // 消耗物品
            takeItem();
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void onBlockBreak(BlockBreakEvent event) {
        Block block = event.getBlock();

        // 检查是否是我们放置的绿宝石矿石
        if (block.getType() == Material.EMERALD_ORE && oreLocations.containsKey(block.getLocation())) {
            // 取消原始事件，防止方块掉落和经验掉落
            event.setCancelled(true);
            event.setDropItems(false);
            event.setExpToDrop(0);

            // 先保存位置信息，因为我们需要在设置方块为空气后使用
            Location dropLocation = block.getLocation().clone().add(0.5, 0.5, 0.5);

            // 移除方块
            block.setType(Material.AIR);

            // 获取配置的掉落范围
            int minEmeralds = 5;
            int maxEmeralds = 15;

            if (config != null) {
                ItemConfigManager.EffectConfig minConfig = config.getEffect("min-emeralds");
                ItemConfigManager.EffectConfig maxConfig = config.getEffect("max-emeralds");

                if (minConfig != null) {
                    minEmeralds = minConfig.getLevel();
                }

                if (maxConfig != null) {
                    maxEmeralds = maxConfig.getLevel();
                }
            }

            // 确保最小值不大于最大值
            if (minEmeralds > maxEmeralds) {
                minEmeralds = maxEmeralds;
            }

            // 随机生成掉落数量
            int amount = random.nextInt(maxEmeralds - minEmeralds + 1) + minEmeralds;

            // 创建绿宝石物品
            ItemStack emeralds = new ItemStack(Material.EMERALD, amount);

            // 在方块位置掉落绿宝石
            block.getWorld().dropItemNaturally(dropLocation, emeralds);

            // 从记录中移除
            oreLocations.remove(block.getLocation());

            // 通知玩家
            event.getPlayer().sendMessage("§a你获得了 " + amount + " 个绿宝石！");

            // 添加调试日志
            plugin.getLogger().info("玩家 " + event.getPlayer().getName() + " 破坏了绿宝石矿石，掉落了 " + amount + " 个绿宝石");
        }
    }

    // 添加一个额外的事件处理器，确保绿宝石矿石不会掉落矿石本身
    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockBreakMonitor(BlockBreakEvent event) {
        // 如果事件已经被取消，不做处理
        if (event.isCancelled()) {
            return;
        }

        Block block = event.getBlock();

        // 再次检查是否是我们的绿宝石矿石
        if (block.getType() == Material.EMERALD_ORE && oreLocations.containsKey(block.getLocation())) {
            // 强制设置不掉落物品
            event.setDropItems(false);

            // 添加调试日志
            plugin.getLogger().info("在MONITOR事件中再次确认绿宝石矿石不会掉落矿石");
        }
    }
}
