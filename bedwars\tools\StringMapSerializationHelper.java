package de.marcely.bedwars.tools;

import java.util.Optional;
import org.bukkit.inventory.ItemStack;

/**
 * Extends another class with some helper methods to simplify the parsing/writing of non-String types.
 */
public interface StringMapSerializationHelper {

  /**
   * Gets the String value of the internal map.
   *
   * @param key The key that contains to the value
   * @return The value of the key. May be empty if it doesn't exist
   */
  Optional<String> get(String key);

  /**
   * Set an entry to the internal map.
   * <p>
   *   It is not possible to have multiple entries with the same key.
   *   Existing ones automatically get replaced with this new one.
   * </p>
   *
   * @param key The key of the value with which you may identify the value later on
   * @param value The value that you want to store
   */
  void set(String key, String value);

  /**
   * Gets the Integer value of the internal map.
   * <p>
   *   It actually gets stored as a String internally.
   *   {@link Helper#parseInt(String)} is being used to parse it.
   * </p>
   *
   * @param key The key that contains to the value
   * @return The value of the key. May be empty if it doesn't exist
   */
  default Optional<Integer> getInt(String key) {
    final Optional<String> val = get(key);

    if (!val.isPresent())
      return Optional.empty();

    return Optional.ofNullable(Helper.get().parseInt(val.get()));
  }

  /**
   * Gets the Long value of the internal map.
   * <p>
   *   It actually gets stored as a String internally.
   *   {@link Helper#parseLong(String)} (String)} is being used to parse it.
   * </p>
   *
   * @param key The key that contains to the value
   * @return The value of the key. May be empty if it doesn't exist
   */
  default Optional<Long> getLong(String key) {
    final Optional<String> val = get(key);

    if (!val.isPresent())
      return Optional.empty();

    return Optional.ofNullable(Helper.get().parseLong(val.get()));
  }

  /**
   * Gets the Boolean value of the internal map.
   * <p>
   *   It actually gets stored as a String internally.
   *   Some checks are being made to make sure that it's actually a boolean.
   * </p>
   *
   * @param key The key that contains to the value
   * @return The value of the key. May be empty if it doesn't exist
   */
  default Optional<Boolean> getBoolean(String key) {
    final Optional<String> val = get(key);

    if (!val.isPresent())
      return Optional.empty();

    switch (val.get().toLowerCase()) {
      case "true":
      case "1":
        return Optional.of(true);
      case "false":
      case "0":
        return Optional.of(false);
      default:
        return Optional.empty();
    }
  }

  /**
   * Gets the Double value of the internal map.
   * <p>
   *   It actually gets stored as a String internally.
   *   {@link Helper#parseDouble(String)} is being used to parse it.
   * </p>
   *
   * @param key The key that contains to the value
   * @return The value of the key. May be empty if it doesn't exist
   */
  default Optional<Double> getDouble(String key) {
    final Optional<String> val = get(key);

    if (!val.isPresent())
      return Optional.empty();

    return Optional.ofNullable(Helper.get().parseDouble(val.get()));
  }

  /**
   * Gets the ItemStack value of the internal map.
   * <p>
   *   It actually gets stored as a String internally.
   *   {@link Helper#parseItemStack(String)} is being used to parse it.
   * </p>
   *
   * @param key The key that contains to the value
   * @return The value of the key. May be empty if it doesn't exist
   */
  default Optional<ItemStack> getItemStack(String key) {
    final Optional<String> val = get(key);

    if (!val.isPresent())
      return Optional.empty();

    return Optional.ofNullable(Helper.get().parseItemStack(val.get()));
  }

  /**
   * Sets a number as the value for the given key.
   * <p>
   *   It is not possible to have multiple entries with the same key.
   *   Existing ones automatically get replaced with this new one.
   * </p>
   *
   * @param key The key of the value with which you may identify the value later on
   * @param value The value that you want to store
   */
  default void set(String key, Number value) {
    Validate.notNull(value, "value");

    set(key, value.toString());
  }

  /**
   * Sets an ItemStack as the value for the given key.
   * <p>
   *   It is not possible to have multiple entries with the same key.
   *   Existing ones automatically get replaced with this new one.
   * </p>
   *
   * @param key The key of the value with which you may identify the value later on
   * @param value The value that you want to store
   */
  default void set(String key, ItemStack value) {
    Validate.notNull(value, "value");

    set(key, Helper.get().composeItemStack(value));
  }

  /**
   * Sets a boolean as the value for the given key.
   * <p>
   *   It is not possible to have multiple entries with the same key.
   *   Existing ones automatically get replaced with this new one.
   * </p>
   *
   * @param key The key of the value with which you may identify the value later on
   * @param value The value that you want to store
   */
  default void set(String key, boolean value) {
    set(key, Boolean.toString(value));
  }
}
