package de.marcely.bedwars.api.unsafe;

import de.marcely.bedwars.api.message.Message;
import net.md_5.bungee.api.ChatColor;
import org.bukkit.Color;
import org.bukkit.DyeColor;
import org.bukkit.command.CommandSender;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

@Deprecated
public interface TeamWrapper {

  ChatColor getChatColor();

  void setChatColor(ChatColor color);

  DyeColor getDyeColor();

  void setDyeColor(DyeColor color);

  Color getBukkitColor();

  void setBukkitColor(Color color);

  String getInitials();

  String getDisplayName();

  String getDisplayName(@Nullable CommandSender sender);

  Message getNameAsMessage();

  ItemStack newItemInstance(@Nullable CommandSender sender);

  void setEnforcedInitials(@Nullable String initials);

  @Nullable
  String getEnforcedInitials();

  void setConfigName(String configName);

  String getConfigName();
}
