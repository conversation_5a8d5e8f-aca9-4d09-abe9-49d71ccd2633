package de.marcely.bedwars.api.player;

import de.marcely.bedwars.api.unsafe.InitBedwarsAPILayer;

/**
 *
 * Contains all default stats of the bedwars plugin
 */
@SuppressWarnings("deprecation")
public class DefaultPlayerStatSet {

  public static final PlayerStatSet WIN_STREAK = get("win_streak");
  public static final PlayerStatSet TOP_WIN_STREAK = get("top_win_streak");
  public static final PlayerStatSet WINS = get("wins");
  public static final PlayerStatSet LOSES = get("loses");
  public static final PlayerStatSet W_L = get("wl");
  public static final PlayerStatSet KILL_STREAK = get("kill_streak");
  public static final PlayerStatSet TOP_KILL_STREAK = get("top_kill_streak");
  public static final PlayerStatSet KILLS = get("kills");
  public static final PlayerStatSet FINAL_KILLS = get("final_kills");
  public static final PlayerStatSet DEATHS = get("deaths");
  public static final PlayerStatSet FINAL_DEATHS = get("final_deaths");
  public static final PlayerStatSet K_D = get("kd");
  public static final PlayerStatSet FINAL_K_D = get("final_kd");
  public static final PlayerStatSet BEDS_DESTROYED = get("beds_destroyed");
  public static final PlayerStatSet BEDS_LOST = get("beds_lost");
  public static final PlayerStatSet ROUNDS_PLAYED = get("rounds_played");
  public static final PlayerStatSet PLAY_TIME = get("play_time");
  public static final PlayerStatSet RANK = get("rank");

  private DefaultPlayerStatSet() {
  }

  private static PlayerStatSet get(String id) {
    return InitBedwarsAPILayer.INSTANCE.getDefaultPlayerStatSet(id);
  }
}
