package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 铁笼子事件
 * 被关到铁笼子中并在头上生成岩浆
 */
public class IronCageEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private FileConfiguration config;
    private final List<Location> placedBlocks = new ArrayList<>();

    public IronCageEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(badDir, "iron_cage.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/iron_cage.yml", false);
                plugin.getLogger().info("已生成铁笼子事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载铁笼子事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载铁笼子事件配置失败: " + e.getMessage());
        }
    }

    @Override
    public EventType getType() {
        return EventType.BAD;
    }

    @Override
    public String getName() {
        return "IRON_CAGE";
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 25) : 25;
    }

    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }

    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }

        // 获取配置参数
        String cageMaterial = config != null ? config.getString("iron_cage.cage_material", "IRON_BARS") : "IRON_BARS";
        int cageSize = config != null ? config.getInt("iron_cage.cage_size", 3) : 3;
        int cageHeight = config != null ? config.getInt("iron_cage.cage_height", 3) : 3;
        boolean hasRoof = config != null ? config.getBoolean("iron_cage.has_roof", true) : true;
        boolean hasFloor = config != null ? config.getBoolean("iron_cage.has_floor", false) : false;
        int lifetime = config != null ? config.getInt("iron_cage.lifetime", 45) : 45;

        // 获取竞技场
        Arena arena = GameAPI.get().getArenaByPlayer(player);
        if (arena == null) {
            plugin.getLogger().warning("玩家不在竞技场中，无法执行铁笼子事件");
            return;
        }

        // 建造笼子
        buildCage(player, arena, cageMaterial, cageSize, cageHeight, hasRoof, hasFloor);

        // 生成岩浆
        placeLava(player, arena);

        // 应用额外效果
        applyAdditionalEffects(player);

        // 设置清理时间
        if (lifetime > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    cleanupBlocks();
                }
            }.runTaskLater(plugin, lifetime * 20L);
        }

        plugin.getLogger().info("玩家 " + player.getName() + " 触发了铁笼子事件");
    }

    /**
     * 建造笼子
     */
    private void buildCage(Player player, Arena arena, String materialName, int size, int height,
            boolean hasRoof, boolean hasFloor) {
        Location playerLoc = player.getLocation();
        Material material;

        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的笼子材料: " + materialName);
            material = Material.IRON_BARS;
        }

        int halfSize = size / 2;

        // 建造笼子墙壁
        for (int y = 0; y < height; y++) {
            for (int x = -halfSize; x <= halfSize; x++) {
                for (int z = -halfSize; z <= halfSize; z++) {
                    // 只在边缘放置方块
                    if (x == -halfSize || x == halfSize || z == -halfSize || z == halfSize) {
                        Location blockLoc = playerLoc.clone().add(x, y, z);
                        placeBlock(blockLoc, material, arena);
                    }
                }
            }
        }

        // 建造顶部
        if (hasRoof) {
            for (int x = -halfSize; x <= halfSize; x++) {
                for (int z = -halfSize; z <= halfSize; z++) {
                    Location blockLoc = playerLoc.clone().add(x, height, z);
                    placeBlock(blockLoc, material, arena);
                }
            }
        }

        // 建造底部
        if (hasFloor) {
            for (int x = -halfSize; x <= halfSize; x++) {
                for (int z = -halfSize; z <= halfSize; z++) {
                    Location blockLoc = playerLoc.clone().add(x, -1, z);
                    placeBlock(blockLoc, material, arena);
                }
            }
        }
    }

    /**
     * 放置岩浆
     */
    private void placeLava(Player player, Arena arena) {
        if (config == null)
            return;

        if (config.getBoolean("iron_cage.lava.enabled", true)) {
            int heightOffset = config.getInt("iron_cage.lava.height_offset", 2);
            Location lavaLoc = player.getLocation().clone().add(0, heightOffset, 0);

            placeBlock(lavaLoc, Material.LAVA, arena);

            // 启动岩浆伤害任务
            startLavaDamageTask(player);
        }
    }

    /**
     * 启动岩浆伤害任务
     */
    private void startLavaDamageTask(Player player) {
        if (config == null)
            return;

        int damageInterval = config.getInt("iron_cage.lava.damage_interval", 20);
        double damageAmount = config.getDouble("iron_cage.lava.damage_amount", 2.0);
        int lavaLifetime = config.getInt("iron_cage.lava.lifetime", 30);

        new BukkitRunnable() {
            private int ticksRemaining = lavaLifetime * 20;

            @Override
            public void run() {
                if (!player.isOnline() || ticksRemaining <= 0) {
                    cancel();
                    return;
                }

                // 检查玩家是否在岩浆附近
                Location playerLoc = player.getLocation();
                Location lavaLoc = playerLoc.clone().add(0, 2, 0);

                if (lavaLoc.getBlock().getType() == Material.LAVA) {
                    // 对玩家造成伤害
                    player.damage(damageAmount);
                    player.setFireTicks(40); // 点燃2秒
                }

                ticksRemaining -= damageInterval;
            }
        }.runTaskTimer(plugin, damageInterval, damageInterval);
    }

    /**
     * 放置方块（按照BedWars API推荐的方式）
     */
    private void placeBlock(Location location, Material material, Arena arena) {
        Block block = location.getBlock();

        // 只在空气方块中放置
        if (block.getType().isAir()) {
            try {
                // 按照BedWars API文档的推荐方式：先检查是否可以放置
                if (arena.canPlaceBlockAt(location, material)) {
                    // 设置方块类型
                    block.setType(material);

                    // 标记为玩家放置
                    arena.setBlockPlayerPlaced(block, true);

                    // 验证标记是否成功
                    boolean isMarked = arena.isBlockPlayerPlaced(block);
                    plugin.getLogger().info("铁笼子方块放置成功: " + isMarked + " 位置: " + location + " 材质: " + material);

                    // 记录方块位置
                    placedBlocks.add(location);

                } else {
                    // BedWars不允许在此位置放置，使用强制放置
                    plugin.getLogger().info("BedWars限制放置，使用强制放置: " + location + " 材质: " + material);

                    // 直接设置方块类型
                    block.setType(material);

                    // 尝试标记为玩家放置
                    try {
                        arena.setBlockPlayerPlaced(block, true);
                        boolean isMarked = arena.isBlockPlayerPlaced(block);
                        plugin.getLogger().info("强制放置方块标记状态: " + isMarked + " 位置: " + location);
                    } catch (Exception markError) {
                        plugin.getLogger().warning("无法标记强制放置的方块: " + markError.getMessage());
                    }

                    // 记录方块位置
                    placedBlocks.add(location);
                }

            } catch (Exception e) {
                plugin.getLogger().severe("放置铁笼子方块失败: " + e.getMessage());
                e.printStackTrace();

                // 回退方案：直接放置并标记
                try {
                    block.setType(material);
                    arena.setBlockPlayerPlaced(block, true);
                    placedBlocks.add(location);
                    plugin.getLogger().info("使用回退方案放置方块: " + location);
                } catch (Exception fallbackError) {
                    plugin.getLogger().severe("回退方案也失败了: " + fallbackError.getMessage());
                }
            }
        }
    }

    /**
     * 应用额外效果
     */
    private void applyAdditionalEffects(Player player) {
        if (config == null)
            return;

        // 缓慢效果
        if (config.getBoolean("iron_cage.additional_effects.slowness.enabled", true)) {
            int level = config.getInt("iron_cage.additional_effects.slowness.level", 1);
            int duration = config.getInt("iron_cage.additional_effects.slowness.duration", 300);

            PotionEffect slowness = new PotionEffect(PotionEffectType.SLOW, duration, level - 1);
            player.addPotionEffect(slowness);
        }

        // 虚弱效果
        if (config.getBoolean("iron_cage.additional_effects.weakness.enabled", true)) {
            int level = config.getInt("iron_cage.additional_effects.weakness.level", 1);
            int duration = config.getInt("iron_cage.additional_effects.weakness.duration", 300);

            PotionEffect weakness = new PotionEffect(PotionEffectType.WEAKNESS, duration, level - 1);
            player.addPotionEffect(weakness);
        }
    }

    /**
     * 清理方块
     */
    private void cleanupBlocks() {
        for (Location blockLoc : placedBlocks) {
            Block block = blockLoc.getBlock();
            if (block.getType() != Material.AIR) {
                block.setType(Material.AIR);
            }
        }
        placedBlocks.clear();
    }

    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null)
            return true;
        return config.getBoolean("messages.send_message", true);
    }

    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null)
            return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }

    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null)
            return "§c你被困在了铁笼子里！";
        return config.getString("messages.event_message", "§c你被困在了铁笼子里！");
    }
}
