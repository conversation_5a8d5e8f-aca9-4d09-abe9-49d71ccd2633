package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.api.unsafe.TeamWrapper;
import net.md_5.bungee.api.ChatColor;
import org.bukkit.Color;
import org.bukkit.DyeColor;
import org.bukkit.command.CommandSender;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * A list of all possible colors that exist in the game
 */
public enum Team {

  YELLOW,
  ORANGE,
  RED,
  BLUE,
  LIGHT_BLUE,
  CYAN,
  LIGHT_GREEN,
  GRE<PERSON>,
  PURPLE,
  PINK,
  WHITE,
  LIGHT_GRAY,
  GRAY,
  BROWN,
  BLACK;

  /**
   * A list of all possible colors that exist in the game.
   * <p>
   *   Same thing as {@link #values()} but as a constant.
   *   Exists as creating a new copy each time is slow.
   *   Try to avoid changing its values!! This WILL cause problems.
   *   If you are insecure about it, use {@link #values()} instead.
   * </p>
   */
  public static Team[] VALUES = values();

  private final TeamWrapper wrapper = null;

  /**
   * Returns the corresponding ChatColor to this team.
   *
   * @return The ChatColor of the team
   */
  public ChatColor getBungeeChatColor() {
    return this.wrapper.getChatColor();
  }

  /**
   * Set the corresponding ChatColor of this team.
   * <p>
   *     This change is not persistent and will change back after a restart.
   * </p>
   *
   * @param chatColor The new color
   */
  public void setBungeeChatColor(ChatColor chatColor) {
    this.wrapper.setChatColor(chatColor);
  }

  /**
   * Returns the corresponding DyeColor to this team.
   *
   * @return The DyeColor of the team
   */
  public DyeColor getDyeColor() {
    return this.wrapper.getDyeColor();
  }

  /**
   * Set the corresponding DyeColor of this team.
   * <p>
   *     This change is not persistent and will change back after a restart.
   * </p>
   *
   * @param dyeColor The new color
   */
  public void setDyeColor(DyeColor dyeColor) {
    this.wrapper.setDyeColor(dyeColor);
  }

  /**
   * Returns the corresponding Color to this team.
   *
   * @return The color of the team
   */
  public Color getBukkitColor() {
    return this.wrapper.getBukkitColor();
  }

  /**
   * Set the corresponding Color of this team.
   * <p>
   *     This change is not persistent and will change back after a restart.
   * </p>
   *
   * @param color The new color
   */
  public void setBukkitColor(Color color) {
    this.wrapper.setBukkitColor(color);
  }

  /**
   * Returns the initials of the team that shall be shown to players.
   * <p>
   *  In case none has been enforced using {@link #setEnforcedInitials(String)}, it will return the default initials.
   *  Default initials are automatically generated based on the team's name.
   *  Examples of auto generation:<br>
   *  When {@link #RED}: R<br>
   *  When {@link #LIGHT_BLUE}: LB<br>
   *  When {@link #CYAN}: C
   * </p>
   *
   * @return The initials of the team
   */
  public String getInitials() {
    return this.wrapper.getInitials();
  }

  /**
   * Set the initials of the team that shall be shown to players.
   * <p>
   *     In case a non-null value is given, the auto generation of initials will be disabled
   *     and {@link #getInitials()} will return the given value.
   * </p>
   *
   * @param initials The new enforced initials. May be <code>null</code> to reset to auto generation
   * @see #getInitials()
   * @see #setEnforcedInitials(String)
   */
  public void setEnforcedInitials(@Nullable String initials) {
    this.wrapper.setEnforcedInitials(initials);
  }

  /**
   * Returns the enforced initials of the team that shall be shown to players.
   * <p>
   *     In case none has been enforced using {@link #setEnforcedInitials(String)},
   *     it will return <code>null</code>.
   * </p>
   *
   * @return The enforced initials of the team. May be <code>null</code>
   * @see #getInitials()
   * @see #setEnforcedInitials(String)
   */
  public @Nullable String getEnforcedInitials() {
    return this.wrapper.getEnforcedInitials();
  }

  /**
   * Get the name of the team in the default language.
   *
   * @return The configured name of the team in the default language
   */
  public String getDisplayName() {
    return getDisplayName(null);
  }

  /**
   * Get the name of the team in the language of the CommandSender.
   *
   * @param sender The person from which it should look up the language. <code>null</code> if it should take the default language
   * @return The configured name of the team in the language of the CommandSender
   */
  public String getDisplayName(@Nullable CommandSender sender) {
    return this.wrapper.getDisplayName(sender);
  }

  /**
   * Get the original Message instance that is used for the name of the team.
   *
   * @return The Message instance fed with the message entry from the messages files
   */
  public Message getNameAsMessage() {
    return this.wrapper.getNameAsMessage();
  }

  /**
   * Set the formatted name of the team as it is present in the configs.
   *
   * @param configName The new raw name
   */
  public void setConfigName(String configName) {
    this.wrapper.setConfigName(configName);
  }

  /**
   * Get the formatted name of the team as it is present in the configs.
   *
   * @return The raw name
   */
  public String getConfigName() {
    return this.wrapper.getConfigName();
  }

  /**
   * Constructs a new item that's being used for placing the bed during the set-up.
   * <p>
   * Uses the configured language for the name of the item.
   *
   * @return The item that's being used for set-up
   */
  public ItemStack newItemInstance() {
    return newItemInstance(null);
  }

  /**
   * Constructs a new item that's being used for placing the bed during the set-up.
   * <p>
   * The name of the item is being translated into the language of the given sender.
   * Passing <code>null</code> causes it to use the default configured language instead.
   *
   * @param sender Will name the item in the langauge of him
   * @return The item that's being used for set-up
   */
  public ItemStack newItemInstance(@Nullable CommandSender sender) {
    return this.wrapper.newItemInstance(sender);
  }

  /**
   * Looks through all teams and checks which team fits the best to the given name.
   * <p>
   * Ignores upper- and undercase, and some other things for better results.
   *
   * @param name The name of the team it should look up for
   * @return The team that fits the best to the name. Returns null if it hasn't found any
   */
  public static @Nullable Team getByName(String name) {
    return BedwarsAPILayer.INSTANCE.getTeamByName(name);
  }
}