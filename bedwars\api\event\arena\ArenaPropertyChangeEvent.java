package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Gets called whenever a property of an arena has changed.
 */
public class ArenaPropertyChangeEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private final Property property;

  public ArenaPropertyChangeEvent(Arena arena, Property property) {
    this.arena = arena;
    this.property = property;
  }

  @Override
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Gets the property that has changed.
   *
   * @return The involved property
   */
  public Property getProperty() {
    return this.property;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  /**
   * Some properties of the arena.
   */
  public enum Property {

    /**
     * {@link Arena#getRegenerationType()}
     */
    REGENERATION_TYPE,

    /**
     * {@link Arena#getPlayersPerTeam()}
     */
    PLAYERS_PER_TEAM,

    /**
     * {@link Arena#getPlayersPerTeam()}
     */
    MIN_PLAYERS,

    /**
     * {@link Arena#getMaxPlayers()}
     */
    MAX_PLAYERS,

    /**
     * {@link Arena#getEnabledTeams()}
     */
    ENABLED_TEAMS,

    /**
     * {@link Arena#getAuthors()}
     */
    AUTHORS,

    /**
     * {@link Arena#getIcon()}
     */
    ICON,

    /**
     * {@link Arena#getCustomName()}
     */
    CUSTOM_NAME,

    /**
     * {@link Arena#isCustomNameEnabled()}
     */
    CUSTOM_NAME_ACTIVATION_STATE,

    /**
     * {@link Arena#getWeatherType()}
     */
    WEATHER_TYPE,

    /**
     * {@link Arena#getTimeType()}
     */
    TIME_TYPE,

    /**
     * {@link Arena#getGameWorld()}
     */
    GAME_WORLD_NAME,

    /**
     * A plugin manually called it using {@link Arena#broadcastCustomPropertyChange()}
     */
    API_CUSTOM
  }
}
