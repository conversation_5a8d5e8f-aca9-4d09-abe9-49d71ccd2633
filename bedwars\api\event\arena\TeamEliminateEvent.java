package de.marcely.bedwars.api.event.arena;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when all players of a team have been eliminated.
 * <p>
 *   This event only get once per team and only in the following cases:<br>
 *   - The bed has been broken and all players have been excluded from the match<br>
 *   - In solo (1 player per team) matches: Player has left the team (with his bed being alive) and missed the rejoin period<br>
 *   - Bed has been destroyed with no players remaining in that team
 * </p>
 */
public class TeamEliminateEvent extends Event implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Team team;
  private final Player causingPlayer;
  private final Player lastPlayer;
  private final boolean causesEnd;

  public TeamEliminateEvent(
      Arena arena,
      Team team,
      Player causingPlayer,
      Player lastPlayer,
      boolean causesEnd) {

    this.arena = arena;
    this.team = team;
    this.causingPlayer = causingPlayer;
    this.lastPlayer = lastPlayer;
    this.causesEnd = causesEnd;
  }

  /**
   * Returns the team that has been eliminated.
   *
   * @return The team that has been eliminated
   */
  public Team getTeam() {
    return this.team;
  }

  /**
   * Returns the player that is responsible for this event.
   * <p>
   *   May be <code>null</code> if nobody is responsible, as e.g. all players have left the match.
   * </p>
   * <p>
   *   Responsible player may i.a. be:<br>
   *   - The one who killed the last alive player of the team<br>
   *   - The one who broke the bed of the team
   * </p>
   *
   * @return The player who caused the team to get eliminated. May be <code>null</code>
   */
  @Nullable
  public Player getCausingPlayer() {
    return this.causingPlayer;
  }

  /**
   * Returns the player that survived as last in the team that has been eliminated
   * <p>
   *   Is null in case the player is not known, e.g. if the bed is being broken with no player of that team being online.
   * </p>
   *
   * @return The player that died as last. May be <code>null</code>
   */
  @Nullable
  public Player getLastPlayer() {
    return this.lastPlayer;
  }

  /**
   * Returns <code>true</code> if the game will end after this event.
   *
   * @return Whether the game will end after the event
   */
  public boolean causesEnd() {
    return this.causesEnd;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
