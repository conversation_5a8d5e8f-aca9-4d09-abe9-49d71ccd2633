package de.marcely.bedwars.api.event;

import de.marcely.bedwars.api.command.SubCommand;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

import java.util.Arrays;

/**
 * Gets called when a player runs a bedwars subcommand.
 * <p>
 * This event isn't getting called when the user e.g. doesn't have the perms.
 */
public class CommandExecuteEvent extends Event implements Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final CommandSender sender;
  private final SubCommand command;
  private final String label;
  private final String[] args;

  private boolean cancel = false;

  public CommandExecuteEvent(CommandSender sender, SubCommand command, String label, String[] args) {
    this.sender = sender;
    this.command = command;
    this.label = label;
    this.args = args;
  }

  /**
   * Returns the person/sender who executed the command
   *
   * @return The sender who fired the command
   */
  public CommandSender getCommandSender() {
    return this.sender;
  }

  /**
   * The subcommand that has been executed
   *
   * @return The command
   */
  public SubCommand getCommand() {
    return this.command;
  }

  /**
   * Returns the name or alias that was used to access the command
   *
   * @return The label that was used by the sender
   */
  public String getLabel() {
    return this.label;
  }

  /**
   * Returns the arguments that were included by the sender.
   *
   * @return The arguments given by the sender
   */
  public String[] getArguments() {
    return Arrays.copyOf(this.args, this.args.length);
  }

  @Override
  public void setCancelled(boolean bool) {
    this.cancel = bool;
  }

  @Override
  public boolean isCancelled() {
    return this.cancel;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
