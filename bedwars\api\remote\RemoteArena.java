package de.marcely.bedwars.api.remote;

import de.marcely.bedwars.api.arena.*;
import de.marcely.bedwars.api.event.arena.ArenaIssuesCheckEvent;
import de.marcely.bedwars.api.event.remote.RemoteArenaPropertiesChangeEvent;
import de.marcely.bedwars.api.game.spectator.SpectateReason;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.command.CommandSender;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Set;
import java.util.function.Consumer;

/**
 * Represents an arena that is located on another server, or on this server.
 */
public interface RemoteArena {

  /**
   * Returns the unique name of the arena.
   * <p>
   *     This name might has been changed in order to retain compatibility with the other existing arenas.
   *     For instance, it might have a @ prefix to indiciate that it's non-local arena.
   *     It might also contain a # suffix with a number, in case there are multiple arenas with the same name.
   *     Because of that, the name likely differs on other servers.
   * </p>
   * <p>
   *     Use {@link #getRealName()} if you'd like to get the actual name with out the suffixes and prefixes.<br>
   *     Use {@link #getDisplayName()} if you'd like to the name that shall be displayed to players.
   * </p>
   *
   * @return Returns the name of the arena
   */
  String getName();

  /**
   * Returns what {@link Arena#getName()} would return on the server, in which the arena is located at.
   * <p>
   *     Basically, it returns {@link #getName()} without the prefixes and suffixes. If the arena is local, then it's equal to {@link #getName()}.
   * </p>
   *
   * @return The actual name without any suffix
   */
  String getRealName();

  /**
   * Returns the non-unique name of the arena for displaying to players
   * <p>
   *  Uses the custom name if one is configured, otherwise uses {@link #getName()}
   * </p>
   *
   * @return The name of the arena used in displays
   */
  String getDisplayName();

  /**
   * Gets whether the arena is located on our server, or if it's being managed by an other one.
   *
   * @return <code>true</code>: it's being managed by this server
   */
  boolean isLocal();

  /**
   * Gets the arena instance that is being represented by this arena.
   * <p>
   *     Returns <code>null</code> in case it is not local. Use {@link #isLocal()} to check that.
   * </p>
   *
   * @return The local arena. May be <code>null</code>
   */
  @Nullable Arena getLocal();

  /**
   * Gets the server on which the arena is being managed on.
   * <p>
   *     Will return the same as {@link RemoteAPI#getLocalServer()} in case {@link #isLocal()} returns true.
   * </p>
   *
   * @return The server on which the arena is located on
   */
  RemoteServer getRemoteServer();

  /**
   * Returns the current state of the arena
   *
   * @return The current state of the arena
   */
  ArenaStatus getStatus();

  /**
   * Returns the regeneration type aka the form/variant of the arena.
   *
   * @return The regeneration type of the arena
   */
  RegenerationType getRegenerationType();

  /**
   * Returns all the players that are currently playing on this arena.
   * <p>
   *     This does not include spectators.
   * </p>
   *
   * @return A collection of all playing players
   */
  Collection<? extends RemotePlayer> getRemotePlayers();

  /**
   * Returns the amount of players that are currently playing on this arena.
   * <p>
   *     This does not include spectators.
   * </p>
   *
   * @return The amount of players that are playing on this arena
   */
  int getPlayersCount();

  /**
   * Returns the amount of players that can be in a team.
   * <p>
   * 	When the arena is {@link RegenerationType#VOTING} then it'll return the max amount of players in the arena
   * </p>
   *
   * @return The configured amount of players that can be in a team
   */
  int getPlayersPerTeam();

  /**
   * Returns the minimum amount of players that are needed to start match.
   * <p>
   * 	While it's not logical to have less than two, it's still legal to have any number (even one that is a minus number).
   * </p>
   * @return The minimum amount of players needed to start a game
   */
  int getMinPlayers();

  /**
   * Calculates [teams amount] x [players per team] when using a normal arena.
   * <p>
   * 	It'll take the configured one when using a voting arena
   * </p>
   *
   * @return The maximum amount of players who can join at the same time
   */
  int getMaxPlayers();

  /**
   * Returns every team that has been enabled for this arena.
   *
   * @return All added teams
   */
  Set<Team> getEnabledTeams();

  /**
   * Returns the authors of the arena.
   *
   * @return The authors or creators of the arena
   */
  String[] getAuthors();

  /**
   * Returns all authors in a string split up by a comma.
   * <p>
   * 	E.g. it might return: "Notch, Marcel, Obama"<br>
   * 	Returns "nobody" in the language of the sender if there are none
   * </p>
   *
   * @param sender Returns in the language of this sender
   * @return The authors of the arena represented in a string
   */
  String getDisplayedAuthors(@Nullable CommandSender sender);

  /**
   * Returns all authors in a string split up by a comma.
   * <p>
   * 	E.g. it might return: "Notch, Marcel, Obama"<br>
   * 	Returns "nobody" in the configured default language if there are none
   * </p>
   *
   * @return The authors of the arena represented in a string
   */
  String getDisplayedAuthors();

  /**
   * Gets the icon of the arena.
   *
   * @return The icon that's being used in GUIs
   */
  ItemStack getIcon();

  /**
   * Returns the amount of players currently spectating the arena.
   *
   * @return The amount of spectators
   */
  int getSpectatorsCount();

  /**
   * Gets the issues that prevent the arena from running-
   *
   * @param sender Will translate the message into the language of the sender. Passing <code>null</code> causes it to be in the default configured language
   * @return All the issues that prevent it from running. If there are none then there's nothing preventing it.
   */
  Set<ArenaIssuesCheckEvent.Issue> getIssues(@Nullable CommandSender sender);

  /**
   * Gets the issues that prevent the arena from running-
   *
   * @return All the issues that prevent it from running. If there are none then there's nothing preventing it.
   */
  default Set<ArenaIssuesCheckEvent.Issue> getIssues() {
    return getIssues(null);
  }

  /**
   * Returns the name of the world in which the game is located at.
   *
   * @return The configured world name in which the arena is located at
   */
  @Nullable
  String getGameWorldName();

  /**
   * Returns whether this arena has been cloned from another arena.
   *
   * @return <code>true</code> in case this arena has been cloned from another arena
   * @see #getCloneParent()
   * @see #getClones()
   */
  boolean isCloned();

  /**
   * Gets the arena from which this arena has been cloned from.
   * <p>
   *     Returns <code>null</code> when this arena hasn't been cloned.
   *     Do note that this method might return <code>null</code>,
   *     even if {@link #isCloned()} returns <code>true</code>. This is
   *     unlikely in 99% of the cases, but might occur due to latency in
   *     transmission.
   * </p>
   *
   * @return The arena from which this arena has been cloned from. May be <code>null</code>
   * @see #isCloned()
   * @see #getClones()
   */
  @Nullable
  RemoteArena getCloneParent();

  /**
   * Gets all arenas that were cloned using this arena (basically the child arenas).
   *
   * @return All arenas that have been cloned from this arena
   * @see #isCloned()
   * @see #getCloneParent()
   */
  RemoteArena[] getClones();

  /**
   * Adds a player (or multiple players) into the arena.
   *
   * @param info The object containing all the info required
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  void addPlayer(AddRemotePlayerInfo info, @Nullable Consumer<RemotePlayerAddResult> callback);

  /**
   * Adds a player into the arena.
   *
   * @param player The player that shall get added into the arena
   * @param team The team it shall automatically join when entering it. May be <code>null</code>
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  default void addPlayer(RemotePlayer player, @Nullable Team team, @Nullable Consumer<RemotePlayerAddResult> callback) {
    Validate.notNull(player, "player");

    addPlayer(
        new AddRemotePlayerInfo()
            .addPlayer(player)
            .setTargetTeam(team),
        callback
    );
  }

  /**
   * Adds a player into the arena.
   *
   * @param player The player that shall get added into the arena
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  default void addPlayer(RemotePlayer player, @Nullable Consumer<RemotePlayerAddResult> callback) {
    addPlayer(player, null, callback);
  }

  /**
   * Adds a player into the arena.
   *
   * @param player The player that shall get added into the arena
   */
  default void addPlayer(RemotePlayer player) {
    addPlayer(player, null, null);
  }

  /**
   * Adds a player into the arena.
   *
   * @param player The player that shall get added into the arena
   * @param team The team it shall automatically join when entering it. May be <code>null</code>
   */
  default void addPlayer(RemotePlayer player, @Nullable Team team) {
    addPlayer(player, team, null);
  }

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   * @param reason The cause that made him into a spectator
   * @param ignoreArenaStatus <code>true</code>: will be able to be one, even if the arena is not running
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  void addSpectator(RemotePlayer player, SpectateReason reason, boolean ignoreArenaStatus, @Nullable Consumer<RemoteSpectatorAddResult> callback);

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   * @param reason The cause that made him into a spectator
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  default void addSpectator(RemotePlayer player, SpectateReason reason, @Nullable Consumer<RemoteSpectatorAddResult> callback) {
    addSpectator(player, reason, true, callback);
  }

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  default void addSpectator(RemotePlayer player, @Nullable Consumer<RemoteSpectatorAddResult> callback) {
    addSpectator(player, SpectateReason.PLUGIN, true, callback);
  }

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   * @param ignoreArenaStatus <code>true</code>: will be able to be one, even if the arena is not running
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  default void addSpectator(RemotePlayer player, boolean ignoreArenaStatus, Consumer<RemoteSpectatorAddResult> callback) {
    addSpectator(player, SpectateReason.PLUGIN, ignoreArenaStatus, callback);
  }

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   * @param reason The cause that made him into a spectator
   * @param ignoreArenaStatus <code>true</code>: will be able to be one, even if the arena is not running
   */
  default void addSpectator(RemotePlayer player, SpectateReason reason, boolean ignoreArenaStatus) {
    addSpectator(player, reason, ignoreArenaStatus, null);
  }

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   * @param reason The cause that made him into a spectator
   */
  default void addSpectator(RemotePlayer player, SpectateReason reason) {
    addSpectator(player, reason, true, null);
  }

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   */
  default void addSpectator(RemotePlayer player) {
    addSpectator(player, SpectateReason.PLUGIN, true, null);
  }

  /**
   * Adds a spectator into the arena.
   *
   * @param player The player that shall be a spectator
   * @param ignoreArenaStatus <code>true</code>: will be able to be one, even if the arena is not running
   */
  default void addSpectator(RemotePlayer player, boolean ignoreArenaStatus) {
    addSpectator(player, SpectateReason.PLUGIN, ignoreArenaStatus, null);
  }

  /**
   * Teleports a player into the arena. He will not play in it!
   * <p>
   *     Basically the same as /bw arena teleport.
   * </p>
   *
   * @param player The player that shall get moved
   * @param sendMessage Whether a message shall be sent to the player to notify about its success or failure
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  void teleportHere(RemotePlayer player, boolean sendMessage, @Nullable Consumer<Boolean> callback);

  /**
   * Teleports a player into the arena. He will not play in it!
   * <p>
   *     Basically the same as /bw arena teleport.
   * </p>
   *
   * @param player The player that shall get moved
   * @param sendMessage Whether a message shall be sent to the player to notify about its success or failure
   */
  default void teleportHere(RemotePlayer player, boolean sendMessage) {
    teleportHere(player, sendMessage, null);
  }

  /**
   * Teleports a player into the arena. He will not play in it!
   * <p>
   *     Basically the same as /bw arena teleport.
   * </p>
   *
   * @param player The player that shall get moved
   */
  default void teleportHere(RemotePlayer player) {
    teleportHere(player, true, null);
  }

  /**
   * Teleports a player into the arena. He will not play in it!
   * <p>
   *     Basically the same as /bw arena teleport.
   * </p>
   *
   * @param player The player that shall get moved
   * @param callback Gets called to notify you whether it was successful or not. May be <code>null</code>
   */
  default void teleportHere(RemotePlayer player, @Nullable Consumer<Boolean> callback) {
    teleportHere(player, true, callback);
  }

  /**
   * Gets a helper class for storing persistent information for exactly this arena instance.
   * <p>
   *   It may also be used to synchronize between servers.
   * </p>
   *
   * @return The persistant storage of this arena
   */
  ArenaPersistentStorage getPersistentStorage();

  /**
   * Forcefully request the server owing this arena to resend us up-to-date poperty information.
   * <p>
   *   Generally the plugin does this automatically, but you may want to do it manually in some cases.
   *   In case everything went successfull, then {@link RemoteArenaPropertiesChangeEvent} will be called.
   * </p>
   *
   * @param properties The properties that shall be synchronized with our server
   * @throws IllegalArgumentException In case the properties are empty
   * @throws IllegalArgumentException If a given property isn't supported
   */
  void syncProperties(RemoteArenaPropertiesChangeEvent.Property... properties);

  /**
   * Returns whether this arena is still existing.
   *
   * @return <code>true</code> if it still exists
   */
  boolean exists();
}
