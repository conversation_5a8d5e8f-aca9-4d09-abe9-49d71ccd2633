package cn.acebrand.acevotemode.events.bad;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.events.EventType;
import cn.acebrand.acevotemode.events.LuckyEvent;

import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.Random;

/**
 * 闪电劈击事件
 * 对玩家连续劈三次闪电
 */
public class LightningStrikeEvent implements LuckyEvent {

    private final AceVoteMode plugin;
    private final Random random = new Random();
    private FileConfiguration config;

    public LightningStrikeEvent(AceVoteMode plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            // 创建events目录
            File eventsDir = new File(plugin.getDataFolder(), "events");
            if (!eventsDir.exists()) {
                eventsDir.mkdirs();
            }

            File badDir = new File(eventsDir, "bad");
            if (!badDir.exists()) {
                badDir.mkdirs();
            }

            // 配置文件路径
            File configFile = new File(badDir, "lightning_strike.yml");

            // 如果配置文件不存在，从资源文件复制
            if (!configFile.exists()) {
                plugin.saveResource("events/bad/lightning_strike.yml", false);
                plugin.getLogger().info("已生成闪电劈击事件配置文件: " + configFile.getPath());
            }

            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            plugin.getLogger().info("已加载闪电劈击事件配置");

        } catch (Exception e) {
            plugin.getLogger().severe("加载闪电劈击事件配置失败: " + e.getMessage());
        }
    }

    @Override
    public EventType getType() {
        return EventType.BAD;
    }

    @Override
    public String getName() {
        return "LIGHTNING_STRIKE";
    }

    @Override
    public int getWeight() {
        return config != null ? config.getInt("event.weight", 30) : 30;
    }

    /**
     * 检查事件是否启用
     */
    public boolean isEnabled() {
        return config != null ? config.getBoolean("event.enabled", true) : true;
    }

    @Override
    public void execute(Player player, Location location) {
        // 发送事件消息
        if (shouldSendMessage()) {
            String messagePrefix = getMessagePrefix();
            String eventMessage = getEventMessage();
            player.sendMessage(messagePrefix + eventMessage);
        }

        // 获取配置参数
        int strikeCount = config != null ? config.getInt("lightning.strike_count", 3) : 3;
        int strikeInterval = config != null ? config.getInt("lightning.strike_interval", 20) : 20;
        double damage = config != null ? config.getDouble("lightning.damage", 4.0) : 4.0;
        boolean setFire = config != null ? config.getBoolean("lightning.set_fire", true) : true;
        int fireDuration = config != null ? config.getInt("lightning.fire_duration", 100) : 100;
        double positionOffset = config != null ? config.getDouble("lightning.position_offset", 2.0) : 2.0;

        // 开始闪电劈击序列
        new LightningStrikeTask(player, location, strikeCount, strikeInterval,
                damage, setFire, fireDuration, positionOffset).runTaskTimer(plugin, 0L, strikeInterval);

        plugin.getLogger().info("玩家 " + player.getName() + " 触发了闪电劈击事件");
    }

    /**
     * 检查是否应该发送消息
     */
    private boolean shouldSendMessage() {
        if (config == null)
            return true;
        return config.getBoolean("messages.send_message", true);
    }

    /**
     * 获取消息前缀
     */
    private String getMessagePrefix() {
        if (config == null)
            return "§c[幸运方块] §f";
        return config.getString("messages.message_prefix", "§c[幸运方块] §f");
    }

    /**
     * 获取事件消息
     */
    private String getEventMessage() {
        if (config == null)
            return "§c天雷滚滚！你被雷神盯上了！";
        return config.getString("messages.event_message", "§c天雷滚滚！你被雷神盯上了！");
    }

    /**
     * 闪电劈击任务
     */
    private class LightningStrikeTask extends BukkitRunnable {
        private final Player player;
        private final double damage;
        private final boolean setFire;
        private final int fireDuration;
        private final double positionOffset;
        private int remainingStrikes;

        public LightningStrikeTask(Player player, Location baseLocation, int strikeCount,
                int interval, double damage, boolean setFire,
                int fireDuration, double positionOffset) {
            this.player = player;
            this.remainingStrikes = strikeCount;
            this.damage = damage;
            this.setFire = setFire;
            this.fireDuration = fireDuration;
            this.positionOffset = positionOffset;
        }

        @Override
        public void run() {
            if (remainingStrikes <= 0 || !player.isOnline()) {
                cancel();
                return;
            }

            // 计算闪电位置（在玩家周围随机偏移）
            Location strikeLocation = player.getLocation().clone();
            if (positionOffset > 0) {
                double offsetX = (random.nextDouble() - 0.5) * 2 * positionOffset;
                double offsetZ = (random.nextDouble() - 0.5) * 2 * positionOffset;
                strikeLocation.add(offsetX, 0, offsetZ);
            }

            // 劈闪电
            strikeLocation.getWorld().strikeLightning(strikeLocation);

            // 对玩家造成伤害
            if (damage > 0) {
                player.damage(damage);
            }

            // 点燃玩家
            if (setFire && fireDuration > 0) {
                player.setFireTicks(fireDuration);
            }

            remainingStrikes--;

            if (remainingStrikes <= 0) {
                cancel();
            }
        }
    }
}
