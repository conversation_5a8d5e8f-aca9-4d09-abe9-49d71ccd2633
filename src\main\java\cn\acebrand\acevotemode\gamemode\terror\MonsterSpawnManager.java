package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.game.spawner.Spawner;
import org.bukkit.*;
import org.bukkit.attribute.Attribute;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.*;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.EntityTargetEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 怪物生成管理系统
 * 负责在绿宝石和钻石资源点生成怪物
 */
public class MonsterSpawnManager implements Listener {

    private final AceVoteMode plugin;
    private final TerrorDescentMode terrorMode;

    // 活跃的怪物 (竞技场 -> 怪物列表)
    private final Map<Arena, Set<LivingEntity>> activeMonsters = new ConcurrentHashMap<>();

    // 怪物清理任务
    private final Map<LivingEntity, BukkitTask> monsterCleanupTasks = new ConcurrentHashMap<>();

    // 怪物类型权重配置
    private final Map<EntityType, MonsterConfig> monsterConfigs = new HashMap<>();

    public MonsterSpawnManager(AceVoteMode plugin, TerrorDescentMode terrorMode) {
        this.plugin = plugin;
        this.terrorMode = terrorMode;

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        // 加载怪物配置
        loadMonsterConfigs();
    }

    /**
     * 怪物配置类
     */
    private static class MonsterConfig {
        final boolean enabled;
        final int weight;
        final double health;
        final int count;

        MonsterConfig(boolean enabled, int weight, double health, int count) {
            this.enabled = enabled;
            this.weight = weight;
            this.health = health;
            this.count = count;
        }
    }

    /**
     * 加载怪物配置
     */
    private void loadMonsterConfigs() {
        FileConfiguration config = terrorMode.getConfig();

        // 加载各种怪物的配置，包括数量设置
        monsterConfigs.put(EntityType.ZOMBIE, new MonsterConfig(
                config.getBoolean("monsters.types.zombie.enabled", true),
                config.getInt("monsters.types.zombie.weight", 30),
                config.getDouble("monsters.types.zombie.health", 25.0),
                config.getInt("monsters.types.zombie.count", 2)));

        monsterConfigs.put(EntityType.SKELETON, new MonsterConfig(
                config.getBoolean("monsters.types.skeleton.enabled", true),
                config.getInt("monsters.types.skeleton.weight", 25),
                config.getDouble("monsters.types.skeleton.health", 20.0),
                config.getInt("monsters.types.skeleton.count", 2)));

        monsterConfigs.put(EntityType.SPIDER, new MonsterConfig(
                config.getBoolean("monsters.types.spider.enabled", true),
                config.getInt("monsters.types.spider.weight", 20),
                config.getDouble("monsters.types.spider.health", 18.0),
                config.getInt("monsters.types.spider.count", 3)));

        monsterConfigs.put(EntityType.CREEPER, new MonsterConfig(
                config.getBoolean("monsters.types.creeper.enabled", true),
                config.getInt("monsters.types.creeper.weight", 15),
                config.getDouble("monsters.types.creeper.health", 22.0),
                config.getInt("monsters.types.creeper.count", 1)));

        monsterConfigs.put(EntityType.ENDERMAN, new MonsterConfig(
                config.getBoolean("monsters.types.enderman.enabled", true),
                config.getInt("monsters.types.enderman.weight", 10),
                config.getDouble("monsters.types.enderman.health", 35.0),
                config.getInt("monsters.types.enderman.count", 1)));
    }

    /**
     * 在资源点生成怪物
     */
    public void spawnMonstersAtResourcePoints(Arena arena) {
        if (!terrorMode.getConfig().getBoolean("monsters.enabled", true)) {
            return;
        }

        List<Location> spawnerLocations = getResourceSpawnerLocations(arena);
        if (spawnerLocations.isEmpty()) {
            plugin.getLogger().warning("竞技场 " + arena.getName() + " 没有找到资源生成器");
            return;
        }

        int totalMonstersSpawned = 0;
        for (Location spawnerLocation : spawnerLocations) {
            int monstersSpawned = spawnMonsterGroup(arena, spawnerLocation);
            totalMonstersSpawned += monstersSpawned;
        }

        // 广播怪物生成消息
        if (totalMonstersSpawned > 0) {
            broadcastMonsterSpawn(arena, totalMonstersSpawned);
        }

        plugin.getLogger().info("在竞技场 " + arena.getName() + " 的资源点生成了 " + totalMonstersSpawned + " 只怪物");
    }

    /**
     * 获取资源生成器位置
     */
    private List<Location> getResourceSpawnerLocations(Arena arena) {
        List<Location> locations = new ArrayList<>();
        FileConfiguration config = terrorMode.getConfig();

        // 目标资源类型
        Set<String> targetTypes = new HashSet<>();
        if (config.getBoolean("monsters.spawn-on-emerald", true)) {
            targetTypes.add("emerald");
        }
        if (config.getBoolean("monsters.spawn-on-diamond", true)) {
            targetTypes.add("diamond");
        }

        // 收集所有符合条件的生成器
        for (Spawner spawner : arena.getSpawners()) {
            DropType dropType = spawner.getDropType();
            String dropTypeId = dropType.getId();

            if (targetTypes.contains(dropTypeId)) {
                Location location = spawner.getLocation().toLocation(arena.getGameWorld());
                locations.add(location);
            }
        }

        return locations;
    }

    /**
     * 在指定位置生成怪物群
     */
    private int spawnMonsterGroup(Arena arena, Location spawnerLocation) {
        FileConfiguration config = terrorMode.getConfig();
        double spawnRadius = config.getDouble("monsters.spawn-radius", 5.0);

        int spawnedCount = 0;

        // 根据怪物类型配置生成不同数量的怪物
        for (Map.Entry<EntityType, MonsterConfig> entry : monsterConfigs.entrySet()) {
            MonsterConfig monsterConfig = entry.getValue();
            if (!monsterConfig.enabled) {
                continue;
            }

            EntityType monsterType = entry.getKey();
            int monsterCount = monsterConfig.count;

            // 根据权重决定是否生成这种怪物
            if (shouldSpawnMonsterType(monsterType)) {
                for (int i = 0; i < monsterCount; i++) {
                    Location spawnLocation = getRandomLocationAround(spawnerLocation, spawnRadius);
                    if (spawnLocation != null) {
                        LivingEntity monster = spawnSpecificMonster(spawnLocation, monsterType);
                        if (monster != null) {
                            setupMonster(arena, monster);
                            spawnedCount++;
                        }
                    }
                }
            }
        }

        return spawnedCount;
    }

    /**
     * 获取周围随机位置
     */
    private Location getRandomLocationAround(Location center, double radius) {
        Random random = new Random();

        for (int attempts = 0; attempts < 10; attempts++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = random.nextDouble() * radius;

            double x = center.getX() + Math.cos(angle) * distance;
            double z = center.getZ() + Math.sin(angle) * distance;

            Location testLocation = new Location(center.getWorld(), x, center.getY(), z);

            // 寻找安全的Y坐标
            for (int y = (int) center.getY(); y <= center.getY() + 10; y++) {
                testLocation.setY(y);
                if (testLocation.getBlock().getType() == Material.AIR &&
                        testLocation.clone().add(0, 1, 0).getBlock().getType() == Material.AIR &&
                        testLocation.clone().subtract(0, 1, 0).getBlock().getType().isSolid()) {
                    return testLocation;
                }
            }
        }

        return center.clone().add(0, 1, 0); // 如果找不到合适位置，就在原地上方生成
    }

    /**
     * 根据权重决定是否生成这种怪物
     */
    private boolean shouldSpawnMonsterType(EntityType monsterType) {
        MonsterConfig config = monsterConfigs.get(monsterType);
        if (config == null || !config.enabled) {
            return false;
        }

        // 根据权重计算生成概率
        int totalWeight = monsterConfigs.values().stream()
                .filter(c -> c.enabled)
                .mapToInt(c -> c.weight)
                .sum();

        if (totalWeight <= 0) {
            return false;
        }

        // 计算这种怪物的生成概率
        double probability = (double) config.weight / totalWeight;
        return Math.random() < probability;
    }

    /**
     * 生成指定类型的怪物
     */
    private LivingEntity spawnSpecificMonster(Location location, EntityType monsterType) {
        try {
            Entity entity = location.getWorld().spawnEntity(location, monsterType);
            if (entity instanceof LivingEntity) {
                return (LivingEntity) entity;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("生成怪物失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 生成随机怪物
     */
    private LivingEntity spawnRandomMonster(Location location) {
        EntityType selectedType = selectRandomMonsterType();
        if (selectedType == null) {
            return null;
        }

        return spawnSpecificMonster(location, selectedType);
    }

    /**
     * 根据权重选择随机怪物类型
     */
    private EntityType selectRandomMonsterType() {
        List<EntityType> availableTypes = new ArrayList<>();
        List<Integer> weights = new ArrayList<>();

        for (Map.Entry<EntityType, MonsterConfig> entry : monsterConfigs.entrySet()) {
            MonsterConfig config = entry.getValue();
            if (config.enabled) {
                availableTypes.add(entry.getKey());
                weights.add(config.weight);
            }
        }

        if (availableTypes.isEmpty()) {
            return null;
        }

        // 计算总权重
        int totalWeight = weights.stream().mapToInt(Integer::intValue).sum();
        int randomValue = new Random().nextInt(totalWeight);

        // 选择怪物类型
        int currentWeight = 0;
        for (int i = 0; i < availableTypes.size(); i++) {
            currentWeight += weights.get(i);
            if (randomValue < currentWeight) {
                return availableTypes.get(i);
            }
        }

        return availableTypes.get(0); // 备用选择
    }

    /**
     * 设置怪物属性
     */
    private void setupMonster(Arena arena, LivingEntity monster) {
        FileConfiguration config = terrorMode.getConfig();

        // 设置生命值
        MonsterConfig monsterConfig = monsterConfigs.get(monster.getType());
        if (monsterConfig != null) {
            try {
                monster.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(monsterConfig.health);
                monster.setHealth(monsterConfig.health);
            } catch (Exception e) {
                // 兼容旧版本
                monster.setMaxHealth(monsterConfig.health);
                monster.setHealth(monsterConfig.health);
            }
        }

        // 设置名称标签
        if (config.getBoolean("monsters.show-name-tags", true)) {
            String namePrefix = config.getString("monsters.name-prefix", "&c恐怖");
            String monsterName = ChatColor.translateAlternateColorCodes('&', namePrefix) +
                    getMonsterDisplayName(monster.getType());
            monster.setCustomName(monsterName);
            monster.setCustomNameVisible(true);
        }

        // 设置白天不燃烧
        if (!config.getBoolean("monsters.burn-in-daylight", false)) {
            if (monster instanceof Zombie || monster instanceof Skeleton) {
                // 对于会在白天燃烧的怪物，给予防火效果
                monster.setFireTicks(0);
                // 可以考虑给予永久的防火药水效果
            }
        }

        // 添加到活跃怪物列表
        activeMonsters.computeIfAbsent(arena, k -> ConcurrentHashMap.newKeySet()).add(monster);

        // 设置生存时间
        int lifetime = config.getInt("monsters.lifetime", 300); // 5分钟
        if (lifetime > 0) {
            BukkitTask cleanupTask = new BukkitRunnable() {
                @Override
                public void run() {
                    if (!monster.isDead()) {
                        removeMonster(arena, monster);
                    }
                }
            }.runTaskLater(plugin, lifetime * 20L);

            monsterCleanupTasks.put(monster, cleanupTask);
        }

        // 设置怪物AI目标
        setupMonsterAI(arena, monster);
    }

    /**
     * 获取怪物显示名称
     */
    private String getMonsterDisplayName(EntityType type) {
        switch (type) {
            case ZOMBIE:
                return "僵尸";
            case SKELETON:
                return "骷髅";
            case SPIDER:
                return "蜘蛛";
            case CREEPER:
                return "苦力怕";
            case ENDERMAN:
                return "末影人";
            default:
                return "怪物";
        }
    }

    /**
     * 设置怪物AI
     */
    private void setupMonsterAI(Arena arena, LivingEntity monster) {
        // 让怪物攻击附近的玩家
        if (monster instanceof Creature) {
            Creature creature = (Creature) monster;

            // 寻找最近的玩家作为目标
            Player nearestPlayer = findNearestPlayer(arena, monster.getLocation());
            if (nearestPlayer != null) {
                creature.setTarget(nearestPlayer);
            }
        }
    }

    /**
     * 寻找最近的玩家
     */
    private Player findNearestPlayer(Arena arena, Location location) {
        Player nearest = null;
        double nearestDistance = Double.MAX_VALUE;

        for (Player player : arena.getPlayers()) {
            if (player.isOnline() && !player.isDead()) {
                double distance = player.getLocation().distance(location);
                if (distance < nearestDistance) {
                    nearest = player;
                    nearestDistance = distance;
                }
            }
        }

        return nearest;
    }

    /**
     * 广播怪物生成消息
     */
    private void broadcastMonsterSpawn(Arena arena, int count) {
        String message = terrorMode.getConfig().getString("messages.monster-spawn", "&e&l【警告】&f 资源点出现了恐怖怪物！");
        message = message.replace("{count}", String.valueOf(count));
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

        for (Player player : arena.getPlayers()) {
            player.sendMessage(coloredMessage);
        }
    }

    /**
     * 监听怪物死亡事件
     */
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        Arena arena = findArenaByMonster(entity);

        if (arena != null) {
            removeMonster(arena, entity);
        }
    }

    /**
     * 监听怪物目标事件
     */
    @EventHandler
    public void onEntityTarget(EntityTargetEvent event) {
        if (!(event.getEntity() instanceof LivingEntity)) {
            return;
        }

        LivingEntity monster = (LivingEntity) event.getEntity();
        Arena arena = findArenaByMonster(monster);

        if (arena != null && event.getTarget() instanceof Player) {
            Player target = (Player) event.getTarget();

            // 确保怪物只攻击同一竞技场的玩家
            if (!arena.getPlayers().contains(target)) {
                event.setCancelled(true);
            }
        }
    }

    /**
     * 根据怪物查找竞技场
     */
    private Arena findArenaByMonster(LivingEntity monster) {
        for (Map.Entry<Arena, Set<LivingEntity>> entry : activeMonsters.entrySet()) {
            if (entry.getValue().contains(monster)) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 移除怪物
     */
    private void removeMonster(Arena arena, LivingEntity monster) {
        // 从活跃列表中移除
        Set<LivingEntity> monsters = activeMonsters.get(arena);
        if (monsters != null) {
            monsters.remove(monster);
        }

        // 取消清理任务
        BukkitTask cleanupTask = monsterCleanupTasks.remove(monster);
        if (cleanupTask != null) {
            cleanupTask.cancel();
        }

        // 移除实体
        if (!monster.isDead()) {
            monster.remove();
        }
    }

    /**
     * 清空所有怪物（Boss被击杀时调用）
     */
    public void clearAllMonsters(Arena arena) {
        Set<LivingEntity> monsters = activeMonsters.get(arena);
        if (monsters != null) {
            int clearedCount = 0;
            for (LivingEntity monster : new ArrayList<>(monsters)) {
                removeMonster(arena, monster);
                clearedCount++;
            }

            plugin.getLogger().info("已清空竞技场 " + arena.getName() + " 的 " + clearedCount + " 只怪物");
        }
    }

    /**
     * 清理竞技场的所有怪物
     */
    public void cleanupArena(Arena arena) {
        Set<LivingEntity> monsters = activeMonsters.remove(arena);
        if (monsters != null) {
            for (LivingEntity monster : monsters) {
                // 取消清理任务
                BukkitTask cleanupTask = monsterCleanupTasks.remove(monster);
                if (cleanupTask != null) {
                    cleanupTask.cancel();
                }

                // 移除实体
                if (!monster.isDead()) {
                    monster.remove();
                }
            }
        }
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        loadMonsterConfigs();
    }

    /**
     * 获取竞技场活跃怪物数量
     */
    public int getActiveMonsterCount(Arena arena) {
        Set<LivingEntity> monsters = activeMonsters.get(arena);
        return monsters != null ? monsters.size() : 0;
    }
}
