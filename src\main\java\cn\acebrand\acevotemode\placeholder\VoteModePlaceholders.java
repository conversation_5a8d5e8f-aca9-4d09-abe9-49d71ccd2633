package cn.acebrand.acevotemode.placeholder;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.UnlimitedFireMode;
import cn.acebrand.acevotemode.gamemode.LowFireMode;
import cn.acebrand.acevotemode.manager.GameModeManager;
import cn.acebrand.acevotemode.manager.GlobalUpgradeManager;
import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

/**
 * AceVoteMode的PlaceholderAPI支持
 */
public class VoteModePlaceholders extends PlaceholderExpansion {

    private final AceVoteMode plugin;

    public VoteModePlaceholders(AceVoteMode plugin) {
        this.plugin = plugin;
    }

    @Override
    public @NotNull String getIdentifier() {
        return "acevotemode";
    }

    @Override
    public @NotNull String getAuthor() {
        return plugin.getDescription().getAuthors().toString();
    }

    @Override
    public @NotNull String getVersion() {
        return plugin.getDescription().getVersion();
    }

    @Override
    public boolean persist() {
        return true;
    }

    @Override
    public String onRequest(OfflinePlayer offlinePlayer, @NotNull String params) {
        if (offlinePlayer == null || !offlinePlayer.isOnline()) {
            return "";
        }

        Player player = (Player) offlinePlayer;
        Arena arena = BedwarsAPI.getGameAPI().getArenaByPlayer(player);

        if (arena == null) {
            arena = BedwarsAPI.getGameAPI().getArenaBySpectator(player);
        }

        switch (params.toLowerCase()) {
            case "next_upgrade": {
                // 智能检测当前模式并显示相应的升级倒计时
                if (arena == null || arena.getStatus() != ArenaStatus.RUNNING) {
                    return "&f加载中...";
                }

                return getNextUpgradeInfo(arena);
            }

            case "next_upgrade_name": {
                // 下一个升级事件的名称（智能检测模式）
                if (arena == null || arena.getStatus() != ArenaStatus.RUNNING) {
                    return "";
                }

                return getNextUpgradeName(arena);
            }

            case "next_upgrade_time": {
                // 下一个升级事件的时间（仅时间）
                if (arena == null || arena.getStatus() != ArenaStatus.RUNNING) {
                    return "";
                }

                UnlimitedFireMode.UpgradeState upgradeState = UnlimitedFireMode.getUpgradeState(arena);
                if (upgradeState == null) {
                    return "--:--";
                }

                // 检查是否还有升级事件
                UnlimitedFireMode.UpgradeInfo nextUpgrade = upgradeState.getCurrentUpgrade();
                if (nextUpgrade == null) {
                    return "--:--";
                }

                int totalSeconds = upgradeState.getSecondsToNextUpgrade();
                if (totalSeconds <= 0) {
                    return "00:00";
                }

                int min = totalSeconds / 60;
                String sec = String.valueOf(totalSeconds - (min * 60));
                if (sec.length() == 1) {
                    sec = "0" + sec;
                }

                return min + ":" + sec;
            }

            default:
                return null;
        }
    }

    /**
     * 获取资源显示名称
     */
    private String getResourceDisplayName(String resourceType) {
        switch (resourceType.toLowerCase()) {
            case "diamond":
                return "钻石";
            case "emerald":
                return "绿宝石";
            case "iron":
                return "铁锭";
            case "gold":
                return "金锭";
            default:
                return resourceType;
        }
    }

    /**
     * 智能获取下一个升级信息（根据当前模式）
     */
    private String getNextUpgradeInfo(Arena arena) {
        // 检测当前竞技场使用的模式
        GameModeManager gameModeManager = plugin.getGameModeManager();
        if (gameModeManager != null) {
            String currentMode = gameModeManager.getCurrentMode(arena);

            if ("unlimited-fire".equals(currentMode)) {
                // 无限火力模式：使用无限火力的升级系统
                return getUnlimitedFireUpgradeInfo(arena);
            } else if ("low-fire".equals(currentMode)) {
                // 火力不足模式：使用火力不足的升级系统
                return getLowFireUpgradeInfo(arena);
            } else {
                // 其他模式：使用全局升级系统
                return getGlobalUpgradeInfo(arena);
            }
        }

        // 如果无法检测模式，尝试全局升级系统
        return getGlobalUpgradeInfo(arena);
    }

    /**
     * 获取无限火力模式的升级信息
     */
    private String getUnlimitedFireUpgradeInfo(Arena arena) {
        try {
            // 这里需要访问无限火力模式的升级状态
            // 由于架构限制，我们需要通过反射或者添加公共接口
            UnlimitedFireMode.UpgradeState upgradeState = UnlimitedFireMode.getUpgradeState(arena);
            if (upgradeState == null) {
                return "§fWaiting...";
            }

            UnlimitedFireMode.UpgradeInfo nextUpgrade = upgradeState.getCurrentUpgrade();
            if (nextUpgrade == null) {
                return "§7暂无升级事件";
            }

            int totalSeconds = upgradeState.getSecondsToNextUpgrade();
            if (totalSeconds <= 0) {
                return "§6正在升级...";
            }

            // 检查是否是自定义事件
            if (!"upgrade".equals(nextUpgrade.eventType)) {
                return formatCustomEventInfo(nextUpgrade.eventType, totalSeconds);
            }

            return formatUpgradeInfo(nextUpgrade.resourceType, nextUpgrade.tier, totalSeconds);
        } catch (Exception e) {
            return "§7升级信息获取失败";
        }
    }

    /**
     * 获取火力不足模式的升级信息
     */
    private String getLowFireUpgradeInfo(Arena arena) {
        try {
            LowFireMode.UpgradeState upgradeState = LowFireMode.getUpgradeState(arena);
            if (upgradeState == null) {
                return "§fWaiting...";
            }

            LowFireMode.UpgradeInfo nextUpgrade = upgradeState.getCurrentUpgrade();
            if (nextUpgrade == null) {
                return "§7暂无升级事件";
            }

            int totalSeconds = upgradeState.getSecondsToNextUpgrade();
            if (totalSeconds <= 0) {
                return "§6正在升级...";
            }

            // 检查是否是自定义事件
            if (!"upgrade".equals(nextUpgrade.eventType)) {
                return formatCustomEventInfo(nextUpgrade.eventType, totalSeconds);
            }

            return formatUpgradeInfo(nextUpgrade.resourceType, nextUpgrade.tier, totalSeconds);
        } catch (Exception e) {
            return "§7升级信息获取失败";
        }
    }

    /**
     * 获取全局升级信息
     */
    private String getGlobalUpgradeInfo(Arena arena) {
        try {
            GlobalUpgradeManager globalUpgradeManager = plugin.getGlobalUpgradeManager();
            if (globalUpgradeManager == null) {
                return "§7暂无升级事件";
            }

            // 获取下一个全局升级事件
            GlobalUpgradeManager.UpgradeInfo nextUpgrade = globalUpgradeManager.getNextUpgrade(arena);
            if (nextUpgrade == null) {
                return "§7暂无升级事件";
            }

            int totalSeconds = globalUpgradeManager.getSecondsToNextUpgrade(arena);
            if (totalSeconds <= 0) {
                return "§6正在升级...";
            }

            // 检查是否是自定义事件
            if (!"upgrade".equals(nextUpgrade.eventType)) {
                return formatCustomEventInfo(nextUpgrade.eventType, totalSeconds);
            }

            return formatUpgradeInfo(nextUpgrade.resourceType, nextUpgrade.tier, totalSeconds);
        } catch (Exception e) {
            return "§7升级信息获取失败";
        }
    }

    /**
     * 格式化升级信息显示
     */
    private String formatUpgradeInfo(String resourceType, int tier, int totalSeconds) {
        int min = totalSeconds / 60;
        String sec = String.valueOf(totalSeconds - (min * 60));
        if (sec.length() == 1) {
            sec = "0" + sec;
        }

        String timeFormat = min + ":" + sec;
        String resourceName = getResourceDisplayName(resourceType);
        String tierName = resourceName + " " + getTierRoman(tier);

        // 中文化格式：钻石 II 在 3:45
        return "§f" + tierName + " §7在 §a" + timeFormat;
    }

    /**
     * 格式化自定义事件信息显示
     */
    private String formatCustomEventInfo(String eventType, int totalSeconds) {
        int min = totalSeconds / 60;
        String sec = String.valueOf(totalSeconds - (min * 60));
        if (sec.length() == 1) {
            sec = "0" + sec;
        }

        String timeFormat = min + ":" + sec;
        String eventName = getCustomEventDisplayName(eventType);

        // 自定义事件格式：末影龙事件 在 3:45
        return "§c" + eventName + " §7在 §a" + timeFormat;
    }

    /**
     * 获取自定义事件的显示名称
     */
    private String getCustomEventDisplayName(String eventType) {
        switch (eventType) {
            case "dragon":
                return "末影龙事件";
            case "bed-destroy":
                return "床摧毁事件";
            case "game-end":
                return "游戏结束";
            default:
                return "自定义事件";
        }
    }

    /**
     * 智能获取下一个升级事件的名称（根据当前模式）
     */
    private String getNextUpgradeName(Arena arena) {
        // 检测当前竞技场使用的模式
        GameModeManager gameModeManager = plugin.getGameModeManager();
        if (gameModeManager != null) {
            String currentMode = gameModeManager.getCurrentMode(arena);

            if ("unlimited-fire".equals(currentMode)) {
                return getUnlimitedFireUpgradeName(arena);
            } else if ("low-fire".equals(currentMode)) {
                return getLowFireUpgradeName(arena);
            } else {
                return getGlobalUpgradeName(arena);
            }
        }

        // 如果无法检测模式，尝试全局升级系统
        return getGlobalUpgradeName(arena);
    }

    /**
     * 获取无限火力模式的下一个升级名称
     */
    private String getUnlimitedFireUpgradeName(Arena arena) {
        try {
            UnlimitedFireMode.UpgradeState upgradeState = UnlimitedFireMode.getUpgradeState(arena);
            if (upgradeState == null) {
                return "暂无";
            }

            UnlimitedFireMode.UpgradeInfo nextUpgrade = upgradeState.getCurrentUpgrade();
            if (nextUpgrade == null) {
                return "暂无";
            }

            // 检查是否是自定义事件
            if (!"upgrade".equals(nextUpgrade.eventType)) {
                return getCustomEventDisplayName(nextUpgrade.eventType);
            }

            String resourceName = getResourceDisplayName(nextUpgrade.resourceType);
            return resourceName + " " + nextUpgrade.tier + "级";
        } catch (Exception e) {
            return "暂无";
        }
    }

    /**
     * 获取火力不足模式的下一个升级名称
     */
    private String getLowFireUpgradeName(Arena arena) {
        try {
            LowFireMode.UpgradeState upgradeState = LowFireMode.getUpgradeState(arena);
            if (upgradeState == null) {
                return "暂无";
            }

            LowFireMode.UpgradeInfo nextUpgrade = upgradeState.getCurrentUpgrade();
            if (nextUpgrade == null) {
                return "暂无";
            }

            // 检查是否是自定义事件
            if (!"upgrade".equals(nextUpgrade.eventType)) {
                return getCustomEventDisplayName(nextUpgrade.eventType);
            }

            String resourceName = getResourceDisplayName(nextUpgrade.resourceType);
            return resourceName + " " + nextUpgrade.tier + "级";
        } catch (Exception e) {
            return "暂无";
        }
    }

    /**
     * 获取全局模式的下一个升级名称
     */
    private String getGlobalUpgradeName(Arena arena) {
        try {
            GlobalUpgradeManager globalUpgradeManager = plugin.getGlobalUpgradeManager();
            if (globalUpgradeManager == null) {
                return "暂无";
            }

            GlobalUpgradeManager.UpgradeInfo nextUpgrade = globalUpgradeManager.getNextUpgrade(arena);
            if (nextUpgrade == null) {
                return "暂无";
            }

            // 检查是否是自定义事件
            if (!"upgrade".equals(nextUpgrade.eventType)) {
                return getCustomEventDisplayName(nextUpgrade.eventType);
            }

            String resourceName = getResourceDisplayName(nextUpgrade.resourceType);
            return resourceName + " " + nextUpgrade.tier + "级";
        } catch (Exception e) {
            return "暂无";
        }
    }

    /**
     * 获取等级显示（中文化）
     */
    private String getTierRoman(int tier) {
        switch (tier) {
            case 1:
                return "I级";
            case 2:
                return "II级";
            case 3:
                return "III级";
            case 4:
                return "IV级";
            case 5:
                return "V级";
            default:
                return tier + "级";
        }
    }
}
