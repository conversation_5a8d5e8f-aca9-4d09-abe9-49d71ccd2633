package de.marcely.bedwars.api.arena.picker.condition;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionInput;
import de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariable;
import de.marcely.bedwars.api.arena.picker.condition.variable.ArenaConditionVariableValue;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.tools.Validate;

/**
 * Represents a data object for comparing two inputs with an operator.
 */
public class ArenaConditionComparative extends ArenaCondition {

  private final ArenaConditionInput leftInput;
  private final ArenaConditionComparisonOperator comparisonOperator;
  private final ArenaConditionInput rightInput;

  public ArenaConditionComparative(ArenaConditionVariable<?> variable, ArenaConditionComparisonOperator comparisonOperator, ArenaConditionVariableValue<?> validatingValue) {
    Validate.notNull(variable, "variable");
    Validate.notNull(comparisonOperator, "comparisonOperator");
    Validate.notNull(validatingValue, "validatingValue");
    Validate.isTrue(variable.getValueClass() == validatingValue.getClass(), "validatingValue is not equal to the expected class of variable");

    this.leftInput = ArenaConditionInput.of(variable);
    this.comparisonOperator = comparisonOperator;
    this.rightInput = ArenaConditionInput.of(validatingValue);
  }

  public ArenaConditionComparative(ArenaConditionInput leftInput, ArenaConditionComparisonOperator comparisonOperator, ArenaConditionInput rightInput) {
    Validate.notNull(leftInput, "leftInput");
    Validate.notNull(comparisonOperator, "comparisonOperator");
    Validate.notNull(rightInput, "rightInput");

    this.leftInput = leftInput;
    this.comparisonOperator = comparisonOperator;
    this.rightInput = rightInput;
  }

  /**
   * Get the input of the left side that we want to check.
   *
   * @return The input on the left side
   */
  public ArenaConditionInput getLeftInput() {
    return this.leftInput;
  }

  /**
   * Get the input of the right side that we want to check.
   *
   * @return The input on the right side
   */
  public ArenaConditionInput getRightInput() {
    return this.rightInput;
  }

  /**
   * Returns the operator of this condition.
   *
   * @return The operator
   */
  public ArenaConditionComparisonOperator getComparisonOperator() {
    return this.comparisonOperator;
  }

  @Override
  public boolean check(Arena arena) {
    Validate.notNull(arena, "arena");

    final ArenaConditionVariableValue<?> leftValue = this.leftInput.getValue(arena);
    final ArenaConditionVariableValue<?> rightValue = this.rightInput.getValue(arena);

    if (leftValue.getType() != rightValue.getType())
      return false;

    return leftValue.check(this.comparisonOperator, rightValue);
  }

  @Override
  public boolean check(RemoteArena arena) {
    Validate.notNull(arena, "arena");

    final ArenaConditionVariableValue<?> leftValue = this.leftInput.getValue(arena);
    final ArenaConditionVariableValue<?> rightValue = this.rightInput.getValue(arena);

    if (leftValue.getType() != rightValue.getType())
      return false;

    return leftValue.check(this.comparisonOperator, rightValue);
  }

  @Override
  public String toString() {
    final StringBuilder builder = new StringBuilder();

    builder.append("(left=");
    builder.append(this.leftInput);
    builder.append(", operator=");
    builder.append(this.comparisonOperator.name());
    builder.append(", right=");
    builder.append(this.rightInput);
    builder.append(")");

    return builder.toString();
  }
}
