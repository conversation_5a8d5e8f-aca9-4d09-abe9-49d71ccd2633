package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.gamemode.TerrorDescentMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 队伍属性效果管理系统
 * 负责管理击杀Boss后给予队伍的属性效果
 */
public class TeamEffectManager {

    private final AceVoteMode plugin;
    private final TerrorDescentMode terrorMode;

    // 队伍效果任务 (竞技场 -> 队伍 -> 任务)
    private final Map<Arena, Map<Team, TeamEffectData>> teamEffects = new ConcurrentHashMap<>();

    // 效果配置
    private final Map<String, EffectConfig> effectConfigs = new HashMap<>();

    public TeamEffectManager(AceVoteMode plugin, TerrorDescentMode terrorMode) {
        this.plugin = plugin;
        this.terrorMode = terrorMode;

        // 加载效果配置
        loadEffectConfigs();
    }

    /**
     * 效果配置类
     */
    private static class EffectConfig {
        final PotionEffectType effectType;
        final boolean enabled;
        final int level;
        final int duration;

        EffectConfig(PotionEffectType effectType, boolean enabled, int level, int duration) {
            this.effectType = effectType;
            this.enabled = enabled;
            this.level = level;
            this.duration = duration;
        }
    }

    /**
     * 队伍效果数据类
     */
    private static class TeamEffectData {
        final long startTime;
        final int duration;
        final BukkitTask refreshTask;
        final BukkitTask removeTask;

        TeamEffectData(long startTime, int duration, BukkitTask refreshTask, BukkitTask removeTask) {
            this.startTime = startTime;
            this.duration = duration;
            this.refreshTask = refreshTask;
            this.removeTask = removeTask;
        }
    }

    /**
     * 加载效果配置
     */
    private void loadEffectConfigs() {
        FileConfiguration config = terrorMode.getConfig();

        // 加载各种效果的配置，支持单独的持续时间
        effectConfigs.put("strength", new EffectConfig(
                PotionEffectType.INCREASE_DAMAGE,
                config.getBoolean("team-effects.effects.strength.enabled", true),
                config.getInt("team-effects.effects.strength.level", 2),
                config.getInt("team-effects.effects.strength.duration", 300)));

        effectConfigs.put("speed", new EffectConfig(
                PotionEffectType.SPEED,
                config.getBoolean("team-effects.effects.speed.enabled", true),
                config.getInt("team-effects.effects.speed.level", 2),
                config.getInt("team-effects.effects.speed.duration", 300)));

        effectConfigs.put("resistance", new EffectConfig(
                PotionEffectType.DAMAGE_RESISTANCE,
                config.getBoolean("team-effects.effects.resistance.enabled", true),
                config.getInt("team-effects.effects.resistance.level", 1),
                config.getInt("team-effects.effects.resistance.duration", 300)));

        effectConfigs.put("regeneration", new EffectConfig(
                PotionEffectType.REGENERATION,
                config.getBoolean("team-effects.effects.regeneration.enabled", true),
                config.getInt("team-effects.effects.regeneration.level", 1),
                config.getInt("team-effects.effects.regeneration.duration", 180)));

        effectConfigs.put("haste", new EffectConfig(
                PotionEffectType.FAST_DIGGING,
                config.getBoolean("team-effects.effects.haste.enabled", false),
                config.getInt("team-effects.effects.haste.level", 2),
                config.getInt("team-effects.effects.haste.duration", 300)));

        effectConfigs.put("jump_boost", new EffectConfig(
                PotionEffectType.JUMP,
                config.getBoolean("team-effects.effects.jump_boost.enabled", false),
                config.getInt("team-effects.effects.jump_boost.level", 2),
                config.getInt("team-effects.effects.jump_boost.duration", 300)));

        effectConfigs.put("fire_resistance", new EffectConfig(
                PotionEffectType.FIRE_RESISTANCE,
                config.getBoolean("team-effects.effects.fire_resistance.enabled", false),
                config.getInt("team-effects.effects.fire_resistance.level", 1),
                config.getInt("team-effects.effects.fire_resistance.duration", 300)));

        effectConfigs.put("water_breathing", new EffectConfig(
                PotionEffectType.WATER_BREATHING,
                config.getBoolean("team-effects.effects.water_breathing.enabled", false),
                config.getInt("team-effects.effects.water_breathing.level", 1),
                config.getInt("team-effects.effects.water_breathing.duration", 300)));
    }

    /**
     * 给予队伍属性效果
     */
    public void applyTeamEffects(Arena arena, Team team) {
        plugin.getLogger().info("开始给予队伍属性效果 - 竞技场: " + arena.getName() + ", 队伍: " + team.getDisplayName());

        FileConfiguration config = terrorMode.getConfig();

        if (!config.getBoolean("team-effects.enabled", true)) {
            plugin.getLogger().warning("队伍属性效果已禁用，跳过给予效果");
            return;
        }

        // 移除现有效果（如果有）
        removeTeamEffects(arena, team);

        int duration = config.getInt("team-effects.duration", 300); // 默认5分钟

        // 应用效果到队伍所有玩家
        applyEffectsToTeamPlayers(arena, team, duration);

        // 启动效果刷新任务
        startEffectRefreshTask(arena, team, duration);

        // 启动效果移除任务
        startEffectRemoveTask(arena, team, duration);

        // 广播效果获得消息
        if (config.getBoolean("team-effects.announce", true)) {
            broadcastEffectGained(arena, team, duration);
        }

        plugin.getLogger().info("队伍 " + team.getDisplayName() + " 在竞技场 " + arena.getName() + " 获得了属性效果，持续时间: " + duration + "秒");
    }

    /**
     * 应用效果到队伍所有玩家
     */
    private void applyEffectsToTeamPlayers(Arena arena, Team team, int duration) {
        List<Player> teamPlayers = arena.getPlayersInTeam(team);

        for (Player player : teamPlayers) {
            if (player.isOnline()) {
                applyEffectsToPlayer(player, duration);
            }
        }
    }

    /**
     * 应用效果到单个玩家
     */
    private void applyEffectsToPlayer(Player player, int baseDuration) {
        FileConfiguration config = terrorMode.getConfig();
        boolean overrideExisting = config.getBoolean("team-effects.override-existing", true);

        for (EffectConfig effectConfig : effectConfigs.values()) {
            if (effectConfig.enabled) {
                // 使用效果自己的持续时间，如果没有则使用基础持续时间
                int effectDuration = effectConfig.duration > 0 ? effectConfig.duration : baseDuration;

                PotionEffect effect = new PotionEffect(
                        effectConfig.effectType,
                        effectDuration * 20, // 转换为tick
                        effectConfig.level - 1, // 等级从0开始
                        false, // 不显示粒子效果
                        true // 显示图标
                );
                player.addPotionEffect(effect, overrideExisting);
            }
        }
    }

    /**
     * 启动效果刷新任务
     */
    private void startEffectRefreshTask(Arena arena, Team team, int duration) {
        BukkitTask refreshTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!terrorMode.isArenaActive(arena)) {
                    cancel();
                    return;
                }

                // 刷新队伍所有玩家的效果
                List<Player> teamPlayers = arena.getPlayersInTeam(team);
                for (Player player : teamPlayers) {
                    if (player.isOnline()) {
                        // 检查玩家是否还有效果，如果没有则重新应用
                        boolean hasAnyEffect = false;
                        for (EffectConfig effectConfig : effectConfigs.values()) {
                            if (effectConfig.enabled && player.hasPotionEffect(effectConfig.effectType)) {
                                hasAnyEffect = true;
                                break;
                            }
                        }

                        if (!hasAnyEffect) {
                            // 计算剩余时间
                            TeamEffectData effectData = getTeamEffectData(arena, team);
                            if (effectData != null) {
                                long elapsedTime = (System.currentTimeMillis() - effectData.startTime) / 1000;
                                int remainingTime = (int) Math.max(0, effectData.duration - elapsedTime);

                                if (remainingTime > 0) {
                                    applyEffectsToPlayer(player, remainingTime);
                                }
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 100L, 100L); // 每5秒检查一次

        // 启动移除任务
        BukkitTask removeTask = new BukkitRunnable() {
            @Override
            public void run() {
                removeTeamEffects(arena, team);
            }
        }.runTaskLater(plugin, duration * 20L);

        // 保存任务数据
        TeamEffectData effectData = new TeamEffectData(
                System.currentTimeMillis(),
                duration,
                refreshTask,
                removeTask);

        teamEffects.computeIfAbsent(arena, k -> new ConcurrentHashMap<>()).put(team, effectData);
    }

    /**
     * 启动效果移除任务
     */
    private void startEffectRemoveTask(Arena arena, Team team, int duration) {
        // 在倒计时最后阶段发送提醒
        int[] reminders = { 60, 30, 10, 5 }; // 提醒时间点（秒）

        for (int reminder : reminders) {
            if (reminder < duration) {
                int delay = (duration - reminder) * 20; // 转换为tick

                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (terrorMode.isArenaActive(arena) && hasTeamEffects(arena, team)) {
                        broadcastEffectReminder(arena, team, reminder);
                    }
                }, delay);
            }
        }
    }

    /**
     * 移除队伍效果
     */
    public void removeTeamEffects(Arena arena, Team team) {
        Map<Team, TeamEffectData> arenaEffects = teamEffects.get(arena);
        if (arenaEffects == null) {
            return;
        }

        TeamEffectData effectData = arenaEffects.remove(team);
        if (effectData != null) {
            // 取消任务
            if (effectData.refreshTask != null) {
                effectData.refreshTask.cancel();
            }
            if (effectData.removeTask != null) {
                effectData.removeTask.cancel();
            }

            // 移除玩家效果
            List<Player> teamPlayers = arena.getPlayersInTeam(team);
            for (Player player : teamPlayers) {
                if (player.isOnline()) {
                    removeEffectsFromPlayer(player);
                }
            }

            plugin.getLogger().info("队伍 " + team.getDisplayName() + " 在竞技场 " + arena.getName() + " 的属性效果已移除");
        }
    }

    /**
     * 移除玩家的效果
     */
    private void removeEffectsFromPlayer(Player player) {
        for (EffectConfig effectConfig : effectConfigs.values()) {
            if (effectConfig.enabled) {
                player.removePotionEffect(effectConfig.effectType);
            }
        }
    }

    /**
     * 广播效果获得消息
     */
    private void broadcastEffectGained(Arena arena, Team team, int duration) {
        String message = terrorMode.getConfig().getString("messages.effect-gained", "&b&l【强化】&f 你的队伍获得了强大的属性效果！");
        message = message.replace("{team}", team.getDisplayName());
        message = message.replace("{duration}", formatTime(duration));
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

        // 获取启用的效果列表
        List<String> enabledEffects = getEnabledEffectNames();
        String effectDetails = terrorMode.getConfig().getString("messages.effect-details", "&7获得效果: {effects}");
        effectDetails = effectDetails.replace("{effects}", String.join("&7, &b", enabledEffects));
        String coloredEffectDetails = ChatColor.translateAlternateColorCodes('&', effectDetails);

        // 只向该队伍的玩家发送消息
        List<Player> teamPlayers = arena.getPlayersInTeam(team);
        for (Player player : teamPlayers) {
            player.sendMessage(coloredMessage);
            if (!enabledEffects.isEmpty()) {
                player.sendMessage(coloredEffectDetails);
            }
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
        }

        // 向其他队伍发送不同的消息
        String otherTeamMessage = "&c&l【警告】&f " + team.getDisplayName() + " 队伍获得了强大的属性效果！";
        String coloredOtherMessage = ChatColor.translateAlternateColorCodes('&', otherTeamMessage);

        for (Player player : arena.getPlayers()) {
            Team playerTeam = arena.getPlayerTeam(player);
            if (playerTeam != null && !playerTeam.equals(team)) {
                player.sendMessage(coloredOtherMessage);
            }
        }
    }

    /**
     * 广播效果提醒消息
     */
    private void broadcastEffectReminder(Arena arena, Team team, int remainingSeconds) {
        String message = "&e&l【提醒】&f 属性效果将在 &e" + remainingSeconds + "秒&f 后消失！";
        String coloredMessage = ChatColor.translateAlternateColorCodes('&', message);

        List<Player> teamPlayers = arena.getPlayersInTeam(team);
        for (Player player : teamPlayers) {
            player.sendMessage(coloredMessage);
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 0.8f);
        }
    }

    /**
     * 格式化时间
     */
    private String formatTime(int seconds) {
        int minutes = seconds / 60;
        int remainingSeconds = seconds % 60;

        if (minutes > 0) {
            return minutes + "分" + (remainingSeconds > 0 ? remainingSeconds + "秒" : "");
        } else {
            return remainingSeconds + "秒";
        }
    }

    /**
     * 检查队伍是否有效果
     */
    public boolean hasTeamEffects(Arena arena, Team team) {
        Map<Team, TeamEffectData> arenaEffects = teamEffects.get(arena);
        return arenaEffects != null && arenaEffects.containsKey(team);
    }

    /**
     * 获取队伍效果剩余时间
     */
    public int getTeamEffectRemainingTime(Arena arena, Team team) {
        TeamEffectData effectData = getTeamEffectData(arena, team);
        if (effectData == null) {
            return 0;
        }

        long elapsedTime = (System.currentTimeMillis() - effectData.startTime) / 1000;
        return (int) Math.max(0, effectData.duration - elapsedTime);
    }

    /**
     * 获取队伍效果数据
     */
    private TeamEffectData getTeamEffectData(Arena arena, Team team) {
        Map<Team, TeamEffectData> arenaEffects = teamEffects.get(arena);
        return arenaEffects != null ? arenaEffects.get(team) : null;
    }

    /**
     * 处理玩家加入队伍
     */
    public void onPlayerJoinTeam(Arena arena, Player player, Team team) {
        if (hasTeamEffects(arena, team)) {
            // 给新加入的玩家应用效果
            int remainingTime = getTeamEffectRemainingTime(arena, team);
            if (remainingTime > 0) {
                applyEffectsToPlayer(player, remainingTime);

                String message = "&b&l【强化】&f 你加入了拥有属性效果的队伍！剩余时间: &b" + formatTime(remainingTime);
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
            }
        }
    }

    /**
     * 处理玩家离开队伍
     */
    public void onPlayerLeaveTeam(Arena arena, Player player, Team team) {
        // 移除玩家的效果
        removeEffectsFromPlayer(player);
    }

    /**
     * 清理竞技场
     */
    public void cleanupArena(Arena arena) {
        Map<Team, TeamEffectData> arenaEffects = teamEffects.remove(arena);
        if (arenaEffects != null) {
            for (Map.Entry<Team, TeamEffectData> entry : arenaEffects.entrySet()) {
                TeamEffectData effectData = entry.getValue();

                // 取消任务
                if (effectData.refreshTask != null) {
                    effectData.refreshTask.cancel();
                }
                if (effectData.removeTask != null) {
                    effectData.removeTask.cancel();
                }

                // 移除玩家效果
                List<Player> teamPlayers = arena.getPlayersInTeam(entry.getKey());
                for (Player player : teamPlayers) {
                    if (player.isOnline()) {
                        removeEffectsFromPlayer(player);
                    }
                }
            }
        }
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        loadEffectConfigs();
    }

    /**
     * 获取效果状态信息
     */
    public String getEffectStatus(Arena arena, Team team) {
        if (!hasTeamEffects(arena, team)) {
            return "无属性效果";
        }

        int remainingTime = getTeamEffectRemainingTime(arena, team);
        return "属性效果剩余: " + formatTime(remainingTime);
    }

    /**
     * 获取所有启用的效果名称
     */
    public List<String> getEnabledEffectNames() {
        List<String> names = new ArrayList<>();
        for (Map.Entry<String, EffectConfig> entry : effectConfigs.entrySet()) {
            if (entry.getValue().enabled) {
                names.add(getEffectDisplayName(entry.getKey()));
            }
        }
        return names;
    }

    /**
     * 获取效果显示名称
     */
    private String getEffectDisplayName(String effectKey) {
        switch (effectKey) {
            case "strength":
                return "力量";
            case "speed":
                return "速度";
            case "resistance":
                return "抗性";
            case "regeneration":
                return "再生";
            case "haste":
                return "急迫";
            case "jump_boost":
                return "跳跃提升";
            default:
                return effectKey;
        }
    }
}
