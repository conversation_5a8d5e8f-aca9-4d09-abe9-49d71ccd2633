package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaVotingHandler;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a player votes for an arena in the voting phase.
 */
public class PlayerArenaVoteEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final Arena oldChoice, newChoice;

  @Getter @Setter
  private boolean cancelled;

  public PlayerArenaVoteEvent(Player player, Arena arena, @Nullable Arena oldChoice, @Nullable Arena newChoice) {
    super(player);

    this.arena = arena;
    this.oldChoice = oldChoice;
    this.newChoice = newChoice;
  }

  /**
   * Get the voting handler of the arena in which the voting is happening.
   *
   * @return The voting handler of the arena in which the voting is happening.
   */
  public ArenaVotingHandler getVoting() {
    return this.arena.getVoting();
  }

  /**
   * Get the previous choice of the player.
   *
   * @return The previous choice of the player, may be <code>null</code> if the player didn't have a previous choice.
   */
  @Nullable
  public Arena getOldChoice() {
    return this.oldChoice;
  }

  /**
   * Get the new choice of the player.
   *
   * @return The new choice of the player, may be <code>null</code> if the player removed his vote.
   */
  @Nullable
  public Arena getNewChoice() {
    return this.newChoice;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
