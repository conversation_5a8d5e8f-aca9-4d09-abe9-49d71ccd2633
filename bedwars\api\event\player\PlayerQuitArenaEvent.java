package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.*;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a player leaves an arena
 */
public class PlayerQuitArenaEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private final KickReason reason;
  private final Team team;
  private final QuitPlayerMemory memory;

  public PlayerQuitArenaEvent(Player player, Arena arena, KickReason reason, @Nullable Team team, @Nullable QuitPlayerMemory memory) {
    super(player);

    this.arena = arena;
    this.reason = reason;
    this.team = team;
    this.memory = memory;
  }

  /**
   * Returns the reason why he left the arena.
   *
   * @return The reason
   */
  public KickReason getReason() {
    return this.reason;
  }

  /**
   * Returns the team in which he was before he left the arena.
   * <p>
   * Might be <code>null</code> when they didn't join any as it's possible during the lobby phase.
   *
   * @return The arena in which he played in
   */
  @Nullable
  public Team getTeam() {
    return this.team;
  }

  /**
   * Returns the memory of his past ingame state.
   * <p>
   *     It might be <code>null</code> if the match hasn't started yet or it has already ended past the endlobby state.
   * </p>
   *
   * @return The memory of the player. Might be <code>null</code>
   */
  @Nullable
  public QuitPlayerMemory getMemory() {
    return this.memory;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
