package cn.acebrand.acevotemode;

import org.bukkit.plugin.java.JavaPlugin;

/**
 * 简单的测试插件，用于验证基本功能
 */
public class TestPlugin extends JavaPlugin {
    
    @Override
    public void onEnable() {
        getLogger().info("=== TEST PLUGIN STARTING ===");
        getLogger().info("If you see this message, the plugin can load successfully!");
        getLogger().info("Plugin: " + getName());
        getLogger().info("Version: " + getDescription().getVersion());
        getLogger().info("=== TEST PLUGIN ENABLED ===");
    }
    
    @Override
    public void onDisable() {
        getLogger().info("=== TEST PLUGIN DISABLED ===");
    }
}
