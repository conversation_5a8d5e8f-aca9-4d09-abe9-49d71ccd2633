package cn.acebrand.acevotemode.task;

import cn.acebrand.acevotemode.AceVoteMode;
import cn.acebrand.acevotemode.manager.VoteManager;
import cn.acebrand.acevotemode.model.GameMode;
import cn.acebrand.acevotemode.util.MessageUtil;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.ArenaStatus;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.time.Duration;
import java.util.Map;

/**
 * 投票倒计时任务
 * 负责处理投票倒计时和结果显示
 */
public class VoteCountdownTask extends BukkitRunnable {
    
    private final AceVoteMode plugin;
    private final Arena arena;
    private final Duration totalDuration;
    private int remainingSeconds;
    private int lastResultDisplay;
    
    public VoteCountdownTask(AceVoteMode plugin, Arena arena, Duration duration) {
        this.plugin = plugin;
        this.arena = arena;
        this.totalDuration = duration;
        this.remainingSeconds = (int) duration.getSeconds();
        this.lastResultDisplay = 0;
    }
    
    @Override
    public void run() {
        // 检查竞技场状态
        if (arena.getStatus() != ArenaStatus.LOBBY) {
            cancel();
            return;
        }
        
        VoteManager.ArenaVoteData voteData = plugin.getVoteManager().getArenaVoteData(arena);
        
        // 检查是否需要关闭投票
        int voteDisableSeconds = plugin.getConfigManager().getVoteDisableSeconds();
        if (remainingSeconds <= voteDisableSeconds && !voteData.isVotingClosed()) {
            // 关闭投票
            plugin.getVoteManager().closeVoting(arena);

            // 通知所有玩家投票已关闭
            String message = plugin.getConfigManager().getMessage("vote-closed");
            for (Player player : arena.getPlayers()) {
                player.sendMessage(message);
            }

            plugin.getLogger().info("Voting closed for arena: " + arena.getName() +
                    " (remaining: " + remainingSeconds + "s)");

            // 显示投票统计
            MessageUtil.displayVoteStatistics(plugin, arena);

            // 延迟显示动态投票结果GUI并选择模式
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                // 显示动态投票结果GUI并选择模式（不立即应用）
                plugin.getVoteResultManager().showVoteResultAndSelectMode(arena);
            }, 40L); // 2秒延迟，让投票统计先显示
        }
        
        // 显示投票结果（只在投票未关闭时显示）
        if (plugin.getConfigManager().isShowVoteCount() && !voteData.isVotingClosed()) {
            int displayInterval = plugin.getConfigManager().getResultDisplayInterval();
            if (remainingSeconds % displayInterval == 0 && remainingSeconds != lastResultDisplay) {
                displayVoteResults();
                lastResultDisplay = remainingSeconds;
            }
        }
        
        remainingSeconds--;
        
        // 倒计时结束
        if (remainingSeconds < 0) {
            cancel();
        }
    }
    
    /**
     * 显示投票结果
     */
    private void displayVoteResults() {
        VoteManager.ArenaVoteData voteData = plugin.getVoteManager().getArenaVoteData(arena);
        Map<String, Integer> voteCounts = voteData.getAllVoteCounts();

        if (voteCounts.isEmpty()) {
            // 没有人投票时显示美化的提示信息
            for (Player player : arena.getPlayers()) {
                player.sendMessage("");
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                player.sendMessage(ChatColor.YELLOW + "" + ChatColor.BOLD + "         📊 当前投票情况 📊");
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                player.sendMessage("");
                player.sendMessage(ChatColor.GRAY + "         💭 当前没有人投票");
                player.sendMessage(ChatColor.GRAY + "         快来投票选择你喜欢的模式吧！");
                player.sendMessage("");
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                player.sendMessage(ChatColor.GRAY + "剩余时间: " + ChatColor.YELLOW + "" + ChatColor.BOLD + remainingSeconds + "秒");
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                player.sendMessage("");
            }
            return; // 已经发送了所有消息，直接返回
        } else {
            // 显示所有模式的投票情况

            // 发送装饰性标题
            for (Player player : arena.getPlayers()) {
                player.sendMessage("");
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                player.sendMessage(ChatColor.YELLOW + "" + ChatColor.BOLD + "         📊 当前投票情况 📊");
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
            }

            // 按票数排序显示所有模式
            voteCounts.entrySet().stream()
                    .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                    .forEach(entry -> {
                        String modeId = entry.getKey();
                        int votes = entry.getValue();

                        GameMode gameMode = plugin.getConfigManager().getGameModes().get(modeId);
                        if (gameMode != null) {
                            // 创建投票条形图效果
                            StringBuilder voteBar = new StringBuilder();
                            for (int i = 0; i < votes; i++) {
                                voteBar.append("█");
                            }

                            String voteMessage = ChatColor.WHITE + " ▸ " + gameMode.getPlainName() +
                                    ChatColor.GRAY + " (" + ChatColor.GREEN + votes + " 票" + ChatColor.GRAY + ") " +
                                    ChatColor.GREEN + voteBar.toString();

                            // 发送给竞技场中的所有玩家
                            for (Player player : arena.getPlayers()) {
                                player.sendMessage(voteMessage);
                            }
                        }
                    });

            // 显示总票数和底部装饰
            int totalVotes = voteData.getTotalVotes();
            for (Player player : arena.getPlayers()) {
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                player.sendMessage(ChatColor.GRAY + "总投票数: " + ChatColor.WHITE + "" + ChatColor.BOLD + totalVotes +
                        ChatColor.GRAY + " | 剩余时间: " + ChatColor.YELLOW + "" + ChatColor.BOLD + remainingSeconds + "秒");
                player.sendMessage(ChatColor.GOLD + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                player.sendMessage("");
            }

            return; // 已经发送了所有消息，直接返回
        }
    }
    
    /**
     * 获取剩余秒数
     */
    public int getRemainingSeconds() {
        return remainingSeconds;
    }
    
    /**
     * 获取总时长
     */
    public Duration getTotalDuration() {
        return totalDuration;
    }
    
    /**
     * 获取竞技场
     */
    public Arena getArena() {
        return arena;
    }
}
