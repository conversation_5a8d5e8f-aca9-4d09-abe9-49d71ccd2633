package cn.acebrand.mbedwarsprop.listeners;

import cn.acebrand.mbedwarsprop.MBedwarsProp;
import cn.acebrand.mbedwarsprop.items.EnderBuilderHandler;
import org.bukkit.ChatColor;
import org.bukkit.entity.Enderman;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;

/**
 * 处理末影建筑师相关的事件
 */
public class EnderBuilderListener implements Listener {

    private final MBedwarsProp plugin;

    public EnderBuilderListener(MBedwarsProp plugin) {
        this.plugin = plugin;
    }

    /**
     * 处理末影人被攻击的事件
     */
    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onEnderBuilderDamage(EntityDamageEvent event) {
        Entity entity = event.getEntity();
        
        // 检查被攻击的实体是否是末影人
        if (entity instanceof Enderman) {
            Enderman enderman = (Enderman) entity;
            
            // 检查末影人是否有自定义名称（是否是建筑师）
            if (enderman.isCustomNameVisible() && enderman.getCustomName() != null && 
                enderman.getCustomName().contains("的建筑师")) {
                
                // 取消伤害事件
                event.setCancelled(true);
                
                // 如果是玩家攻击，发送提示消息
                if (event instanceof EntityDamageByEntityEvent) {
                    EntityDamageByEntityEvent damageByEntityEvent = (EntityDamageByEntityEvent) event;
                    
                    if (damageByEntityEvent.getDamager() instanceof Player) {
                        Player player = (Player) damageByEntityEvent.getDamager();
                        player.sendMessage(ChatColor.RED + "这是一个末影建筑师，它不能被攻击！");
                    }
                }
            }
        }
    }
    
    /**
     * 处理玩家与末影人交互的事件
     */
    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onEnderBuilderInteract(PlayerInteractEntityEvent event) {
        Entity entity = event.getRightClicked();
        
        // 检查交互的实体是否是末影人
        if (entity instanceof Enderman) {
            Enderman enderman = (Enderman) entity;
            
            // 检查末影人是否有自定义名称（是否是建筑师）
            if (enderman.isCustomNameVisible() && enderman.getCustomName() != null && 
                enderman.getCustomName().contains("的建筑师")) {
                
                // 取消交互事件
                event.setCancelled(true);
                
                // 发送提示消息
                Player player = event.getPlayer();
                player.sendMessage(ChatColor.YELLOW + "这是一个末影建筑师，它正在专心建造桥梁！");
            }
        }
    }
}
