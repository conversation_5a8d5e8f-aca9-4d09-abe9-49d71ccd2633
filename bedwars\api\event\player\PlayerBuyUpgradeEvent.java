package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.upgrade.UpgradeLevel;
import de.marcely.bedwars.tools.Validate;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.plugin.Plugin;

import java.util.Collections;
import java.util.List;

public class PlayerBuyUpgradeEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final Arena arena;
  private final Team team;
  private final UpgradeLevel level;

  private final List<Problem> problems;
  private boolean broadcastingMessage;
  private boolean broadcastingSound;
  private boolean doingUpgrade;
  private boolean takePayments;

  public PlayerBuyUpgradeEvent(
      Player player,
      Arena arena,
      Team team,
      UpgradeLevel level,
      List<Problem> problems,
      boolean broadcastingMessage,
      boolean broadcastingSound,
      boolean doingUpgrade,
      boolean takePayments) {

    super(player);

    this.arena = arena;
    this.team = team;
    this.level = level;
    this.problems = problems;
    this.broadcastingMessage = broadcastingMessage;
    this.broadcastingSound = broadcastingSound;
    this.doingUpgrade = doingUpgrade;
    this.takePayments = takePayments;

  }

  /**
   * Returns the arena in which the player is playing.
   *
   * @return The arena in which the player currently is inside
   */
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the team in which the player bought the upgrade for.
   *
   * @return the team that is being upgraded
   */
  public Team getTeam() {
    return this.team;
  }

  /**
   * Returns if the handler attached to this upgrade level is a trap handler
   *
   * @return check if the upgrade level that was purchased is a trap
   */
  public boolean isTrap() {
    return level.isTrap();
  }

  /**
   * The upgrade level that was purchased by the player
   *
   * @return get the upgrade level that was purchased
   */
  public UpgradeLevel getUpgradeLevel() {
    return this.level;
  }

  /**
   * Whether or not a message will be sent out to all team players when an
   * upgrade or trap is purchased
   *
   * @return if a message will be broadcast
   */
  public boolean isBroadcastingMessage() {
    return this.broadcastingMessage;
  }

  /**
   * Sets whether or not a message will be sent out to all team players when an
   * upgrade or trap is purchased
   *
   * @param broadcastingMessage if a message should be broadcast
   */
  public void setBroadcastingMessage(boolean broadcastingMessage) {
    this.broadcastingMessage = broadcastingMessage;
  }

  /**
   * Whether or not a sound will be played when an upgrade or trap is purchased
   *
   * @return if the sound is being played
   */
  public boolean isBroadcastingSound() {
    return this.broadcastingSound;
  }

  /**
   * Set whether or not a sound should be played when an upgrade or trap is purchased
   *
   * @param broadcastingSound if the sound should be played
   */
  public void setBroadcastingSound(boolean broadcastingSound) {
    this.broadcastingSound = broadcastingSound;
  }

  /**
   * Returns the problems that are preventing the item from being bought
   *
   * @return The problems
   */
  public List<Problem> getProblems() {
    return Collections.unmodifiableList(this.problems);
  }

  /**
   * Add a problem that will prevent the item from being bought.
   * <p>
   * Act's similar as {@link Cancellable#setCancelled(boolean)}.
   *
   * @param problem The problem why he can't buy it
   * @return <code>false</code> if it already has been added
   */
  public boolean addProblem(Problem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.add(problem);
  }

  /**
   * Add a problem that will prevent the item from being bought.
   * <p>
   * Act's similar as {@link Cancellable#setCancelled(boolean)}.
   *
   * @param problem The problem why he can't buy it
   * @return <code>false</code> if it already has been added
   */
  public boolean addProblem(DefaultProblem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.add(problem.get());
  }

  /**
   * Removes a problem.
   *
   * @param problem The problem
   * @return If it has been removed or not
   */
  public boolean removeProblem(Problem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.remove(problem);
  }

  /**
   * Removes a problem.
   *
   * @param problem The problem
   * @return If it has been removed or not
   */
  public boolean removeProblem(DefaultProblem problem) {
    Validate.notNull(problem, "problem");

    return this.problems.remove(problem.get());
  }

  /**
   * Removes all problems and makes it buyable by that.
   *
   * @return The amount of problems that have been removed
   */
  public int removeAllProblems() {
    final int amount = this.problems.size();

    this.problems.clear();

    return amount;
  }

  /**
   * Returns whether or not the player will receive the products of the item.
   * <p>
   * Does not concern when there are problems as he won't receive them if there are problems anyways.
   *
   * @return If he'll get the products or not
   */

  public boolean isDoingUpgrade() {
    return this.doingUpgrade;
  }

  /**
   * Define whether or not if he'll get products.
   * <p>
   * Keep in mind that problems can prevent products being given to him.
   *
   * @param doingUpgrade The new value
   */
  public void setDoingUpgrade(boolean doingUpgrade) {
    this.doingUpgrade = doingUpgrade;
  }

  /**
   * Returns whether or not it will take the payments from the player.
   * <p>
   * Does not concern when there are problems as they're preventing them from being taken anyways.
   *
   * @return If the payments will be taken or not
   */
  public boolean isTakingPayments() {
    return this.takePayments;
  }

  /**
   * Define whether or not the payments will be taken from the player.
   * <p>
   * Keep in mind that problems can prevent this
   *
   * @param takePayments The new value
   */
  public void setTakingPayments(boolean takePayments) {
    this.takePayments = takePayments;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }

  public static abstract class Problem {

    /**
     * Returns the plugin that has created this problem
     *
     * @return The plugin that created it
     */
    public abstract Plugin getPlugin();

    /**
     * Notify the player about the problem
     *
     * @param event The event that happened before
     */
    public abstract void handleNotification(PlayerBuyUpgradeEvent event);
  }

  /**
   * Default problems used and provided by the plugin
   */
  public enum DefaultProblem {

    NOT_ENOUGH_ITEMS,
    MAX_UPGRADE_VALUE;

    private Problem instance;

    /**
     * Returns the {@link PlayerBuyInShopEvent.Problem} instanced backed by this type
     *
     * @return The Problem instance of this type
     */
    public Problem get() {
      return this.instance;
    }
  }
}
