package cn.acebrand.acevotemode.gamemode.terror;

import cn.acebrand.acevotemode.AceVoteMode;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.message.Message;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;

/**
 * 增强版Boss管理器
 * 支持Boss阶段检测、技能监控、召唤物管理
 */
public class EnhancedBossManager {

    private final AceVoteMode plugin;
    private final Arena arena;
    private final ConfigurationSection config;

    // Boss状态跟踪
    private LivingEntity currentBoss;
    private String currentPhase = "none";

    // 召唤物跟踪
    private final Map<String, List<Entity>> minions = new HashMap<>();
    private final Map<String, Integer> minionCounts = new HashMap<>();

    public EnhancedBossManager(AceVoteMode plugin, Arena arena, ConfigurationSection config) {
        this.plugin = plugin;
        this.arena = arena;
        this.config = config;

        // 初始化召唤物计数
        initializeMinionCounts();

        // 启动Boss监控任务
        startBossMonitoring();
    }

    /**
     * 初始化召唤物计数
     */
    private void initializeMinionCounts() {
        ConfigurationSection minionsConfig = config.getConfigurationSection("boss.minions");
        if (minionsConfig != null) {
            for (String minionType : minionsConfig.getKeys(false)) {
                minionCounts.put(minionType, 0);
                minions.put(minionType, new ArrayList<>());
            }
        }
    }

    /**
     * 启动Boss监控任务
     */
    private void startBossMonitoring() {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (currentBoss != null && !currentBoss.isDead()) {
                    checkBossPhase();
                    cleanupDeadMinions();
                    updateBossStatus();
                } else {
                    currentBoss = null;
                    currentPhase = "none";
                }
            }
        }.runTaskTimer(plugin, 20L, 20L); // 每秒检查一次
    }

    /**
     * 设置当前Boss
     */
    public void setBoss(LivingEntity boss) {
        this.currentBoss = boss;
        this.currentPhase = "phase-1";

        // 广播Boss生成消息
        String spawnMessage = config.getString("messages.boss-spawn", "&4&l⚡【恐怖降临】⚡ &c恐怖领主已经苏醒！");
        arena.broadcast(Message.build(spawnMessage));

        plugin.getLogger().info("Enhanced Boss spawned in arena: " + arena.getName());
    }

    /**
     * 检查Boss阶段
     */
    private void checkBossPhase() {
        if (currentBoss == null)
            return;

        // 计算血量百分比
        double health = currentBoss.getHealth();
        double maxHealth = currentBoss.getMaxHealth();
        int healthPercent = (int) ((health / maxHealth) * 100);

        // 检查是否需要切换阶段
        String newPhase = determinePhase(healthPercent);
        if (!newPhase.equals(currentPhase)) {
            changePhase(newPhase, healthPercent);
        }

        // 血量百分比已更新
    }

    /**
     * 根据血量确定阶段
     */
    private String determinePhase(int healthPercent) {
        if (healthPercent >= 75) {
            return "phase-1";
        } else if (healthPercent >= 50) {
            return "phase-2";
        } else if (healthPercent >= 25) {
            return "phase-3";
        } else {
            return "phase-4";
        }
    }

    /**
     * 切换Boss阶段
     */
    private void changePhase(String newPhase, int healthPercent) {
        String oldPhase = currentPhase;
        currentPhase = newPhase;
        // 阶段切换时间已记录

        // 获取阶段信息
        ConfigurationSection phaseConfig = config.getConfigurationSection("boss.phases." + newPhase);
        if (phaseConfig != null) {
            String phaseMessage = phaseConfig.getString("message", "&c阶段切换！");

            // 广播阶段切换消息
            arena.broadcast(Message.build(phaseMessage));

            // 记录日志
            plugin.getLogger().info(String.format("Boss phase changed from %s to %s (%d%% health) in arena: %s",
                    oldPhase, newPhase, healthPercent, arena.getName()));

            // 触发阶段特殊效果
            triggerPhaseEffects(newPhase);
        }
    }

    /**
     * 触发阶段特殊效果
     */
    private void triggerPhaseEffects(String phase) {
        switch (phase) {
            case "phase-2":
                // 第二阶段：增加移动速度
                // 这里可以添加特殊效果
                break;
            case "phase-3":
                // 第三阶段：召唤更多小怪
                triggerEmergencySummon();
                break;
            case "phase-4":
                // 第四阶段：狂暴模式
                triggerBerserkMode();
                break;
        }
    }

    /**
     * 触发紧急召唤
     */
    private void triggerEmergencySummon() {
        // 召唤额外的恐怖仆从
        summonMinion("terror-minion", 2);
        arena.broadcast(Message.build("&c💀 Boss召唤了额外的恐怖仆从！"));
    }

    /**
     * 触发狂暴模式
     */
    private void triggerBerserkMode() {
        arena.broadcast(Message.build("&4&l🔥 Boss进入狂暴状态！攻击力和速度大幅提升！"));
        // 这里可以通过MythicMobs API给Boss添加效果
    }

    /**
     * 召唤小怪
     */
    public void summonMinion(String minionType, int count) {
        ConfigurationSection minionConfig = config.getConfigurationSection("boss.minions." + minionType);
        if (minionConfig == null || currentBoss == null)
            return;

        String mythicMobId = minionConfig.getString("mythic-mob-id");
        int maxCount = minionConfig.getInt("max-count", 5);
        int spawnRadius = minionConfig.getInt("spawn-radius", 10);

        // 检查当前数量
        int currentCount = minionCounts.getOrDefault(minionType, 0);
        int toSpawn = Math.min(count, maxCount - currentCount);

        if (toSpawn <= 0)
            return;

        Location bossLocation = currentBoss.getLocation();

        // 使用MythicMobs API召唤
        for (int i = 0; i < toSpawn; i++) {
            // 计算随机位置
            double angle = Math.random() * 2 * Math.PI;
            double distance = Math.random() * spawnRadius + 2;
            double x = bossLocation.getX() + Math.cos(angle) * distance;
            double z = bossLocation.getZ() + Math.sin(angle) * distance;
            Location spawnLocation = new Location(bossLocation.getWorld(), x, bossLocation.getY(), z);

            // 这里需要调用MythicMobs API
            // Entity minion = MythicBukkit.inst().getMobManager().spawnMob(mythicMobId,
            // spawnLocation);
            // if (minion != null) {
            // minions.get(minionType).add(minion);
            // minionCounts.put(minionType, currentCount + 1);
            // }
        }

        plugin.getLogger()
                .info(String.format("Summoned %d %s minions in arena: %s", toSpawn, minionType, arena.getName()));
    }

    /**
     * 清理死亡的召唤物
     */
    private void cleanupDeadMinions() {
        for (String minionType : minions.keySet()) {
            List<Entity> minionList = minions.get(minionType);
            minionList.removeIf(Entity::isDead);
            minionCounts.put(minionType, minionList.size());
        }
    }

    /**
     * 更新Boss状态
     */
    private void updateBossStatus() {
        if (currentBoss == null)
            return;

        // 检查Boss是否逃跑
        Location bossLocation = currentBoss.getLocation();
        // 这里可以添加Boss逃跑检测逻辑

        // 更新Boss血条（如果需要）
        updateBossBar();
    }

    /**
     * 更新Boss血条
     */
    private void updateBossBar() {
        if (currentBoss == null)
            return;

        double health = currentBoss.getHealth();
        double maxHealth = currentBoss.getMaxHealth();
        int healthPercent = (int) ((health / maxHealth) * 100);

        // 这里可以更新自定义血条显示
        String title = String.format("&4&l⚡ 恐怖领主 ⚡ &c%d%%", healthPercent);

        // 向竞技场内的玩家发送ActionBar
        for (Player player : arena.getPlayers()) {
            // player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
            // TextComponent.fromLegacyText(title));
        }
    }

    /**
     * Boss死亡处理
     */
    public void onBossDeath() {
        if (currentBoss == null)
            return;

        // 广播Boss死亡消息
        String deathMessage = config.getString("messages.boss-death", "&a&l🎉【史诗胜利】🎉 &f恐怖领主已被击败！");
        arena.broadcast(Message.build(deathMessage));

        // 清理所有召唤物
        clearAllMinions();

        // 重置状态
        currentBoss = null;
        currentPhase = "none";

        plugin.getLogger().info("Enhanced Boss defeated in arena: " + arena.getName());
    }

    /**
     * 清理所有召唤物
     */
    private void clearAllMinions() {
        for (List<Entity> minionList : minions.values()) {
            for (Entity minion : minionList) {
                if (!minion.isDead()) {
                    minion.remove();
                }
            }
            minionList.clear();
        }

        for (String minionType : minionCounts.keySet()) {
            minionCounts.put(minionType, 0);
        }

        arena.broadcast(Message.build("&a&l【净化】&f 所有恐怖召唤物已被清除！"));
    }

    /**
     * 获取当前Boss
     */
    public LivingEntity getCurrentBoss() {
        return currentBoss;
    }

    /**
     * 获取当前阶段
     */
    public String getCurrentPhase() {
        return currentPhase;
    }

    /**
     * 获取Boss血量百分比
     */
    public int getBossHealthPercent() {
        if (currentBoss == null)
            return 0;
        double health = currentBoss.getHealth();
        double maxHealth = currentBoss.getMaxHealth();
        return (int) ((health / maxHealth) * 100);
    }

    /**
     * 获取召唤物数量
     */
    public int getMinionCount(String minionType) {
        return minionCounts.getOrDefault(minionType, 0);
    }

    /**
     * 获取总召唤物数量
     */
    public int getTotalMinionCount() {
        return minionCounts.values().stream().mapToInt(Integer::intValue).sum();
    }
}
