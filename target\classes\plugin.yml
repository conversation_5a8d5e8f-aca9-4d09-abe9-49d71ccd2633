name: AceVoteMode
version: 1.0.0
main: cn.acebrand.acevotemode.AceVoteMode
api-version: 1.20
author: AceBrand
description: A voting system addon for Bedwars with custom game modes
website: https://acebrand.cn

depend:
  - MBedwars

softdepend:
  - MythicMobs

permissions:
  acevotemode.admin:
    description: Admin permission for AceVoteMode
    default: op
  acevotemode.vote:
    description: Permission to vote for game modes
    default: true

commands:
  acevotemode:
    description: Main command for AceVoteMode
    usage: /acevotemode <reload|help>
    permission: acevotemode.admin
    aliases: [avm]
  luckytest:
    description: Test lucky block events and rewards
    usage: /luckytest <event_type> <reward_name> [player]
    permission: acevotemode.admin
    aliases: [ltest, luckyblocktest]
  terrorboss:
    description: Manage terror descent mode boss spawn locations
    usage: /terrorboss <set|list|remove> [location_name]
    permission: acevotemode.admin
    aliases: [tboss, terrordescentboss]
  terrortest:
    description: Test terror descent mode MythicMobs configuration
    usage: /terrortest <check|spawn|list> [boss_id]
    permission: acevotemode.admin
    aliases: [ttest, terrordescenttest]
